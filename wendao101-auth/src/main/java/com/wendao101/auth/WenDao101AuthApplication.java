package com.wendao101.auth;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import com.wendao101.common.security.annotation.EnableRyFeignClients;

/**
 * 认证授权中心
 *
 * <AUTHOR>
 */
@EnableRyFeignClients
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
public class WenDao101AuthApplication {
    public static void main(String[] args) {
        SpringApplication.run(WenDao101AuthApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  认证授权中心启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                "                                                                                        dddddddd                                  \n" +
                "                                                                                        d::::::d                                  \n" +
                "                                                                                        d::::::d                                  \n" +
                "                                                                                        d::::::d                                  \n" +
                "                                                                                        d:::::d                                   \n" +
                "wwwwwww           wwwww           wwwwwww eeeeeeeeeeee    nnnn  nnnnnnnn        ddddddddd:::::d   aaaaaaaaaaaaa     ooooooooooo   \n" +
                " w:::::w         w:::::w         w:::::wee::::::::::::ee  n:::nn::::::::nn    dd::::::::::::::d   a::::::::::::a  oo:::::::::::oo \n" +
                "  w:::::w       w:::::::w       w:::::we::::::eeeee:::::een::::::::::::::nn  d::::::::::::::::d   aaaaaaaaa:::::ao:::::::::::::::o\n" +
                "   w:::::w     w:::::::::w     w:::::we::::::e     e:::::enn:::::::::::::::nd:::::::ddddd:::::d            a::::ao:::::ooooo:::::o\n" +
                "    w:::::w   w:::::w:::::w   w:::::w e:::::::eeeee::::::e  n:::::nnnn:::::nd::::::d    d:::::d     aaaaaaa:::::ao::::o     o::::o\n" +
                "     w:::::w w:::::w w:::::w w:::::w  e:::::::::::::::::e   n::::n    n::::nd:::::d     d:::::d   aa::::::::::::ao::::o     o::::o\n" +
                "      w:::::w:::::w   w:::::w:::::w   e::::::eeeeeeeeeee    n::::n    n::::nd:::::d     d:::::d  a::::aaaa::::::ao::::o     o::::o\n" +
                "       w:::::::::w     w:::::::::w    e:::::::e             n::::n    n::::nd:::::d     d:::::d a::::a    a:::::ao::::o     o::::o\n" +
                "        w:::::::w       w:::::::w     e::::::::e            n::::n    n::::nd::::::ddddd::::::dda::::a    a:::::ao:::::ooooo:::::o\n" +
                "         w:::::w         w:::::w       e::::::::eeeeeeee    n::::n    n::::n d:::::::::::::::::da:::::aaaa::::::ao:::::::::::::::o\n" +
                "          w:::w           w:::w         ee:::::::::::::e    n::::n    n::::n  d:::::::::ddd::::d a::::::::::aa:::aoo:::::::::::oo \n" +
                "           www             www            eeeeeeeeeeeeee    nnnnnn    nnnnnn   ddddddddd   ddddd  aaaaaaaaaa  aaaa  ooooooooooo   ");
    }
}
