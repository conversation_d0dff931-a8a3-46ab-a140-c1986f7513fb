# 快手课程审核任务表

## ks_course_audit_task - 快手课程审核任务表

```sql
CREATE TABLE `ks_course_audit_task` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `course_title` varchar(255) DEFAULT NULL COMMENT '课程标题',
  `teacher_id` bigint(20) NOT NULL COMMENT '老师ID',
  `app_id` varchar(50) NOT NULL COMMENT '快手应用ID',
  `app_name_type` int(11) NOT NULL COMMENT '应用类型：1-问到好课，2-问到课堂',
  `audit_status` int(11) NOT NULL DEFAULT '0' COMMENT '审核状态：0-待上传资源，1-资源上传中，2-资源上传完成待检查，3-资源检查中，4-资源检查完成待提交，5-课程提交中，6-课程审核中，7-审核通过，8-审核驳回，9-任务失败',
  `ks_course_id` varchar(100) DEFAULT NULL COMMENT '快手课程ID',
  `ks_course_version` int(11) DEFAULT NULL COMMENT '快手课程版本号',
  `error_msg` text COMMENT '错误信息',
  `retry_count` int(11) DEFAULT '0' COMMENT '重试次数',
  `max_retry_count` int(11) DEFAULT '3' COMMENT '最大重试次数',
  `next_execute_time` datetime DEFAULT NULL COMMENT '下次执行时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_next_execute_time` (`next_execute_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快手课程审核任务表';
```

## ks_course_audit_log - 快手课程审核日志表

```sql
CREATE TABLE `ks_course_audit_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `task_id` bigint(20) NOT NULL COMMENT '任务ID',
  `course_id` bigint(20) NOT NULL COMMENT '课程ID',
  `audit_status` int(11) NOT NULL COMMENT '审核状态',
  `operation` varchar(100) DEFAULT NULL COMMENT '操作描述',
  `result` int(11) DEFAULT NULL COMMENT '操作结果：1-成功，0-失败',
  `error_msg` text COMMENT '错误信息',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_id` (`task_id`),
  KEY `idx_course_id` (`course_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='快手课程审核日志表';
```

