package com.wendao101.douyin;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.message.WxMaMessageRouter;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.plusmapper.CourseOrderMapperPlus;
import com.wendao101.douyin.plusmapper.LearningMaterialsMapperPlus;
import com.wendao101.douyin.plusmapper.StudyDataMapperPlus;
import com.wendao101.ktma.api.dto.GetKtWxOpenIdResult;
import com.wendao101.ktma.api.dto.GetKtWxUserPhoneDTO;
import com.wendao101.ktma.api.dto.GetKtWxUserPhoneResult;
import com.wendao101.ktma.api.dto.KtWxJsCode2SessionDTO;
import com.wendao101.ktma.api.feign.KtWxLoginService;
import com.wendao101.teacher.domain.CourseOrder;
import com.wendao101.teacher.domain.LearningMaterials;
import com.wendao101.teacher.domain.StudyData;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest
public class WxMaTest {
    @Autowired
    private WxMaService wxService;
    @Autowired
    private RedisService redisService;

    @Autowired
    private KtWxLoginService ktWxLoginService;




    @Autowired
    private WxMaConfig config;

    @Autowired
    private WxMaMessageRouter messageRouter;
    @Test
    public void test1() throws WxErrorException {
        //String accessToken = wxService.getAccessToken();
        //System.out.println(accessToken);
    }

    @Test
    public void test2() throws WxErrorException {
        for(int i=0;i<10;i++){
            long incr = redisService.incr("202344127", 1586954);
            System.out.println(incr);
        }
    }

    @Test
    public void test3() throws WxErrorException {
        String appid = wxService.getWxMaConfig().getAppid();
        String secret = wxService.getWxMaConfig().getSecret();
        String aesKey = wxService.getWxMaConfig().getAesKey();
        String token = wxService.getWxMaConfig().getToken();
        String msgDataFormat = wxService.getWxMaConfig().getMsgDataFormat();
        System.out.println(appid);
        System.out.println(secret);
        System.out.println(aesKey);
        System.out.println(token);
        System.out.println(msgDataFormat);
        //String accessToken = wxService.getAccessToken();
        //System.out.println(accessToken);
    }

    @Test
    public void test4 (){
        GetKtWxOpenIdResult wxOpenId1 = ktWxLoginService.getWxOpenId(KtWxJsCode2SessionDTO.builder().jsCode("code").build());
        System.out.println(JSON.toJSONString(wxOpenId1));
    }

    @Test
    public void test5 (){
        GetKtWxUserPhoneResult wxUserPhone = ktWxLoginService.getWxUserPhone(GetKtWxUserPhoneDTO.builder().code("123").openid("456").build());
        System.out.println(JSON.toJSONString(wxUserPhone));
    }


    @Test
    public void test6(){
        System.out.println(config);
        System.out.println(messageRouter);
    }


}
