<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.douyin.mapper.DyCollectionManagementMapper">
    
    <resultMap type="DyCollectionManagement" id="DyCollectionManagementResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseTitle"    column="course_title"    />
        <result property="coverPicUrl"    column="cover_pic_url"    />
        <result property="price"    column="price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="courseType"    column="course_type"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="teacherDesc"    column="teacher_desc"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="type"    column="type"    />
        <result property="platform"    column="platform"    />
        <result property="visualLearnNum"    column="visual_learn_num"    />
        <result property="isDiscounts"    column="is_discounts"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDyCollectionManagementVo">
        select id, user_id, course_id, teacher_id, course_title, cover_pic_url, price, original_price, course_type, teacher_name, teacher_desc, avatar_url, type, platform, visual_learn_num, is_discounts, create_time, update_time from dy_collection_management
    </sql>

    <select id="selectDyCollectionManagementList" parameterType="DyCollectionManagement" resultMap="DyCollectionManagementResult">
        <include refid="selectDyCollectionManagementVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="courseTitle != null  and courseTitle != ''"> and course_title = #{courseTitle}</if>
            <if test="coverPicUrl != null  and coverPicUrl != ''"> and cover_pic_url = #{coverPicUrl}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="originalPrice != null "> and original_price = #{originalPrice}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="teacherDesc != null  and teacherDesc != ''"> and teacher_desc like concat('%', #{teacherDesc}, '%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url like concat('%', #{avatarUrl}, '%')</if>
            <if test="type != null "> and type = #{type}</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="visualLearnNum != null "> and visual_learn_num = #{visualLearnNum}</if>
            <if test="isDiscounts != null "> and is_discounts = #{isDiscounts}</if>
        </where>
    </select>


    <select id="selectCourseFavoriteList" parameterType="com.wendao101.douyin.vo.CourseFavoriteVO" resultMap="DyCollectionManagementResult">
        <include refid="selectDyCollectionManagementVo"/>
        <where>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="platform != null">
                and platform = #{platform}
            </if>
            and user_id = #{userId}
        </where>
       order by create_time desc
    </select>
    
    <select id="selectDyCollectionManagementById" parameterType="Long" resultMap="DyCollectionManagementResult">
        <include refid="selectDyCollectionManagementVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDyCollectionManagement" parameterType="DyCollectionManagement" useGeneratedKeys="true" keyProperty="id">
        insert into dy_collection_management
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseTitle != null">course_title,</if>
            <if test="coverPicUrl != null">cover_pic_url,</if>
            <if test="price != null">price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="courseType != null">course_type,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="teacherDesc != null">teacher_desc,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="type != null">type,</if>
            <if test="platform != null">platform,</if>
            <if test="visualLearnNum != null">visual_learn_num,</if>
            <if test="isDiscounts != null">is_discounts,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseTitle != null">#{courseTitle},</if>
            <if test="coverPicUrl != null">#{coverPicUrl},</if>
            <if test="price != null">#{price},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="teacherDesc != null">#{teacherDesc},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="type != null">#{type},</if>
            <if test="platform != null">#{platform},</if>
            <if test="visualLearnNum != null">#{visualLearnNum},</if>
            <if test="isDiscounts != null">#{isDiscounts},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <select id="selectIsFavorite" resultType="java.lang.Integer">
        select count(1) from dy_collection_management where user_id = #{userId} and course_id = #{courseId}
    </select>

    <update id="updateDyCollectionManagement" parameterType="DyCollectionManagement">
        update dy_collection_management
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseTitle != null">course_title = #{courseTitle},</if>
            <if test="coverPicUrl != null">cover_pic_url = #{coverPicUrl},</if>
            <if test="price != null">price = #{price},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="teacherDesc != null">teacher_desc = #{teacherDesc},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="type != null">type = #{type},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="visualLearnNum != null">visual_learn_num = #{visualLearnNum},</if>
            <if test="isDiscounts != null">is_discounts = #{isDiscounts},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDyCollectionManagementById" parameterType="Long">
        delete from dy_collection_management where id = #{id}
    </delete>

    <delete id="deleteById" parameterType="Long">
        delete from dy_collection_management
        <where>
            <if test="userId != null">
                and user_id = #{userId}
            </if>
            <if test="courseId != null">
                and course_id = #{courseId}
            </if>
            <if test="teacherId != null">
                and teacher_id = #{teacherId}
            </if>
        </where>
    </delete>

    <delete id="deleteDyCollectionManagementByIds" parameterType="String">
        delete from dy_collection_management where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDyCollectionManagementByIdsAndUserId">
        delete from dy_collection_management where user_id=#{userId} and id in
        <foreach item="id" collection="list" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>