<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.douyin.mapper.PcLoginMapper">
    
    <resultMap type="PcLogin" id="PcLoginResult">
        <result property="id"    column="id"    />
        <result property="uuid"    column="uuid"    />
        <result property="userId"    column="user_id"    />
        <result property="openid"    column="openid"    />
        <result property="platform"    column="platform"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPcLoginVo">
        select id, uuid, user_id, openid, platform, app_name_type, create_time, update_time from `wendao101-teacher`.pc_login
    </sql>

    <select id="selectPcLoginList" parameterType="PcLogin" resultMap="PcLoginResult">
        <include refid="selectPcLoginVo"/>
        <where>  
            <if test="uuid != null  and uuid != ''"> and uuid = #{uuid}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectPcLoginById" parameterType="Long" resultMap="PcLoginResult">
        <include refid="selectPcLoginVo"/>
        where id = #{id}
    </select>
    <select id="selectPcLoginByUuid" resultMap="PcLoginResult">
        <include refid="selectPcLoginVo"/>
        where uuid = #{uuid} limit 1
    </select>

    <insert id="insertPcLogin" parameterType="PcLogin" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-teacher`.pc_login
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uuid != null">uuid,</if>
            <if test="userId != null">user_id,</if>
            <if test="openid != null">openid,</if>
            <if test="platform != null">platform,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uuid != null">#{uuid},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openid != null">#{openid},</if>
            <if test="platform != null">#{platform},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePcLogin" parameterType="PcLogin">
        update `wendao101-teacher`.pc_login
        <trim prefix="SET" suffixOverrides=",">
            <if test="uuid != null">uuid = #{uuid},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePcLoginById" parameterType="Long">
        delete from `wendao101-teacher`.pc_login where id = #{id}
    </delete>

    <delete id="deletePcLoginByIds" parameterType="String">
        delete from `wendao101-teacher`.pc_login where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>