package com.wendao101.teacher.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 学习资料对象 study_data
 * 
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
@TableName("`wendao101-teacher`.study_data")
public class StudyData
{

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 学习文件名称 */
    @Excel(name = "学习文件名称")
    private String studyFileName;

    /** 学习文件类型 */
    @Excel(name = "学习文件类型")
    private String studyFileType;

    /** 文件url地址 */
    @Excel(name = "文件url地址")
    private String studyFileUrl;

    /** 文件大小 */
    @Excel(name = "文件大小")
    private Long studyFileSize;

    /** 审核状态 0未审核 1审核 */
    @Excel(name = "审核状态 0未审核 1审核")
    private Integer status;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer delType;


}
