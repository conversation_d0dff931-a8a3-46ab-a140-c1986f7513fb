package com.wendao101.douyin.service;

public interface SmsOrderService {

    /**
     * 发送短信  （添加老师企业微信）
     *
     * @param phoneNumber
     * @param platform
     * @param title
     * @param code
     */
    void sendJumpWxSms(String phoneNumber,String platform,String title,String code);

    /**
     * 发送短信  （添加老师企业微信）
     *
     * @param phoneNumber
     * @param platform
     * @param title
     * @param cid
     * @param tid
     */
    void sendAddTeacherWechat(String phoneNumber,String platform,String title,Long cid,Long tid);

    /**
     * 发送短信  （添加老师微信号/手机号）
     *
     * @param phoneNumber
     * @param platform
     * @param title
     * @param name
     */
    void sendAddWechatOrPhone(String phoneNumber,String platform,String title,String name);

    /**
     * 附赠实物短信
     *
     * @param phoneNumber
     * @param title
     * @param material
     * @param expNumber
     */
    void sendSmsMaterial(String phoneNumber,String title,String material,String expNumber);

    /**
     * 发送短信  （提现驳回通知）
     *
     * @param phoneNumber
     * @param reason
     */
    void sendSmsReject(String phoneNumber,String reason);


    /**
     * 发送短信 （开户通知审核中）
     *
     * @param phoneNumber
     */
    void sendSmsAudit(String phoneNumber);

    /**
     * 发送短信 （开户驳回通知）
     *
     * @param phoneNumber
     * @param reason
     */
    void sendSmsAuditReject(String phoneNumber,String reason);

    void sendSmsMaterial2(String phoneNumber,String title,String material,String expNumber, String expressName);

    void sendDownloadMaterialSms(String telNumber, String code,String openId,Long courseId);
}
