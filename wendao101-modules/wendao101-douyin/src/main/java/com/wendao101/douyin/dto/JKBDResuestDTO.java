package com.wendao101.douyin.dto;

import com.wendao101.douyin.domain.QJkbd;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

@Data
public class JKBDResuestDTO {
    private String openId;
    private Integer platform;
    private Integer appNameType;
    private String carType;
    private Integer kemu;

    private Integer questionId;

    private String uuid;
    private Integer userAnswer;


    private Boolean userAnswerIsCorrect;


    private QJkbd questionInfo;


    private Long id;

}
