package com.wendao101.douyin.service;

import java.util.List;
import com.wendao101.douyin.domain.DyMiniClass;

/**
 * 抖音小程序上的分类Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-08
 */
public interface IDyMiniClassService 
{
    /**
     * 查询抖音小程序上的分类
     * 
     * @param id 抖音小程序上的分类主键
     * @return 抖音小程序上的分类
     */
    public DyMiniClass selectDyMiniClassById(Long id);

    /**
     * 查询抖音小程序上的分类列表
     * 
     * @param dyMiniClass 抖音小程序上的分类
     * @return 抖音小程序上的分类集合
     */
    public List<DyMiniClass> selectDyMiniClassList(DyMiniClass dyMiniClass);

    /**
     * 新增抖音小程序上的分类
     * 
     * @param dyMiniClass 抖音小程序上的分类
     * @return 结果
     */
    public int insertDyMiniClass(DyMiniClass dyMiniClass);

    /**
     * 修改抖音小程序上的分类
     * 
     * @param dyMiniClass 抖音小程序上的分类
     * @return 结果
     */
    public int updateDyMiniClass(DyMiniClass dyMiniClass);

    /**
     * 批量删除抖音小程序上的分类
     * 
     * @param ids 需要删除的抖音小程序上的分类主键集合
     * @return 结果
     */
    public int deleteDyMiniClassByIds(Long[] ids);

    /**
     * 删除抖音小程序上的分类信息
     * 
     * @param id 抖音小程序上的分类主键
     * @return 结果
     */
    public int deleteDyMiniClassById(Long id);
}
