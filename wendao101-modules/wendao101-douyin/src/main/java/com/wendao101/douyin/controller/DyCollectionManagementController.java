package com.wendao101.douyin.controller;

import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.douyin.service.IDyCollectionManagementService;
import com.wendao101.douyin.vo.DyCollectionManagementSelectVO;
import com.wendao101.douyin.vo.DyCollectionManagementSelectdetailVO;
import com.wendao101.douyin.vo.DyCollectionManagementVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 收藏管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@RestController
@RequestMapping("/management")
public class DyCollectionManagementController extends BaseController {
    @Autowired
    private IDyCollectionManagementService dyCollectionManagementService;

    /**
     * 查询是否收藏
     */
    @PostMapping("/select")
    public AjaxResult select(@RequestBody DyCollectionManagementSelectVO dyCollectionManagementSelectVO) {
        if (StringUtils.isEmpty(dyCollectionManagementSelectVO.getOpenId())){
            return AjaxResult.error("openId未传值");
        }
        return toAjax(dyCollectionManagementService.selectIsFavorite(dyCollectionManagementSelectVO));
    }

    /**
     * 新增收藏
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody DyCollectionManagementVO dyCollectionManagementVO) {
        if (StringUtils.isEmpty(dyCollectionManagementVO.getOpenId())){
            return AjaxResult.error("openId未传值");
        }
        return toAjax(dyCollectionManagementService.insertDyCollectionManagement(dyCollectionManagementVO));
    }

    /**
     * 收藏列表取消收藏
     */
    @GetMapping("/deletes/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dyCollectionManagementService.deleteDyCollectionManagementByIds(ids));
    }

    /**
     * 详情页取消收藏
     */
    @PostMapping("/detail/delete")
    public AjaxResult remove(@RequestBody  DyCollectionManagementSelectdetailVO vo) {
        if (StringUtils.isEmpty(vo.getOpenId())){
            return AjaxResult.error("openId未传值");
        }
        return toAjax(dyCollectionManagementService.deleteById(vo));
    }

}
