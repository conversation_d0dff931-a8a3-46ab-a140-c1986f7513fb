package com.wendao101.douyin.controller;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wendao101.common.core.doudian.HeXiaoDTO;
import com.wendao101.common.core.kspaydto.CreateWendaoOrderDTO;
import com.wendao101.common.core.ktdto.GiveCourseUserChooseDTO;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.dto.JumpWxDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.feign.KuaishouAccessTokenService;
import com.wendao101.douyin.api.feign.WxKuaishouOrderService;
import com.wendao101.douyin.domain.CourseOrder;
import com.wendao101.douyin.domain.TDySms;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.dto.DoudianQueryOrderDTO;
import com.wendao101.douyin.plusmapper.CourseDyMapperPlus;
import com.wendao101.douyin.plusmapper.TeacherMapperPlus;
import com.wendao101.douyin.service.ICourseOrderService;
import com.wendao101.douyin.service.IDyTSmsService;
import com.wendao101.douyin.service.IWendaoUserService;
import com.wendao101.douyin.service.SmsOrderService;
import com.wendao101.teacher.domain.CourseDy;
import com.wendao101.teacher.domain.Teacher;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@RestController
@RequestMapping("/dd")
@Slf4j
public class JumpToDoudianOrderController extends BaseController {

    @Autowired
    private WxKuaishouOrderService wxkuaishouOrderService;

    @Autowired
    TeacherMapperPlus teacherMapper;

    @Autowired
    KuaishouAccessTokenService kuaishouAccessTokenService;

    @Autowired
    private IDyTSmsService tSmsService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICourseOrderService courseorderService;

    @Autowired
    CourseDyMapperPlus courseDyMapper;

    @Autowired
    SmsOrderService smsOrderService;

    @Autowired
    private IWendaoUserService wendaoUserService;

    private static final String doudian_order_coupons_redis_prefix = "doudian_order_coupons_redis_prefix:";
    private static final String doudian_order_coupons_hexiao_redis_prefix = "doudian_order_coupons_hexiao_redis_prefix:";

    private static final String doudian_order_coupons_redis_prefix_shop_id = "doudian_order_coupons_redis_prefix_shop_id:";
    private static final String course_give_away_code_gen = "course_give_away_code_gen:";

    private static final String give_order_id_cache_list = "give_order_id_cache_list:";

    private static final String jump_to_wx_prefix_for_tid_cid = "jump_to_wx_prefix_for_tid_cid:";

    private static final String give_order_user_choose_course_list = "give_order_user_choose_course_list:";

    private static final String order_send_wecome_info = "order_send_wecome_info:";
    private static final String order_already_receive_by_user = "order_already_receive_by_user:";
    @PostMapping("/queryNavigateAppid")
    public AjaxResult queryNavigateAppid(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        if(StringUtils.isBlank(doudianQueryOrderDTO.getOrderId())){
            return error("订单号不能为空");
        }
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            return error("订单不存在");
        }
        if(courseOrder.getAppNameType()==1){
            //问到好课
            return AjaxResult.success("成功","wx3b0ef5cd084c2e38");
        }
        if(courseOrder.getAppNameType()==2){
            //问到课堂
            return AjaxResult.success("成功","wx0e5e01d239197bb1");
        }
        return error("错误的appNameType");
    }

    @PostMapping("/queryDyMiniAppNavigateAppid")
    public AjaxResult queryDyMiniAppNavigateAppid(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        if(StringUtils.isBlank(doudianQueryOrderDTO.getOrderId())){
            return error("订单号不能为空");
        }
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            return error("订单不存在");
        }
        if(courseOrder.getAppNameType()==1){
            //问到好课
            return AjaxResult.success("成功","tt1795612888aad1f401");
        }
        if(courseOrder.getAppNameType()==2){
            //问到课堂
            return AjaxResult.success("成功","ttf54283ff6a7c241401");
        }
        return error("错误的appNameType");
    }

    @PostMapping("/receiveOrderNew")
    public AjaxResult receiveOrderNew(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        Map<String, Long> map = new HashMap<>();
        if (StringUtils.isBlank(doudianQueryOrderDTO.getOrderId())) {
            return error("订单号不能为空");
        }
        if (StringUtils.isBlank(doudianQueryOrderDTO.getOpenId())) {
            return error("openId不能为空");
        }
        String alreadyReceived = redisService.getCacheObject(order_already_receive_by_user + doudianQueryOrderDTO.getOrderId());
        WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
        if (wendaoUserWx == null) {
            return error("当前用户的openId未匹配到用户");
        }
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            Long courseId = redisService.getCacheObject(course_give_away_code_gen + doudianQueryOrderDTO.getOrderId());
            if (courseId != null && courseId == 0L) {
                return error("订单已领取，不能重复领！");
            }
            if (StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "JCK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BGK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "MXK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "HHX")||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BPK")) {
                courseId = 32359L;
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BGK")){
                    courseId = 32228L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "JCK")){
                    courseId = 32359L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "MXK")){
                    courseId = 28810L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "HHX")){
                    courseId = 32355L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BPK")){
                    courseId = 36449L;
                }
                String wbbOrderId = StringUtils.replaceIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BGK", "");
                wbbOrderId = StringUtils.replaceIgnoreCase(wbbOrderId, "JCK", "");
                wbbOrderId = StringUtils.replaceIgnoreCase(wbbOrderId, "MXK", "");
                wbbOrderId = StringUtils.replaceIgnoreCase(wbbOrderId, "HHX", "");
                CreateWendaoOrderDTO createWendaoOrderDTO = new CreateWendaoOrderDTO();
                createWendaoOrderDTO.setUserId(wendaoUserWx.getId());
                createWendaoOrderDTO.setWbbOrderId(wbbOrderId);
                createWendaoOrderDTO.setCourseId(courseId);
                createWendaoOrderDTO.setPlatform(wendaoUserWx.getPlatform());
                AjaxResult ajaxResult = wxkuaishouOrderService.createWendaoOrderGiveAway(createWendaoOrderDTO);
                if (!ajaxResult.isSuccess()) {
                    String failReason = (String) ajaxResult.get(AjaxResult.MSG_TAG);
                    if (failReason.contains("资源包")) {
                        return error(failReason + ",请联系老师购买资源包!");
                    } else {
                        return ajaxResult;
                    }
                }
                Long endCourseId = 0L;
                redisService.setCacheObject(course_give_away_code_gen + doudianQueryOrderDTO.getOrderId(), endCourseId);
                map.put("courseId", courseId);
                return AjaxResult.success("绑定成功", map);
            }else{
                return error("订单不存在");
            }
        }
        if (courseOrder.getOrderPlatform() != 11) {
            return error("非抖店订单");
        }
        String telNumber = wendaoUserWx.getTelNumber();
        if(StringUtils.isBlank(telNumber)){
            return error("当前用户未成功授权手机号！请去[我的]页面授权！");
        }
        if (StringUtils.isNotBlank(wendaoUserWx.getTelNumber()) && StringUtils.isNotBlank(courseOrder.getBuyerUserMobile()) && !StringUtils.equals(wendaoUserWx.getTelNumber(), courseOrder.getBuyerUserMobile())) {
            return error("您小程序授权的手机号和抖店订单填写的手机号码不一致!");
        }
        if (StringUtils.isNotBlank(wendaoUserWx.getTelNumber()) && StringUtils.isNotBlank(courseOrder.getBuyerUserMobile()) && StringUtils.equals(wendaoUserWx.getTelNumber(), courseOrder.getBuyerUserMobile())) {
            //核销过了
            if(StringUtils.isNotBlank(alreadyReceived)){
                map.put("courseId", courseOrder.getCourseId());
                return AjaxResult.success("绑定成功", map);
            }
        }
        WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(courseOrder.getBuyerUserId());
        wendaoUser.setTelNumber(telNumber);
        wendaoUserService.updateWendaoUser(wendaoUser);
        courseOrder.setBuyerUserMobile(telNumber);
        courseorderService.updateCourseOrder(courseOrder);
        map.put("courseId", courseOrder.getCourseId());
        //设置已经领取过了
        redisService.setCacheObject(order_already_receive_by_user + doudianQueryOrderDTO.getOrderId(), "received");
        //虚拟商品核销卡券
        String couponNumber = redisService.getCacheObject(doudian_order_coupons_redis_prefix + doudianQueryOrderDTO.getOrderId());
        if (StringUtils.isNotBlank(couponNumber)) {
            //保存核销码
            redisService.setCacheObject(doudian_order_coupons_hexiao_redis_prefix + doudianQueryOrderDTO.getOrderId(), couponNumber);
            //执行核销
            //获取店铺id
            Long ddShopId = redisService.getCacheObject(doudian_order_coupons_redis_prefix_shop_id + doudianQueryOrderDTO.getOrderId());
            if (ddShopId != null) {
                hexiao(ddShopId, couponNumber,doudianQueryOrderDTO.getOrderId());
            }
        }
        String sent = redisService.getCacheObject(order_send_wecome_info + courseOrder.getOrderId());
        if (StringUtils.isBlank(sent)) {
            QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
            sectionQueryWrapper.eq("id", courseOrder.getCourseId());
            CourseDy courseAudit = courseDyMapper.selectOne(sectionQueryWrapper, false);
            if (Objects.nonNull(courseAudit)) {
                if (courseAudit.getIncreaseTeacherWecom() == null) {
                    courseAudit.setIncreaseTeacherWecom(0);
                }
                if (courseAudit.getIncreaseTeacherWxphone() == null) {
                    courseAudit.setIncreaseTeacherWxphone(0);
                }
                //购买人手机号不为空 根据购买人手机号发送短信通知
                if (courseAudit.getIncreaseTeacherWecom() == 1) {
                    redisService.setCacheObject(order_send_wecome_info + courseOrder.getOrderId(), "sent");
                    String code = RandomStringUtils.randomAlphabetic(8);
                    JumpWxDTO jumpWxDTO = new JumpWxDTO();
                    jumpWxDTO.setCid(String.valueOf(courseAudit.getId()));
                    redisService.setCacheObject(jump_to_wx_prefix_for_tid_cid + code, jumpWxDTO);
                    //添加老师企业微信
                    smsOrderService.sendJumpWxSms(telNumber, "抖店", courseAudit.getTitle(), code);
                    //添加老师企业微信
                    //smsOrderService.sendAddTeacherWechat(telNumber, "抖店", courseAudit.getTitle(), courseAudit.getId(), courseOrder.getTeacherId());
                } else if (courseAudit.getIncreaseTeacherWxphone() == 1) {
                    redisService.setCacheObject(order_send_wecome_info + courseOrder.getOrderId(), "sent");
                    //添加老师微信号/手机号
                    smsOrderService.sendAddWechatOrPhone(telNumber, "抖店", courseAudit.getTitle(), courseAudit.getTeacherWxphoneContent());
                }
            }
        }
        //查询赠课列表
        List<String> giveOrderIdList = redisService.getCacheList(give_order_id_cache_list + courseOrder.getOrderId());
        if (CollectionUtils.isNotEmpty(giveOrderIdList)) {
            for (String giveOrderId : giveOrderIdList) {
                CourseOrder courseOrderGive = courseorderService.selectCourseOrderByOrderId2(giveOrderId);
                if (courseOrderGive == null) {
                    continue;
                }
                courseOrderGive.setBuyerUserMobile(wendaoUserWx.getTelNumber());
                courseorderService.updateCourseOrder(courseOrderGive);
            }
        }
        //判断没有赠课则触发赠课
        if (courseOrder.getOrderStatus() != null && (courseOrder.getOrderStatus() == 1 || courseOrder.getOrderStatus() == 6)) {
            GiveCourseUserChooseDTO giveCourseUserChoose = redisService.getCacheObject(give_order_user_choose_course_list + courseOrder.getOrderId());
            if (giveCourseUserChoose == null && CollectionUtils.isEmpty(giveOrderIdList)) {
                wxkuaishouOrderService.genDoudianGiveOrder(courseOrder.getOrderId());
            }
        }
        return AjaxResult.success("绑定成功", map);
    }

    @PostMapping("/receiveOrder")
    public AjaxResult receiveOrder(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        if(StringUtils.isBlank(doudianQueryOrderDTO.getOrderId())){
            return error("订单号不能为空");
        }
        if(StringUtils.isBlank(doudianQueryOrderDTO.getOpenId())){
            return error("openId不能为空");
        }
        if(StringUtils.isBlank(doudianQueryOrderDTO.getPhoneNumber())){
            return error("手机号不能为空");
        }
        if(StringUtils.isBlank(doudianQueryOrderDTO.getCode())){
            return error("验证码不能为空");
        }
        if(StringUtils.isBlank(doudianQueryOrderDTO.getUuid())){
            return error("uuid不能为空");
        }
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            return error("订单不存在");
        }
        if (courseOrder.getOrderPlatform() != 11) {
            return error("非抖店订单");
        }
        if(StringUtils.isNotBlank(courseOrder.getBuyerUserMobile())){
            return error("手机号已被绑定,不能二次绑定!");
        }
        WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
        if(wendaoUserWx==null){
            return error("当前用户的openId未匹配到用户");
        }
        if(StringUtils.isNotBlank(wendaoUserWx.getTelNumber())&&!StringUtils.equals(wendaoUserWx.getTelNumber(),doudianQueryOrderDTO.getPhoneNumber())){
            return error("当前小程序用户已授权过手机号码但与当前输入手机号不匹配,请使用当前小程序授权手机号码进行操作");
        }
        TDySms tSms = new TDySms();
        tSms.setPhoneNumber(doudianQueryOrderDTO.getPhoneNumber());
        tSms.setCodeIndexKey(doudianQueryOrderDTO.getUuid());
        List<TDySms> tSms1 = tSmsService.selectTSmsList(tSms);
        if (CollectionUtils.isNotEmpty(tSms1)) {
            TDySms tSms2 = tSms1.get(0);
            if (StringUtils.equalsIgnoreCase(tSms2.getCode(), doudianQueryOrderDTO.getCode())) {
                WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(courseOrder.getBuyerUserId());
                wendaoUser.setTelNumber(doudianQueryOrderDTO.getPhoneNumber());
                wendaoUserService.updateWendaoUser(wendaoUser);
                courseOrder.setBuyerUserMobile(doudianQueryOrderDTO.getPhoneNumber());
                courseorderService.updateCourseOrder(courseOrder);
                //修改微信用户号码
                if(StringUtils.isBlank(wendaoUserWx.getTelNumber())){
                    wendaoUserWx.setTelNumber(doudianQueryOrderDTO.getPhoneNumber());
                    wendaoUserService.updateWendaoUser(wendaoUserWx);
                }
                Map<String, Long> map = new HashMap<>();
                map.put("courseId", courseOrder.getCourseId());
                //虚拟商品核销卡券
                String couponNumber = redisService.getCacheObject(doudian_order_coupons_redis_prefix+doudianQueryOrderDTO.getOrderId());
                if (StringUtils.isNotBlank(couponNumber)) {
                    //保存核销码
                    redisService.setCacheObject(doudian_order_coupons_hexiao_redis_prefix + doudianQueryOrderDTO.getOrderId(), couponNumber);
                    //执行核销
                    //获取店铺id
                    Long ddShopId = redisService.getCacheObject(doudian_order_coupons_redis_prefix_shop_id + doudianQueryOrderDTO.getOrderId());
                    if (ddShopId != null) {
                        hexiao(ddShopId, couponNumber,doudianQueryOrderDTO.getOrderId());
                    }
                }
                {
                    QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
                    sectionQueryWrapper.eq("id", courseOrder.getCourseId());
                    CourseDy courseAudit = courseDyMapper.selectOne(sectionQueryWrapper, false);
                    if (Objects.nonNull(courseAudit)) {
                        if (courseAudit.getIncreaseTeacherWecom() == null) {
                            courseAudit.setIncreaseTeacherWecom(0);
                        }
                        if (courseAudit.getIncreaseTeacherWxphone() == null) {
                            courseAudit.setIncreaseTeacherWxphone(0);
                        }
                        //购买人手机号不为空 根据购买人手机号发送短信通知
                        if (courseAudit.getIncreaseTeacherWecom() == 1) {
                            String code = RandomStringUtils.randomAlphabetic(8);
                            JumpWxDTO jumpWxDTO = new JumpWxDTO();
                            jumpWxDTO.setCid(String.valueOf(courseAudit.getId()));
                            redisService.setCacheObject(jump_to_wx_prefix_for_tid_cid + code, jumpWxDTO);
                            //添加老师企业微信
                            smsOrderService.sendJumpWxSms(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), code);
                            //添加老师企业微信
                            //smsOrderService.sendAddTeacherWechat(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), courseAudit.getId(), courseOrder.getTeacherId());
                        } else if (courseAudit.getIncreaseTeacherWxphone() == 1) {
                            //添加老师微信号/手机号
                            smsOrderService.sendAddWechatOrPhone(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), courseAudit.getTeacherWxphoneContent());
                        }
                    }
                }
                wxkuaishouOrderService.genDoudianGiveOrder(courseOrder.getOrderId());
                return AjaxResult.success("绑定成功", map);
            } else {
                return error("短信验证码错误!");
            }
        } else {
            return error("未找到发送记录!");
        }
    }


    @GetMapping("/{urlCode}")
    public void getOpenId(@PathVariable("urlCode") String urlCode, HttpServletResponse response) {
        //判断一下jumpCode的长度,如果大于8个字符,取jumpCode的前面8个字符
        if(urlCode.contains("，")){
            urlCode = urlCode.substring(0, urlCode.indexOf("，"));
        }
//        if (urlCode.length() > 8) {
//            urlCode = urlCode.substring(0, 8);
//        }
//        if (urlCode.length() > 8) {
//            urlCode = urlCode.substring(0, 8);
//        }
        String doudianJumpUrl = redisService.getCacheObject("doudian_Sms_Url_Code:" + urlCode);
        if (StringUtils.isEmpty(doudianJumpUrl)) {
            response.setStatus(404);
        } else {
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", doudianJumpUrl);
        }
    }

    @PostMapping("/queryOrderInfo")
    public AjaxResult queryOrderInfo(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            if (StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "JCK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BGK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "MXK")
                    ||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "HHX")||StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BPK")) {
                long courseIdx = 32359L;
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BGK")){
                    courseIdx = 32228L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "JCK")){
                    courseIdx = 32359L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "MXK")){
                    courseIdx = 28810L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "HHX")){
                    courseIdx = 32355L;
                }
                if(StringUtils.startsWithIgnoreCase(doudianQueryOrderDTO.getOrderId(), "BPK")){
                    courseIdx = 36449L;
                }
                //28810  MXK
                //32228  BGK
                //32359  JCK
                QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
                sectionQueryWrapper.eq("id",courseIdx);
                CourseDy courseAudit = courseDyMapper.selectOne(sectionQueryWrapper, false);
                if (Objects.nonNull(courseAudit)) {
                    courseOrder = new CourseOrder();
                    courseOrder.setTeacherId(courseAudit.getTeacherId());
                    courseOrder.setCourseId(courseIdx);
                    courseOrder.setCourseTitle(courseAudit.getTitle());
                    courseOrder.setCourseImgUrl(courseAudit.getCoverPicUrl());
                    courseOrder.setOrderPlatform(11);
                }else{
                    return error("订单不存在");
                }
            }else{
                return error("订单不存在");
            }
        }
        if (courseOrder.getOrderPlatform() != 11) {
            return error("非抖店订单");
        }
//        WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
//        if (wendaoUserWx == null) {
//            return error("当前用户的openId未匹配到用户");
//        }
        QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("teacher_id", courseOrder.getTeacherId());
        Teacher teacher = teacherMapper.selectOne(queryWrapper,false);
        if(teacher!=null){
            teacher.setPassword(null);
            teacher.setMobile(null);
        }
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> courseInfo = new HashMap<>();
        //图片和标题
        courseInfo.put("courseId", courseOrder.getCourseId());
        courseInfo.put("courseTitle", courseOrder.getCourseTitle());
        courseInfo.put("courseImgUrl", courseOrder.getCourseImgUrl());
        map.put("teacher", teacher);
        map.put("courseInfo", courseInfo);
        //if (StringUtils.isNotBlank(wendaoUserWx.getTelNumber()) && StringUtils.isNotBlank(courseOrder.getBuyerUserMobile()) && StringUtils.equals(wendaoUserWx.getTelNumber(), courseOrder.getBuyerUserMobile())) {
        String alreadyReceived = redisService.getCacheObject(order_already_receive_by_user + doudianQueryOrderDTO.getOrderId());
        if (StringUtils.isNotEmpty(alreadyReceived)) {
            return new AjaxResult(209, "手机号已绑定,正在跳转课程详情", map);
        }
        //}
        return AjaxResult.success("查询成功",map);
    }
    @PostMapping("/bindOrderUserPhone")
    public AjaxResult bindOrderUserPhone(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        if (StringUtils.isNotBlank(doudianQueryOrderDTO.getPhoneNumber()) && StringUtils.isNotBlank(doudianQueryOrderDTO.getCode())) {
            //去除前后空格
            doudianQueryOrderDTO.setPhoneNumber(doudianQueryOrderDTO.getPhoneNumber().trim());
            doudianQueryOrderDTO.setCode(doudianQueryOrderDTO.getCode().trim());
            //查询订单
            CourseOrder courseOrder = courseorderService.selectOrderByOrderIdAndAppNameTypeAndBuyerUserId(doudianQueryOrderDTO.getOrderId(), doudianQueryOrderDTO.getAppNameType(), doudianQueryOrderDTO.getBuyerUserId());
            if (courseOrder == null) {
                return error("订单不存在");
            }
            if (courseOrder.getOrderPlatform() != 11) {
                return error("非抖店订单");
            }
            if(StringUtils.isNotBlank(courseOrder.getBuyerUserMobile())){
                return error("手机号已被绑定,不能二次绑定!");
            }
            WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
            if(wendaoUserWx==null){
                return error("当前用户的openId未匹配到用户");
            }
            if(StringUtils.isNotBlank(wendaoUserWx.getTelNumber())&&!StringUtils.equals(wendaoUserWx.getTelNumber(),doudianQueryOrderDTO.getPhoneNumber())){
                return error("当前小程序用户已授权过手机号码但与当前输入手机号不匹配,请使用当前小程序授权手机号码进行操作");
            }
            TDySms tSms = new TDySms();
            tSms.setPhoneNumber(doudianQueryOrderDTO.getPhoneNumber());
            tSms.setCodeIndexKey(doudianQueryOrderDTO.getUuid());
            List<TDySms> tSms1 = tSmsService.selectTSmsList(tSms);
            if (CollectionUtils.isNotEmpty(tSms1)) {
                TDySms tSms2 = tSms1.get(0);
                if (StringUtils.equalsIgnoreCase(tSms2.getCode(), doudianQueryOrderDTO.getCode())) {
                    WendaoUser wendaoUser = wendaoUserService.selectWendaoUserById(courseOrder.getBuyerUserId());
                    wendaoUser.setTelNumber(doudianQueryOrderDTO.getPhoneNumber());
                    wendaoUserService.updateWendaoUser(wendaoUser);
                    courseOrder.setBuyerUserMobile(doudianQueryOrderDTO.getPhoneNumber());
                    courseorderService.updateCourseOrder(courseOrder);
                    //修改微信用户号码
                    if(StringUtils.isBlank(wendaoUserWx.getTelNumber())){
                        wendaoUserWx.setTelNumber(doudianQueryOrderDTO.getPhoneNumber());
                        wendaoUserService.updateWendaoUser(wendaoUserWx);
                    }
                    Map<String, Long> map = new HashMap<>();
                    map.put("courseId", courseOrder.getCourseId());
                    //虚拟商品核销卡券
                    String couponNumber = redisService.getCacheObject(doudian_order_coupons_redis_prefix+doudianQueryOrderDTO.getOrderId());
                    if (StringUtils.isNotBlank(couponNumber)) {
                        //保存核销码
                        redisService.setCacheObject(doudian_order_coupons_hexiao_redis_prefix + doudianQueryOrderDTO.getOrderId(), couponNumber);
                        //执行核销
                        //获取店铺id
                        Long ddShopId = redisService.getCacheObject(doudian_order_coupons_redis_prefix_shop_id + doudianQueryOrderDTO.getOrderId());
                        if (ddShopId != null) {
                            hexiao(ddShopId, couponNumber,doudianQueryOrderDTO.getOrderId());
                        }
                    }
                    {
                        QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
                        sectionQueryWrapper.eq("id", courseOrder.getCourseId());
                        CourseDy courseAudit = courseDyMapper.selectOne(sectionQueryWrapper, false);
                        if (Objects.nonNull(courseAudit)) {
                            if (courseAudit.getIncreaseTeacherWecom() == null) {
                                courseAudit.setIncreaseTeacherWecom(0);
                            }
                            if (courseAudit.getIncreaseTeacherWxphone() == null) {
                                courseAudit.setIncreaseTeacherWxphone(0);
                            }
                            //购买人手机号不为空 根据购买人手机号发送短信通知
                            if (courseAudit.getIncreaseTeacherWecom() == 1) {
                                String code = RandomStringUtils.randomAlphabetic(8);
                                JumpWxDTO jumpWxDTO = new JumpWxDTO();
                                jumpWxDTO.setCid(String.valueOf(courseAudit.getId()));
                                redisService.setCacheObject(jump_to_wx_prefix_for_tid_cid + code, jumpWxDTO);
                                //添加老师企业微信
                                //smsOrderService.sendAddTeacherWechat(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), courseAudit.getId(), courseOrder.getTeacherId());
                                smsOrderService.sendJumpWxSms(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), code);
                            } else if (courseAudit.getIncreaseTeacherWxphone() == 1) {
                                //添加老师微信号/手机号
                                smsOrderService.sendAddWechatOrPhone(tSms2.getPhoneNumber(), "抖店", courseAudit.getTitle(), courseAudit.getTeacherWxphoneContent());
                            }
                        }
                    }
                    wxkuaishouOrderService.genDoudianGiveOrder(courseOrder.getOrderId());
                    return AjaxResult.success("绑定成功", map);
                } else {
                    return error("短信验证码错误!");
                }
            } else {
                return error("未找到发送记录!");
            }
        } else {
            return error("手机号和验证码不能为空");
        }

    }


    private void hexiao(Long shopId, String couponNumber,String orderId) {
        HeXiaoDTO heXiaoDTO = new HeXiaoDTO();
        heXiaoDTO.setShopId(shopId);
        heXiaoDTO.setCouponNumber(couponNumber);
        heXiaoDTO.setOrderId(orderId);
        wxkuaishouOrderService.hexiao(heXiaoDTO);
    }
}
