package com.wendao101.douyin.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * pc端登录对象 pc_login
 * 
 * <AUTHOR>
 * @date 2024-02-01
 */
public class PcLogin extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** uuid */
    @Excel(name = "uuid")
    private String uuid;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 用户的openid */
    @Excel(name = "用户的openid")
    private String openid;

    /** 平台,0抖音,1微信,2快手 */
    @Excel(name = "平台,0抖音,1微信,2快手")
    private Integer platform;

    /** 1问到好课,2问到课堂 */
    @Excel(name = "1问到好课,2问到课堂")
    private Integer appNameType;


    private String nickName;

    private String avatarUrl;

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setUuid(String uuid) 
    {
        this.uuid = uuid;
    }

    public String getUuid() 
    {
        return uuid;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setPlatform(Integer platform) 
    {
        this.platform = platform;
    }

    public Integer getPlatform() 
    {
        return platform;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uuid", getUuid())
            .append("userId", getUserId())
            .append("openid", getOpenid())
            .append("platform", getPlatform())
            .append("appNameType", getAppNameType())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
