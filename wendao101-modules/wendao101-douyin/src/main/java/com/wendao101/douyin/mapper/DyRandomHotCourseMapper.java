package com.wendao101.douyin.mapper;

import java.util.List;
import com.wendao101.douyin.domain.DyRandomHotCourse;

/**
 * dy_random_classMapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-08
 */
public interface DyRandomHotCourseMapper 
{
    /**
     * 查询dy_random_class
     * 
     * @param id dy_random_class主键
     * @return dy_random_class
     */
    public DyRandomHotCourse selectDyRandomHotCourseById(Long id);

    /**
     * 查询dy_random_class列表
     * 
     * @param dyRandomHotCourse dy_random_class
     * @return dy_random_class集合
     */
    public List<DyRandomHotCourse> selectDyRandomHotCourseList(DyRandomHotCourse dyRandomHotCourse);

    /**
     * 新增dy_random_class
     * 
     * @param dyRandomHotCourse dy_random_class
     * @return 结果
     */
    public int insertDyRandomHotCourse(DyRandomHotCourse dyRandomHotCourse);

    /**
     * 修改dy_random_class
     * 
     * @param dyRandomHotCourse dy_random_class
     * @return 结果
     */
    public int updateDyRandomHotCourse(DyRandomHotCourse dyRandomHotCourse);

    /**
     * 删除dy_random_class
     * 
     * @param id dy_random_class主键
     * @return 结果
     */
    public int deleteDyRandomHotCourseById(Long id);

    /**
     * 批量删除dy_random_class
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDyRandomHotCourseByIds(Long[] ids);
}
