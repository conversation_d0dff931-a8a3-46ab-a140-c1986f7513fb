package com.wendao101.douyin.avchatroomdto;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * {
 *     "From_Account":"id",
 *     "ProfileItem":
 *     [
 *         {
 *             "Tag":"Tag_Profile_IM_Nick",
 *             "Value":"MyNickName"
 *         }
 *     ]
 * }
 */
@Data
public class PortraitSetDTO {
    /**
     * 需要设置该 UserID 的资料
     */
    @JsonProperty("From_Account")
    @JSONField(name = "From_Account")
    String fromAccount;
    /**
     * 待设置的用户的资料对象数组，数组中每一个对象都包含了 Tag 和 Value
     */
    @JsonProperty("ProfileItem")
    @JSONField(name = "ProfileItem")
    List<ProfileItemDTO> profileItem;
    @Data
    public static class ProfileItemDTO {
        /**
         * 指定要设置的资料字段的名称，支持设置的资料字段有：
         * 标配资料字段，详情可参见 标配资料字段
         * 自定义资料字段，详情可参见 自定义资料字段
         */
        @JsonProperty("Tag")
        @JSONField(name = "Tag")
        String tag;
        /**
         * 待设置的资料字段的值，详情可参见 资料字段
         */
        @JsonProperty("Value")
        @JSONField(name = "Value")
        String value;
    }

    public void addProfileItem(ProfileItemDTO profileItemDTO) {
        List<ProfileItemDTO> profileItem = this.getProfileItem();
        if(CollectionUtil.isEmpty(profileItem)){
            profileItem = new ArrayList<>();
            this.setProfileItem(profileItem);
        }
        profileItem.add(profileItemDTO);
    }
}
