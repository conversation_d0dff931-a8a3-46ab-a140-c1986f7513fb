package com.wendao101.douyin.service.impl;

import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.douyin.domain.WendaoLiveCourse;
import com.wendao101.douyin.mapper.WendaoLiveCourseMapper;
import com.wendao101.douyin.service.IWendaoLiveCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 直播关联售卖课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-28
 */
@Service
public class WendaoLiveCourseServiceImpl implements IWendaoLiveCourseService
{
    @Autowired
    private WendaoLiveCourseMapper wendaoLiveCourseMapper;

    /**
     * 查询直播关联售卖课程
     * 
     * @param id 直播关联售卖课程主键
     * @return 直播关联售卖课程
     */
    @Override
    public WendaoLiveCourse selectWendaoLiveCourseById(Long id)
    {
        return wendaoLiveCourseMapper.selectWendaoLiveCourseById(id);
    }

    /**
     * 查询直播关联售卖课程列表
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 直播关联售卖课程
     */
    @Override
    public List<WendaoLiveCourse> selectWendaoLiveCourseList(WendaoLiveCourse wendaoLiveCourse)
    {
        return wendaoLiveCourseMapper.selectWendaoLiveCourseList(wendaoLiveCourse);
    }

    /**
     * 新增直播关联售卖课程
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 结果
     */
    @Override
    public int insertWendaoLiveCourse(WendaoLiveCourse wendaoLiveCourse)
    {
        wendaoLiveCourse.setCreateTime(DateUtils.getNowDate());
        return wendaoLiveCourseMapper.insertWendaoLiveCourse(wendaoLiveCourse);
    }

    /**
     * 修改直播关联售卖课程
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 结果
     */
    @Override
    public int updateWendaoLiveCourse(WendaoLiveCourse wendaoLiveCourse)
    {
        wendaoLiveCourse.setUpdateTime(DateUtils.getNowDate());
        return wendaoLiveCourseMapper.updateWendaoLiveCourse(wendaoLiveCourse);
    }

    /**
     * 批量删除直播关联售卖课程
     * 
     * @param ids 需要删除的直播关联售卖课程主键
     * @return 结果
     */
    @Override
    public int deleteWendaoLiveCourseByIds(Long[] ids)
    {
        return wendaoLiveCourseMapper.deleteWendaoLiveCourseByIds(ids);
    }

    /**
     * 删除直播关联售卖课程信息
     * 
     * @param id 直播关联售卖课程主键
     * @return 结果
     */
    @Override
    public int deleteWendaoLiveCourseById(Long id)
    {
        return wendaoLiveCourseMapper.deleteWendaoLiveCourseById(id);
    }
}
