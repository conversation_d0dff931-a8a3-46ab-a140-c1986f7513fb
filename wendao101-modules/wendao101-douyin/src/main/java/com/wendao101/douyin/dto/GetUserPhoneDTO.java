package com.wendao101.douyin.dto;

import lombok.Data;

/**
 * cloudId: ""
 * encryptedData: "SjrXNYnJfWfV9ezhSSGl3NUHi8j327SSQ9ob7siXdfI6EM1jgnTvvbl3I702viLt3V3tQvfl7EffLeNQTNtnZ/fxFxLwUhnQ6/AHvx9w6KReabq/mQunaLLWX+dsQc+c4HsK1FRcCvTf061qdtZRGsnNNuF0tsQv27WmweStyB5iFPJ1mwG7jgpphCKpfnWpEL2SLI5QdKWz4rctLOKYiQ=="
 * errMsg: "getPhoneNumber:ok"
 * iv: "xYd5FQN6lfWeM463jwmsYw=="
 */
@Data
public class GetUserPhoneDTO {
    private String cloudId;
    private String encryptedData;
    private String errMsg;
    private String iv;
    private String openid;
    /**
     * 来源的APP,1问到好课,2问到课堂
     */
    private int appNameType = 1;

    private Long courseId;

    private String userPhone;

    /**
     * 非前端字段,抖音苹果支付特殊字段
     */
    private Long userId;
    /**
     * 非前端字段,抖音苹果支付特殊字段
     */
    private String randomStr;
}
