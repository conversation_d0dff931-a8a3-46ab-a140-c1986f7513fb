package com.wendao101.douyin.vo;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

@Data
public class PromoterEarningsVO {

    /**
     * openId
     */
    @Excel(name = "openId")
    private String openId;

//    /**
//     * unionId
//     */
//    @Excel(name = "unionId")
//    private String unionId;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 开始时间  当天的 00:00:00
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    /**
     * 结束时间 当天的 23:59:59
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;

    private Integer appNameType;

    private Integer platform;
}
