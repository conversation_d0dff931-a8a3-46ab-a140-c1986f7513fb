package com.wendao101.douyin.controller;

import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.log.annotation.Log;
import com.wendao101.common.log.enums.BusinessType;
import com.wendao101.common.security.annotation.RequiresPermissions;
import com.wendao101.douyin.domain.WendaoLiveRoomWap;
import com.wendao101.douyin.service.IWendaoLiveRoomWapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * h5直播关联售卖课程Controller
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
@RestController
@RequestMapping("/WendaoLiveRoomWap")
public class WendaoLiveRoomWapController extends BaseController
{
    @Autowired
    private IWendaoLiveRoomWapService wendaoLiveRoomWapService;

    /**
     * 查询h5直播关联售卖课程列表
     */
    @RequiresPermissions("teacher:wap:list")
    @GetMapping("/list")
    public TableDataInfo list(WendaoLiveRoomWap wendaoLiveRoomWap)
    {
        startPage();
        List<WendaoLiveRoomWap> list = wendaoLiveRoomWapService.selectWendaoLiveRoomWapList(wendaoLiveRoomWap);
        return getDataTable(list);
    }

    /**
     * 导出h5直播关联售卖课程列表
     */
    @RequiresPermissions("teacher:wap:export")
    @Log(title = "h5直播关联售卖课程", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WendaoLiveRoomWap wendaoLiveRoomWap)
    {
        List<WendaoLiveRoomWap> list = wendaoLiveRoomWapService.selectWendaoLiveRoomWapList(wendaoLiveRoomWap);
        ExcelUtil<WendaoLiveRoomWap> util = new ExcelUtil<WendaoLiveRoomWap>(WendaoLiveRoomWap.class);
        util.exportExcel(response, list, "h5直播关联售卖课程数据");
    }

    /**
     * 获取h5直播关联售卖课程详细信息
     */
    @RequiresPermissions("teacher:wap:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(wendaoLiveRoomWapService.selectWendaoLiveRoomWapById(id));
    }

    /**
     * 新增h5直播关联售卖课程
     */
    @RequiresPermissions("teacher:wap:add")
    @Log(title = "h5直播关联售卖课程", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WendaoLiveRoomWap wendaoLiveRoomWap)
    {
        return toAjax(wendaoLiveRoomWapService.insertWendaoLiveRoomWap(wendaoLiveRoomWap));
    }

    /**
     * 修改h5直播关联售卖课程
     */
    @RequiresPermissions("teacher:wap:edit")
    @Log(title = "h5直播关联售卖课程", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WendaoLiveRoomWap wendaoLiveRoomWap)
    {
        return toAjax(wendaoLiveRoomWapService.updateWendaoLiveRoomWap(wendaoLiveRoomWap));
    }

    /**
     * 删除h5直播关联售卖课程
     */
    @RequiresPermissions("teacher:wap:remove")
    @Log(title = "h5直播关联售卖课程", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(wendaoLiveRoomWapService.deleteWendaoLiveRoomWapByIds(ids));
    }
}
