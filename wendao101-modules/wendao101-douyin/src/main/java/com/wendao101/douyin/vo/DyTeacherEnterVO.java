package com.wendao101.douyin.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 讲师入驻对象 teacher_enter
 *
 * <AUTHOR>
 * @date 2023-08-19
 */
@Data
public class DyTeacherEnterVO {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * openId
     */
    @Excel(name = "openId")
    private String openId;

    /**
     * 入驻讲师姓名
     */
    @Excel(name = "入驻讲师姓名")
    private String teacherEnterName;

    /**
     * 入驻讲师手机号
     */
    @Excel(name = "入驻讲师手机号")
    private Long teacherEnterPhone;

    /**
     * 入驻平台 0抖音 1微信 2快手 3视频号
     */
    @Excel(name = "入驻平台 0抖音 1微信 2快手 3视频号")
    private Integer appPlatform;

    /**
     * 入驻抖音号
     */
    @Excel(name = "入驻抖音号")
    private String teacherEnterDy;

    /**
     * 粉丝数
     */
    @Excel(name = "粉丝数")
    private Integer fansNum;

    /**
     * 邀请码
     */
    @Excel(name = "邀请码")
    private String invitationCode;

    /**
     * 所属类目 ：1传统文化,2体育健身,3家庭教育,4人文,5心理健康,6生活兴趣,7语言培训,8个人提升,9企业管理,10职业考试,11其他
     */
    @Excel(name = "所属类目 ：1传统文化,2体育健身,3家庭教育,4人文,5心理健康,6生活兴趣,7语言培训,8个人提升,9企业管理,10职业考试,11其他")
    private Integer categoryType;

    /**
     * 入驻类型 0个人 1机构
     */
    @Excel(name = "入驻类型 0个人 1机构")
    private Integer teacherEnterType;

    /**
     * 是否有开播经验 0否 1是
     */
    @Excel(name = "是否有开播经验 0否 1是")
    private Integer isExperience;

    /**
     * 是否有自己课程 0否（分销课程） 1是（自制课程）
     */
    @Excel(name = "是否有自己课程 0否（分销课程） 1是（自制课程）", readConverterExp = "分=销课程")
    private Integer isCourse;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /** 创建时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private Integer appNameType;

    /**
     * 问到员工的openid
     */
    private String wendaoPromoterOpenId;


    private String uuid;
    private String code;

}
