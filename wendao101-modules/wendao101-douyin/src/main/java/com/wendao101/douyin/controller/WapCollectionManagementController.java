package com.wendao101.douyin.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.domain.DyCollectionManagement;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.service.IDyCollectionManagementService;
import com.wendao101.douyin.vo.DyCollectionManagementSelectVO;
import com.wendao101.douyin.vo.DyCollectionManagementSelectdetailVO;
import com.wendao101.douyin.vo.DyCollectionManagementVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.List;

/**
 * 收藏管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-16
 */
@RestController
@RequestMapping("/wap/management")
public class WapCollectionManagementController extends BaseController {
    @Autowired
    private IDyCollectionManagementService dyCollectionManagementService;

    @Autowired
    RedisService redisService;

    @GetMapping("/list")
    public TableDataInfo list(DyCollectionManagement collect,HttpServletRequest request)
    {
        WendaoUser currentUser = WapController.getCurrentUser(request,redisService);
        TableDataInfo tableDataInfo = new TableDataInfo();
        if(currentUser==null){
            tableDataInfo.setCode(401);
            tableDataInfo.setMsg("未登录");
            return tableDataInfo;
        }
        collect.setUserId(currentUser.getId());
        if(collect.getType()==null||(collect.getType()!=1&&collect.getType()!=2)){
            tableDataInfo.setCode(500);
            tableDataInfo.setMsg("类型为空或者类型错误,只能传1或2");
            return tableDataInfo;
        }
        startPage();
        List<DyCollectionManagement> list = dyCollectionManagementService.selectDyCollectionManagementList(collect);
        return getDataTable(list);
    }

    /**
     * 查询是否收藏
     */
    @PostMapping("/select")
    public AjaxResult select(@RequestBody DyCollectionManagementSelectVO dyCollectionManagementSelectVO,HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request,redisService);
        if(currentUser==null){
            return new AjaxResult(401, "未登录");
        }
        dyCollectionManagementSelectVO.setOpenId(null);
        dyCollectionManagementSelectVO.setUserId(currentUser.getId());
        int i = dyCollectionManagementService.selectIsFavorite(dyCollectionManagementSelectVO);
        return AjaxResult.success("查询成功",i>0?Boolean.TRUE:Boolean.FALSE);
    }

    /**
     * 新增收藏
     */
    @PostMapping("/add")
    public AjaxResult add(@RequestBody DyCollectionManagementVO dyCollectionManagementVO,HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request,redisService);
        if(currentUser==null){
            return new AjaxResult(401, "未登录");
        }
        dyCollectionManagementVO.setOpenId(null);
        dyCollectionManagementVO.setUserId(currentUser.getId());
        dyCollectionManagementVO.setPlatform(5);
        return toAjax(dyCollectionManagementService.insertDyCollectionManagement(dyCollectionManagementVO));
    }

    /**
     * 收藏列表取消收藏
     */
    @GetMapping("/deletes/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids,HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request,redisService);
        if(currentUser==null){
            return new AjaxResult(401, "未登录");
        }
        if(ids==null||ids.length==0){
            return AjaxResult.error("没有选择要取消的收藏对象!");
        }
        List<Long> list = Arrays.asList(ids);
        return toAjax(dyCollectionManagementService.deleteDyCollectionManagementByIdsAndUserId(list,currentUser.getId()));
    }

    /**
     * 详情页取消收藏
     */
    @PostMapping("/detail/delete")
    public AjaxResult remove(@RequestBody DyCollectionManagementSelectdetailVO vo,HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request,redisService);
        if(currentUser==null){
            return new AjaxResult(401, "未登录");
        }
        vo.setOpenId(null);
        vo.setUserId(currentUser.getId());
        if(vo.getCourseId()==null&&vo.getTeacherId()==null){
            return AjaxResult.error("取消收藏必须有老师id或课程id中的一个!");
        }
        return toAjax(dyCollectionManagementService.deleteById(vo));
    }

}
