package com.wendao101.douyin.mapper;

import java.util.Date;
import java.util.List;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.dto.SmsRecordDTO;
import com.wendao101.douyin.dto.WendaoStoreCanvasDTO;
import com.wendao101.douyin.dto.WxUpdateUserDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 问到用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface WendaoUserMapper 
{
    /**
     * 查询问到用户
     * 
     * @param id 问到用户主键
     * @return 问到用户
     */
    public WendaoUser selectWendaoUserById(Long id);

    /**
     * 查询问到用户列表
     * 
     * @param wendaoUser 问到用户
     * @return 问到用户集合
     */
    public List<WendaoUser> selectWendaoUserList(WendaoUser wendaoUser);

    /**
     * 新增问到用户
     * 
     * @param wendaoUser 问到用户
     * @return 结果
     */
    public int insertWendaoUser(WendaoUser wendaoUser);

    /**
     * 修改问到用户
     * 
     * @param wendaoUser 问到用户
     * @return 结果
     */
    public int updateWendaoUser(WendaoUser wendaoUser);

    /**
     * 删除问到用户
     * 
     * @param id 问到用户主键
     * @return 结果
     */
    public int deleteWendaoUserById(Long id);

    /**
     * 批量删除问到用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoUserByIds(Long[] ids);

    WendaoUser selectWendaoUserByOpenId(@Param("openid") String openid);

    /**
     * 根据opinId修改用户头像和昵称
     * @param wxUpdateUserDTO
     * @return
     */
    int updateAvatarUrlNickNameByOpenId(WxUpdateUserDTO wxUpdateUserDTO);

    WendaoUser selectWendaoUserByOpenIdNew(@Param("openid")String openid,@Param("appNameType") Integer appNameType);

    WendaoUser selectByOpenIdPlatformAndAppNameType(@Param("openid")String openid, @Param("platform")Integer platform, @Param("appNameType")Integer appNameType);

    int saveSmsRecord(@Param("phoneNumber")String phoneNumber, @Param("code")String code, @Param("codeIndexKey")String codeIndexKey,@Param("expireTime") Date expireTime);

    SmsRecordDTO querySmsRecord(@Param("phoneNumber")String phoneNumber, @Param("codeIndexKey")String codeIndexKey);

    WendaoUser selectWxmpUserByPhone(@Param("phoneNumber")String phoneNumber,@Param("appNameType")Integer appNameType);

    WendaoStoreCanvasDTO selectCanvasByTeacher(WendaoStoreCanvasDTO wendaoStoreCanvas);

    WendaoUser selectUserByPhoneAndPlatorm(@Param("phoneNumber")String phoneNumber, @Param("platform")Integer platform);

    public int updateIgnoreWendaoUser(WendaoUser wendaoUser);

    List<WendaoUser> selectWendaoUserListByIds(@Param("list")List<Long> usrIdList);

    WendaoUser selectByPhonePlatformAndAppNameType(@Param("phoneNumber")String phoneNumber, @Param("platform")Integer platform, @Param("appNameType")Integer appNameType);

    WendaoUser selectUserByOpenIdAndPlatorm(@Param("openid")String openid, @Param("platform")Integer platform);
}
