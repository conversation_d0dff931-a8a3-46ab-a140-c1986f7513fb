package com.wendao101.douyin.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import com.wendao101.teacher.domain.Course;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 直播关联售卖课程对象 wendao_live_course
 *
 * <AUTHOR>
 * @date 2024-04-28
 */
public class WendaoLiveCourse extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 直播id
     */
    @Excel(name = "直播id")
    private Long liveId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private Long courseId;

    private Course course;

    private Boolean current = Boolean.FALSE;

    public Course getCourse() {
        return course;
    }

    public void setCourse(Course course) {
        this.course = course;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setLiveId(Long liveId) {
        this.liveId = liveId;
    }

    public Long getLiveId() {
        return liveId;
    }

    public void setCourseId(Long courseId) {
        this.courseId = courseId;
    }

    public Long getCourseId() {
        return courseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teacherId", getTeacherId())
                .append("liveId", getLiveId())
                .append("courseId", getCourseId())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("current", getCurrent())
                .toString();
    }

    public Boolean getCurrent() {
        return current;
    }

    public void setCurrent(Boolean current) {
        this.current = current;
    }
}
