package com.wendao101.douyin.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 watchhistory
 * 
 * <AUTHOR>
 * @date 2024-01-15
 */
public class Watchhistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long classid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long subid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long userid;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long watchtime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long watchtimeNow;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date modifytime;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long isWatch;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setClassid(Long classid) 
    {
        this.classid = classid;
    }

    public Long getClassid() 
    {
        return classid;
    }
    public void setSubid(Long subid) 
    {
        this.subid = subid;
    }

    public Long getSubid() 
    {
        return subid;
    }
    public void setUserid(Long userid) 
    {
        this.userid = userid;
    }

    public Long getUserid() 
    {
        return userid;
    }
    public void setWatchtime(Long watchtime) 
    {
        this.watchtime = watchtime;
    }

    public Long getWatchtime() 
    {
        return watchtime;
    }
    public void setWatchtimeNow(Long watchtimeNow) 
    {
        this.watchtimeNow = watchtimeNow;
    }

    public Long getWatchtimeNow() 
    {
        return watchtimeNow;
    }
    public void setModifytime(Date modifytime) 
    {
        this.modifytime = modifytime;
    }

    public Date getModifytime() 
    {
        return modifytime;
    }
    public void setIsWatch(Long isWatch) 
    {
        this.isWatch = isWatch;
    }

    public Long getIsWatch() 
    {
        return isWatch;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("classid", getClassid())
            .append("subid", getSubid())
            .append("userid", getUserid())
            .append("watchtime", getWatchtime())
            .append("watchtimeNow", getWatchtimeNow())
            .append("modifytime", getModifytime())
            .append("isWatch", getIsWatch())
            .toString();
    }
}
