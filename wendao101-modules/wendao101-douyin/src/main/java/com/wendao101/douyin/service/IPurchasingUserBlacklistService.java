package com.wendao101.douyin.service;

import java.util.List;
import com.wendao101.douyin.domain.PurchasingUserBlacklist;
import com.wendao101.douyin.vo.IsBlacklistedVO;

/**
 * 用户管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-30
 */
public interface IPurchasingUserBlacklistService 
{
    /**
     * 查询用户管理
     * 
     * @param id 用户管理主键
     * @return 用户管理
     */
    public PurchasingUserBlacklist selectPurchasingUserBlacklistById(Long id);

    /**
     * 查询用户管理列表
     * 
     * @param purchasingUserBlacklist 用户管理
     * @return 用户管理集合
     */
    public List<PurchasingUserBlacklist> selectPurchasingUserBlacklistList(PurchasingUserBlacklist purchasingUserBlacklist);

    /**
     * 是否被列入黑名单
     * @param isBlacklistedVO
     * @return
     */
    public Integer isBlacklisted(IsBlacklistedVO isBlacklistedVO);

    /**
     * 新增用户管理
     * 
     * @param purchasingUserBlacklist 用户管理
     * @return 结果
     */
    public int insertPurchasingUserBlacklist(PurchasingUserBlacklist purchasingUserBlacklist);


    /**
     * 根据用户id/讲师id查询
     * @param userId 用户id
     * @param teacherId 讲师id
     * @return
     */
    public PurchasingUserBlacklist selectPurchasingUserBlacklistByUserIdAndTeacher(Long userId,Long teacherId);

    /**
     * 修改用户管理
     * 
     * @param purchasingUserBlacklist 用户管理
     * @return 结果
     */
    public int updatePurchasingUserBlacklist(PurchasingUserBlacklist purchasingUserBlacklist);

    /**
     * 批量删除用户管理
     * 
     * @param ids 需要删除的用户管理主键集合
     * @return 结果
     */
    public int deletePurchasingUserBlacklistByIds(Long[] ids);

    /**
     * 删除用户管理信息
     * 
     * @param id 用户管理主键
     * @return 结果
     */
    public int deletePurchasingUserBlacklistById(Long id);
}
