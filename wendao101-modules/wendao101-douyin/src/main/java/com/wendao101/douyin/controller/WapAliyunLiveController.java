package com.wendao101.douyin.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wendao101.common.core.LiveCourseRelation;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.core.wendao.LiveCurrentCourseDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.domain.*;
import com.wendao101.douyin.plusmapper.CourseDyMapperPlus;
import com.wendao101.douyin.plusmapper.TeacherMapperPlus;
import com.wendao101.douyin.service.*;
import com.wendao101.system.api.RemoteTeacherService;
import com.wendao101.system.api.domain.RemoteWendaoLiveCourseWap;
import com.wendao101.system.api.domain.RemoteWendaoLiveWap;
import com.wendao101.teacher.domain.Course;
import com.wendao101.teacher.domain.Teacher;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@RestController
@RequestMapping("/wap/webdao_aliyun_live")
public class WapAliyunLiveController extends BaseController {
    @Autowired
    CourseDyMapperPlus courseDyMapper;
    @Autowired
    TeacherMapperPlus teacherMapper;
    @Autowired
    IWendaoLiveRoomWapService wendaoLiveRoomWapService;
    @Autowired
    ICourseOrderService courseOrderService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IWendaoLiveWapService wendaoLiveWapService;
    @Autowired
    private IWendaoLiveService wendaoLiveService;
    @Autowired
    private IWendaoLiveCourseWapService wendaoLiveCourseWapService;

    @Autowired
    private IWendaoUserService wendaoUserService;

    @Autowired
    private RemoteTeacherService remoteTeacherService;

    @GetMapping("/changeLiveStatusByAppServer")
    public AjaxResult changeLiveStatusByAppServer(@RequestParam("liveId") Long liveId,@RequestParam("status")Integer status) {
        WendaoLiveWap wendaoLiveWap = wendaoLiveWapService.selectWendaoLiveWapById(liveId);
        if(wendaoLiveWap!=null){
            wendaoLiveWap.setLiveStatus(status);
            wendaoLiveWapService.updateWendaoLiveWap(wendaoLiveWap);
            return AjaxResult.success();
        }else{
            WendaoLive wendaoLive = wendaoLiveService.selectWendaoLiveById(liveId,null);
            if(wendaoLive!=null){
                wendaoLive.setLiveStatus(status);
                wendaoLiveService.updateWendaoLive(wendaoLive);
                return AjaxResult.success();
            }
        }
        return AjaxResult.error();
    }

    @GetMapping("/checkIsNeedUpdateNickName")
    public AjaxResult checkIsNeedUpdateNickName(HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        if(org.apache.commons.lang3.StringUtils.isBlank(currentUser.getNickName())){
            return new AjaxResult(200,"查询成功",true);
        }
        //判断currentUser.getNickName()是不是这样的形式139****5496
        if(currentUser.getNickName().length()==11&&isMaskedFormat(currentUser.getNickName())){
            return new AjaxResult(200,"查询成功",true);
        }
        return new AjaxResult(200,"查询成功",false);
    }
    @PostMapping("/updateNickName")
    public AjaxResult updateNickName(@RequestBody WendaoUser newCurrentUser, HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        if(newCurrentUser==null|| StringUtils.isBlank(newCurrentUser.getNickName())){
            return error("昵称不能为空");
        }
        currentUser.setNickName(newCurrentUser.getNickName());
        wendaoUserService.updateWendaoUser(currentUser);
        //从request中取出token头
        String token = request.getHeader("token");
        if (org.apache.commons.lang3.StringUtils.isNotBlank(token)) {
            redisService.setCacheObject(WapController.WX_WAP_STORE_USER_TOKEN_KEY + token,currentUser);
        }
        return success(currentUser);
    }

    /**
     * 新增直播
     */
    @PostMapping("/createNewLive")
    public AjaxResult createNewLive(@RequestBody WendaoLiveWap wendaoLiveWap,HttpServletRequest request) {
        if (wendaoLiveWap.getTeacherId() == null) {
            return error("老师id必传!");
        }
        Long teacherId = wendaoLiveWap.getTeacherId();
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        boolean isThisTeacherCanLive = false;
        if (StringUtils.isNotBlank(currentUser.getTelNumber())) {
            //按手机号和老师Id查询老师
            QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", currentUser.getTelNumber());
            List<Teacher> teacherList = teacherMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(teacherList)) {
                for (Teacher t : teacherList) {
                    if (t.getWapLiveOpen() != null && t.getWapLiveOpen() == 1 && Objects.equals(teacherId, t.getTeacherId())) {
                        isThisTeacherCanLive = true;
                        break;
                    }
                }
            }
        }
        if(!isThisTeacherCanLive){
            return error("您没有权限开直播!请先去开通!");
        }
        RemoteWendaoLiveWap remoteWendaoLiveWap = new RemoteWendaoLiveWap();
        BeanUtils.copyProperties(wendaoLiveWap, remoteWendaoLiveWap);
        List<RemoteWendaoLiveCourseWap> listCourse = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(wendaoLiveWap.getWendaoLiveCourseList())){
            for(WendaoLiveCourseWap wendaoLiveCourseWap : wendaoLiveWap.getWendaoLiveCourseList()){
                RemoteWendaoLiveCourseWap remoteCourse  = new RemoteWendaoLiveCourseWap();
                BeanUtils.copyProperties(wendaoLiveCourseWap, remoteCourse);
                listCourse.add(remoteCourse);
            }
            remoteWendaoLiveWap.setWendaoLiveCourseList(listCourse);
        }
        LiveCourseRelation liveCourseRelation = wendaoLiveWap.getLiveCourseRelation();
        if(liveCourseRelation!=null){
            remoteWendaoLiveWap.setLiveCourseRelation(liveCourseRelation);
        }else{
            remoteWendaoLiveWap.setLiveCourseRelation(new LiveCourseRelation());
        }
        remoteWendaoLiveWap.setWapLiveCreateUserId(currentUser.getId());
        return remoteTeacherService.webdaoAliyunLiveWap(remoteWendaoLiveWap);
    }
    /**
     * 查询直播列表,h5直播列表
     * 不需要登录
     */
    @GetMapping("/list")
    public TableDataInfo list(WendaoLiveWap wendaoLive, HttpServletRequest request) {
//        if (wendaoLive.getTeacherId() == null) {
//            return TableDataInfo.error("老师id未传,查询失败!");
//        }
        wendaoLive.setLiveOnShelfStatus(1);
        wendaoLive.setIsDelete(0);
        wendaoLive.setLiveUpTime(new Date());
        startPage();
        List<WendaoLiveWap> list = wendaoLiveWapService.selectWendaoLiveWapList(wendaoLive);
        for (WendaoLiveWap item : list) {
            //push地址设置为空
            item.setPushUrl(null);
        }
        return getDataTable(list);
    }

    /**
     * 查询直播列表,h5直播列表
     */
    @GetMapping("/listForCourse")
    public AjaxResult listForCourse(@RequestParam("teacherId") Long teacherId,@RequestParam("courseId") Long courseId, HttpServletRequest request) {
        //if (!checkLogin(request)) {
            //return new AjaxResult(401, "未登录");
        //}
        WendaoLiveWap wendaoLive = new WendaoLiveWap();
        wendaoLive.setTeacherId(teacherId);
        wendaoLive.setLiveOnShelfStatus(1);
        wendaoLive.setIsDelete(0);
        wendaoLive.setLiveUpTime(new Date());
        List<WendaoLiveWap> list = wendaoLiveWapService.selectWendaoLiveWapList(wendaoLive);
        Iterator<WendaoLiveWap> iterator = list.iterator();
        while (iterator.hasNext()) {
            WendaoLiveWap item = iterator.next();
            //push地址设置为空
            item.setPushUrl(null);
            //读取redis数据
            LiveCourseRelation liveCourseRelation = redisService.getCacheObject(LiveCourseRelation.LIVE_COURSE_RELATION_KEY + item.getId());
            item.setLiveCourseRelation(liveCourseRelation);
            if(item.getLiveCourseRelation()==null){
                item.setLiveCourseRelation(new LiveCourseRelation());
            }
            //如果liveCourseRelation为noPermission,则列表中移除
            if (item.getLiveCourseRelation().getCourseLivePermission() == 1) {
                iterator.remove();
                continue;
            }
            if(item.getLiveCourseRelation().getCourseLivePermission()!=2 && item.getLiveCourseRelation().getCourseLivePermission()!= 3){
                iterator.remove();
                continue;
            }
            if(item.getLiveCourseRelation().getCourseLivePermission() == 3){
                //课程列表为空或者判断课程id是否在课程列表中
                if(CollectionUtils.isEmpty(item.getLiveCourseRelation().getCourseIds())||!item.getLiveCourseRelation().getCourseIds().contains(courseId)){
                    iterator.remove();
                }
            }
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取直播详细信息,按id查询直播详情
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        WendaoUser wendaoUser = WapController.getCurrentUser(request, redisService);
        if (wendaoUser == null) {
            return new AjaxResult(401, "未登录");
        }
        if (id == null) {
            return error("直播id不能为空");
        }
        WendaoLiveWap wendaoLiveWap = wendaoLiveWapService.selectWendaoLiveWapById(id);
        if (wendaoLiveWap == null) {
            return error("不存在此直播,或已经删除");
        }
        if (wendaoLiveWap.getLiveOnShelfStatus() != null && wendaoLiveWap.getLiveOnShelfStatus() != 1) {
            return error("直播已下架");
        }
        //wendaoLiveWap.setPassword(null);
        //判断是否是老师本人标志位
        boolean isOwner = false;
        if (StringUtils.isNotBlank(wendaoUser.getTelNumber())) {
            //按手机号和老师Id查询老师
            QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", wendaoUser.getTelNumber());
            queryWrapper.eq("teacher_id", wendaoLiveWap.getTeacherId());
            Teacher teacher = teacherMapper.selectOne(queryWrapper, false);
            if (teacher != null) {
                teacher.setPassword(null);
                wendaoUser.setTeacher(teacher);
                wendaoLiveWap.setTeacher(teacher);
                isOwner = true;
            }
        }
        wendaoLiveWap.setCurrentUserBuy(false);
        WendaoLiveCourseWap wendaoLiveCourseWap = new WendaoLiveCourseWap();
        wendaoLiveCourseWap.setLiveId(id);
        List<WendaoLiveCourseWap> courses = wendaoLiveCourseWapService.selectWendaoLiveCourseWapList(wendaoLiveCourseWap);
        LiveCurrentCourseDTO liveCurrentCourseDTO = redisService.getCacheObject(WendaoLiveController.LIVE_COURSE_INFO_PREFIX + id);
        if (liveCurrentCourseDTO != null && "start".equals(liveCurrentCourseDTO.getAction()) && CollectionUtils.isNotEmpty(courses)) {
            // 遍历列表查找目标课程
            int targetIndex = -1;
            for (int i = 0; i < courses.size(); i++) {
                if (courses.get(i).getCourseId().equals(liveCurrentCourseDTO.getCourseId())) {
                    targetIndex = i;
                    break;
                }
            }
            // 若找到目标元素，移除并插入到第一位
            if (targetIndex != -1) {
                WendaoLiveCourseWap targetCourse = courses.remove(targetIndex);
                targetCourse.setCurrent(true);
                courses.add(0, targetCourse);
            }
        }
        for (WendaoLiveCourseWap liveCourse : courses) {
            QueryWrapper<com.wendao101.teacher.domain.CourseDy> sectionQueryWrapper = new QueryWrapper<>();
            sectionQueryWrapper.eq("is_delete", 0);
            sectionQueryWrapper.eq("id", liveCourse.getCourseId());
            com.wendao101.teacher.domain.CourseDy courseDy = courseDyMapper.selectOne(sectionQueryWrapper, false);
            if (courseDy != null) {
                Course course = new Course();
                BeanUtils.copyProperties(courseDy, course);
                liveCourse.setCourse(course);
            }
        }
        wendaoLiveWap.setWendaoLiveCourseList(courses);
        if (!isOwner) {
            //非老师本人,把推送url设置为空
            wendaoLiveWap.setPushUrl(null);
        }
        //查询聊天室信息
        WendaoLiveRoomWap wendaoLiveRoomWap = wendaoLiveRoomWapService.selectWendaoLiveRoomWapByLiveId(id);
        wendaoLiveWap.setWendaoLiveRoom(wendaoLiveRoomWap);
        //判断当前用户是否购买!!
        List<CourseOrder> courseOrders = courseOrderService.selectByOrderStatusAndUserIdOrPhone(null, wendaoUser.getId(), wendaoLiveWap.getId());
        wendaoLiveWap.setCurrentUserBuy(CollectionUtils.isNotEmpty(courseOrders));
        //如果是老师自己的账号看的话直接设置为true
        if (isOwner) {
            wendaoLiveWap.setCurrentUserBuy(true);
        }
        //查询用户对这个直播的权限
        LiveCourseRelation liveCourseRelation = redisService.getCacheObject(LiveCourseRelation.LIVE_COURSE_RELATION_KEY + id);
        if(liveCourseRelation!=null&&liveCourseRelation.getCourseLivePermission()!=null&&wendaoLiveWap.getSaleType()!=null&&(wendaoLiveWap.getSaleType()==2||wendaoLiveWap.getSaleType()==3)){
            //query user buy list
            if(liveCourseRelation.getCourseLivePermission()==2){
                //判断用户手机号是否为空
                int buyCount = 0;
                if (StringUtils.isNotBlank(wendaoUser.getTelNumber())) {
                    buyCount = courseOrderService.countOrderByTeacherIdAndUserIdOrPhone(wendaoLiveWap.getTeacherId(), wendaoUser.getId(), wendaoUser.getTelNumber());
                } else {
                    buyCount = courseOrderService.countOrderByTeacherIdAndUserId(wendaoLiveWap.getTeacherId(), wendaoUser.getId());
                }
                if (buyCount > 0) {
                    wendaoLiveWap.setCurrentUserBuy(true);
                    wendaoLiveWap.setSaleType(2);
                }
            }else if(liveCourseRelation.getCourseLivePermission()==3){
                //判断用户是否购买过
                //先获取课程id列表
                List<Long> courseIds = liveCourseRelation.getCourseIds();
                if(CollectionUtils.isNotEmpty(courseIds)){
                    int buyCount = 0;
                    if (StringUtils.isNotBlank(wendaoUser.getTelNumber())) {
                        buyCount = courseOrderService.countOrderByTeacherIdAndUserIdOrPhoneAndCourseIds(wendaoLiveWap.getTeacherId(), wendaoUser.getId(), wendaoUser.getTelNumber(),courseIds);
                    } else {
                        buyCount = courseOrderService.countOrderByTeacherIdAndUserIdAndCourseIds(wendaoLiveWap.getTeacherId(), wendaoUser.getId(),courseIds);
                    }
                    if (buyCount > 0) {
                        wendaoLiveWap.setCurrentUserBuy(true);
                        wendaoLiveWap.setSaleType(2);
                    }
                }
            }
        }
        {
            QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("teacher_id", wendaoLiveWap.getTeacherId());
            Teacher teacher = teacherMapper.selectOne(queryWrapper, false);
            if (teacher != null) {
                teacher.setPassword(null);
                wendaoLiveWap.setLiveTeacher(teacher);
            }
        }
        return success(wendaoLiveWap);
    }

    private boolean isMaskedFormat(String nickName) {
        // 正则表达式：3位数字 + 4个* + 4位数字
        String regex = "^\\d{3}\\*{4}\\d{4}$";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(nickName);
        return matcher.matches();
    }
}
