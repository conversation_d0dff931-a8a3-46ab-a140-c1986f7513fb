package com.wendao101.douyin.service;

import java.util.List;
import com.wendao101.douyin.domain.DyMiniBanner;

/**
 * 小程序bannerService接口
 * 
 * <AUTHOR>
 * @date 2023-08-08
 */
public interface IDyMiniBannerService 
{
    /**
     * 查询小程序banner
     * 
     * @param id 小程序banner主键
     * @return 小程序banner
     */
    public DyMiniBanner selectDyMiniBannerById(Long id);

    /**
     * 查询小程序banner列表
     * 
     * @param dyMiniBanner 小程序banner
     * @return 小程序banner集合
     */
    public List<DyMiniBanner> selectDyMiniBannerList(DyMiniBanner dyMiniBanner);

    /**
     * 新增小程序banner
     * 
     * @param dyMiniBanner 小程序banner
     * @return 结果
     */
    public int insertDyMiniBanner(DyMiniBanner dyMiniBanner);

    /**
     * 修改小程序banner
     * 
     * @param dyMiniBanner 小程序banner
     * @return 结果
     */
    public int updateDyMiniBanner(DyMiniBanner dyMiniBanner);

    /**
     * 批量删除小程序banner
     * 
     * @param ids 需要删除的小程序banner主键集合
     * @return 结果
     */
    public int deleteDyMiniBannerByIds(Long[] ids);

    /**
     * 删除小程序banner信息
     * 
     * @param id 小程序banner主键
     * @return 结果
     */
    public int deleteDyMiniBannerById(Long id);
}
