package com.wendao101.douyin.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 直播关联售卖课程对象 wendao_live_room
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public class WendaoLiveRoom extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 直播id */
    @Excel(name = "直播id")
    private Long liveId;

    /** avchatroom群id */
    @Excel(name = "avchatroom群id")
    private String groupId;

    /** 群名字,如果不填写,默认生成随机字符串 */
    @Excel(name = "群名字,后台创建时如果不填写,默认生成随机字符串")
    private String groupName;

    /** 管理员urerid列表,可能有多个,最多5个管理员 */
    @Excel(name = "管理员urerid列表,可能有多个,最多5个管理员")
    private String groupAdmin;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setLiveId(Long liveId) 
    {
        this.liveId = liveId;
    }

    public Long getLiveId() 
    {
        return liveId;
    }
    public void setGroupId(String groupId) 
    {
        this.groupId = groupId;
    }

    public String getGroupId() 
    {
        return groupId;
    }
    public void setGroupName(String groupName) 
    {
        this.groupName = groupName;
    }

    public String getGroupName() 
    {
        return groupName;
    }
    public void setGroupAdmin(String groupAdmin) 
    {
        this.groupAdmin = groupAdmin;
    }

    public String getGroupAdmin() 
    {
        return groupAdmin;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("liveId", getLiveId())
            .append("groupId", getGroupId())
            .append("groupName", getGroupName())
            .append("groupAdmin", getGroupAdmin())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
