package com.wendao101.douyin.mapper;

import java.util.List;
import java.util.Set;

import com.wendao101.douyin.domain.CourseOrder;
import com.wendao101.douyin.dto.*;
import com.wendao101.douyin.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-15
 */
public interface CourseOrderMapper 
{
    /**
     * 查询订单信息
     * 
     * @param id 订单信息主键
     * @return 订单信息
     */
    public CourseOrderDTO selectCourseOrderById(@Param("id")Long id,@Param("orderId")String orderId);

    Long selectCourseOrderCount(Long courseId);

    Long updateCourseOrderByStudyDuration(CourseOrder courseOrder);

    /**
     * 查询订单信息列表
     * 
     * @param courseOrder 订单信息
     * @return 订单信息集合
     */
    public List<CourseOrder> selectCourseOrderList(CourseOrder courseOrder);

    /**
     * 根据用户id查询订单信息列表
     *
     * @param coursePurchasedDyVO 订单信息
     * @return 订单信息集合
     */
    public List<CourseOrderAndHoursDyDTO> selectPurchasedCourseList(CoursePurchasedDyVO coursePurchasedDyVO);


    /**
     * 查询我的订单信息列表
     *
     * @param courseOrderVO 订单信息
     * @return 订单信息集合
     */
    public List<CourseOrder> selectCourseOrderDyList(CourseOrderVO courseOrderVO);
    /**
     * 新增订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int insertCourseOrder(CourseOrder courseOrder);

    /**
     * 根据课程id查询订单
     * @param courseId
     * @return
     */
    public List<RecentlyPurchasedDTO> selectCourseOrderByCourseId(Long courseId);

    /**
     * 修改订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int updateCourseOrder(CourseOrder courseOrder);

    public CourseOrder selectCourseOrder(@Param("userId")Long userId,@Param("orderId") String orderId);

    /**
     * 删除订单信息
     * 
     * @param id 订单信息主键
     * @return 结果
     */
    public int deleteCourseOrderById(Long id);

    /**
     * 批量删除订单信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseOrderByIds(Long[] ids);

    CourseOrder selectCourseOrderByOrderId(String orderId);

    int updateOrderStatusByOrderId(@Param("orderId") String orderId,@Param("appNameType")Integer appNameType);

    /**
     * 抖音查看推广员订单列表
     *
     * @param dyPromoterOrderVO
     * @return
     */
    List<DyPromoterOrderDTO> getPromoterOrder(DyPromoterOrderVO dyPromoterOrderVO);

    /**
     * 取消订单
     *
     * @param dyCancelOrderVO
     * @return
     */
    int updateCancelOrder(DyCancelOrderVO dyCancelOrderVO);

    /**
     * 删除订单
     *
     * @param dyCancelOrderVO
     * @return
     */
    int removeOrder(DyCancelOrderVO dyCancelOrderVO);

    /**
     * 添加退款订单
     *
     * @param dyCourseRefundVO
     * @return
     */
    int addCourseRefundOrder(DyCourseRefundVO dyCourseRefundVO);

    /**
     * 删除退款订单
     *
     * @param dyCancelOrderVO
     * @return
     */
    int removeRefundOrder(DyCancelOrderVO dyCancelOrderVO);

    /**
     * 新增讲师入驻信息
     *
     * @param dyTeacherEnterVO
     * @return
     */
    int addDyTeacherEnter(DyTeacherEnterVO dyTeacherEnterVO);

    /**
     * 获取老师信息
     *
     * @param teacherId
     * @return
     */
    DyCourseTeacherDTO getTeacherByTeacherId(Long teacherId);

    CourseOrder selectCourseOrderByOrderIdAndUserId(@Param("orderId") String orderId, @Param("userId") Long userId);

    /**
     * 是否收藏该老师
     *
     * @param teacherId
     * @return
     */
    List<Long> getIsCollectionType(@Param("teacherId") Long teacherId,@Param("userId") Long userId);

    /**
     * 根据openId 查询是否入驻过
     *
     * @param openId
     * @return
     */
    List<Long> getTeacherEnter(@Param("openId") String openId);


    List<CourseTeacherDTO> getAllDyCourse(DyCourseVO dyCourseVO);


    List<DyTeacherDTO> getDyTeacher(DyCourseVO dyCourseVO);

    CourseOrder selectPureCourseOrderById(@Param("id")Long id);

    void updateUserOrderPhone(@Param("buyUserId")Long buyUserId, @Param("purePhoneNumber")String purePhoneNumber);

    List<CourseOrder> selectByOrderStatusAndUserIdOrPhone(@Param("mobile")String mobile,  @Param("buyUserId")Long buyUserId,@Param("courseId")Long courseId);

    Integer countCourseAudioVide(@Param("courseId")Long courseId);

    List<CourseOrderAndHoursDyDTO> selectPurchasedCourseListHaveMobile(CoursePurchasedDyVO coursePurchasedDyVO);

    List<CourseOrder> selectByOpenIdAndPlatformSuccess(@Param("openid")String openid,@Param("buyUserId") Long buyUserId);

    List<KtClassIdDTO> queryIdMapByCourseId(@Param("list") Set<Long> courseIdSet);

    List<DyExportPromoterOrderDTO> getExportPromoterOrder(DyPromoterOrderVO dyPromoterOrderVO);

    void insertDerivedRecord(DerivedRecordDTO derivedRecord);

    List<DerivedRecordDTO> selectDerivedRecordList(DerivedRecordDTO derivedRecord);

    void updateCourseOrderTuWenByUserIdAndCourseId(@Param("courseId")Long courseId, @Param("buyUserId")Long buyUserId);

    DyTeacherDTO queryKnowledgeStoreInfo(@Param("storeDomain")String storeDomain);

    DyCourseTeacherDTO getTeacherByTeacherIdForKnowledgeStore(@Param("teacherId") Long teacherId);

    List<CourseOrder> selectByOrderStatusAndUserId(@Param("buyUserId")Long userId,@Param("courseId")Long courseId);

    CourseOrder selectDYCourseOrderByCourseIdAndUserId(@Param("courseId")Long courseId, @Param("buyUserId")Long buyUserId);

    CourseOrderDTO selectCdOrderById(@Param("id")Long id,@Param("orderId")String orderId);

    List<CourseOrder> selectByOrderStatusAndUserIdForPc(@Param("buyUserId")Long userId,@Param("courseId")Long courseId);

    List<CourseTeacherDTO> getAllDyCourseForPc(PcCourseVO dyCourseVO);

    List<DyTeacherDTO> getDyTeacherForPc(PcCourseVO dyCourseVO);

    CourseOrder selectCourseOrderByOrderId2(@Param("orderId")String orderId);

    void removeRefundOrderByOrderId(@Param("orderId") String orderId);

    int updateOrderDeleteStatus(@Param("id")Long id);

    List<DyPromoterOrderDTO> getPromoterOrderByPlatformList(DyPromoterOrderVO dyPromoterOrderVO);

    String selectOrderItemIdByOrderId(@Param("orderId")String orderId);

    CourseOrder selectCourseOrderByPrimaryId(@Param("id")Long id);

    CourseOrder selectCourseOrderByOrderIdReal(@Param("orderId")String orderId);

    void updateCourseKsAuditStatus(@Param("courseId")String courseId, @Param("version")Integer version, @Param("auditStatus")Integer auditStatus);

    void updateCourseDyKsAuditStatus(@Param("courseId")String courseId, @Param("version")Integer version, @Param("auditStatus")Integer auditStatus);

    CourseOrder selectOrderByOrderIdAndAppNameTypeAndBuyerUserId(@Param("orderId")String orderId, @Param("appNameType")Integer appNameType, @Param("buyerUserId")Long buyerUserId);

    List<CourseOrder> selectDyDDCourseOrderDyList(CourseOrderVO courseOrderVO);

    List<PlatformOrderCountDTO> queryTodayPlatformOrderCount();

    TodayOrderCountAndMoneyDTO queryTodayTotalOrderMoney();

    List<EveryDayOrderCountDTO> queryLast7DaysOrderCountPerDay();

    List<PlatformOrderMoneyDTO> queryTodayPlatformOrderMoney();

    List<CourseOrder> queryLast10Orders();

    List<CatCourseNumberDpDTO> queryCatCourseNumber();

    List<TodaySaleMoneyRankingDTO> queryTodayTeacherSaleRanking();

    //List<TeacherEnterDTO> queryTeacherEnterCount();

    List<UserCountPlatformDTO> queryUserCountPlatform();

    ThisMonthLastMonthSaleMoneyDTO queryThisMonthSaleMoney();

    YesterdayOrderCountAndMoneyDTO queryYesterdayTotalOrderMoney();

    List<PlatformOrderMoneyDTO> queryYesterdayPlatformOrderMoney();

    List<CourseOrder> queryByUserIdAndTeacherId(@Param("teacherId") Long teacherId, @Param("userId")Long userId);

    List<CourseOrder> queryByUserId(@Param("userId")Long userId);

    List<CourseOrder> queryCdOrderByUserId(@Param("userId")Long userId);

    List<CourseOrder> queryCdOrderByUserIdAndTeacherId(@Param("teacherId")Long teacherId, @Param("userId")Long userId);

    List<String> selectImageTextByStringArray(@Param("list") List<String> stringList);

    int countOrderByTeacherIdAndUserIdOrPhone(@Param("teacherId")Long teacherId, @Param("userId")Long userId, @Param("telNumber")String telNumber);

    int countOrderByTeacherIdAndUserId(@Param("teacherId")Long teacherId, @Param("userId")Long userId);

    int countOrderByTeacherIdAndUserIdOrPhoneAndCourseIds(@Param("teacherId")Long teacherId, @Param("userId")Long userId, @Param("telNumber")String telNumber, @Param("courseIds")List<Long> courseIds);

    int countOrderByTeacherIdAndUserIdAndCourseIds(@Param("teacherId")Long teacherId, @Param("userId")Long userId, @Param("courseIds")List<Long> courseIds);
}
