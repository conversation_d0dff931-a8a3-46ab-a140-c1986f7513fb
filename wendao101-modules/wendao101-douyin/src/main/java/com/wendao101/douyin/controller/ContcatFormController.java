package com.wendao101.douyin.controller;

import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.wendao101.common.core.utils.ip.IpUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.douyin.domain.ContcatForm;
import com.wendao101.douyin.dto.SmsRecordDTO;
import com.wendao101.douyin.service.*;
import com.wendao101.douyin.vo.DyTeacherEnterVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 招商信息Controller
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/contcat_form")
public class ContcatFormController extends BaseController {
    @Autowired
    private IContcatFormService contcatFormService;

    // 云片API密钥
    private static final String APIKEY = "102222afce402a62dd7b44276e736fff";
    private static final String ENCODING = "UTF-8";
    private static final String API_URL = "https://sms.yunpian.com/v2/sms/tpl_single_send.json";
    private static final Long captchaAppId = 192824652L;
    private static final String appSecretKey = "DcFo2A7miP6veg4jhHwkOwVaC";
    private static final Long captchaType = 9L;
    @Autowired
    private IWendaoUserService wendaoUserService;
    @Autowired
    AvChatRoomService avChatRoomService;
    @Autowired
    IWendaoLiveRoomService wendaoLiveRoomService;
    @Resource
    private ICourseOrderService courseOrderService;
    @Autowired
    AliyunSmsService aliyunSmsService;

    /**
     * 新增讲师入驻
     *
     * @param dyTeacherEnterVO
     * @return
     */
    @PostMapping("/addDyTeacherEnter")
    public AjaxResult addDyTeacherEnter(@RequestBody DyTeacherEnterVO dyTeacherEnterVO, HttpServletResponse response) {
        String phone = String.valueOf(dyTeacherEnterVO.getTeacherEnterPhone());
        if (dyTeacherEnterVO.getTeacherEnterPhone() == null || StringUtils.length(phone) != 11) {
            return AjaxResult.error("手机号不正确");
        }
        if (StringUtils.isBlank(dyTeacherEnterVO.getUuid())) {
            return AjaxResult.error("发送验证码uuid不能为空");
        }
        SmsRecordDTO smsRecordDTO = wendaoUserService.querySmsRecord(phone, dyTeacherEnterVO.getUuid());
        if (smsRecordDTO == null) {
            return AjaxResult.error("未找到发送记录!");
        }
        if (smsRecordDTO.getExpireTime() != null && smsRecordDTO.getExpireTime().before(new Date())) {
            return AjaxResult.error("验证码已过期!");
        }
        if (!StringUtils.equals(dyTeacherEnterVO.getCode(), smsRecordDTO.getCode())) {
            return AjaxResult.error("验证码输入错误!");
        }
        return success(courseOrderService.addDyTeacherEnter(dyTeacherEnterVO));
    }

    /**
     * 初始化加密滑动验证码数据
     *
     * @return
     */
    @GetMapping("/t_captcha/init")
    public AjaxResult captchaInit(HttpServletResponse response) {
        int remainder = 32 % appSecretKey.length();
        String key = appSecretKey + appSecretKey.substring(0, remainder);
        long curTime = new Date().getTime() / 1000;
        long expireTime = 86400L;
        String plaintext = captchaAppId + "&" + curTime + "&" + expireTime;
        String iv = RandomStringUtils.randomNumeric(16);
        String ciphertext = null;
        try {
            ciphertext = encrypt(plaintext, key, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String captchaAppidEncrypted = ciphertext;
        return AjaxResult.success("success", captchaAppidEncrypted);
    }

    /**
     * 验证滑块并发送验证码
     *
     * @param phoneNumber
     * @param ticket
     * @param randomStr
     * @param request
     * @return
     */
    @GetMapping("/sms/sendSms")
    public AjaxResult sendSms(@RequestParam(value = "phoneNumber") String phoneNumber, @RequestParam(value = "ticket") String ticket, @RequestParam(value = "randomStr") String randomStr, HttpServletRequest request, HttpServletResponse response) {
        //发送短信验证码
        AjaxResult ajaxResult = this.checkCaptcha(ticket, randomStr, request);
        if (ajaxResult.isSuccess()) {
            //发送短信
            String code = RandomStringUtils.randomNumeric(4);
            String uuid = send(phoneNumber, code);
            //存起来
            //有效期5分钟
            Date expireTime = new Date(System.currentTimeMillis() + 1000 * 60 * 5);
            int row = wendaoUserService.saveSmsRecord(phoneNumber, code, uuid, expireTime);
            if (row <= 0) {
                return AjaxResult.error("短信发送失败!");
            }
            Map<String, String> map = new HashMap<>();
            map.put("uuid", uuid);
            map.put("desc", "有效期为5分钟!");
            return AjaxResult.success("发送短信成功", map);
        } else {
            return ajaxResult;
        }
    }

    @GetMapping("/t_captcha/checkCaptcha")
    public AjaxResult checkCaptcha(@RequestParam(value = "ticket") String ticket, @RequestParam(value = "randomStr") String randomStr, HttpServletRequest request) {
        String userIp = IpUtils.getIpAddr(request);
        try {
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("captcha.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
            DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
            req.setCaptchaType(captchaType);
            req.setTicket(ticket);
            req.setUserIp(userIp);
            req.setRandstr(randomStr);
            req.setCaptchaAppId(captchaAppId);
            req.setAppSecretKey(appSecretKey);
            req.setNeedGetCaptchaTime(1L);
            DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);
            System.out.println(AbstractModel.toJsonString(resp));
            Long captchaCode = resp.getCaptchaCode();
            if (captchaCode.intValue() == 1) {
                return AjaxResult.success("success");
            } else {
                return AjaxResult.error(StringUtils.isNotBlank(resp.getCaptchaMsg()) ? resp.getCaptchaMsg() : "验证失败!");
            }
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
            return AjaxResult.error("验证失败!原因:" + e.getMessage());
        }
    }

    /**
     * 处理OPTIONS预检请求
     *
     * @param response
     */
    @RequestMapping(value = "/addContactInfo", method = RequestMethod.OPTIONS)
    public ResponseEntity<Void> handleOptionsRequest(HttpServletResponse response) {
        // 设置跨域响应头
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
        response.setHeader("Access-Control-Max-Age", "3600");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        return ResponseEntity.ok().build();
    }

    /**
     * 新增招商信息
     */
    @PostMapping("/addContactInfo")
    public AjaxResult add(@RequestBody ContcatForm contcatForm, HttpServletResponse response) {
        if (StringUtils.isBlank(contcatForm.getPhone())) {
            return AjaxResult.error("手机号不能为空");
        }
        if (StringUtils.isBlank(contcatForm.getUuid())) {
            return AjaxResult.error("发送验证码uuid不能为空");
        }
        SmsRecordDTO smsRecordDTO = wendaoUserService.querySmsRecord(contcatForm.getPhone(), contcatForm.getUuid());
        if (smsRecordDTO == null) {
            return AjaxResult.error("未找到发送记录!");
        }
        if (smsRecordDTO.getExpireTime() != null && smsRecordDTO.getExpireTime().before(new Date())) {
            return AjaxResult.error("验证码已过期!");
        }
        if (!StringUtils.equals(contcatForm.getCode(), smsRecordDTO.getCode())) {
            return AjaxResult.error("验证码输入错误!");
        }
        ContcatForm queryForm = new ContcatForm();
        queryForm.setPhone(contcatForm.getPhone());
        List<ContcatForm> list = contcatFormService.selectContcatFormList(queryForm);
        if (CollectionUtils.isNotEmpty(list)) {
            return AjaxResult.error("手机号已存在");
        }
        return toAjax(contcatFormService.insertContcatForm(contcatForm));
    }

    private String encrypt(String plaintext, String key, String iv) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        byte[] ivAndEncrypted = new byte[iv.length() + encrypted.length];
        System.arraycopy(iv.getBytes(StandardCharsets.UTF_8), 0, ivAndEncrypted, 0, iv.length());
        System.arraycopy(encrypted, 0, ivAndEncrypted, iv.length(), encrypted.length);
        return Base64.getEncoder().encodeToString(ivAndEncrypted);
    }

    private String send(String phoneNumber, String code) {
        String uuid = UUID.randomUUID().toString();
        aliyunSmsService.sendVerificationCode(phoneNumber, code);
        return uuid;
    }

//    private void sendCode(String mobile, String code) {
//        // 接收短信的手机号码
//        //String mobile = "18667016502";
//        // 模板ID
//        long tplId = 6056480;
//
//        // 构建模板参数
//        Map<String, String> tplParams = new HashMap<>();
//        tplParams.put("code", code);
//
//        try {
//            String result = tplSingleSend(mobile, tplId, tplParams);
//            System.out.println("发送结果：" + result);
//        } catch (Exception e) {
//            System.err.println("发送失败：" + e.getMessage());
//        }
//    }

    private String tplSingleSend(String mobile, long tplId, Map<String, String> tplParams) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("apikey", APIKEY);
        params.put("mobile", mobile);
        params.put("tpl_id", String.valueOf(tplId));

        // 构建模板变量值字符串
        StringBuilder tplValue = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : tplParams.entrySet()) {
            if (!first) {
                tplValue.append("&");
            }
            tplValue.append(URLEncoder.encode("#" + entry.getKey() + "#", ENCODING))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), ENCODING));
            first = false;
        }
        params.put("tpl_value", tplValue.toString());

        return post(API_URL, params);
    }

    private String post(String url, Map<String, String> params) throws IOException {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        // 构建表单参数
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (postData.length() != 0) {
                postData.append('&');
            }
            postData.append(URLEncoder.encode(param.getKey(), ENCODING));
            postData.append('=');
            postData.append(URLEncoder.encode(param.getValue(), ENCODING));
        }

        // 设置请求头
        post.setHeader("Accept", "application/json;charset=utf-8");
        post.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求体
        StringEntity entity = new StringEntity(postData.toString());
        post.setEntity(entity);

        // 发送请求并获取响应
        HttpResponse response = client.execute(post);
        String result = EntityUtils.toString(response.getEntity(), ENCODING);

        client.close();
        return result;
    }
} 