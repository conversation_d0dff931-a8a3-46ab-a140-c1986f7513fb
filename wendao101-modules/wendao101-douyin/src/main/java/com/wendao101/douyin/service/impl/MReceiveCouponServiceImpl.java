package com.wendao101.douyin.service.impl;

import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.douyin.dto.MreceiveCouponDTO;
import com.wendao101.douyin.mapper.MReceiveCouponMapper;
import com.wendao101.douyin.service.IMReceiveCouponService;
import com.wendao101.douyin.vo.MreceiveCouponVO;
import com.wendao101.douyin.domain.MReceiveCoupon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 领取优惠券Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
@Service
public class MReceiveCouponServiceImpl implements IMReceiveCouponService
{
    @Autowired
    private MReceiveCouponMapper mReceiveCouponMapper;

    /**
     * 查询领取优惠券列表
     *
     */
    @Override
    public List<MreceiveCouponDTO> selectReceiveCoupon(MreceiveCouponVO mreceiveCouponVO)
    {
        return mReceiveCouponMapper.selectReceiveCoupon(mreceiveCouponVO);
    }

    /**
     * 查询领取优惠券
     * 
     * @param id 领取优惠券主键
     * @return 领取优惠券
     */
    @Override
    public MReceiveCoupon selectMReceiveCouponById(Long id)
    {
        return mReceiveCouponMapper.selectMReceiveCouponById(id);
    }

    /**
     * 查询领取优惠券列表
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 领取优惠券
     */
    @Override
    public List<MReceiveCoupon> selectMReceiveCouponList(MReceiveCoupon mReceiveCoupon)
    {
        return mReceiveCouponMapper.selectMReceiveCouponList(mReceiveCoupon);
    }

    /**
     * 新增领取优惠券
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 结果
     */
    @Override
    public int insertMReceiveCoupon(MReceiveCoupon mReceiveCoupon)
    {
        mReceiveCoupon.setCreateTime(DateUtils.getNowDate());
        return mReceiveCouponMapper.insertMReceiveCoupon(mReceiveCoupon);
    }

    /**
     * 修改领取优惠券
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 结果
     */
    @Override
    public int updateMReceiveCoupon(MReceiveCoupon mReceiveCoupon)
    {
        mReceiveCoupon.setUpdateTime(DateUtils.getNowDate());
        return mReceiveCouponMapper.updateMReceiveCoupon(mReceiveCoupon);
    }

    /**
     * 批量删除领取优惠券
     * 
     * @param ids 需要删除的领取优惠券主键
     * @return 结果
     */
    @Override
    public int deleteMReceiveCouponByIds(Long[] ids)
    {
        return mReceiveCouponMapper.deleteMReceiveCouponByIds(ids);
    }

    /**
     * 删除领取优惠券信息
     * 
     * @param id 领取优惠券主键
     * @return 结果
     */
    @Override
    public int deleteMReceiveCouponById(Long id)
    {
        return mReceiveCouponMapper.deleteMReceiveCouponById(id);
    }

    @Override
    public void plusOneReceiveCouponSum(Long discountsId) {
         mReceiveCouponMapper.plusOneReceiveCouponSum(discountsId);
    }

    @Override
    public MReceiveCoupon selectMReceiveCouponByUserIdAndDiscountsId(Long userId, Long discountsId) {
        return mReceiveCouponMapper.selectMReceiveCouponByUserIdAndDiscountsId(userId,discountsId);
    }

    @Override
    public Long selectMReceiveCouponByUserIdAndDiscountsId1(Long userId, Long discountsId) {
        return mReceiveCouponMapper.selectMReceiveCouponByUserIdAndDiscountsId1(userId,discountsId);
    }
}
