package com.wendao101.douyin.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 快手课程资源上传对象 ks_resource_upload
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
public class KsResourceUpload extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long resourceId;

    /** 资源类型0、图片;1、视频履约内容;2、音频履约内容;默认0图片 */
    @Excel(name = "资源类型0、图片;1、视频履约内容;2、音频履约内容;默认0图片")
    private Integer resourceType;

    /** 原始资源url */
    @Excel(name = "原始资源url")
    private String resourceUrl;

    /** 快手图片上传后的imgKey */
    @Excel(name = "快手图片上传后的imgKey")
    private String imgKey;

    /** 快手视频上传后的视频id */
    @Excel(name = "快手视频上传后的视频id")
    private String videoId;

    /** 快手音频上传后的音频id */
    @Excel(name = "快手音频上传后的音频id")
    private String audioId;

    /** 快手资源状态及回调状态音视频:1.处理中2.转码成功3.转码失败4.视频拉取失败 */
    @Excel(name = "快手资源状态及回调状态音视频:1.处理中2.转码成功3.转码失败4.视频拉取失败")
    private Integer ksStatus;

    /** 来源的APP,1问到好课,2问到课堂 */
    @Excel(name = "来源的APP,1问到好课,2问到课堂")
    private Integer appNameType;

    /** 错误码，1为成功,其他为失败 */
    @Excel(name = "错误码，1为成功,其他为失败")
    private Integer result;

    /** 错误内容 */
    @Excel(name = "错误内容")
    private String errorMsg;

    /** 快手appid */
    @Excel(name = "快手appid")
    private String ksAppId;

    public void setResourceId(Long resourceId) 
    {
        this.resourceId = resourceId;
    }

    public Long getResourceId() 
    {
        return resourceId;
    }
    public void setResourceType(Integer resourceType) 
    {
        this.resourceType = resourceType;
    }

    public Integer getResourceType() 
    {
        return resourceType;
    }
    public void setResourceUrl(String resourceUrl) 
    {
        this.resourceUrl = resourceUrl;
    }

    public String getResourceUrl() 
    {
        return resourceUrl;
    }
    public void setImgKey(String imgKey) 
    {
        this.imgKey = imgKey;
    }

    public String getImgKey() 
    {
        return imgKey;
    }
    public void setVideoId(String videoId) 
    {
        this.videoId = videoId;
    }

    public String getVideoId() 
    {
        return videoId;
    }
    public void setAudioId(String audioId) 
    {
        this.audioId = audioId;
    }

    public String getAudioId() 
    {
        return audioId;
    }
    public void setKsStatus(Integer ksStatus) 
    {
        this.ksStatus = ksStatus;
    }

    public Integer getKsStatus() 
    {
        return ksStatus;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }
    public void setResult(Integer result) 
    {
        this.result = result;
    }

    public Integer getResult() 
    {
        return result;
    }
    public void setErrorMsg(String errorMsg) 
    {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() 
    {
        return errorMsg;
    }
    public void setKsAppId(String ksAppId) 
    {
        this.ksAppId = ksAppId;
    }

    public String getKsAppId() 
    {
        return ksAppId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("resourceId", getResourceId())
            .append("resourceType", getResourceType())
            .append("resourceUrl", getResourceUrl())
            .append("imgKey", getImgKey())
            .append("videoId", getVideoId())
            .append("audioId", getAudioId())
            .append("ksStatus", getKsStatus())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("appNameType", getAppNameType())
            .append("result", getResult())
            .append("errorMsg", getErrorMsg())
            .append("ksAppId", getKsAppId())
            .toString();
    }
}
