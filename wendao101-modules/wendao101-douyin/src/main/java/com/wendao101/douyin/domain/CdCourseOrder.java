package com.wendao101.douyin.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 抽单订单信息对象 cd_course_order
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public class CdCourseOrder extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 购买人id */
    @Excel(name = "购买人id")
    private Long buyerUserId;

    /** 购买人头像 */
    @Excel(name = "购买人头像")
    private String buyerUserImg;

    /** 购买人姓名 */
    @Excel(name = "购买人姓名")
    private String buyerUserName;

    /** 购买人手机号 */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /** 课程总时长 ， 单位是秒 */
    @Excel(name = "课程总时长 ， 单位是秒")
    private Long courseDuration;

    /** 课程图片 */
    @Excel(name = "课程图片")
    private String courseImgUrl;

    /** 学习时长 ， 单位是秒 */
    @Excel(name = "学习时长 ， 单位是秒")
    private Long studyDuration;

    /** 有效期 */
    @Excel(name = "有效期")
    private Long validity;

    /** 推广比率 */
    @Excel(name = "推广比率")
    private String promotionRatio;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 是否是推广订单 0不是  1是 */
    @Excel(name = "是否是推广订单 0不是  1是")
    private Integer isPromoter;

    /** 推广员名称 */
    @Excel(name = "推广员名称")
    private String promoterName;

    /** 推广员电话 */
    @Excel(name = "推广员电话")
    private String promoterMobile;

    /** 我的收益 */
    @Excel(name = "我的收益")
    private BigDecimal myEarningsPrice;

    /** 推广员收益 */
    @Excel(name = "推广员收益")
    private BigDecimal promoterEarningsPrice;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String courseTitle;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal coursePrice;

    /** 是否赠送课程 0否 1是 */
    @Excel(name = "是否赠送课程 0否 1是")
    private Integer isCourse;

    /** 支付金额 */
    @Excel(name = "支付金额")
    private BigDecimal payPrice;

    /** 课程划线价格 */
    @Excel(name = "课程划线价格")
    private BigDecimal originalPrice;

    /** 订单状态 0 待支付 1已支付 2已退款 3已关闭 4退款中 5退款失败 6拒绝退款 7已取消 8投诉中 */
    @Excel(name = "订单状态 0 待支付 1已支付 2已退款 3已关闭 4退款中 5退款失败 6拒绝退款 7已取消 8投诉中")
    private Integer orderStatus;

    /** 订单类型 0课程订单 1推广订单 */
    @Excel(name = "订单类型 0课程订单 1推广订单")
    private Integer orderType;

    /** 订单来源 0抖音 1微信 2快速 3视频号 */
    @Excel(name = "订单来源 0抖音 1微信 2快速 3视频号")
    private Integer orderPlatform;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 资金类型 0在途 1已入账 */
    @Excel(name = "资金类型 0在途 1已入账")
    private Integer fundsType;

    /** 支付方式  支付宝支付 微信支付 银行卡支付 抖音支付 */
    @Excel(name = "支付方式  支付宝支付 微信支付 银行卡支付 抖音支付")
    private String payWay;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 平台订单号 */
    @Excel(name = "平台订单号")
    private String outOrderNumber;

    /** 渠道支付单号 */
    @Excel(name = "渠道支付单号")
    private String tradingOrderNumber;

    /** 物流单号 */
    @Excel(name = "物流单号")
    private String deliverGoodsOrderId;

    /** 删除状态 0否 1是 */
    @Excel(name = "删除状态 0否 1是")
    private Integer isDelete;

    /** 学习上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "学习上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date learningReportingTime;

    /** 来源的APP,1问到好课,2问到课堂 */
    @Excel(name = "来源的APP,1问到好课,2问到课堂")
    private Integer appNameType;

    /** 用户的openid */
    @Excel(name = "用户的openid")
    private String openId;

    /** 用户的unionid */
    @Excel(name = "用户的unionid")
    private String unionId;

    /** 快手结算状态:0未结算,1已经结算 */
    @Excel(name = "快手结算状态:0未结算,1已经结算")
    private Integer settleStatus;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setBuyerUserId(Long buyerUserId) 
    {
        this.buyerUserId = buyerUserId;
    }

    public Long getBuyerUserId() 
    {
        return buyerUserId;
    }
    public void setBuyerUserImg(String buyerUserImg) 
    {
        this.buyerUserImg = buyerUserImg;
    }

    public String getBuyerUserImg() 
    {
        return buyerUserImg;
    }
    public void setBuyerUserName(String buyerUserName) 
    {
        this.buyerUserName = buyerUserName;
    }

    public String getBuyerUserName() 
    {
        return buyerUserName;
    }
    public void setBuyerUserMobile(String buyerUserMobile) 
    {
        this.buyerUserMobile = buyerUserMobile;
    }

    public String getBuyerUserMobile() 
    {
        return buyerUserMobile;
    }
    public void setCourseDuration(Long courseDuration) 
    {
        this.courseDuration = courseDuration;
    }

    public Long getCourseDuration() 
    {
        return courseDuration;
    }
    public void setCourseImgUrl(String courseImgUrl) 
    {
        this.courseImgUrl = courseImgUrl;
    }

    public String getCourseImgUrl() 
    {
        return courseImgUrl;
    }
    public void setStudyDuration(Long studyDuration) 
    {
        this.studyDuration = studyDuration;
    }

    public Long getStudyDuration() 
    {
        return studyDuration;
    }
    public void setValidity(Long validity) 
    {
        this.validity = validity;
    }

    public Long getValidity() 
    {
        return validity;
    }
    public void setPromotionRatio(String promotionRatio) 
    {
        this.promotionRatio = promotionRatio;
    }

    public String getPromotionRatio() 
    {
        return promotionRatio;
    }
    public void setPromoterId(Long promoterId) 
    {
        this.promoterId = promoterId;
    }

    public Long getPromoterId() 
    {
        return promoterId;
    }
    public void setIsPromoter(Integer isPromoter) 
    {
        this.isPromoter = isPromoter;
    }

    public Integer getIsPromoter() 
    {
        return isPromoter;
    }
    public void setPromoterName(String promoterName) 
    {
        this.promoterName = promoterName;
    }

    public String getPromoterName() 
    {
        return promoterName;
    }
    public void setPromoterMobile(String promoterMobile) 
    {
        this.promoterMobile = promoterMobile;
    }

    public String getPromoterMobile() 
    {
        return promoterMobile;
    }
    public void setMyEarningsPrice(BigDecimal myEarningsPrice) 
    {
        this.myEarningsPrice = myEarningsPrice;
    }

    public BigDecimal getMyEarningsPrice() 
    {
        return myEarningsPrice;
    }
    public void setPromoterEarningsPrice(BigDecimal promoterEarningsPrice) 
    {
        this.promoterEarningsPrice = promoterEarningsPrice;
    }

    public BigDecimal getPromoterEarningsPrice() 
    {
        return promoterEarningsPrice;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setCourseTitle(String courseTitle) 
    {
        this.courseTitle = courseTitle;
    }

    public String getCourseTitle() 
    {
        return courseTitle;
    }
    public void setCoursePrice(BigDecimal coursePrice) 
    {
        this.coursePrice = coursePrice;
    }

    public BigDecimal getCoursePrice() 
    {
        return coursePrice;
    }
    public void setIsCourse(Integer isCourse) 
    {
        this.isCourse = isCourse;
    }

    public Integer getIsCourse() 
    {
        return isCourse;
    }
    public void setPayPrice(BigDecimal payPrice) 
    {
        this.payPrice = payPrice;
    }

    public BigDecimal getPayPrice() 
    {
        return payPrice;
    }
    public void setOriginalPrice(BigDecimal originalPrice) 
    {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getOriginalPrice() 
    {
        return originalPrice;
    }
    public void setOrderStatus(Integer orderStatus) 
    {
        this.orderStatus = orderStatus;
    }

    public Integer getOrderStatus() 
    {
        return orderStatus;
    }
    public void setOrderType(Integer orderType) 
    {
        this.orderType = orderType;
    }

    public Integer getOrderType() 
    {
        return orderType;
    }
    public void setOrderPlatform(Integer orderPlatform) 
    {
        this.orderPlatform = orderPlatform;
    }

    public Integer getOrderPlatform() 
    {
        return orderPlatform;
    }
    public void setOrderTime(Date orderTime) 
    {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() 
    {
        return orderTime;
    }
    public void setFundsType(Integer fundsType) 
    {
        this.fundsType = fundsType;
    }

    public Integer getFundsType() 
    {
        return fundsType;
    }
    public void setPayWay(String payWay) 
    {
        this.payWay = payWay;
    }

    public String getPayWay() 
    {
        return payWay;
    }
    public void setPayTime(Date payTime) 
    {
        this.payTime = payTime;
    }

    public Date getPayTime() 
    {
        return payTime;
    }
    public void setOutOrderNumber(String outOrderNumber) 
    {
        this.outOrderNumber = outOrderNumber;
    }

    public String getOutOrderNumber() 
    {
        return outOrderNumber;
    }
    public void setTradingOrderNumber(String tradingOrderNumber) 
    {
        this.tradingOrderNumber = tradingOrderNumber;
    }

    public String getTradingOrderNumber() 
    {
        return tradingOrderNumber;
    }
    public void setDeliverGoodsOrderId(String deliverGoodsOrderId) 
    {
        this.deliverGoodsOrderId = deliverGoodsOrderId;
    }

    public String getDeliverGoodsOrderId() 
    {
        return deliverGoodsOrderId;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setLearningReportingTime(Date learningReportingTime) 
    {
        this.learningReportingTime = learningReportingTime;
    }

    public Date getLearningReportingTime() 
    {
        return learningReportingTime;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setUnionId(String unionId) 
    {
        this.unionId = unionId;
    }

    public String getUnionId() 
    {
        return unionId;
    }
    public void setSettleStatus(Integer settleStatus) 
    {
        this.settleStatus = settleStatus;
    }

    public Integer getSettleStatus() 
    {
        return settleStatus;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("teacherId", getTeacherId())
            .append("buyerUserId", getBuyerUserId())
            .append("buyerUserImg", getBuyerUserImg())
            .append("buyerUserName", getBuyerUserName())
            .append("buyerUserMobile", getBuyerUserMobile())
            .append("courseDuration", getCourseDuration())
            .append("courseImgUrl", getCourseImgUrl())
            .append("studyDuration", getStudyDuration())
            .append("validity", getValidity())
            .append("promotionRatio", getPromotionRatio())
            .append("promoterId", getPromoterId())
            .append("isPromoter", getIsPromoter())
            .append("promoterName", getPromoterName())
            .append("promoterMobile", getPromoterMobile())
            .append("myEarningsPrice", getMyEarningsPrice())
            .append("promoterEarningsPrice", getPromoterEarningsPrice())
            .append("courseId", getCourseId())
            .append("courseTitle", getCourseTitle())
            .append("coursePrice", getCoursePrice())
            .append("isCourse", getIsCourse())
            .append("payPrice", getPayPrice())
            .append("originalPrice", getOriginalPrice())
            .append("orderStatus", getOrderStatus())
            .append("orderType", getOrderType())
            .append("orderPlatform", getOrderPlatform())
            .append("orderTime", getOrderTime())
            .append("fundsType", getFundsType())
            .append("payWay", getPayWay())
            .append("payTime", getPayTime())
            .append("outOrderNumber", getOutOrderNumber())
            .append("tradingOrderNumber", getTradingOrderNumber())
            .append("deliverGoodsOrderId", getDeliverGoodsOrderId())
            .append("isDelete", getIsDelete())
            .append("learningReportingTime", getLearningReportingTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("appNameType", getAppNameType())
            .append("openId", getOpenId())
            .append("unionId", getUnionId())
            .append("settleStatus", getSettleStatus())
            .toString();
    }
}
