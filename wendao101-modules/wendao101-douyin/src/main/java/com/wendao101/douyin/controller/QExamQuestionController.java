package com.wendao101.douyin.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.domain.*;
import com.wendao101.douyin.dto.QuestionResultDTO;
import com.wendao101.douyin.dto.SubmitTestResultDTO;
import com.wendao101.douyin.dto.SubmitSingleResultDTO;
import com.wendao101.douyin.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 题库测试题Controller
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
@RestController
@RequestMapping("/exam_question")
public class QExamQuestionController extends BaseController {
    @Autowired
    private IQExamQuestionService qExamQuestionService;
    @Autowired
    private IQBankQuestionRelationService qBankQuestionRelationService;
    @Autowired
    private IWendaoUserService weaUserService;
    @Autowired
    private IQQuestionBankService qQuestionBankService;
    @Autowired
    private IQExamBatchService qExamBatchService;
    @Autowired
    private IQExamRecordService qExamRecordService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IUserExamResultService userExamResultService;

    public static final String examPoint_prefix = "examPoint_prefix:";
    public static final String examResult_prefix = "examResult_prefix:";
    @PostMapping("/submit_test_point")
    public AjaxResult submitTestPoint(@RequestBody SubmitTestResultDTO qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return error("题库id必传且必须大于0!");
        }
        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(qExamQuestion.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if (!Objects.equals(qQuestionBank.getAppNameType(), qExamQuestion.getAppNameType())) {
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }
        String key = examPoint_prefix+qExamQuestion.getOpenId()+qExamQuestion.getPlatform()+qExamQuestion.getAppNameType()+qExamQuestion.getQuestionBankId();
        redisService.setCacheObject(key,qExamQuestion);
        return success();
    }

    @PostMapping("/query_test_point")
    public AjaxResult queryTestPoint(@RequestBody SubmitTestResultDTO qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return error("题库id必传且必须大于0!");
        }
        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(qExamQuestion.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if (!Objects.equals(qQuestionBank.getAppNameType(), qExamQuestion.getAppNameType())) {
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }
        String key = examPoint_prefix+qExamQuestion.getOpenId()+qExamQuestion.getPlatform()+qExamQuestion.getAppNameType()+qExamQuestion.getQuestionBankId();
        SubmitTestResultDTO qExamQuestionResult = redisService.getCacheObject(key);
        return success(qExamQuestionResult);
    }

    @PostMapping("/clear_test_point")
    public AjaxResult clearTestPoint(@RequestBody SubmitTestResultDTO qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return error("题库id必传且必须大于0!");
        }
        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(qExamQuestion.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if (!Objects.equals(qQuestionBank.getAppNameType(), qExamQuestion.getAppNameType())) {
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }
        String key = examPoint_prefix+qExamQuestion.getOpenId()+qExamQuestion.getPlatform()+qExamQuestion.getAppNameType()+qExamQuestion.getQuestionBankId();
        String listKey = examResult_prefix+qExamQuestion.getOpenId()+qExamQuestion.getPlatform()+qExamQuestion.getAppNameType()+qExamQuestion.getQuestionBankId();
        redisService.deleteObject(key);
        userExamResultService.deleteUserExamResultByKey(listKey);
        redisService.deleteObject(listKey);
        return success();
    }

    @PostMapping("/submit_single_question_result")
    public AjaxResult submit_single_question_result(@RequestBody SubmitSingleResultDTO singleResultDTO) {
        if (StringUtils.isBlank(singleResultDTO.getOpenId())) {
            return error("openId必传!");
        }
        if (singleResultDTO.getPlatform() == null) {
            return error("platform必传!");
        }
        if (singleResultDTO.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(singleResultDTO.getOpenId(), singleResultDTO.getPlatform(), singleResultDTO.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        if (singleResultDTO.getQuestionBankId() == null || singleResultDTO.getQuestionBankId() <= 0L) {
            return error("题库id必传且必须大于0!");
        }
        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(singleResultDTO.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if (!Objects.equals(qQuestionBank.getAppNameType(), singleResultDTO.getAppNameType())) {
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }

        String key = examResult_prefix + singleResultDTO.getOpenId() + singleResultDTO.getPlatform() + singleResultDTO.getAppNameType() + singleResultDTO.getQuestionBankId();
        QuestionResultDTO questionResultDTO = singleResultDTO.getQuestionResultDTO();
        if (questionResultDTO != null && questionResultDTO.getQuestionId() != null) {
            saveResult(key, questionResultDTO);
        }
        return success();
    }

    private void saveResult(String key, QuestionResultDTO questionResultDTO) {
        //先查询下
        UserExamResult userExamResult = new UserExamResult();
        userExamResult.setExamResultKey(key);
        userExamResult.setQuestionId(questionResultDTO.getQuestionId());
        List<UserExamResult> userExamResults = userExamResultService.selectUserExamResultList(userExamResult);
        if (CollectionUtils.isNotEmpty(userExamResults)) {
            userExamResult.setId(userExamResults.get(0).getId());
        }
        userExamResult.setAnswer(questionResultDTO.getAnswer());
        userExamResult.setIsCurrent(questionResultDTO.getIsCurrent());
        userExamResult.setUpdateTime(new Date());
        if (userExamResult.getId() == null) {
            userExamResult.setCreateTime(new Date());
            userExamResultService.insertUserExamResult(userExamResult);
        } else {
            userExamResultService.updateUserExamResult(userExamResult);
        }
    }

    @GetMapping("/listMergeResult")
    public TableDataInfo listMergeResult(QExamQuestion qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return TableDataInfo.error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return TableDataInfo.error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return TableDataInfo.error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return TableDataInfo.error("用户不存在!");
        }
        qExamQuestion.setIsDelete(0);
        startPage();
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return TableDataInfo.error("题库id必传且必须大于0!");
        }
        qExamQuestion.setDuration(null);

        String key = examResult_prefix + qExamQuestion.getOpenId() + qExamQuestion.getPlatform() + qExamQuestion.getAppNameType() + qExamQuestion.getQuestionBankId();
        List<UserExamResult> userExamResults = userExamResultService.selectUserExamResultByKey(key);
        List<QExamQuestion> list = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestion);
        //userExamResults中的questionId和list中对象id相等的，合并
        Map<Long, UserExamResult> cacheMap = new HashMap<>();
        for (UserExamResult item : userExamResults) {
            cacheMap.put(item.getQuestionId(), item);
        }
        if (!cacheMap.isEmpty()) {
            list.forEach(item -> {
                UserExamResult userResult = cacheMap.get(item.getId());
                if (userResult != null) {
                    item.setUserAnswer(userResult.getAnswer());
                    item.setIsCurrent(userResult.getIsCurrent());
                }
            });
        }
        return getDataTable(list);
    }

    @PostMapping("/submit_test_result")
    public AjaxResult submitTestResult(@RequestBody SubmitTestResultDTO qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return error("题库id必传且必须大于0!");
        }
        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(qExamQuestion.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if (!Objects.equals(qQuestionBank.getAppNameType(), qExamQuestion.getAppNameType())) {
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }
        int count = qBankQuestionRelationService.selectCountBankQuestion(qExamQuestion.getQuestionBankId());
        qQuestionBank.setQuestionNum(count);

        List<QuestionResultDTO> questionResultList = qExamQuestion.getQuestionResultList();
        if (CollectionUtils.isEmpty(questionResultList)) {
            return error("答题结果不能为空!");
        }

        //是否符合提交条件
        if (qQuestionBank.getIsLimitSubmit() != null && qQuestionBank.getIsLimitSubmit() == 1) {
            //提交限制
            Integer limitSubmitRate = qQuestionBank.getLimitSubmitRate();
            if (limitSubmitRate != null && limitSubmitRate > 0) {
                BigDecimal rate = (new BigDecimal(limitSubmitRate)).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                BigDecimal rateNum = new BigDecimal(count).multiply(rate);
                if (questionResultList.size() < rateNum.intValue()) {
                    return error("答题数量小于" + rateNum.intValue() + "，请继续答题");
                }
            }
        }
        //符合答题条件,检验答案,存入批次
        //创建批次
        QExamBatch qExamBatch = new QExamBatch();
        qExamBatch.setAppNameType(qExamQuestion.getAppNameType());
        qExamBatch.setQuestionBankId(qExamQuestion.getQuestionBankId());
        qExamBatch.setTeacherId(qQuestionBank.getTeacherId());
        qExamBatch.setUserId(wendaoUser.getId());
        qExamBatchService.insertQExamBatch(qExamBatch);
        //计算分数并更新
        boolean isScore = qQuestionBank.getScoringRule() != null && qQuestionBank.getScoringRule() == 1;

        Long batchId = qExamBatch.getId();
        Integer totalScore = 0;
        int correctNum = 0;
        //一次性查询出题库所有题目
        QExamQuestion qExamQuestionAll = new QExamQuestion();
        qExamQuestionAll.setQuestionBankId(qExamQuestion.getQuestionBankId());
        List<QExamQuestion> list = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestionAll);
        Map<Long, QExamQuestion> allMap = list.stream().collect(Collectors.toMap(QExamQuestion::getId, Function.identity()));

        //按分数计算和按比例计算
        for (QuestionResultDTO dto : questionResultList) {
            dto.setIsCurrent(false);
            dto.setExamQuestion(allMap.get(dto.getQuestionId()));
            //计算结果是否正确
            Long questionId = dto.getQuestionId();
            QExamQuestion qExamQuestion1 = qExamQuestionService.selectQExamQuestionById(questionId);

            QExamQuestion qExamQuestion2 = allMap.get(dto.getQuestionId());

            QExamRecord qExamRecord = new QExamRecord();
            qExamRecord.setUserId(wendaoUser.getId());
            qExamRecord.setTeacherId(qQuestionBank.getTeacherId());
            qExamRecord.setQuestionId(dto.getQuestionId());
            qExamRecord.setAnswer(dto.getAnswer());
            qExamRecord.setAnswerCorrect(0);
            qExamRecord.setAppNameType(qExamQuestion.getAppNameType());
            qExamRecord.setQuestionType(qExamQuestion1.getQuestionType());
            qExamRecord.setSerialNumber(qExamQuestion2 == null ? null : qExamQuestion2.getSerialNumber());
            qExamRecord.setBatchId(batchId);


            String answer = qExamQuestion1.getAnswer();
            String userAnswer = dto.getAnswer();
            //两个答案比较是否相等,相等条件按逗号分隔,每个答案转换为小写再排序如a,b,c在比较相等与否
            if (StringUtils.isNotBlank(answer) && StringUtils.isNotBlank(userAnswer)) {
                answer = answer.toLowerCase();
                userAnswer = userAnswer.toLowerCase();
                String[] answerArr = answer.split(",");
                String[] userAnswerArr = userAnswer.split(",");
                //对answerArr排序,从字母小到到a-z
                Arrays.sort(answerArr);
                Arrays.sort(userAnswerArr);
                String join1 = StringUtils.join(answerArr, ",");
                String join2 = StringUtils.join(userAnswerArr, ",");
                if (StringUtils.equalsIgnoreCase(join1, join2)) {
                    //答案正确
                    dto.setIsCurrent(true);
                    //保存答题记录
                    qExamRecord.setAnswerCorrect(1);
                    if (isScore) {
                        QBankQuestionRelation qBankQuestionRelationQuery = new QBankQuestionRelation();
                        qBankQuestionRelationQuery.setQuestionBankId(qExamQuestion.getQuestionBankId());
                        qBankQuestionRelationQuery.setExamQuestionId(questionId);
                        List<QBankQuestionRelation> qBankQuestionRelations = qBankQuestionRelationService.selectQBankQuestionRelationList(qBankQuestionRelationQuery);
                        if(CollectionUtils.isNotEmpty(qBankQuestionRelations)){
                            QBankQuestionRelation qBankQuestionRelation = qBankQuestionRelations.get(0);
                            totalScore += qBankQuestionRelation.getScore();
                        }
                    } else {
                        correctNum++;
                    }
                }
            }
            qExamRecordService.insertQExamRecord(qExamRecord);
        }

        Map<String, Object> result = new HashMap<>();
        //最终得分
        if (isScore) {
            result.put("totalScore", totalScore);
        } else {
            Integer questionNum = qQuestionBank.getQuestionNum();
            if (questionNum != null && questionNum > 0) {
                result.put("totalScore", new BigDecimal(correctNum).divide(new BigDecimal(questionNum), 2, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).intValue());
            } else {
                result.put("totalScore", 0);
            }
        }
        result.put("questionResultList",questionResultList);
        return success(result);
    }

    @GetMapping("/query_question_bank")
    public AjaxResult queryQuestionBank(QExamQuestion qExamQuestion) {
        if(StringUtils.isBlank(qExamQuestion.getOpenId())){
            return error("openId必传!");
        }
        if(qExamQuestion.getPlatform()==null){
            return error("platform必传!");
        }
        if(qExamQuestion.getAppNameType()==null){
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if(wendaoUser==null){
            return error("用户不存在!");
        }

        if(qExamQuestion.getQuestionBankId()==null||qExamQuestion.getQuestionBankId()<=0L){
            return error("题库id必传且必须大于0!");
        }

        QQuestionBank qQuestionBank = qQuestionBankService.selectQQuestionBankById(qExamQuestion.getQuestionBankId());
        if (qQuestionBank == null) {
            return error("题库不存在");
        }
        if(!Objects.equals(qQuestionBank.getAppNameType(),qExamQuestion.getAppNameType())){
            return error("题库所属app非当前app!");
        }
        if (qQuestionBank.getIsDelete() != null && qQuestionBank.getIsDelete() == 1) {
            return error("题库已被删除");
        }
        int count = qBankQuestionRelationService.selectCountBankQuestion(qExamQuestion.getQuestionBankId());
        qQuestionBank.setQuestionNum(count);
        return success(qQuestionBank);
    }

    /**
     * 抖音端查询题库测试题列表
     */
    @GetMapping("/list")
    public TableDataInfo list(QExamQuestion qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return TableDataInfo.error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return TableDataInfo.error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return TableDataInfo.error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return TableDataInfo.error("用户不存在!");
        }
        qExamQuestion.setIsDelete(0);
        if (qExamQuestion.getDuration() == null || qExamQuestion.getDuration() <= 0) {
            startPage();
        }
        if (qExamQuestion.getQuestionBankId() == null || qExamQuestion.getQuestionBankId() <= 0L) {
            return TableDataInfo.error("题库id必传且必须大于0!");
        }
        if (qExamQuestion.getDuration() == null) {
            qExamQuestion.setDuration(0);
        }
        //第一位数是单选,第二个多选,第三是判断
        //例如*********
        //当qExamQuestion.getDuration()>0且>100时执行
        if (qExamQuestion.getDuration() > 100) {
            String input = String.valueOf(qExamQuestion.getDuration());
            if (input.startsWith("100") && input.length() >= 9 && input.matches("\\d+")) {
                try {
                    // 提取各部分数值
                    //int total = Integer.parseInt(input.substring(0, 3));
                    int singleChoice = Integer.parseInt(input.substring(3, 5));
                    int multiChoice = Integer.parseInt(input.substring(5, 7));
                    int judgment = Integer.parseInt(input.substring(7, 9));
                    List<QExamQuestion> listAll = new ArrayList<>();
                    if (singleChoice > 0) {
                        qExamQuestion.setDuration(singleChoice);
                        qExamQuestion.setQuestionType(1);
                        List<QExamQuestion> listSingleChoice = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestion);
                        listAll.addAll(listSingleChoice);
                    }
                    if (multiChoice > 0) {
                        qExamQuestion.setDuration(multiChoice);
                        qExamQuestion.setQuestionType(2);
                        List<QExamQuestion> listMultiChoice = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestion);
                        listAll.addAll(listMultiChoice);
                    }
                    if (judgment > 0) {
                        qExamQuestion.setDuration(judgment);
                        qExamQuestion.setQuestionType(3);
                        List<QExamQuestion> listJudgment = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestion);
                        listAll.addAll(listJudgment);
                    }
                    TableDataInfo tableDataInfo = new TableDataInfo();
                    tableDataInfo.setTotal(100);
                    tableDataInfo.setCode(200);
                    tableDataInfo.setMsg("查询成功");
                    tableDataInfo.setRows(listAll);
                    return tableDataInfo;
                } catch (NumberFormatException e) {
                    System.out.println("数字格式化错误");
                }
            }
        }
        List<QExamQuestion> list = qBankQuestionRelationService.selectQExamQuestionListByQuestionBankId(qExamQuestion);
        return getDataTable(list);
    }

    /**
     * 获取题库测试题详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, QExamQuestion qExamQuestion) {
        if (StringUtils.isBlank(qExamQuestion.getOpenId())) {
            return error("openId必传!");
        }
        if (qExamQuestion.getPlatform() == null) {
            return error("platform必传!");
        }
        if (qExamQuestion.getAppNameType() == null) {
            return error("appNameType必传!");
        }
        //查询出用户
        WendaoUser wendaoUser = weaUserService.selectByOpenIdPlatformAndAppNameType(qExamQuestion.getOpenId(), qExamQuestion.getPlatform(), qExamQuestion.getAppNameType());
        if (wendaoUser == null) {
            return error("用户不存在!");
        }
        qExamQuestion = qExamQuestionService.selectQExamQuestionById(id);
        if (qExamQuestion == null) {
            return error("题目不存在");
        }
        if (qExamQuestion.getIsDelete() != null && qExamQuestion.getIsDelete() == 1) {
            return error("此题已被删除");
        }
        return success(qExamQuestion);
    }

}
