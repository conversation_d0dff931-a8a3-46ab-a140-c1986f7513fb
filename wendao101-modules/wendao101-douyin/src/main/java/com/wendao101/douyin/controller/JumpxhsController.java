package com.wendao101.douyin.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.feign.KuaishouAccessTokenService;
import com.wendao101.douyin.api.feign.WxKuaishouOrderService;
import com.wendao101.douyin.domain.CourseOrder;
import com.wendao101.douyin.plusmapper.TeacherMapperPlus;
import com.wendao101.douyin.service.ICourseOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@RestController
@RequestMapping("/jumpxhs")
@Slf4j
public class JumpxhsController extends BaseController {

    @Autowired
    TeacherMapperPlus teacherMapper;

    @Autowired
    KuaishouAccessTokenService kuaishouAccessTokenService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICourseOrderService courseOrderService;

    @Autowired
    private WxKuaishouOrderService wxKuaishouOrderService;

    /**
     *orderId为订单主键
     * @param orderId
     * @param response
     */
    @GetMapping("/{userId}/{orderId}")
    public void getOpenId(@PathVariable("userId") Long userId,@PathVariable("orderId") Long orderId, HttpServletResponse response) {
        String url = redisService.getCacheObject("wxcodexhs:"+userId+orderId);
        if(StringUtils.isEmpty(url)){
            CourseOrder courseOrder = courseOrderService.selectCourseOrderByPrimaryId(orderId);
            if(courseOrder!=null&&courseOrder.getOrderStatus()!=null){
                if(courseOrder.getOrderStatus() ==1 || courseOrder.getOrderStatus() ==6){
                    //重新生成
                    if(Objects.equals(courseOrder.getBuyerUserId(), userId)){
                        AjaxResult ajaxResult = wxKuaishouOrderService.generateXhsWxLink(orderId);
                        if(ajaxResult.isSuccess()){
                            url = (String) ajaxResult.get("data");
                            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                            response.setHeader("Location", url);
                            return;
                        }
                    }
                }
            }
            response.setStatus(404);
        }else{
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", url);
        }
    }
}
