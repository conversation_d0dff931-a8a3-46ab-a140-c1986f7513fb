package com.wendao101.douyin.mapper;


import com.wendao101.douyin.domain.QExamRecord;

import java.util.List;

/**
 * 用户每道题答题记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface QExamRecordMapper {
    /**
     * 查询用户每道题答题记录
     *
     * @param id 用户每道题答题记录主键
     * @return 用户每道题答题记录
     */
    QExamRecord selectQExamRecordById(Long id);

    /**
     * 查询用户每道题答题记录列表
     *
     * @param qExamRecord 用户每道题答题记录
     * @return 用户每道题答题记录集合
     */
    List<QExamRecord> selectQExamRecordList(QExamRecord qExamRecord);

    /**
     * 新增用户每道题答题记录
     *
     * @param qExamRecord 用户每道题答题记录
     * @return 结果
     */
    int insertQExamRecord(QExamRecord qExamRecord);

    /**
     * 修改用户每道题答题记录
     *
     * @param qExamRecord 用户每道题答题记录
     * @return 结果
     */
    int updateQExamRecord(QExamRecord qExamRecord);

    /**
     * 删除用户每道题答题记录
     *
     * @param id 用户每道题答题记录主键
     * @return 结果
     */
    int deleteQExamRecordById(Long id);

    /**
     * 批量删除用户每道题答题记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteQExamRecordByIds(Long[] ids);
}
