package com.wendao101.douyin.service;


import com.wendao101.douyin.domain.WendaoLiveCourse;

import java.util.List;

/**
 * 直播关联售卖课程Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-28
 */
public interface IWendaoLiveCourseService 
{
    /**
     * 查询直播关联售卖课程
     * 
     * @param id 直播关联售卖课程主键
     * @return 直播关联售卖课程
     */
    public WendaoLiveCourse selectWendaoLiveCourseById(Long id);

    /**
     * 查询直播关联售卖课程列表
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 直播关联售卖课程集合
     */
    public List<WendaoLiveCourse> selectWendaoLiveCourseList(WendaoLiveCourse wendaoLiveCourse);

    /**
     * 新增直播关联售卖课程
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 结果
     */
    public int insertWendaoLiveCourse(WendaoLiveCourse wendaoLiveCourse);

    /**
     * 修改直播关联售卖课程
     * 
     * @param wendaoLiveCourse 直播关联售卖课程
     * @return 结果
     */
    public int updateWendaoLiveCourse(WendaoLiveCourse wendaoLiveCourse);

    /**
     * 批量删除直播关联售卖课程
     * 
     * @param ids 需要删除的直播关联售卖课程主键集合
     * @return 结果
     */
    public int deleteWendaoLiveCourseByIds(Long[] ids);

    /**
     * 删除直播关联售卖课程信息
     * 
     * @param id 直播关联售卖课程主键
     * @return 结果
     */
    public int deleteWendaoLiveCourseById(Long id);
}
