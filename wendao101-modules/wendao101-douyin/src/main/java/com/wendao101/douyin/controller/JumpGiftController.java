package com.wendao101.douyin.controller;

import cn.binarywang.wx.miniapp.api.WxMaLinkService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkDTO;
import com.wendao101.common.core.douyin.generatelink.GenerateUrlLinkResult;
import com.wendao101.common.core.kspaydto.CreateWendaoOrderDTO;
import com.wendao101.common.core.ktdto.GenerateUrlLinkRequestExt;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.config.DouyinConfig;
import com.wendao101.douyin.api.config.KuaishouConfig;
import com.wendao101.douyin.api.config.ZkKuaishouConfig;
import com.wendao101.douyin.api.feign.KuaishouAccessTokenService;
import com.wendao101.douyin.api.feign.WxKuaishouOrderService;
import com.wendao101.douyin.constants.Appids;
import com.wendao101.douyin.domain.CourseOrder;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.dto.DoudianQueryOrderDTO;
import com.wendao101.douyin.dto.RecvMyOrderDTO;
import com.wendao101.douyin.plusmapper.TeacherMapperPlus;
import com.wendao101.douyin.service.ICourseOrderService;
import com.wendao101.douyin.service.IWendaoUserService;
import com.wendao101.ktma.api.feign.KtWxLoginService;
import com.wendao101.teacher.domain.Teacher;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/giftjump")
@Slf4j
public class JumpGiftController extends BaseController {

    @Autowired
    private WxKuaishouOrderService wxkuaishouOrderService;

    @Autowired
    private KuaishouConfig kuaishouConfig;

    @Autowired
    private ZkKuaishouConfig zkKuaishouConfig;

    private static final String dy_order_code_url = "dy_order_code_url:";
    private static final String dy_order_code_url1 = "dy_order_code_url1:";
    private static final String ks_order_code_url = "ks_order_code_url:";
    private static final String ks_order_code_url1 = "ks_order_code_url1:";

    @Autowired
    TeacherMapperPlus teacherMapper;

    @Autowired
    KuaishouAccessTokenService kuaishouAccessTokenService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private ICourseOrderService courseOrderService;
    private static final String course_give_away = "course_give_away:";
    @Autowired
    private WxMaService wxService;
    /**
     * 问到课堂微信接口
     */
    @Autowired
    private KtWxLoginService ktWxLoginService;

    @Autowired
    private DouyinConfig dyConfig;

    private static final String needminiapp_user_auth = "needminiapp_user_auth:";
    private static final String course_give_away_code_gen = "course_give_away_code_gen:";

    @Autowired
    private ICourseOrderService courseorderService;

    @Autowired
    private IWendaoUserService wendaoUserService;

    /**
     * 按老师生成的代码领取课程
     * @param recvMyOrderDTO
     * @return
     */
    @PostMapping("/receiveMyCodeOrder")
    public AjaxResult receiveMyCodeOrder(@RequestBody RecvMyOrderDTO recvMyOrderDTO) {
        //领课码领课接口
        WendaoUser user = wendaoUserService.selectWendaoUserById(recvMyOrderDTO.getBuyerUserId());
        if (user == null) {
            return error("当前用户Id未匹配到用户");
        }
        Long courseId = redisService.getCacheObject(course_give_away_code_gen + recvMyOrderDTO.getMyCode());
        if (courseId == null || courseId <= 0L) {
            return error("赠课领取码不存在或已失效");
        }
        //GIFT_COURSE_JJHEUJDSEP_365412//或者w开头
        CreateWendaoOrderDTO createWendaoOrderDTO = new CreateWendaoOrderDTO();
        createWendaoOrderDTO.setUserId(user.getId());
        createWendaoOrderDTO.setCourseId(courseId);
        createWendaoOrderDTO.setPlatform(user.getPlatform());
        AjaxResult ajaxResult = wxkuaishouOrderService.createWendaoOrderGiveAway(createWendaoOrderDTO);
        if (!ajaxResult.isSuccess()) {
            String failReason = (String) ajaxResult.get(AjaxResult.MSG_TAG);
            if (failReason.contains("资源包")) {
                return error(failReason + ",请联系老师购买资源包!");
            } else {
                return ajaxResult;
            }
        }
        Long endCourseId = 0L;
        redisService.setCacheObject(course_give_away_code_gen + recvMyOrderDTO.getMyCode(), endCourseId);
        return AjaxResult.success("领取成功,正在跳转到我的课程");
    }
    /**
     * 新增查询订单详情
     *
     * @param doudianQueryOrderDTO
     * @return
     */
    @PostMapping("/queryGiveOrderInfo")
    public AjaxResult queryGiveOrderInfo(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            return error("订单不存在");
        }
        WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
        if (wendaoUserWx == null) {
            return error("当前用户的openId未匹配到用户");
        }
        String telNumber = wendaoUserWx.getTelNumber();
        QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("teacher_id", courseOrder.getTeacherId());
        Teacher teacher = teacherMapper.selectOne(queryWrapper, false);
        if (teacher != null) {
            teacher.setPassword(null);
            teacher.setMobile(null);
        }
        Map<String, Object> map = new HashMap<>();
        Map<String, Object> courseInfo = new HashMap<>();
        //图片和标题
        courseInfo.put("courseId", courseOrder.getCourseId());
        courseInfo.put("courseTitle", courseOrder.getCourseTitle());
        courseInfo.put("courseImgUrl", courseOrder.getCourseImgUrl());
        map.put("teacher", teacher);
        map.put("courseInfo", courseInfo);
        //判断是否领取成功
        //String needAuth = redisService.getCacheObject(needminiapp_user_auth + courseOrder.getOrderId());
        if (StringUtils.isNotBlank(telNumber) && StringUtils.equals(telNumber, courseOrder.getBuyerUserMobile())) {
            redisService.deleteObject(needminiapp_user_auth + courseOrder.getOrderId());
            return new AjaxResult(209, "手机号已绑定,正在跳转课程详情", map);
        }
//        if (StringUtils.isBlank(needAuth)) {
//            return new AjaxResult(200, "查询成功", map);
//        }
        return AjaxResult.success("查询成功", map);
    }

    /**
     * 领取课程
     *
     * @param doudianQueryOrderDTO
     * @return
     */
    @PostMapping("/receiveGiveOrder")
    public AjaxResult receiveGiveOrder(@RequestBody DoudianQueryOrderDTO doudianQueryOrderDTO) {
        if (StringUtils.isBlank(doudianQueryOrderDTO.getOrderId())) {
            return error("订单号不能为空");
        }
        if (StringUtils.isBlank(doudianQueryOrderDTO.getOpenId())) {
            return error("openId不能为空");
        }
        CourseOrder courseOrder = courseorderService.selectCourseOrderByOrderId2(doudianQueryOrderDTO.getOrderId());
        if (courseOrder == null) {
            return error("订单不存在");
        }
        WendaoUser wendaoUserWx = wendaoUserService.selectWendaoUserByOpenId(doudianQueryOrderDTO.getOpenId());
        if (wendaoUserWx == null) {
            return error("当前用户的openId未匹配到用户");
        }
        String telNumber = wendaoUserWx.getTelNumber();
        if (StringUtils.isBlank(telNumber)) {
            return error("当前用户未成功授权手机号！请去[我的]页面授权！");
        }
        if (!StringUtils.equals(telNumber, courseOrder.getBuyerUserMobile())) {
            return error("领课失败，当前授权手机号与赠课订单中手机号不一致");
        } else {
            redisService.deleteObject(needminiapp_user_auth + courseOrder.getOrderId());
        }
        Map<String, Long> map = new HashMap<>();
        map.put("courseId", courseOrder.getCourseId());
        return AjaxResult.success("绑定成功", map);
    }

    /**
     * 赠课链接跳转
     *
     * @param jumpCode
     * @param response
     */
    @GetMapping("/{jumpCode}")
    public void getOpenId(@PathVariable("jumpCode") String jumpCode, HttpServletResponse response) throws IOException {
        //判断一下jumpCode的长度,如果大于8个字符,取jumpCode的前面8个字符
        if (jumpCode.length() > 8) {
            jumpCode = jumpCode.substring(0, 8);
        }
        String orderId = redisService.getCacheObject(course_give_away + jumpCode);
        if (StringUtils.isBlank(orderId)) {
            response.setStatus(404);
            return;
        }
        CourseOrder courseOrder = courseOrderService.selectCourseOrderByOrderId2(orderId);
        if (courseOrder == null) {
            response.setStatus(404);
            return;
        }
        //课程领取完毕需要删除
        String needAuth = redisService.getCacheObject(needminiapp_user_auth + orderId);
        if (StringUtils.isNotBlank(needAuth)) {
            //跳转到新页面
            System.out.println("跳转到新页面");
            String authLink = generateAuthLink(courseOrder);
            if (StringUtils.isNotBlank(authLink)) {
                if (courseOrder.getOrderPlatform() == 2) {
                    //快手
                    response.setContentType("text/html;charset=utf-8");
                    response.getWriter().write(authLink);
                    return;
                } else {
                    //其他
                    response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                    response.setHeader("Location", authLink);
                    return;
                }
            } else {
                response.setStatus(404);
                return;
            }
        }
        //跳转链接
        Integer appNameType = courseOrder.getAppNameType();
        Integer platform = courseOrder.getOrderPlatform();
        Long courseId = courseOrder.getCourseId();
        //订单来源 0抖音 1微信 2快速 3视频号
        if (platform == 0 || platform == 11) {
            if(platform == 11){
                courseOrder.setOrderPlatform(0);
                CourseOrder courseOrderUpdate = new CourseOrder();
                courseOrderUpdate.setOrderPlatform(0);
                courseOrderUpdate.setId(courseOrder.getId());
                courseOrderService.updateCourseOrder(courseOrderUpdate);
            }
            //好课
            String clientTokenKey = dyConfig.getClientAccessTokenKey() + ":" + appNameType;
            String clentAccessToken = redisService.getCacheObject(clientTokenKey);
            String link = redisService.getCacheObject(dy_order_code_url + courseId);
            if (StringUtils.isBlank(link) && StringUtils.isNotBlank(clentAccessToken)) {
                GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
                generateUrlLinkDTO.setApp_id(Appids.getAppidByAppNameType(appNameType));
                generateUrlLinkDTO.setApp_name("douyin");
                //加160天
                long time = (new Date().getTime() + 160L * 24L * 60L * 60L * 1000L) / 1000;
                generateUrlLinkDTO.setExpire_time(time);
                generateUrlLinkDTO.setQuery("{\"course_id\":\"" + courseId + "\"}");
                generateUrlLinkDTO.setPath("pages_details/details/details");
                GenerateUrlLinkResult generateUrlLinkResult = generateUrlLink(generateUrlLinkDTO, GenerateUrlLinkResult.class, clentAccessToken);
                if (generateUrlLinkResult != null && generateUrlLinkResult.getErr_no() == 0) {
                    redisService.setCacheObject(dy_order_code_url + courseId, generateUrlLinkResult.getData().getUrl_link(), 160L, TimeUnit.DAYS);
                    response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                    response.setHeader("Location", generateUrlLinkResult.getData().getUrl_link());
                    return;
                }
            } else {
                if (StringUtils.isNotBlank(link)) {
                    response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                    response.setHeader("Location", link);
                    return;
                }
            }
        }
        if (platform == 1 || platform == 7 || platform == 3) {
            if(platform == 7 || platform == 3){
                courseOrder.setOrderPlatform(1);
                CourseOrder courseOrderUpdate = new CourseOrder();
                courseOrderUpdate.setOrderPlatform(1);
                courseOrderUpdate.setId(courseOrder.getId());
                courseOrderService.updateCourseOrder(courseOrderUpdate);
            }
            GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
            if (appNameType == 1) {
                dto.setAppId("wx3b0ef5cd084c2e38");
            } else {
                dto.setAppId("wx0e5e01d239197bb1");
            }
            dto.setPath("/pages_details/details/details");
            dto.setQuery("course_id=" + courseId);
            dto.setEnvVersion("release");
            dto.setExpireInterval(30);
            dto.setExpireType(1);
            dto.setIsExpire(true);
            String link1 = redisService.getCacheObject("wxcode:" + courseId);
            if (StringUtils.isBlank(link1)) {
                AjaxResult ajaxResult = generateUrlLink(dto);
                if (ajaxResult.isSuccess()) {
                    String linkUrl = (String) ajaxResult.get("data");
                    redisService.setCacheObject("wxcode:" + courseId, linkUrl, 29L, TimeUnit.DAYS);
                    response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                    response.setHeader("Location", linkUrl);
                    return;
                }
            } else {
                response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
                response.setHeader("Location", link1);
                return;
            }
        }
        if (platform == 2) {
            //发送快手链接
            String appid = null;
            if (appNameType == 1) {
                appid = kuaishouConfig.getAppid();
            }
            if (appNameType == 2) {
                appid = zkKuaishouConfig.getAppid();
            }
            if (StringUtils.isNotBlank(appid)) {
                String link = redisService.getCacheObject(ks_order_code_url + courseId);
                if (StringUtils.isBlank(link)) {
                    link = "kwai://miniapp?appId=" + appid +
                            "&KSMP_source=011005&KSMP_internal_source=011005&path=pages_details%2Fdetails%2Fdetails%3Fid%3D" +
                            courseId + "%26platform%3D2%26appNameType%3D" + appNameType;
                    redisService.setCacheObject(ks_order_code_url + courseId, link, 160L, TimeUnit.DAYS);
                }
                String htmlBody = "<!DOCTYPE html>\n" +
                        "<html lang=\"en\">\n" +
                        "<head>\n" +
                        "    <meta charset=\"UTF-8\">\n" +
                        "    <title>Title</title>\n" +
                        "</head>\n" +
                        "<script>\n" +
                        "    location.href=\"%s\";\n" +
                        "</script>\n" +
                        "<body>\n" +
                        "</body>\n" +
                        "</html>";
                htmlBody = String.format(htmlBody, link);
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write(htmlBody);
                return;
            }
        }
        /**
         * h5
         */
        if (platform == 5) {
            String url = "https://wap.wendao101.com/#/pages_details/details/details?course_id=" + courseId;
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", url);
        }
    }

    /**
     * 查询订单并生成跳转链接
     *
     * @param courseOrder
     * @return
     */
    private String generateAuthLink(CourseOrder courseOrder) {
        Integer appNameType = courseOrder.getAppNameType();
        Integer platform = courseOrder.getOrderPlatform();
        String orderId = courseOrder.getOrderId();
        //订单来源 0抖音 1微信 2快速 3视频号
        if (platform == 0 ) {
            //好课
            String clientTokenKey = dyConfig.getClientAccessTokenKey() + ":" + appNameType;
            String clentAccessToken = redisService.getCacheObject(clientTokenKey);
            String link = redisService.getCacheObject(dy_order_code_url1 + orderId);
            if (StringUtils.isBlank(link) && StringUtils.isNotBlank(clentAccessToken)) {
                GenerateUrlLinkDTO generateUrlLinkDTO = new GenerateUrlLinkDTO();
                generateUrlLinkDTO.setApp_id(Appids.getAppidByAppNameType(appNameType));
                generateUrlLinkDTO.setApp_name("douyin");
                //加160天
                long time = (new Date().getTime() + 160L * 24L * 60L * 60L * 1000L) / 1000;
                generateUrlLinkDTO.setExpire_time(time);
                generateUrlLinkDTO.setQuery("{\"order_id\":\"" + orderId + "\"}");
                generateUrlLinkDTO.setPath("pages_mine/get_course/get_course");
                GenerateUrlLinkResult generateUrlLinkResult = generateUrlLink(generateUrlLinkDTO, GenerateUrlLinkResult.class, clentAccessToken);
                if (generateUrlLinkResult != null && generateUrlLinkResult.getErr_no() == 0) {
                    redisService.setCacheObject(dy_order_code_url1 + orderId, generateUrlLinkResult.getData().getUrl_link(), 160L, TimeUnit.DAYS);
                    return generateUrlLinkResult.getData().getUrl_link();
                }
            } else {
                if (StringUtils.isNotBlank(link)) {
                    return link;
                }
            }
        }
        if (platform == 1) {
            GenerateUrlLinkRequestExt dto = new GenerateUrlLinkRequestExt();
            if (appNameType == 1) {
                dto.setAppId("wx3b0ef5cd084c2e38");
            } else {
                dto.setAppId("wx0e5e01d239197bb1");
            }
            dto.setPath("/pages_mine/get_course/get_course");
            dto.setQuery("order_id=" + courseOrder.getOrderId());
            dto.setEnvVersion("release");
            dto.setExpireInterval(30);
            dto.setExpireType(1);
            dto.setIsExpire(true);
            String link1 = redisService.getCacheObject("wxcode1:" + orderId);
            if (StringUtils.isBlank(link1)) {
                AjaxResult ajaxResult = generateUrlLink(dto);
                if (ajaxResult.isSuccess()) {
                    String linkUrl = (String) ajaxResult.get("data");
                    redisService.setCacheObject("wxcode1:" + orderId, linkUrl, 29L, TimeUnit.DAYS);
                    return linkUrl;
                }
            } else {
                return link1;
            }
        }
        if (platform == 2) {
            //发送快手链接
            String appid = null;
            if (appNameType == 1) {
                appid = kuaishouConfig.getAppid();
            }
            if (appNameType == 2) {
                appid = zkKuaishouConfig.getAppid();
            }
            if (StringUtils.isNotBlank(appid)) {
                String link = redisService.getCacheObject(ks_order_code_url1 + orderId);
                if (StringUtils.isBlank(link)) {
                    link = "kwai://miniapp?appId=" + appid +
                            "&KSMP_source=011005&KSMP_internal_source=011005&path=pages_mine%2Fget_course%2Fget_course%3Forder_id%3D" +
                            courseOrder.getOrderId() + "%26platform%3D2%26appNameType%3D" + courseOrder.getAppNameType();
                    redisService.setCacheObject(ks_order_code_url1 + orderId, link, 160L, TimeUnit.DAYS);
                }
                String htmlBody = "<!DOCTYPE html>\n" +
                        "<html lang=\"en\">\n" +
                        "<head>\n" +
                        "    <meta charset=\"UTF-8\">\n" +
                        "    <title>Title</title>\n" +
                        "</head>\n" +
                        "<script>\n" +
                        "    location.href=\"%s\";\n" +
                        "</script>\n" +
                        "<body>\n" +
                        "</body>\n" +
                        "</html>";
                htmlBody = String.format(htmlBody, link);
                return htmlBody;
            }
        }
        return null;
    }

    private <T, K> T generateUrlLink(K dto, Class<T> clazz, String accessToken) {
        String url = "https://open.douyin.com/api/apps/v1/url_link/generate/";
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("access-token", accessToken);
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    @PostMapping("/generateUrlLink")
    public AjaxResult generateUrlLink(@RequestBody GenerateUrlLinkRequestExt dto) {
        if (StringUtils.isBlank(dto.getAppId())) {
            return AjaxResult.error("appId未传!");
        }
        String appid = wxService.getWxMaConfig().getAppid();
        GenerateUrlLinkRequest request = new GenerateUrlLinkRequest();
        BeanUtils.copyProperties(dto, request);
        if (StringUtils.equals(appid, dto.getAppId())) {
            WxMaLinkService linkService = wxService.getLinkService();
            try {
                String generate = linkService.generateUrlLink(request);
                return AjaxResult.success("success", generate);
            } catch (WxErrorException e) {
                return AjaxResult.error(e.getError().getErrorMsg());
            }
        } else {
            return ktWxLoginService.generateUrlLink(request);
        }
    }
}
