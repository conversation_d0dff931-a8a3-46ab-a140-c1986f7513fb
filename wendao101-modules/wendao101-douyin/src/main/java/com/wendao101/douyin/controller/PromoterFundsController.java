package com.wendao101.douyin.controller;

import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.log.annotation.Log;
import com.wendao101.common.log.enums.BusinessType;
import com.wendao101.common.security.annotation.RequiresPermissions;
import com.wendao101.douyin.domain.PromoterFunds;
import com.wendao101.douyin.service.IDYWenDaoUserService;
import com.wendao101.douyin.service.IPromoterFundsService;
import com.wendao101.douyin.vo.PromoterTableDataInfo;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 推广员资金列Controller
 * 
 * <AUTHOR>
 * @date 2023-08-18
 */
@RestController
@RequestMapping("/promoter_funds")
public class PromoterFundsController extends BaseController
{
    @Autowired
    private IPromoterFundsService promoterFundsService;

    /**
     * 查询推广员资金列列表
     */
   // @RequiresPermissions("douyin:funds:list")
    @GetMapping("/list")
    public TableDataInfo list(PromoterFunds promoterFunds)
    {
        String openid = promoterFunds.getOpenid();
        if(StringUtils.isBlank(openid)){
            return TableDataInfo.error("openid必须传值");
        }
        startPage();
        List<PromoterFunds> list = promoterFundsService.selectPromoterFundsList(promoterFunds);
        TableDataInfo dataTable = getDataTable(list);
        PromoterTableDataInfo promoterTableDataInfo = new PromoterTableDataInfo();
        //
        BigDecimal moneyAmount = promoterFundsService.sumPromoterMoneyByPromoterId(promoterFunds);
        BeanUtils.copyProperties(dataTable,promoterTableDataInfo);
        promoterTableDataInfo.setTotalMoney(moneyAmount);

        return promoterTableDataInfo;
    }

    /**
     * 导出推广员资金列列表
     */
    @RequiresPermissions("douyin:funds:export")
    @Log(title = "推广员资金列", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PromoterFunds promoterFunds)
    {
        List<PromoterFunds> list = promoterFundsService.selectPromoterFundsList(promoterFunds);
        ExcelUtil<PromoterFunds> util = new ExcelUtil<PromoterFunds>(PromoterFunds.class);
        util.exportExcel(response, list, "推广员资金列数据");
    }

    /**
     * 获取推广员资金列详细信息
     */
    @RequiresPermissions("douyin:funds:query")
    @GetMapping(value = "/{fundsId}")
    public AjaxResult getInfo(@PathVariable("fundsId") Long fundsId)
    {
        return success(promoterFundsService.selectPromoterFundsByFundsId(fundsId));
    }

    /**
     * 新增推广员资金列
     */
    @RequiresPermissions("douyin:funds:add")
    @Log(title = "推广员资金列", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PromoterFunds promoterFunds)
    {
        return toAjax(promoterFundsService.insertPromoterFunds(promoterFunds));
    }

    /**
     * 修改推广员资金列
     */
    @RequiresPermissions("douyin:funds:edit")
    @Log(title = "推广员资金列", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PromoterFunds promoterFunds)
    {
        return toAjax(promoterFundsService.updatePromoterFunds(promoterFunds));
    }

    /**
     * 删除推广员资金列
     */
    @RequiresPermissions("douyin:funds:remove")
    @Log(title = "推广员资金列", businessType = BusinessType.DELETE)
	@DeleteMapping("/{fundsIds}")
    public AjaxResult remove(@PathVariable Long[] fundsIds)
    {
        return toAjax(promoterFundsService.deletePromoterFundsByFundsIds(fundsIds));
    }
}
