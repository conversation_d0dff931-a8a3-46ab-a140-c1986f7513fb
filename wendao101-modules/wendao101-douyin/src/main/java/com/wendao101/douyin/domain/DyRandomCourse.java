package com.wendao101.douyin.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 抖音首页生活兴趣对象 dy_random_course
 * 
 * <AUTHOR>
 * @date 2023-08-08
 */
public class DyRandomCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键自增 */
    private Long id;

    /** 封面图 */
    @Excel(name = "封面图")
    private String coverUrl;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 学习次数 */
    @Excel(name = "学习次数")
    private Long learnCount;

    /** 售卖次数 */
    @Excel(name = "售卖次数")
    private Long saleCount;

    /** 是否限时优惠0不是，1是 */
    @Excel(name = "是否限时优惠0不是，1是")
    private Long type;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discount;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 满减金额 */
    @Excel(name = "满减金额")
    private BigDecimal satisfyAmount;

    /** 满减的扣除金额 */
    @Excel(name = "满减的扣除金额")
    private BigDecimal reduceAmount;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date beginTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 副标题 */
    @Excel(name = "副标题")
    private String subTitle;

    /** 关联的课程id */
    @Excel(name = "关联的课程id")
    private Long courseId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCoverUrl(String coverUrl) 
    {
        this.coverUrl = coverUrl;
    }

    public String getCoverUrl() 
    {
        return coverUrl;
    }
    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }
    public void setLearnCount(Long learnCount) 
    {
        this.learnCount = learnCount;
    }

    public Long getLearnCount() 
    {
        return learnCount;
    }
    public void setSaleCount(Long saleCount) 
    {
        this.saleCount = saleCount;
    }

    public Long getSaleCount() 
    {
        return saleCount;
    }
    public void setType(Long type) 
    {
        this.type = type;
    }

    public Long getType() 
    {
        return type;
    }
    public void setDiscount(BigDecimal discount) 
    {
        this.discount = discount;
    }

    public BigDecimal getDiscount() 
    {
        return discount;
    }
    public void setPrice(BigDecimal price) 
    {
        this.price = price;
    }

    public BigDecimal getPrice() 
    {
        return price;
    }
    public void setSatisfyAmount(BigDecimal satisfyAmount) 
    {
        this.satisfyAmount = satisfyAmount;
    }

    public BigDecimal getSatisfyAmount() 
    {
        return satisfyAmount;
    }
    public void setReduceAmount(BigDecimal reduceAmount) 
    {
        this.reduceAmount = reduceAmount;
    }

    public BigDecimal getReduceAmount() 
    {
        return reduceAmount;
    }
    public void setBeginTime(Date beginTime) 
    {
        this.beginTime = beginTime;
    }

    public Date getBeginTime() 
    {
        return beginTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setSubTitle(String subTitle) 
    {
        this.subTitle = subTitle;
    }

    public String getSubTitle() 
    {
        return subTitle;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("coverUrl", getCoverUrl())
            .append("courseName", getCourseName())
            .append("learnCount", getLearnCount())
            .append("saleCount", getSaleCount())
            .append("type", getType())
            .append("discount", getDiscount())
            .append("price", getPrice())
            .append("satisfyAmount", getSatisfyAmount())
            .append("reduceAmount", getReduceAmount())
            .append("beginTime", getBeginTime())
            .append("endTime", getEndTime())
            .append("subTitle", getSubTitle())
            .append("courseId", getCourseId())
            .toString();
    }
}
