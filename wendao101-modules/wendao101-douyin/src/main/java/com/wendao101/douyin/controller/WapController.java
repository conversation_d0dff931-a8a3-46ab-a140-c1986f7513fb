package com.wendao101.douyin.controller;

import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageInfo;
import com.tencentcloudapi.captcha.v20190722.CaptchaClient;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultRequest;
import com.tencentcloudapi.captcha.v20190722.models.DescribeCaptchaResultResponse;
import com.tencentcloudapi.common.AbstractModel;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.wendao101.common.core.constant.WendaoRedisKey;
import com.wendao101.common.core.kspaydto.CreateKuaishouPreOrderDTO;
import com.wendao101.common.core.kspaydto.CreateWendaoOrderDTO;
import com.wendao101.common.core.kspaydto.CreateWendaoOrderGiveManyOrderDTO;
import com.wendao101.common.core.ktdto.CourseInfoDTO;
import com.wendao101.common.core.ktdto.GiveCourseUserChooseDTO;
import com.wendao101.common.core.utils.ip.IpUtils;
import com.wendao101.common.core.utils.uuid.IdUtils;
import com.wendao101.common.core.visitdata.VisitDataDTO;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.PageDomain;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.core.web.page.TableSupport;
import com.wendao101.common.core.wendao.CourseAuditDTO;
import com.wendao101.common.core.wendao.HotTeacherDTO;
import com.wendao101.common.core.wendao.RequestHotTrySeeDTO;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.feign.WxKuaishouOrderService;
import com.wendao101.douyin.domain.CdCourseOrder;
import com.wendao101.douyin.domain.DyMiniClass;
import com.wendao101.douyin.domain.LearningRecord;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.dto.*;
import com.wendao101.douyin.mapper.LearningRecordMapper;
import com.wendao101.douyin.plusmapper.*;
import com.wendao101.douyin.service.*;
import com.wendao101.douyin.vo.*;
import com.wendao101.order.domain.SecondKill;
import com.wendao101.teacher.domain.*;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/wap")
@Slf4j
public class WapController extends BaseController {

    // 云片API密钥
    private static final String APIKEY = "102222afce402a62dd7b44276e736fff";
    private static final String ENCODING = "UTF-8";
    private static final String API_URL = "https://sms.yunpian.com/v2/sms/tpl_single_send.json";

    private static final String WX_PAY_UUID = "WX_PAY_UUID:";
    @Autowired
    AliyunSmsService aliyunSmsService;

    @Autowired
    CourseDyMapperPlus courseDyMapper;
    @Autowired
    TeacherMapperPlus teacherMapper;
    @Autowired
    ChapterDyMapperPlus chapterDyMapper;
    @Autowired
    CourseDirectoryDyMapperPlus courseDirectoryDyMapper;
    @Autowired
    MDiscountsMapperPlus mDiscountsMapper;
    @Autowired
    CourseOrderMapperPlus courseOrderMapper;
    @Autowired
    CourseInAuditMapperPlus courseInAuditMapper;
    @Autowired
    CourseTwContentMapperPlus courseTwContentMapper;
    @Autowired
    ChapterInAuditMapperPlus chapterInAuditMapper;
    @Autowired
    CourseDirectoryInAuditMapperPlus courseDirectoryInAuditMapper;
    @Autowired
    IClockInQuestService clockInQuestService;
    @Autowired
    IMDiscountsService imDiscountsService;
    @Autowired
    RedisService redisService;
    @Autowired
    private MGiveEntityMapperPlus mGiveEntityMapperPlus;
    @Autowired
    private LearningRecordMapper learningRecordMapper;
    @Autowired
    private StudyDataMapperPlus studyDataMapperPlus;
    @Autowired
    private LearningMaterialsMapperPlus learningMaterialsMapperPlus;
    @Autowired
    private ShippingAddressMapperPlus shippingAddressMapperPlus;
    @Autowired
    private ICourseOrderService courseOrderService;
    @Autowired
    private ISecondKillService secondKillService;
    private static final String COURSE_DETAIL = "course_detail_front:";
    private static final Long captchaAppId = 192824652L;
    private static final String appSecretKey = "DcFo2A7miP6veg4jhHwkOwVaC";
    private static final Long captchaType = 9L;
    @Autowired
    private IWendaoUserService wendaoUserService;
    @Autowired
    private WxMpService wxService;
    private final static String WX_REDIS_CODE_KEY = "WX_REDIS_CODE_KEY:";
    public final static String WX_WAP_STORE_USER_TOKEN_KEY = "WX_WAP_STORE_USER_TOKEN_KEY:";

    @Autowired
    private ILearningRecordService learningRecordService;

    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Value("${wendao.cos.region}")
    private String region;
    @Value("${wendao.cos.outputBucket}")
    private String outputBucket;


    @Autowired
    private KnowledgeStoreDomainService knowledgeStoreDomainService;

    @Autowired
    private IWendaoLiveService wendaoLiveService;

    @Autowired
    private WxKuaishouOrderService wxkuaishouOrderService;

//    @Autowired
//    AvChatRoomService avChatRoomService;
    @Autowired
    IWendaoLiveRoomService wendaoLiveRoomService;

    @Resource
    private ICourseDyService courseDyService;

    @Autowired
    private ICdCourseOrderService cdCourseOrderService;

    private static final String home_page_course_list_for_wap = "home_page_course_list_for_wap:";
    private static final String home_page_hot_sale_course_list_for_wap = "home_page_hot_sale_course_list_for_wap:";

    private static final String home_page_hot_teacher_list_for_wap = "home_page_hot_teacher_list_for_wap:";

    private static final String home_page_new_course_list_for_wap = "home_page_new_course_list_for_wap:";

    private static final Integer PLATFORM = 5;
    @Autowired
    private IDyMiniClassService dyMiniClassService;
    private static final String wx_wap_ticket_temp = "wx_wap_ticket_temp:";
    private static final String wx_wap_returnUrl_temp = "wx_wap_returnUrl_temp:";

    private static final String course_visit_record_list = "course_visit_record_list:";
    private static final String course_Have_visit_record_list = "course_Have_visit_record_list:";
    public static final String FRONT_SECOND_KILL_REDIS_KEY = "FRONT_SECOND_KILL_REDIS_KEY:";

    private static final String give_order_user_choose_course_list = "give_order_user_choose_course_list:";

    @Autowired
    private WxKuaishouOrderService wxKuaishouOrderService;

    @Autowired
    QueryMediaPlaySignService queryMediaPlaySignService;

    /**
     * 查询课程基本信息
     */
    @GetMapping("/course/info")
    public AjaxResult courseInfo(@RequestParam("courseId")Long courseId, HttpServletRequest request) {
        QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
        sectionQueryWrapper.eq("id", courseId);
        CourseDy courseDy = courseDyMapper.selectOne(sectionQueryWrapper, false);
        if (courseDy == null) {
            return AjaxResult.error("课程不存在!");
        }
        return success(courseDy);
    }

    @PostMapping("/queryClockWorkPlayUrl")
    public AjaxResult queryClockWorkPlayUrl(@RequestBody MiniAppClockWorkQueryPlayUrlVO miniAppClockWorkQueryPlayUrlVO) {
        return queryMediaPlaySignService.queryPlayUrlForClockWork(miniAppClockWorkQueryPlayUrlVO);
    }

    /**
     * 查询播放链接
     *
     * @param wapKnowledgeQueryPlayUrlVO
     * @return
     */
    @PostMapping("/shield/queryPlayUrl")
    public AjaxResult shieldQueryPlayUrl(@RequestBody WapKnowledgeQueryPlayUrlVO wapKnowledgeQueryPlayUrlVO,HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser != null ) {
            wapKnowledgeQueryPlayUrlVO.setUserId(currentUser.getId());
        }
        return queryMediaPlaySignService.queryPlayUrlForWap(wapKnowledgeQueryPlayUrlVO);
    }

    /**
     * 买课赠课选择课程
     *
     * @return
     */
    @GetMapping("/choose_my_give_course/{ids}")
    public AjaxResult chooseMyGiveCourse(@PathVariable("ids") Long[] ids, @RequestParam("orderId") String orderId, HttpServletRequest request) {
        if (ids == null || ids.length == 0) {
            return AjaxResult.error("ids未传值");
        }
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null ) {
            return new AjaxResult(401, "未登录");
        }
//        DYWenDaoUserDTO user = null;
//        if (com.wendao101.common.core.utils.StringUtils.isNotBlank(openId) && appNameType != null) {
//            user = idyWenDaoUserService.getWenDaoUserByOpenIdAndAppNameType(openId, appNameType);
//        }
//        if (user == null && currentUser == null) {
//            return AjaxResult.error("用户不存在!");
//        }
//        Long userId = null;
//        if (currentUser != null) {
//            userId = currentUser.getId();
//        } else {
//            userId = user.getId();
//        }
//        CourseOrder courseOrder = new CourseOrder();
//        courseOrder.setOrderId(orderId);
//        courseOrder.setBuyerUserId(user.getId());
//        courseOrder.setOrderPlatform(platform);
//        List<CourseOrder> courseOrders = courseOrderService.selectCourseOrderList(courseOrder);
//        if (CollectionUtils.isEmpty(courseOrders)) {
//            return AjaxResult.error("订单不存在或权限错误或平台错误!");
//        }
//        CourseOrder courseOrder1 = courseOrders.get(0);
//        GiveCourseUserChooseDTO giveCourseUserChoose = redisService.getCacheObject(give_order_user_choose_course_list + courseOrder1.getOrderId());
        GiveCourseUserChooseDTO giveCourseUserChoose = redisService.getCacheObject(give_order_user_choose_course_list + orderId);
        if (giveCourseUserChoose == null) {
            return AjaxResult.error("已领取或赠课已取消!");
        }
        List<com.wendao101.common.core.ktdto.CourseInfoDTO> courseInfoList = giveCourseUserChoose.getCourseInfoList();
        if (CollectionUtils.isEmpty(courseInfoList)) {
            return AjaxResult.error("符合条件的课程列表为空!");
        }
        List<Long> collect = courseInfoList.stream().map(CourseInfoDTO::getId).collect(Collectors.toList());
        List<Long> result = new ArrayList<>();
        for (Long item : ids) {
            if (collect.contains(item)) {
                result.add(item);
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            return AjaxResult.error("没有符合你选择的课程!");
        }
        CreateWendaoOrderGiveManyOrderDTO createWendaoOrderGiveManyOrderDTO = new CreateWendaoOrderGiveManyOrderDTO();
        createWendaoOrderGiveManyOrderDTO.setUserId(currentUser.getId());
        createWendaoOrderGiveManyOrderDTO.setCourseIds(result);
        createWendaoOrderGiveManyOrderDTO.setPlatform(PLATFORM);
        //createWendaoOrderGiveManyOrderDTO.setPlatform(courseOrder1.getOrderPlatform());
        //createWendaoOrderGiveManyOrderDTO.setAppNameType(appNameType);
        createWendaoOrderGiveManyOrderDTO.setPrimaryOrderId(orderId);
        AjaxResult resultAjaxResult = wxKuaishouOrderService.createWendaoOrderGiveAwayManyOrder(createWendaoOrderGiveManyOrderDTO);
        //领取成功删除缓存
        //redisService.deleteObject(give_order_user_choose_course_list + courseOrder1.getOrderId());
        redisService.deleteObject(give_order_user_choose_course_list + orderId);
        return resultAjaxResult;
    }

    /**
     * 买课赠课选课列表
     *
     * @return
     */
    @GetMapping("/get_choose_course_list")
    public AjaxResult getChooseCourseList(@RequestParam("orderId") String orderId, HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null ) {
            return new AjaxResult(401, "未登录");
        }
        GiveCourseUserChooseDTO giveCourseUserChoose = redisService.getCacheObject(give_order_user_choose_course_list + orderId);
        if (giveCourseUserChoose == null) {
            return AjaxResult.error("已领取或赠课已取消!");
        }
        return AjaxResult.success(giveCourseUserChoose);
    }

    @GetMapping("/secondKill/list")
    public TableDataInfo list(SecondKillDTOFront secondKillDTOFront) {
        PageDomain pageDomain = TableSupport.buildPageRequest();
        Integer pageNum = pageDomain.getPageNum();
        Integer pageSize = pageDomain.getPageSize();
        if (pageSize > 50) {
            //不能大于10
            pageSize = 10;
        }
        String keySuffix = "secondKillList_" + pageNum + "_" + pageSize + "_" + secondKillDTOFront.getPlatform() + "_" + (secondKillDTOFront.getAppNameType() == null ? "all" : secondKillDTOFront.getAppNameType());
        //查询缓存
        TableDataInfo dataTable0 = redisService.getCacheObject(FRONT_SECOND_KILL_REDIS_KEY + keySuffix);
        if (dataTable0 != null && CollectionUtils.isNotEmpty(dataTable0.getRows())) {
            List<SecondKillDTOFront> list0 = (List<SecondKillDTOFront>) dataTable0.getRows();
            List<SecondKillDTOFront> listFront = getSecondKillDTOFronts(list0);
            dataTable0.setRows(listFront);
            return dataTable0;
        }
        Integer killStatus = 0;
        Date now = new Date();
        startPage();
        List<SecondKillDTOFront> list = secondKillService.selectSecondKillListForFront(killStatus, now, secondKillDTOFront.getPlatform(), secondKillDTOFront.getAppNameType());
        TableDataInfo dataTable = getDataTable(list);
        redisService.setCacheObject(FRONT_SECOND_KILL_REDIS_KEY + keySuffix, dataTable, 300L, TimeUnit.SECONDS);
        if (dataTable != null && CollectionUtils.isNotEmpty(dataTable.getRows())) {
            List<SecondKillDTOFront> list0 = (List<SecondKillDTOFront>) dataTable.getRows();
            List<SecondKillDTOFront> listFront = getSecondKillDTOFronts(list0);
            dataTable.setRows(listFront);
            return dataTable;
        } else {
            dataTable.setRows(Collections.emptyList());
            return dataTable;
        }
    }

    @NotNull
    private List<SecondKillDTOFront> getSecondKillDTOFronts(List<SecondKillDTOFront> list) {
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        for (SecondKillDTOFront secondKillDTOFront : list) {
            SecondKill secondKill = secondKillService.selectValidRecordFromRedis(secondKillDTOFront.getCourseId());
            if (secondKill != null) {
                //实时秒杀数量
                String keySecNumber = "wendao_sec_kill:" + secondKill.getId();
                Integer number = redisService.getCacheObject(keySecNumber);
                if (number != null) {
                    if (number > secondKill.getSeckillNum()) {
                        number = secondKill.getSeckillNum();
                    }
                    secondKillDTOFront.setSecondKillLeftNum(Math.max(secondKill.getSeckillNum() - number, 0));
                    BigDecimal minProportion = secondKill.getMinProportion();
                    BigDecimal maxProportion = secondKill.getMaxProportion();
                    Integer num = secondKill.getSeckillNum();
                    BigDecimal multiply = (maxProportion.subtract(minProportion)).multiply((new BigDecimal(number).divide(new BigDecimal(num), 2, RoundingMode.HALF_UP)));
                    BigDecimal bigDecimal = multiply.setScale(2, RoundingMode.HALF_UP);
                    secondKillDTOFront.setCurrentPercet(minProportion.add(bigDecimal));
                } else {
                    secondKillDTOFront.setSecondKillLeftNum(secondKill.getSeckillNum());
                    secondKillDTOFront.setCurrentPercet(secondKill.getMinProportion());
                }
            }

        }
        return list;
    }

    /**
     * 获取授权页面地址
     *
     * @param returnUrl 登录后返回的地址
     * @return
     */
    @GetMapping("/wx/buildWxAuthUrl")
    public AjaxResult buildWxAuthUrl(@RequestParam(value = "returnUrl") String returnUrl,@RequestParam(value = "silentLogin",required = false,defaultValue = "false") boolean silentLogin) {
        String loginUrl = "https://wap.wendao101.com/#/pages/h5_login/h5_login";
        //进行urlEncode
        try {
            loginUrl = URLEncoder.encode(loginUrl, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        String uuid = UUID.randomUUID().toString().replace("-", "");
        if(silentLogin){
            uuid = "silentLogin_"+UUID.randomUUID().toString().replace("-", "");
        }
        redisService.setCacheObject(wx_wap_returnUrl_temp + uuid, returnUrl, 60L * 60L, TimeUnit.SECONDS);
        //问到课堂公众号
        String appid = "wx53bd355db2a7f7ac";
        wxService.switchover(appid);
        String redirectUrl = "https://wap.wendao101.com/wap/wx/" + appid + "/loginByCode?uuid=%s&returnUrl=%s";
        redirectUrl = String.format(redirectUrl, uuid, loginUrl);
        //修改为静默授权
        String url = "";
        if (silentLogin) {
            url = wxService.getOAuth2Service().buildAuthorizationUrl(redirectUrl, WxConsts.OAuth2Scope.SNSAPI_BASE, "STATE");
        } else {
            url = wxService.getOAuth2Service().buildAuthorizationUrl(redirectUrl, WxConsts.OAuth2Scope.SNSAPI_USERINFO, "STATE");
        }
        return AjaxResult.success("success", url);
    }

    /**
     * 微信授权登录
     * //https://wxapi.shop.wendao101.com/?code=001A1pHa1XQllH0YyMFa1va09Z2A1pHo&state=STATE#/
     *
     * @return
     */
    @GetMapping("/wx/{appid}/loginByCode")
    public void loginByCode(@RequestParam("uuid") String uuid, @PathVariable("appid") String appid, @RequestParam(value = "code") String code, @RequestParam(value = "returnUrl") String returnUrl, HttpServletResponse response) {
        wxService.switchover(appid);
        try {
            WxOAuth2AccessToken accessToken = wxService.getOAuth2Service().getAccessToken(code);

            WxOAuth2UserInfo userInfo = null;
            if(uuid.startsWith("silentLogin_")){
                //如果是静默授权
                userInfo = new WxOAuth2UserInfo();
                userInfo.setOpenid(accessToken.getOpenId());
            }else{
                //非静默授权
                userInfo = wxService.getOAuth2Service().getUserInfo(accessToken, code);
            }
            System.out.println("获取的用户信息:" + JSON.toJSONString(userInfo));
            //生成ticket
            redisService.setCacheObject(wx_wap_ticket_temp + uuid, userInfo, 60L * 60L * 24L, TimeUnit.SECONDS);
            try {
                returnUrl = URLDecoder.decode(returnUrl, "utf-8");
                if(StringUtils.isNotBlank(returnUrl)&&returnUrl.contains("?")){
                    if(returnUrl.endsWith("&")){
                        returnUrl = returnUrl + "wxLoginInInfoKey=" + uuid;
                    }else{
                        returnUrl = returnUrl + "&wxLoginInInfoKey=" + uuid;
                    }
                }else{
                    returnUrl = returnUrl + "?wxLoginInInfoKey=" + uuid;
                }
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            response.setStatus(HttpServletResponse.SC_MOVED_TEMPORARILY);
            response.setHeader("Location", returnUrl);
        } catch (WxErrorException e) {
            System.out.println(e.getError().getErrorMsg());
        }
    }


    @PostMapping("/createWxWapOrderInWeiXin")
    public AjaxResult createWxWapOrderInWeiXin(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        createKuaishouPreOrderDTO.setUserId(currentUser.getId());
        createKuaishouPreOrderDTO.setOpenid(currentUser.getOpenId());
        return wxkuaishouOrderService.createWxWapOrderInWeiXin(createKuaishouPreOrderDTO);
    }

    @PostMapping("/createWxWapOrder")
    public AjaxResult createWxWapOrder(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request) {
        WendaoUser currentUser = WapController.getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        createKuaishouPreOrderDTO.setUserId(currentUser.getId());
        return wxkuaishouOrderService.createWxWapOrder(createKuaishouPreOrderDTO);
    }

    /**
     * 解决知识店铺微信中无法支付问题
     * @param openid
     * @param orderId
     * @param returnUrl
     * @param response
     * @throws IOException
     */
    @GetMapping("/goToPayInWeiXin")
    public void goToPayInWeiXin(@RequestParam("openid") String openid, @RequestParam("orderId") String orderId, @RequestParam("returnUrl") String returnUrl, HttpServletResponse response) throws IOException {
        String template = "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\n" +
                "    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n" +
                "    <title>微信支付</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "            display: flex;\n" +
                "            justify-content: center;\n" +
                "            align-items: center;\n" +
                "            min-height: 100vh;\n" +
                "            background-color: #f5f5f5;\n" +
                "            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <!-- 引入微信JS SDK -->\n" +
                "    <script src=\"https://res.wx.qq.com/open/js/jweixin-1.6.0.js\"></script>\n" +
                "    <script>\n" +
                "        function onBridgeReady() {\n" +
                "            WeixinJSBridge.invoke('getBrandWCPayRequest', {\n" +
                "                \"appId\": \"#{appId}\",\n" +
                "                \"timeStamp\": \"#{timeStamp}\",\n" +
                "                \"nonceStr\": \"#{nonceStr}\",\n" +
                "                \"package\": \"#{package}\",\n" +
                "                \"signType\": \"#{signType}\",\n" +
                "                \"paySign\": \"#{paySign}\"\n" +
                "            },\n" +
                "                function (res) {\n" +
                "                    if (res.err_msg == \"get_brand_wcpay_request:ok\") {\n" +
                "                        //alert(\"支付成功！\");\n" +
                "                        window.location.href = \"#{redirectUrl}\";\n" +
                "                    } else if (res.err_msg == \"get_brand_wcpay_request:cancel\") {\n" +
                "                        //alert(\"支付已取消\");\n" +
                "                        window.location.href = \"#{redirectUrl}\";\n" +
                "                    } else {\n" +
                "                        //alert(\"支付失败：\" + res.err_msg);\n" +
                "                        window.location.href = \"#{redirectUrl}\";\n" +
                "                    }\n" +
                "                });\n" +
                "        }\n" +
                "\n" +
                "        // 检查WeixinJSBridge是否就绪\n" +
                "        if (typeof WeixinJSBridge == \"undefined\") {\n" +
                "            if (document.addEventListener) {\n" +
                "                document.addEventListener('WeixinJSBridgeReady', onBridgeReady, false);\n" +
                "            } else if (document.attachEvent) {\n" +
                "                document.attachEvent('WeixinJSBridgeReady', onBridgeReady);\n" +
                "                document.attachEvent('onWeixinJSBridgeReady', onBridgeReady);\n" +
                "            }\n" +
                "        } else {\n" +
                "            onBridgeReady();\n" +
                "        }\n" +
                "    </script>\n" +
                "</body>\n" +
                "</html>";
        com.wendao101.douyin.domain.CourseOrder currentOrder = courseOrderService.selectCourseOrderByOrderId2(orderId);
        CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO = new CreateKuaishouPreOrderDTO();
        createKuaishouPreOrderDTO.setOrderId(orderId);
        createKuaishouPreOrderDTO.setUserId(currentOrder.getBuyerUserId());
        createKuaishouPreOrderDTO.setOpenid(openid);
        AjaxResult wxWapOrderInWeiXin = wxkuaishouOrderService.createWxWapOrderInWeiXin(createKuaishouPreOrderDTO);
        if (wxWapOrderInWeiXin.isSuccess()) {
            LinkedHashMap<String,String> map = (LinkedHashMap<String,String>) wxWapOrderInWeiXin.get("data");
            String appId = map.get("appId");
            String timeStamp = map.get("timeStamp");
            String nonceStr = map.get("nonceStr");
            String packageValue = map.get("packageValue");
            String signType = map.get("signType");
            String paySign = map.get("paySign");
//            try {
//                returnUrl = URLDecoder.decode(returnUrl, "utf-8");
//            } catch (UnsupportedEncodingException e) {
//                throw new RuntimeException(e);
//            }
            template = template.replace("#{appId}", appId);
            template = template.replace("#{timeStamp}", timeStamp);
            template = template.replace("#{nonceStr}", nonceStr);
            template = template.replace("#{package}", packageValue);
            template = template.replace("#{signType}", signType);
            template = template.replace("#{paySign}", paySign);
            template = template.replace("#{redirectUrl}", returnUrl);
            response.setContentType("text/html;charset=utf-8");
            response.getWriter().write(template);
        }
    }


    @GetMapping("/goTo")
    public void goTo(@RequestParam("uuid")String uuid,HttpServletResponse response) throws IOException {
        String template = "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\n" +
                "    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n" +
                "    <meta http-equiv=\"Refresh\" content=\"0;url=#{redirectUrl}\">\n" +
                "    <title>正在跳转...</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "            display: flex;\n" +
                "            justify-content: center;\n" +
                "            align-items: center;\n" +
                "            min-height: 100vh;\n" +
                "            background-color: #f5f5f5;\n" +
                "            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n" +
                "        }\n" +
                "        .loading {\n" +
                "            text-align: center;\n" +
                "            color: #666;\n" +
                "            font-size: 16px;\n" +
                "        }\n" +
                "        .dots {\n" +
                "            display: inline-block;\n" +
                "            width: 50px;\n" +
                "            text-align: left;\n" +
                "        }\n" +
                "        @keyframes dot {\n" +
                "            0% { content: ''; }\n" +
                "            25% { content: '.'; }\n" +
                "            50% { content: '..'; }\n" +
                "            75% { content: '...'; }\n" +
                "            100% { content: ''; }\n" +
                "        }\n" +
                "        .dots::after {\n" +
                "            content: '';\n" +
                "            animation: dot 2s infinite steps(4);\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"loading\">\n" +
                "        正在跳转<span class=\"dots\"></span>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
        String url = redisService.getCacheObject(WX_PAY_UUID + uuid);
        url = url.replace("&","&amp;");
        String content = template.replace("#{redirectUrl}", url);
        response.setContentType("text/html;charset=utf-8");
        response.getWriter().write(content);
    }


    /**
     * 解决知识店铺无法支付的问题
     * @param orderId
     * @param returnUrl
     * @param response
     * @throws IOException
     */
    @GetMapping("/goToPay")
    public void goToPay(@RequestParam("orderId")String orderId,@RequestParam("returnUrl")String returnUrl,HttpServletResponse response) throws IOException {
        String template = "<!DOCTYPE html>\n" +
                "<html lang=\"zh-CN\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no\">\n" +
                "    <meta http-equiv=\"X-UA-Compatible\" content=\"ie=edge\">\n" +
                "    <meta http-equiv=\"Refresh\" content=\"0;url=#{redirectUrl}\">\n" +
                "    <title>正在跳转...</title>\n" +
                "    <style>\n" +
                "        body {\n" +
                "            margin: 0;\n" +
                "            padding: 0;\n" +
                "            display: flex;\n" +
                "            justify-content: center;\n" +
                "            align-items: center;\n" +
                "            min-height: 100vh;\n" +
                "            background-color: #f5f5f5;\n" +
                "            font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Arial, sans-serif;\n" +
                "        }\n" +
                "        .loading {\n" +
                "            text-align: center;\n" +
                "            color: #666;\n" +
                "            font-size: 16px;\n" +
                "        }\n" +
                "        .dots {\n" +
                "            display: inline-block;\n" +
                "            width: 50px;\n" +
                "            text-align: left;\n" +
                "        }\n" +
                "        @keyframes dot {\n" +
                "            0% { content: ''; }\n" +
                "            25% { content: '.'; }\n" +
                "            50% { content: '..'; }\n" +
                "            75% { content: '...'; }\n" +
                "            100% { content: ''; }\n" +
                "        }\n" +
                "        .dots::after {\n" +
                "            content: '';\n" +
                "            animation: dot 2s infinite steps(4);\n" +
                "        }\n" +
                "    </style>\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"loading\">\n" +
                "        正在跳转<span class=\"dots\"></span>\n" +
                "    </div>\n" +
                "</body>\n" +
                "</html>";
        com.wendao101.douyin.domain.CourseOrder currentOrder = courseOrderService.selectCourseOrderByOrderId2(orderId);
        CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO = new CreateKuaishouPreOrderDTO();
        createKuaishouPreOrderDTO.setOrderId(orderId);
        createKuaishouPreOrderDTO.setUserId(currentOrder.getBuyerUserId());
        AjaxResult wxWapOrder = wxkuaishouOrderService.createWxWapOrder(createKuaishouPreOrderDTO);
        String payUrl = "";
        if(wxWapOrder.isSuccess()){
            payUrl = (String)wxWapOrder.get("data");
            payUrl = payUrl.replace("&","&amp;");
            //把returnUrl存起来:returnUrl,returnUrl是被urlDecode过的
            String uuid = IdUtils.fastUUID() ;
            redisService.setCacheObject(WX_PAY_UUID+uuid, returnUrl, 2L, TimeUnit.DAYS);
            String redirectUrl2 = "https://wap.wendao101.com/wap/goTo?uuid="+uuid;
            redirectUrl2 = URLEncoder.encode(redirectUrl2, "utf-8");
            payUrl = payUrl+"&amp;redirect_url="+redirectUrl2;
        }
        String content = template.replace("#{redirectUrl}", payUrl);
        response.setContentType("text/html;charset=utf-8");
        response.getWriter().write(content);
    }

    /**
     * 查询抖音小程序上的分类列表
     */
    @PostMapping("/mini_index/category")
    public AjaxResult category() {
        List<DyMiniClass> list = dyMiniClassService.selectDyMiniClassList(null);
        return AjaxResult.success(list);
    }

    /**
     * 获取服务器时间
     *
     * @return
     */
    @PostMapping("/get_server_time")
    public AjaxResult getServerTime() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = new Date();
        Map<String, String> serverTime = new HashMap<>();
        serverTime.put("serverTime", sdf.format(date));
        return AjaxResult.success(serverTime);
    }

    /**
     * 热门试看
     *
     * @return
     */
    @PostMapping("/hotTrySeeList")
    public AjaxResult hotTrySeeList() {
        List<CourseAuditDTO> cacheList = getCourseListByHomepageType(home_page_course_list_for_wap);
        return AjaxResult.success(cacheList);
    }

    /**
     * 热卖
     *
     * @return
     */
    @PostMapping("/hotSaleList")
    public AjaxResult hotSaleList() {
        List<CourseAuditDTO> cacheList = getCourseListByHomepageType(home_page_hot_sale_course_list_for_wap);
        //取0-5
        return AjaxResult.success(cacheList.size() >= 5 ? cacheList.subList(0, 5) : cacheList);
    }

    /**
     * 推荐
     *
     * @return
     */
    @PostMapping("/recommendList")
    public AjaxResult recommendList() {
        List<CourseAuditDTO> cacheList = getCourseListByHomepageType(home_page_hot_sale_course_list_for_wap);
        //取5-10
        return AjaxResult.success(cacheList.size() > 5 ? cacheList.subList(5, cacheList.size()) : new ArrayList<>());
    }

    /**
     * 热门老师
     *
     * @return
     */
    @PostMapping("/hotTeacherList")
    public AjaxResult hotTeacherList() {
        List<HotTeacherDTO> cacheList = getCourseListByHomepageType(home_page_hot_teacher_list_for_wap);
        return AjaxResult.success(cacheList);
    }

    /**
     * 新课
     *
     * @param requestHotTrySeeDTO
     * @return
     */
    @PostMapping("/newCourseList")
    public TableDataInfo newCourseList(@RequestBody RequestHotTrySeeDTO requestHotTrySeeDTO) {
        if (requestHotTrySeeDTO.getPageNum() == null || requestHotTrySeeDTO.getPageSize() == null) {
            return TableDataInfo.error("参数错误");
        }
        if (requestHotTrySeeDTO.getPageNum() < 1) {
            return TableDataInfo.error("参数错误,pageNum从1开始");
        }
        List<CourseAuditDTO> cacheList = getCourseListByHomepageType(home_page_new_course_list_for_wap);
        if (requestHotTrySeeDTO.getPageNum() * requestHotTrySeeDTO.getPageSize() > cacheList.size()) {
            return new TableDataInfo(new ArrayList<>(), cacheList.size());
        }
        return new TableDataInfo(cacheList.subList((requestHotTrySeeDTO.getPageNum() - 1) * requestHotTrySeeDTO.getPageSize(), requestHotTrySeeDTO.getPageNum() * requestHotTrySeeDTO.getPageSize()), cacheList.size());
    }

    private <T> List<T> getCourseListByHomepageType(String cacheKeyPrefix) {
        int dayOfMonth = Calendar.getInstance().get(Calendar.DAY_OF_MONTH);
        int dayOfMonthMod = dayOfMonth % 2;
        String key = cacheKeyPrefix + dayOfMonthMod;
        List<T> cacheList = redisService.getCacheList(key);
        if (org.springframework.util.CollectionUtils.isEmpty(cacheList)) {
            if (dayOfMonthMod == 0) {
                dayOfMonthMod = 1;
            } else {
                dayOfMonthMod = 0;
            }
            key = cacheKeyPrefix + dayOfMonthMod;
            cacheList = redisService.getCacheList(key);
        }
        return cacheList == null ? new ArrayList<>() : cacheList;
    }

    /**
     * wap端首页根据分类查询课程
     *
     * @param courseDyVO
     * @return
     */
    @PostMapping("/course_list")
    public AjaxResult selectCourseList(@RequestBody CourseDyVO courseDyVO) {
        return success(courseDyService.selectCourseListForPc(courseDyVO));
    }

    /**
     * 初始化加密滑动验证码数据
     *
     * @return
     */
    @GetMapping("/t_captcha/init")
    public AjaxResult captchaInit() {
        int remainder = 32 % appSecretKey.length();
        String key = appSecretKey + appSecretKey.substring(0, remainder);
        long curTime = new Date().getTime() / 1000;
        long expireTime = 86400L;
        String plaintext = captchaAppId + "&" + curTime + "&" + expireTime;
        String iv = RandomStringUtils.randomNumeric(16);
        String ciphertext = null;
        try {
            ciphertext = encrypt(plaintext, key, iv);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        String captchaAppidEncrypted = ciphertext;
        return AjaxResult.success("success", captchaAppidEncrypted);
    }

    /**
     * 滑块验证校验,暂时用不到
     *
     * @param ticket
     * @param randomStr
     * @param request
     * @return
     */
    @Deprecated
    @GetMapping("/t_captcha/checkCaptcha")
    public AjaxResult checkCaptcha(@RequestParam(value = "ticket") String ticket, @RequestParam(value = "randomStr") String randomStr, HttpServletRequest request) {
        String userIp = IpUtils.getIpAddr(request);
        try {
            Credential cred = new Credential("AKIDeFj6Vny8HLXf7cBjHmXEvgLtKcmsqdps", "73pQs38B8xiLrbK83Sr9KKJAqW5ylmp9");
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("captcha.tencentcloudapi.com");
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            CaptchaClient client = new CaptchaClient(cred, "", clientProfile);
            DescribeCaptchaResultRequest req = new DescribeCaptchaResultRequest();
            req.setCaptchaType(captchaType);
            req.setTicket(ticket);
            req.setUserIp(userIp);
            req.setRandstr(randomStr);
            req.setCaptchaAppId(captchaAppId);
            req.setAppSecretKey(appSecretKey);
            req.setNeedGetCaptchaTime(1L);
            DescribeCaptchaResultResponse resp = client.DescribeCaptchaResult(req);
            System.out.println(AbstractModel.toJsonString(resp));
            Long captchaCode = resp.getCaptchaCode();
            if (captchaCode.intValue() == 1) {
                return AjaxResult.success("success");
            } else {
                return AjaxResult.error(StringUtils.isNotBlank(resp.getCaptchaMsg()) ? resp.getCaptchaMsg() : "验证失败!");
            }
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
            return AjaxResult.error("验证失败!原因:" + e.getMessage());
        }
    }

    /**
     * 验证滑块并发送验证码
     *
     * @param phoneNumber
     * @param ticket
     * @param randomStr
     * @param request
     * @return
     */
    @GetMapping("/sms/sendSms")
    public AjaxResult sendSms(@RequestParam(value = "phoneNumber") String phoneNumber, @RequestParam(value = "ticket") String ticket, @RequestParam(value = "randomStr") String randomStr, HttpServletRequest request) {
        //发送短信验证码
        AjaxResult ajaxResult = this.checkCaptcha(ticket, randomStr, request);
        if (ajaxResult.isSuccess()) {
            //发送短信
            String code = RandomStringUtils.randomNumeric(6);
            String uuid = send(phoneNumber, code);
            //存起来
            //有效期5分钟
            Date expireTime = new Date(System.currentTimeMillis() + 1000 * 60 * 5);
            int row = wendaoUserService.saveSmsRecord(phoneNumber, code, uuid, expireTime);
            if (row <= 0) {
                return AjaxResult.error("短信发送失败!");
            }
            Map<String, String> map = new HashMap<>();
            map.put("uuid", uuid);
            map.put("desc", "有效期为5分钟!");
            return AjaxResult.success("发送短信成功", map);
        } else {
            return ajaxResult;
        }
    }

    /**
     * 验证用户输入的验证码
     * 暂时只有问到课堂
     * pc端用户不分好课课堂!只根据课程来
     * platform 9为pc平台
     *
     * @param phoneNumber
     * @param code
     * @param uuid
     * @return
     */
    @GetMapping("/sms/validateSms")
    public AjaxResult validateSms(@RequestParam(name = "wxLoginInInfoKey", required = false) String wxLoginInInfoKey, @RequestParam(value = "phoneNumber") String phoneNumber, @RequestParam(value = "code") String code, @RequestParam(value = "uuid") String uuid, HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        boolean isWxBrowser = com.wendao101.common.core.utils.StringUtils.isNotBlank(userAgent) && userAgent.toLowerCase().contains("micromessenger");
        WxOAuth2UserInfo userInfo = null;
        String frontReturnUrl = null;
        if (isWxBrowser) {
            //必须传ticket
            if (StringUtils.isBlank(wxLoginInInfoKey)) {
                return AjaxResult.error("微信内登录,必须传wxLoginInInfoKey!");
            }
            userInfo = redisService.getCacheObject(wx_wap_ticket_temp + wxLoginInInfoKey);
            frontReturnUrl = redisService.getCacheObject(wx_wap_returnUrl_temp + wxLoginInInfoKey);
            try {
                frontReturnUrl = URLDecoder.decode(frontReturnUrl, StandardCharsets.UTF_8.name());
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
        }

        SmsRecordDTO smsRecordDTO = wendaoUserService.querySmsRecord(phoneNumber, uuid);
        if (smsRecordDTO == null) {
            return AjaxResult.error("未找到发送记录!");
        }
        if (smsRecordDTO.getExpireTime() != null && smsRecordDTO.getExpireTime().before(new Date())) {
            return AjaxResult.error("验证码已过期!");
        }
        if (StringUtils.equals(code, smsRecordDTO.getCode())) {
            // 应该返回个token
            String token = UUID.randomUUID().toString();
            WendaoUser wendaoUser = null;
            //保存用户
            if(userInfo!=null&&StringUtils.isNotBlank(userInfo.getOpenid())){
                wendaoUser = wendaoUserService.selectUserByOpenIdAndPlatorm(userInfo.getOpenid(), PLATFORM);
            }else{
                wendaoUser = wendaoUserService.selectUserByPhoneAndPlatorm(smsRecordDTO.getPhoneNumber(), PLATFORM);
            }
            //WendaoUser wendaoUser = wendaoUserService.selectUserByOpenIdAndPlatorm(userInfo.getOpenid(), PLATFORM);
            //WendaoUser wendaoUser = wendaoUserService.selectUserByPhoneAndPlatorm(smsRecordDTO.getPhoneNumber(), PLATFORM);
            if (wendaoUser == null) {
                wendaoUser = new WendaoUser();
                wendaoUser.setTelNumber(smsRecordDTO.getPhoneNumber());
                //更新openId,unionId
                if (userInfo != null) {
                    wendaoUser.setOpenId(userInfo.getOpenid());
                    wendaoUser.setUnionId(userInfo.getUnionId());
                    //如果wendaoUser.getNickName为"w_"开头，且userInfo.getNickname()不为空则设置为userInfo.getNickname()
                    if (StringUtils.startsWith(wendaoUser.getNickName(), "w_") && StringUtils.isNotBlank(userInfo.getNickname())) {
                        wendaoUser.setNickName(userInfo.getNickname());
                    }
                    wendaoUser.setAvatarUrl(userInfo.getHeadImgUrl());
                }
                wendaoUser.setPlatform(PLATFORM);
                //设置手机号码脱敏的数据为昵称
                String nickName = DesensitizedUtil.mobilePhone(wendaoUser.getTelNumber());
                wendaoUser.setNickName(nickName);
                wendaoUserService.insertWendaoUser(wendaoUser);
                //创建聊天室用户
//                boolean isSuccess =avChatRoomService.createUserForAvChatRoom(wendaoUser);
//                if(!isSuccess){
//                    System.out.println("创建聊天室用户失败!");
//                }
            } else {
                wendaoUser.setTelNumber(smsRecordDTO.getPhoneNumber());
                //更新openId,unionId
                if (userInfo != null) {
                    wendaoUser.setOpenId(userInfo.getOpenid());
                    wendaoUser.setUnionId(userInfo.getUnionId());
                    if (StringUtils.isBlank(wendaoUser.getNickName()) || (StringUtils.startsWith(wendaoUser.getNickName(), "w_") && StringUtils.isNotBlank(userInfo.getNickname()))) {
                        wendaoUser.setNickName(userInfo.getNickname());
                    }
                    if (StringUtils.isBlank(wendaoUser.getNickName()) || StringUtils.startsWith(wendaoUser.getNickName(), "w_")){
                        //设置手机号码脱敏的数据为昵称
                        String nickName = DesensitizedUtil.mobilePhone(wendaoUser.getTelNumber());
                        wendaoUser.setNickName(nickName);
                    }
                    if (StringUtils.isBlank(wendaoUser.getAvatarUrl())) {
                        wendaoUser.setAvatarUrl(userInfo.getHeadImgUrl());
                    }
                    //如果是用户登陆过一个微信且openId存在再登录另一个微信是更新不了的,直接忽略错误
                }
                wendaoUserService.updateIgnoreWendaoUser(wendaoUser);
                //创建聊天室用户
//                boolean isSuccess =avChatRoomService.createUserForAvChatRoom(wendaoUser);
//                if(!isSuccess){
//                    System.out.println("创建聊天室用户失败!");
//                }
            }
            Map<String, Object> result = new HashMap<>();
            result.put("token", token);
            //判断是否是老师自己!
            //带进老师的数据
            if (StringUtils.isNotBlank(wendaoUser.getTelNumber())) {
                //按手机号和老师Id查询老师
                QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("mobile", wendaoUser.getTelNumber());
                List<Teacher> teacherList = teacherMapper.selectList(queryWrapper);
                if(CollectionUtils.isNotEmpty(teacherList)){
                    List<Teacher> teacherList1 = new ArrayList<>();
                    for(Teacher t:teacherList){
                        t.setPassword(null);
                        if(t.getWapLiveOpen()!=null&&t.getWapLiveOpen()==1){
                            teacherList1.add(t);
                        }
                    }
                    if(CollectionUtils.isNotEmpty(teacherList1)){
                        wendaoUser.setTeacherList(teacherList1);
                    }

                }
            }
            result.put("wendaoUser", wendaoUser);
            if (StringUtils.isNotBlank(frontReturnUrl)) {
                result.put("frontReturnUrl", frontReturnUrl);
            }
            redisService.setCacheObject(WX_WAP_STORE_USER_TOKEN_KEY + token, wendaoUser, 730L, TimeUnit.DAYS);
            return AjaxResult.success("验证成功!", result);
        } else {
            return AjaxResult.error("验证码错误!");
        }
    }

    @GetMapping("/wx_silent_login")
    public AjaxResult wxSilentLogin(@RequestParam(name = "wxLoginInInfoKey", required = false) String wxLoginInInfoKey,HttpServletRequest request) {
        String userAgent = request.getHeader("user-agent");
        boolean isWxBrowser = com.wendao101.common.core.utils.StringUtils.isNotBlank(userAgent) && userAgent.toLowerCase().contains("micromessenger");
        if (!isWxBrowser) {
            return AjaxResult.error("非微信内必须手机号码登录！");
        }
        WxOAuth2UserInfo userInfo = redisService.getCacheObject(wx_wap_ticket_temp + wxLoginInInfoKey);
        String frontReturnUrl = redisService.getCacheObject(wx_wap_returnUrl_temp + wxLoginInInfoKey);
        try {
            frontReturnUrl = URLDecoder.decode(frontReturnUrl, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
        if (userInfo == null || StringUtils.isBlank(userInfo.getOpenid())) {
            return AjaxResult.error("微信静默授权失败!");
        }
        // 应该返回个token
        String token = UUID.randomUUID().toString();
        //保存用户
        WendaoUser wendaoUser = wendaoUserService.selectUserByOpenIdAndPlatorm(userInfo.getOpenid(), PLATFORM);
        if (wendaoUser == null) {
            wendaoUser = new WendaoUser();
            //随机生成8个英文字符作为昵称
            wendaoUser.setOpenId(userInfo.getOpenid());
            wendaoUser.setUnionId(userInfo.getUnionId());
            wendaoUser.setNickName(StringUtils.isBlank(wendaoUser.getNickName()) ? "w_"+RandomUtil.randomString(8) : userInfo.getNickname());
            wendaoUser.setAvatarUrl(userInfo.getHeadImgUrl());
            wendaoUser.setPlatform(PLATFORM);
            //设置手机号码脱敏的数据为昵称
            String nickName = DesensitizedUtil.mobilePhone(wendaoUser.getTelNumber());
            wendaoUser.setNickName(StringUtils.isNotBlank(nickName) ? nickName : wendaoUser.getNickName());
            wendaoUserService.insertWendaoUser(wendaoUser);
            //创建聊天室用户
            //boolean isSuccess = avChatRoomService.createUserForAvChatRoom(wendaoUser);
//            if (!isSuccess) {
//                System.out.println("创建聊天室用户失败!");
//            }
        } else {
            //更新openId,unionId
            wendaoUser.setOpenId(userInfo.getOpenid());
            wendaoUser.setUnionId(userInfo.getUnionId());
            if (StringUtils.isBlank(wendaoUser.getNickName())) {
                wendaoUser.setNickName(userInfo.getNickname());
            }
            if (StringUtils.isBlank(wendaoUser.getAvatarUrl())) {
                wendaoUser.setAvatarUrl(userInfo.getHeadImgUrl());
            }
            //如果是用户登陆过一个微信且openId存在再登录另一个微信是更新不了的,直接忽略错误
            wendaoUserService.updateIgnoreWendaoUser(wendaoUser);
            //创建聊天室用户
//            boolean isSuccess = avChatRoomService.createUserForAvChatRoom(wendaoUser);
//            if (!isSuccess) {
//                System.out.println("创建聊天室用户失败!");
//            }
        }
        Map<String, Object> result = new HashMap<>();
        result.put("token", token);
        //判断是否是老师自己!
        //带进老师的数据
        if (StringUtils.isNotBlank(wendaoUser.getTelNumber())) {
            //按手机号和老师Id查询老师
            QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", wendaoUser.getTelNumber());
            List<Teacher> teacherList = teacherMapper.selectList(queryWrapper);
            if (CollectionUtils.isNotEmpty(teacherList)) {
                List<Teacher> teacherList1 = new ArrayList<>();
                for (Teacher t : teacherList) {
                    t.setPassword(null);
                    if (t.getWapLiveOpen() != null && t.getWapLiveOpen() == 1) {
                        teacherList1.add(t);
                    }
                }
                if (CollectionUtils.isNotEmpty(teacherList1)) {
                    wendaoUser.setTeacherList(teacherList1);
                }

            }
        }
        result.put("wendaoUser", wendaoUser);
        if (StringUtils.isNotBlank(frontReturnUrl)) {
            result.put("frontReturnUrl", frontReturnUrl);
        }
        redisService.setCacheObject(WX_WAP_STORE_USER_TOKEN_KEY + token, wendaoUser, 730L, TimeUnit.DAYS);
        return AjaxResult.success("验证成功!", result);
    }

    /**
     * 查询课程详情
     *
     * @param queryCourseDetailDTO
     * @return
     */
    @PostMapping("/course/detail")
    public AjaxResult detail(@RequestBody QueryCourseDetailKnowledgeStoreDTO queryCourseDetailDTO, HttpServletRequest request) {
        WendaoUser currentUser = getCurrentUser(request, redisService);
        if (currentUser == null) {
            currentUser = new WendaoUser();
            currentUser.setId(-1L);
        }
        if(StringUtils.isBlank(currentUser.getTelNumber())){
            currentUser.setTelNumber("99988877765");
        }
        if (queryCourseDetailDTO.getCourseId() == null) {
            return AjaxResult.error("课程id不能为空!");
        }
        Long userId = currentUser.getId();
        Long courseId = queryCourseDetailDTO.getCourseId();
        QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
        sectionQueryWrapper.eq("id", courseId);
        int updatedCourseCount = 0;
        CourseDy courseDy = courseDyMapper.selectOne(sectionQueryWrapper, false);
        if (courseDy == null) {
            return AjaxResult.error("课程不存在!");
        }
        Teacher teacher = teacherMapper.selectById(courseDy.getTeacherId());
        if (teacher != null) {
            //敏感数据置空
            teacher.setPassword(null);
//            teacher.setTeacherName(null);
//            teacher.setMobile(null);
        }
        courseDy.setTeacherInfo(teacher);

        //访问记录
        if (userId.intValue() > 0) {
            Date visitNow = new Date();
            //访问记录每个小时记录一次
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
            String visitTimeString = sdf.format(visitNow);
            Set<Long> courseIdSet = new HashSet<>();
            courseIdSet.add(courseId);
            redisService.setCacheSet(course_Have_visit_record_list + visitTimeString, courseIdSet);
            VisitDataDTO visitDataDTO = new VisitDataDTO();
            visitDataDTO.setCourseId(courseId);
            visitDataDTO.setTeacherId(teacher == null ? -1 : teacher.getTeacherId());
            visitDataDTO.setUserId(userId);
            visitDataDTO.setAppNameType(teacher == null ? -1 : teacher.getAppNameType());
            visitDataDTO.setPlatform(5);
            visitDataDTO.setVisitTime(visitNow);
            List<VisitDataDTO> visitDataDTOList = new ArrayList<>();
            visitDataDTOList.add(visitDataDTO);
            //保存访问记录到redis
            redisService.setCacheList(course_visit_record_list + courseId + "_" + visitTimeString, visitDataDTOList);
        }
        //读取课程缓存数据
        CourseDy courseDyFromRedis = redisService.getCacheObject(COURSE_DETAIL + courseId);
        List<ChapterDy> chapterList;
        List<CourseDirectoryDy> notInChapterList;
        List<Long> courseDirectoryIdList = new ArrayList<>();
        if (courseDyFromRedis != null) {
            courseDy.setChapterList(courseDyFromRedis.getChapterList());
            chapterList = courseDy.getChapterList();
            courseDy.setCourseDirectoryNotInChapterList(courseDyFromRedis.getCourseDirectoryNotInChapterList());
            notInChapterList = courseDy.getCourseDirectoryNotInChapterList();
            courseDy.setTrySee(courseDyFromRedis.isTrySee());
            courseDy.setTrySeeDirList(courseDyFromRedis.getTrySeeDirList());
            courseDy.setCourseDirectoryIdList(courseDyFromRedis.getCourseDirectoryIdList());
            courseDirectoryIdList = courseDy.getCourseDirectoryIdList();
            courseDy.setUpdatedCourseCount(courseDyFromRedis.getUpdatedCourseCount());
        } else {
            //执行逻辑并缓存
            QueryWrapper<ChapterDy> chapterWrapper = new QueryWrapper<>();
            chapterWrapper.eq("is_delete", 0).eq("course_id", courseId).orderByAsc("serial_number");
            //章节
            chapterList = chapterDyMapper.selectList(chapterWrapper);
            courseDy.setChapterList(chapterList);
            List<CourseDirectoryDy> trySeeList = new ArrayList<>();
            for (ChapterDy cdy : chapterList) {
                //查询每个章节的目录
                QueryWrapper<CourseDirectoryDy> courseDirectoryWrapper = new QueryWrapper<>();
                courseDirectoryWrapper.eq("is_delete", 0).eq("course_id", courseId).eq("chapter_id", cdy.getId()).orderByAsc("serial_number");
                List<CourseDirectoryDy> directoryDyList = courseDirectoryDyMapper.selectList(courseDirectoryWrapper);

                List<Long> collect1 = directoryDyList.stream().map(CourseDirectoryDy::getId).collect(Collectors.toList());
                courseDirectoryIdList.addAll(collect1);

                List<CourseDirectoryDy> collect = directoryDyList.stream().filter(item -> item.getIsTrySee() != null && item.getIsTrySee() == 1).collect(Collectors.toList());
                trySeeList.addAll(collect);
                int size = directoryDyList.size();
                updatedCourseCount = updatedCourseCount + size;
                cdy.setCourseDirectoryList(directoryDyList);
            }
            //不在章节里的目录
            QueryWrapper<CourseDirectoryDy> cdWrapper = new QueryWrapper<>();
            cdWrapper.eq("is_delete", 0).eq("course_id", courseId).isNull("chapter_id").orderByAsc("serial_all_number");
            notInChapterList = courseDirectoryDyMapper.selectList(cdWrapper);
            courseDy.setCourseDirectoryNotInChapterList(notInChapterList);

            List<CourseDirectoryDy> notInChapter = notInChapterList.stream().filter(item -> item.getIsTrySee() != null && item.getIsTrySee() == 1).collect(Collectors.toList());
            trySeeList.addAll(notInChapter);
            if (!trySeeList.isEmpty()) {
                courseDy.setTrySee(true);
            }
            courseDy.setTrySeeDirList(trySeeList);
            updatedCourseCount = updatedCourseCount + notInChapterList.size();
            //已上传课的节数
            courseDy.setUpdatedCourseCount(updatedCourseCount);
            List<Long> collect = notInChapterList.stream().map(CourseDirectoryDy::getId).collect(Collectors.toList());
            courseDirectoryIdList.addAll(collect);
            courseDy.setCourseDirectoryIdList(courseDirectoryIdList);
            //缓存
            redisService.setCacheObject(COURSE_DETAIL + courseId, courseDy);
        }
        List<LearningRecord> learnList = learningRecordMapper.queryItemListByLongList(courseDirectoryIdList, userId);
        Map<Long, LearningRecord> map = new HashMap<>();
        for (LearningRecord record : learnList) {
            map.put(record.getCourseDirectoryId(), record);
        }
        boolean isAV = false;
        //循环所有课程
        for (ChapterDy cdy : chapterList) {
            List<CourseDirectoryDy> directoryDyList1 = cdy.getCourseDirectoryList();
            if (CollectionUtils.isEmpty(directoryDyList1)) continue;
            for (CourseDirectoryDy item : directoryDyList1) {
                if (item.getDirectoryType() != null && (item.getDirectoryType() == 1 || item.getDirectoryType() == 2)) {
                    if (!isAV) {
                        isAV = true;
                    }
                }
                Long id = item.getId();
                LearningRecord record = map.get(id);
                if (record != null) {
                    item.setLearningLocation(record.getLearningLocation());
                    if (item.getDuration() == null) {
                        item.setDuration(0L);
                    }
                    item.setCumulativeLearningTime(record.getCumulativeLearningTime() == null ? 0L : (record.getCumulativeLearningTime().compareTo(item.getDuration()) > 0 ? item.getDuration() : record.getCumulativeLearningTime()));
                }
            }
        }
        for (CourseDirectoryDy item : notInChapterList) {
            if (item.getDirectoryType() != null && (item.getDirectoryType() == 1 || item.getDirectoryType() == 2)) {
                if (!isAV) {
                    isAV = true;
                }
            }
            Long id = item.getId();
            LearningRecord record = map.get(id);
            if (record != null) {
                item.setLearningLocation(record.getLearningLocation());
                if (item.getDuration() == null) {
                    item.setDuration(0L);
                }
                item.setCumulativeLearningTime(record.getCumulativeLearningTime() == null ? 0L : (record.getCumulativeLearningTime().compareTo(item.getDuration()) > 0 ? item.getDuration() : record.getCumulativeLearningTime()));
            }
        }
        //图文课
        if (courseDy.getCourseType() == 2) {
            courseDy.setTuWen(true);
        } else {
            //不含音视频的课程,也属于图文
            courseDy.setTuWen(!isAV);
        }
        if (courseDy.getId() != null && courseDy.getId() == 39067L) {
            courseDy.setTuWen(false);
        }
        //学习人数
        int learnPersonCount = 0;
        if (courseDy.getVisualLearnNumType() != null && courseDy.getVisualLearnNumType() == 1) {
            long visualLearnNum = courseDy.getVisualLearnNum() == null ? 0 : courseDy.getVisualLearnNum();
            learnPersonCount = learnPersonCount + (int) visualLearnNum;
        }
        QueryWrapper<CourseOrder> orderWrapper = new QueryWrapper<>();
        orderWrapper.eq("course_id", courseId);
        orderWrapper.eq("order_status", 1);
        //orderWrapper.eq("order_platform", 9);
        Long orderCount = courseOrderMapper.selectCount(orderWrapper);
        learnPersonCount = learnPersonCount + (orderCount == null ? 0 : orderCount.intValue());
        courseDy.setLearnPersonCount(learnPersonCount);

        List<MDiscountsDTO> mDiscountsDTOS = imDiscountsService.selectReceiveCouponList(userId, queryCourseDetailDTO.getCourseId());
        MDiscountsDTO shareMDiscount = null;
        MDiscountsDTO mDiscount = null;
        if (CollectionUtils.isNotEmpty(mDiscountsDTOS)) {
            for (MDiscountsDTO mdto : mDiscountsDTOS) {
                if (mdto.getDiscountsType() == 1) {
                    shareMDiscount = mdto;
                    break;
                } else {
                    mDiscount = mdto;
                    break;
                }
            }
        }
        if (shareMDiscount != null) {
            MDiscounts mDiscounts = new MDiscounts();
            BeanUtils.copyProperties(shareMDiscount, mDiscounts);
            courseDy.setDiscountsInfo(mDiscounts);
        } else if (mDiscount != null) {
            MDiscounts mDiscounts1 = new MDiscounts();
            BeanUtils.copyProperties(mDiscount, mDiscounts1);
            courseDy.setDiscountsInfo(mDiscounts1);
        }
        if (StringUtils.isNotBlank(currentUser.getTelNumber()) && currentUser.getId() > 0L) {
            String mobile = currentUser.getTelNumber();
            List<com.wendao101.douyin.domain.CourseOrder> currentOrder = courseOrderService.selectByOrderStatusAndUserIdOrPhone(mobile, userId, courseId);
            //查订单,如果有状态已支付的
            if (!currentOrder.isEmpty()) {
                courseDy.setCurrentUserBuy(true);
                courseDy.setOrderId(currentOrder.get(0).getOrderId());
                courseDy.setOrderPrimaryId(currentOrder.get(0).getId());
            } else {
                //再执行抽单搜索
                List<CdCourseOrder> cdCourseOrder = cdCourseOrderService.selectByOrderStatusAndUserIdOrPhone(mobile, userId, courseId);
                if (!cdCourseOrder.isEmpty()) {
                    courseDy.setCurrentUserBuy(true);
                    courseDy.setOrderId(cdCourseOrder.get(0).getOrderId());
                    courseDy.setOrderPrimaryId(cdCourseOrder.get(0).getId());
                }
            }
        }
        //获取当前用户领取的优惠券
        if (courseDy.getDiscountsInfo() != null) {
            MDiscountsDTO receive = shareMDiscount == null ? mDiscount : shareMDiscount;
            com.wendao101.douyin.domain.MReceiveCoupon mReceiveCoupon1 = new com.wendao101.douyin.domain.MReceiveCoupon();
            mReceiveCoupon1.setReceiveTime(receive.getReceiveCouponTime());
            mReceiveCoupon1.setId(receive.getFetchRecordId());
            mReceiveCoupon1.setDiscountsId(receive.getId());
            mReceiveCoupon1.setUserId(userId);

            courseDy.setCouponReceiveTime(mReceiveCoupon1.getReceiveTime());
            courseDy.setHaveCoupon(true);
            courseDy.setReceiveCouponId(mReceiveCoupon1.getId());
            courseDy.setReceiveCoupon(mReceiveCoupon1);
        } else {
            QueryWrapper<MDiscounts> discountsQueryWrapper = new QueryWrapper<>();
            discountsQueryWrapper.eq("course_id", courseId).in("discounts_type", 0).eq("discounts_status", 1).eq("is_stop_action", 0).eq("is_delete", 0);
            //是否有优惠券
            MDiscounts mDiscount2 = mDiscountsMapper.selectOne(discountsQueryWrapper, false);
            //
            if (mDiscount2 != null && mDiscount2.getDiscountsNumberType() == 0) {
                if (mDiscount2.getReceiveCouponSum() == null) {
                    mDiscount2.setReceiveCouponSum(0);
                }
                if (mDiscount2.getDiscountsSum() == null) {
                    mDiscount2.setDiscountsSum(0);
                }
                Date now = new Date();
                if (mDiscount2.getReceiveCouponEndTime() != null && now.after(mDiscount2.getReceiveCouponEndTime())) {
                    mDiscount2 = null;
                }
                //判断优惠券的领取数量
                if (mDiscount2 != null && mDiscount2.getReceiveCouponSum().intValue() >= mDiscount2.getDiscountsSum().intValue()) {
                    mDiscount2 = null;
                }
            }
            if (mDiscount2 != null) {
                courseDy.setDiscountsInfo(mDiscount2);
            }
        }

        //是否附赠实际物
        QueryWrapper<MGiveEntity> MGiveEntityQueryWrapper = new QueryWrapper<>();
        MGiveEntityQueryWrapper.eq("course_id", courseId);
        MGiveEntity mGiveEntity = mGiveEntityMapperPlus.selectOne(MGiveEntityQueryWrapper, false);
        if (mGiveEntity != null) {
            int phoneType = mGiveEntity.getPhoneType() == null ? 0 : mGiveEntity.getPhoneType();
            int isGetAddress = mGiveEntity.getIsGetAdress() == null ? 0 : mGiveEntity.getIsGetAdress();
            courseDy.setGiveEntityIsNeedPhone(phoneType == 1);
            courseDy.setGiveEntityIsNeedAddress(isGetAddress == 1);
            courseDy.setGiveEntity(mGiveEntity);
        }
        //学习资料
        QueryWrapper<LearningMaterials> queryWrapperLearningMaterials = new QueryWrapper<>();
        queryWrapperLearningMaterials.eq("course_id", courseDy.getId());
        List<LearningMaterials> learningMaterials = learningMaterialsMapperPlus.selectList(queryWrapperLearningMaterials);
        if (CollectionUtils.isNotEmpty(learningMaterials)) {
            List<Long> learningMaterialIds = new ArrayList<>();
            for (LearningMaterials lm : learningMaterials) {
                learningMaterialIds.add(lm.getStudyDataId());
            }
            QueryWrapper<StudyData> queryWrapper = new QueryWrapper<>();
            queryWrapper.in("id", learningMaterialIds);
            List<StudyData> studyData2 = studyDataMapperPlus.selectList(queryWrapper);
            courseDy.setStudyDataList(studyData2);
        }
        //是否有收货地址
        boolean haveShippingAddress = false;
        QueryWrapper<ShippingAddress> addressQueryWrapper = new QueryWrapper<>();
        addressQueryWrapper.eq("user_id", userId);
        List<ShippingAddress> shippingAddresses = shippingAddressMapperPlus.selectList(addressQueryWrapper);
        if (CollectionUtils.isNotEmpty(shippingAddresses)) {
            haveShippingAddress = true;
        }
        courseDy.setHaveShippingAddress(haveShippingAddress);
        if (courseDy.getCourseType() == 2) {
            QueryWrapper<CourseTwContent> tuWenQueryWrapper = new QueryWrapper<>();
            tuWenQueryWrapper.eq("course_id", courseId);
            CourseTwContent courseTwContent = courseTwContentMapper.selectOne(tuWenQueryWrapper, false);
            courseDy.setTuWenContent(courseTwContent.getTextAndImagePublished());
        }
        //返回秒杀数据
        SecondKill secondKill = secondKillService.selectValidRecordFromRedis(courseId);
        if (secondKill != null) {
            String uuid = com.wendao101.common.core.utils.uuid.UUID.fastUUID().toString(true);
            courseDy.setSecondKill(secondKill);
            courseDy.setSecondKillUUID(uuid);
            String key = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_FRONT_KEY + uuid;
            redisService.setCacheObject(key, secondKill, 2L, TimeUnit.DAYS);
            //实时秒杀数量
            String keySecNumber = "wendao_sec_kill:" + secondKill.getId();
            Integer number = redisService.getCacheObject(keySecNumber);
            if (number != null) {
                if (number > secondKill.getSeckillNum()) {
                    number = secondKill.getSeckillNum();
                }
                courseDy.setCurrentSecondKillNumber(number);
                BigDecimal minProportion = secondKill.getMinProportion();
                BigDecimal maxProportion = secondKill.getMaxProportion();
                Integer seckillNum = secondKill.getSeckillNum();
                BigDecimal multiply = (maxProportion.subtract(minProportion)).multiply((new BigDecimal(number).divide(new BigDecimal(seckillNum), 2, RoundingMode.HALF_UP)));
                BigDecimal bigDecimal = multiply.setScale(2, RoundingMode.HALF_UP);
                courseDy.setCurrentPercet(minProportion.add(bigDecimal));
            } else {
                courseDy.setCurrentSecondKillNumber(0);
                courseDy.setCurrentPercet(secondKill.getMinProportion());
            }
        }
        return AjaxResult.success(courseDy);
    }

    @PostMapping("/createWendaoOrder")
    public AjaxResult createWendaoOrder(@RequestBody CreateWendaoOrderDTO createWendaoOrderDTO, HttpServletRequest request) {
        if (createWendaoOrderDTO.getCourseId() <= 0L) {
            return AjaxResult.error("课程id错误!");
        }
        WendaoUser currentUser = getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        createWendaoOrderDTO.setUserId(currentUser.getId());
        createWendaoOrderDTO.setPlatform(PLATFORM);
        QueryWrapper<CourseDy> sectionQueryWrapper = new QueryWrapper<>();
        sectionQueryWrapper.eq("id", createWendaoOrderDTO.getCourseId());
        CourseDy courseDy = courseDyMapper.selectOne(sectionQueryWrapper, false);
        if (courseDy == null) {
            return AjaxResult.error("课程不存在!");
        }
        createWendaoOrderDTO.setAppNameType(courseDy.getAppNameType());
        return wxkuaishouOrderService.createWendaoOrder(createWendaoOrderDTO);
    }

    @PostMapping("/createAlipayOrder")
    public AjaxResult createAlipayOrderForPc(@RequestBody CreateKuaishouPreOrderDTO createKuaishouPreOrderDTO, HttpServletRequest request) {
        if (StringUtils.isBlank(createKuaishouPreOrderDTO.getOrderId())) {
            return AjaxResult.error("orderId必传!");
        }
        WendaoUser currentUser = getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        createKuaishouPreOrderDTO.setUserId(currentUser.getId());
        com.wendao101.douyin.domain.CourseOrder courseOrder = courseOrderService.selectCourseOrderForH5ByOrderId(createKuaishouPreOrderDTO.getOrderId(), currentUser.getId());
        if (courseOrder == null) {
            return AjaxResult.error("订单不存在!");
        }
        createKuaishouPreOrderDTO.setAppNameType(courseOrder.getAppNameType());
        return wxkuaishouOrderService.createAlipayH5Order(createKuaishouPreOrderDTO);
    }

    /**
     * pc端我的订单
     *
     * @param courseOrderVO
     * @return
     */
    @PostMapping("/course_order_list")
    public AjaxResult selectH5CourseOrderDyList(@RequestBody CourseOrderVO courseOrderVO, HttpServletRequest request) {
        WendaoUser currentUser = getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        courseOrderVO.setOrderPlatform(PLATFORM);
        courseOrderVO.setUserId(currentUser.getId());
        courseOrderVO.setMobile(StringUtils.isNotBlank(currentUser.getTelNumber())?currentUser.getTelNumber():"99988877765");
        return success(courseOrderService.selectCourseOrderDyList(courseOrderVO));
    }


    /**
     * 查看我的信息
     *
     * @param request
     * @return
     */
    @PostMapping("/getUserInfo")
    public AjaxResult selectH5CourseOrderDyList(HttpServletRequest request) {
        WendaoUser currentUser = getCurrentUser(request, redisService);
        if (currentUser == null) {
            return new AjaxResult(401, "未登录");
        }
        //创建聊天室用户
//        boolean isSuccess =avChatRoomService.createUserForAvChatRoom(currentUser);
//        if(!isSuccess){
//            System.out.println("创建聊天室用户失败!");
//        }
        if (StringUtils.isNotBlank(currentUser.getTelNumber())) {
            //按手机号和老师Id查询老师
            QueryWrapper<Teacher> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("mobile", currentUser.getTelNumber());
            List<Teacher> teacherList = teacherMapper.selectList(queryWrapper);
            if(CollectionUtils.isNotEmpty(teacherList)){
                List<Teacher> teacherList1 = new ArrayList<>();
                for(Teacher t:teacherList){
                    t.setPassword(null);
                    if(t.getWapLiveOpen()!=null&&t.getWapLiveOpen()==1){
                        teacherList1.add(t);
                    }
                }
                if(CollectionUtils.isNotEmpty(teacherList1)){
                    currentUser.setTeacherList(teacherList1);
                }

            }
        }
        return AjaxResult.success(currentUser);
    }


    /**
     * 搜索  不用登录查出课程
     *
     * @param dyCourseVO
     * @return
     */
    @PostMapping("/getAllDyCourse")
    public AjaxResult getAllDyCourse(@RequestBody PcCourseVO dyCourseVO) {
        //课程列表
        if (StringUtils.isEmpty(dyCourseVO.getKeywords())) {
            PageInfo<CourseTeacherDTO> result = courseOrderService.getAllDyCourseForPc(dyCourseVO);
            return success(result);
        } else if (Objects.nonNull(dyCourseVO.getStatus())) {
            //课程 达人 全部列表
            if (dyCourseVO.getStatus() == 0) {
                DyCourseDTO result = courseOrderService.getAllDyCourseAndTeacherForPc(dyCourseVO);
                return success(result);
            }

            //达人列表
            if (dyCourseVO.getStatus() == 1) {
                PageInfo<DyTeacherDTO> result = courseOrderService.getDyTeacherForPc(dyCourseVO);
                return success(result);
            }

            //课程列表
            if (dyCourseVO.getStatus() == 2) {
                PageInfo<CourseTeacherDTO> result = courseOrderService.getAllDyCourseForPc(dyCourseVO);
                return success(result);
            }
        }
        return success(new ArrayList<>());
    }

    /**
     * 获取当前用户信息加校验登录场景
     *
     * @param request
     * @return
     */
    public static WendaoUser getCurrentUser(HttpServletRequest request, RedisService rs) {
        //从request中取出token头
        String token = request.getHeader("token");
        if (StringUtils.isNotBlank(token)) {
            return rs.getCacheObject(WapController.WX_WAP_STORE_USER_TOKEN_KEY + token);
        }
        return null;
    }

    private String encrypt(String plaintext, String key, String iv) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), "AES");
        IvParameterSpec ivSpec = new IvParameterSpec(iv.getBytes(StandardCharsets.UTF_8));
        cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
        byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
        byte[] ivAndEncrypted = new byte[iv.length() + encrypted.length];
        System.arraycopy(iv.getBytes(StandardCharsets.UTF_8), 0, ivAndEncrypted, 0, iv.length());
        System.arraycopy(encrypted, 0, ivAndEncrypted, iv.length(), encrypted.length);
        return Base64.getEncoder().encodeToString(ivAndEncrypted);
    }

    private String send(String phoneNumber, String code) {
        String uuid = UUID.randomUUID().toString();
        aliyunSmsService.sendVerificationCode(phoneNumber, code);
        return uuid;
    }

//    private void sendCode(String mobile,String code) {
//        // 接收短信的手机号码
//        //String mobile = "18667016502";
//        // 模板ID
//        long tplId = 6056480;
//
//        // 构建模板参数
//        Map<String, String> tplParams = new HashMap<>();
//        tplParams.put("code", code);
//
//        try {
//            String result = tplSingleSend(mobile, tplId, tplParams);
//            System.out.println("发送结果：" + result);
//        } catch (Exception e) {
//            System.err.println("发送失败：" + e.getMessage());
//        }
//    }

    private String tplSingleSend(String mobile, long tplId, Map<String, String> tplParams) throws IOException {
        Map<String, String> params = new HashMap<>();
        params.put("apikey", APIKEY);
        params.put("mobile", mobile);
        params.put("tpl_id", String.valueOf(tplId));

        // 构建模板变量值字符串
        StringBuilder tplValue = new StringBuilder();
        boolean first = true;
        for (Map.Entry<String, String> entry : tplParams.entrySet()) {
            if (!first) {
                tplValue.append("&");
            }
            tplValue.append(URLEncoder.encode("#" + entry.getKey() + "#", ENCODING))
                    .append("=")
                    .append(URLEncoder.encode(entry.getValue(), ENCODING));
            first = false;
        }
        params.put("tpl_value", tplValue.toString());

        return post(API_URL, params);
    }

    private String post(String url, Map<String, String> params) throws IOException {
        CloseableHttpClient client = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        // 构建表单参数
        StringBuilder postData = new StringBuilder();
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (postData.length() != 0) {
                postData.append('&');
            }
            postData.append(URLEncoder.encode(param.getKey(), ENCODING));
            postData.append('=');
            postData.append(URLEncoder.encode(param.getValue(), ENCODING));
        }

        // 设置请求头
        post.setHeader("Accept", "application/json;charset=utf-8");
        post.setHeader("Content-Type", "application/x-www-form-urlencoded;charset=utf-8");

        // 设置请求体
        StringEntity entity = new StringEntity(postData.toString());
        post.setEntity(entity);

        // 发送请求并获取响应
        HttpResponse response = client.execute(post);
        String result = EntityUtils.toString(response.getEntity(), ENCODING);

        client.close();
        return result;
    }
}
