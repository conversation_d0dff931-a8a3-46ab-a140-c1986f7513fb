package com.wendao101.douyin.controller;

import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.douyin.domain.CourseOrder;
import com.wendao101.douyin.domain.WendaoUser;
import com.wendao101.douyin.dto.KtClassIdDTO;
import com.wendao101.douyin.dto.OldUserInfoDTO;
import com.wendao101.douyin.service.ICourseOrderService;
import com.wendao101.douyin.service.IWendaoUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@RestController
@RequestMapping("/kt_check_first")
public class KtCheckFirstController {

    @Autowired
    private ICourseOrderService courseOrderService;

    @Autowired
    private IWendaoUserService wendaoUserService;


    @PostMapping("/convertUidAndCourseId")
    public AjaxResult convertUidAndCourseId(@RequestBody OldUserInfoDTO oldUserInfoDTO) {
        if (oldUserInfoDTO.getAppNameType() == 2 && StringUtils.isNotBlank(oldUserInfoDTO.getOpenid())) {
            WendaoUser wendaoUser = wendaoUserService.selectWendaoUserByOpenIdNew(oldUserInfoDTO.getOpenid(), 2);
            Long buyUserId = -1L;
            if(wendaoUser!=null){
                buyUserId = wendaoUser.getId();
            }
            //问到课堂
            //查询订单和openid.和视频号平台,支付成功的订单.
            List<CourseOrder> courseOrderList = courseOrderService.selectByOpenIdAndPlatformSuccess(oldUserInfoDTO.getOpenid(),buyUserId);
            Set<Long> courseIdSet = new HashSet<>();
            for (CourseOrder courseOrder : courseOrderList) {
                courseIdSet.add(courseOrder.getCourseId());
            }
            if (!courseIdSet.isEmpty()) {
                List<KtClassIdDTO> ids = courseOrderService.queryIdMapByCourseId(courseIdSet);
                Map<Long, Long> classIdMap = new HashMap<>();
                for (KtClassIdDTO dto : ids) {
                    classIdMap.put(dto.getKtClassId(), dto.getNewClassId());
                }
                if (classIdMap.get(oldUserInfoDTO.getDataId()) != null) {
                    oldUserInfoDTO.setDataId(classIdMap.get(oldUserInfoDTO.getDataId()));
                }
            }
        }
        return AjaxResult.success(oldUserInfoDTO);
    }
}
