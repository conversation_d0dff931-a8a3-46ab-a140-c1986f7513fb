package com.wendao101.douyin.service;


import com.wendao101.douyin.domain.QExamBatch;

import java.util.List;

/**
 * 用户答题批次Service接口
 *
 * <AUTHOR>
 * @date 2024-07-18
 */
public interface IQExamBatchService {
    /**
     * 查询用户答题批次
     *
     * @param id 用户答题批次主键
     * @return 用户答题批次
     */
    QExamBatch selectQExamBatchById(Long id);

    /**
     * 查询用户答题批次列表
     *
     * @param qExamBatch 用户答题批次
     * @return 用户答题批次集合
     */
    List<QExamBatch> selectQExamBatchList(QExamBatch qExamBatch);

    /**
     * 新增用户答题批次
     *
     * @param qExamBatch 用户答题批次
     * @return 结果
     */
    int insertQExamBatch(QExamBatch qExamBatch);

    /**
     * 修改用户答题批次
     *
     * @param qExamBatch 用户答题批次
     * @return 结果
     */
    int updateQExamBatch(QExamBatch qExamBatch);

    /**
     * 批量删除用户答题批次
     *
     * @param ids 需要删除的用户答题批次主键集合
     * @return 结果
     */
    int deleteQExamBatchByIds(Long[] ids);

    /**
     * 删除用户答题批次信息
     *
     * @param id 用户答题批次主键
     * @return 结果
     */
    int deleteQExamBatchById(Long id);
}
