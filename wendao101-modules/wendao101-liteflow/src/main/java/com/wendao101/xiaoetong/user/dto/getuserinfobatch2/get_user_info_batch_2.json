{"openapi": "3.0.0", "info": {"title": "获取用户列表2.0", "version": "2.0.0"}, "paths": {"/xe.user.batch.get/2.0.0": {"post": {"summary": "获取用户列表", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserInfoBatch2Request"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetUserInfoBatch2Response"}}}}}}}}, "components": {"schemas": {"GetUserInfoBatch2Request": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "es_skip": {"type": "object", "properties": {"id": {"type": "string", "description": "用户id"}, "user_created_at": {"type": "integer", "format": "int64", "description": "用户创建时间戳"}}, "description": "上一页最后一条数据中的es_skip字段,不传则为第一页，用于翻页"}, "page_size": {"type": "integer", "description": "每页条数，最大50条"}, "phone": {"type": "string", "description": "手机号，与nickname和name不同时生效，优先级：高"}, "nickname": {"type": "string", "description": "昵称, 与phone和name不同时生效, 优先级中"}, "name": {"type": "string", "description": "姓名, 与nickname和phone不同时生效, 优先级低"}, "tag_id": {"type": "string", "description": "用户标签id,若多个则用逗号隔开，如：241151,240333"}, "from": {"type": "integer", "description": "用户来源： -1-全部、0-微信、1-sdk、2-字节跳动、3-百度、4-飞书企业内训、5-小班课app、6-通用h5、7-微博、8-B端导入、9-手机号注册、10-QQ"}, "user_type": {"type": "integer", "description": "用户身份 全部-0、黑名单-1、超级会员-2"}, "last_paytime_start": {"type": "string", "description": "查询支付起始时间 yyyy-MM-dd"}, "last_paytime_end": {"type": "string", "description": "查询支付截至时间 yyyy-MM-dd"}, "user_created_start": {"type": "string", "description": "查询用户创建起始时间 yyyy-mm-dd，起始时间必须小于结束时间"}, "user_created_end": {"type": "string", "description": "查询用户创建截止时间 yyyy-mm-dd"}, "min_pay_sum": {"type": "string", "description": "查询支付最小金额"}, "max_pay_sum": {"type": "string", "description": "查询支付最大金额"}, "min_punch_count": {"type": "string", "description": "查询支付最小次数"}, "max_punch_count": {"type": "string", "description": "查询支付最大次数"}, "latest_visited_start": {"type": "string", "description": "最近访问时间起始时间 yyyy-mm-dd，起始时间必须小于结束时间"}, "latest_visited_end": {"type": "string", "description": "最近访问时间结束时间 yyyy-mm-dd"}, "need_column": {"type": "array", "items": {"type": "string"}, "description": "需要返回的字段,可选字段：latest_visited_at-用户最后一次访问店铺的时间"}}, "required": ["access_token", "page_size"]}, "GetUserInfoBatch2Response": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：0-成功，其他为错误"}, "msg": {"type": "string", "description": "对返回码的文本描述内容"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"type": "object", "properties": {"avatar": {"type": "string", "description": "头像"}, "bind_phone": {"type": "string", "description": "绑定手机号"}, "collect_phone": {"type": "string", "description": "采集手机号"}, "es_skip": {"type": "object", "properties": {"id": {"type": "string", "description": "用户id"}, "user_created_at": {"type": "integer", "format": "int64", "description": "用户创建时间戳"}}, "description": "用于翻页的字段"}, "from": {"type": "string", "description": "用户来源：0-微信、1-sdk、2-字节跳动、3-百度、4-飞书企业内训、5-小班课app、6-通用h5、7-微博、8-B端导入、9-手机号注册、10-QQ"}, "pay_sum": {"type": "number", "format": "float", "description": "消费总额（分）"}, "punch_count": {"type": "integer", "description": "购买次数"}, "user_created_at": {"type": "string", "description": "用户创建时间"}, "user_id": {"type": "string", "description": "用户id"}, "user_nickname": {"type": "string", "description": "用户昵称"}, "wx_app_open_id": {"type": "string", "description": "小程序 open_id"}, "wx_open_id": {"type": "string", "description": "微信 open_id"}, "wx_union_id": {"type": "string", "description": "微信 union_id"}, "latest_visited_at": {"type": "integer", "format": "int64", "description": "用户最后一次访问店铺的时间，毫秒时间戳，为0时表示没有访问过店铺"}}}}, "total": {"type": "integer", "description": "查询结果记录数"}}}}}}}}