CREATE TABLE `goods_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '自增主键',
  `app_id` varchar(64) NOT NULL COMMENT '店铺id',
  `goods_id` varchar(64) NOT NULL COMMENT '商品id',
  `goods_type` tinyint(4) NOT NULL COMMENT '商品类型(5-会员,6-专栏,8-大专栏,20-电子书,25-训练营)',
  `resource_id` varchar(64) NOT NULL COMMENT '资源id',
  `title` varchar(255) NOT NULL COMMENT '资源名称',
  `img_url` varchar(255) DEFAULT NULL COMMENT '缩略图',
  `img_url_compress` varchar(255) DEFAULT NULL COMMENT '缩略图压缩图片',
  `resource_type` tinyint(4) NOT NULL COMMENT '资源类型(1-图文,2-音频,3-视频,4-直播,6-专栏,8-大专栏,20-电子书,25-训练营)',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '浏览量',
  `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_resource` (`goods_id`, `resource_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_resource_id` (`resource_id`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_updated_at` (`updated_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品关联资源表';
