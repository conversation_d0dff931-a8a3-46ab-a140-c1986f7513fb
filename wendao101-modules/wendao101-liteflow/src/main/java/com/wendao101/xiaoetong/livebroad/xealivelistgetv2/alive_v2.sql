CREATE TABLE `alive_v2` (
  `id` varchar(64) NOT NULL COMMENT '直播id',
  `app_id` varchar(64) NOT NULL COMMENT '店铺ID',
  `room_id` varchar(64) NOT NULL COMMENT '房间ID',
  `title` varchar(255) NOT NULL COMMENT '直播标题',
  `img_url` varchar(255) DEFAULT NULL COMMENT '直播封面图',
  `img_url_compressed` varchar(255) DEFAULT NULL COMMENT '封面压缩后的路径',
  `page_url` varchar(255) DEFAULT NULL COMMENT '店铺直播详情页地址',
  `comment_count` int(11) NOT NULL DEFAULT '0' COMMENT '评论数量',
  `is_takegoods` tinyint(1) NOT NULL DEFAULT '0' COMMENT '带货开关：1=开，0=关',
  `takegoods` varchar(64) DEFAULT NULL COMMENT '带货商品分组ID',
  `payment_type` tinyint(4) NOT NULL COMMENT '付费类型：1-免费、2-单笔、3-付费产品包',
  `is_public` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否公开售卖，1公开，0不公开',
  `is_stop_sell` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否停售,0-否、1-是',
  `is_transcode` tinyint(1) NOT NULL DEFAULT '0' COMMENT '视频是否转码，0-表示未转码，1-表示已转码,2-转码失败',
  `piece_price` int(11) NOT NULL DEFAULT '0' COMMENT 'payment_type为2时，单笔价格（分）;payment_type为3时，专栏价格（分）',
  `line_price` int(11) NOT NULL DEFAULT '0' COMMENT '划线价',
  `have_password` tinyint(1) NOT NULL DEFAULT '0' COMMENT '该资源是否需要密码',
  `alive_type` tinyint(4) NOT NULL COMMENT '直播类型：0-语音直播,1-视频直播,2-推流直播,3-ppt直播',
  `purchase_count` int(11) NOT NULL DEFAULT '0' COMMENT '订阅量',
  `reward_sum` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '打赏金额（元）',
  `is_ban` tinyint(1) NOT NULL DEFAULT '0' COMMENT '强制封禁：0-否 1-是',
  `on_shelf` tinyint(1) NOT NULL DEFAULT '0' COMMENT '强制下架：0-否 1-是',
  `recycle_bin_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '上下架状态：0-上架，1-下架',
  `push_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '推流状态，0断流，1推流中，2推流未开始',
  `state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '直播状态：0-可见，1-关闭，2-删除',
  `start_at` datetime NOT NULL COMMENT '上架时间',
  `zb_start_at` datetime NOT NULL COMMENT '直播开始时间',
  `zb_stop_at` datetime DEFAULT NULL COMMENT '直播结束时间',
  `manual_stop_at` datetime DEFAULT NULL COMMENT '手动结束直播时间',
  `source_shop_name` varchar(255) DEFAULT NULL COMMENT '转播店铺名称',
  `material_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '素材状态',
  `video_length` int(11) NOT NULL DEFAULT '0' COMMENT '视频时长（s）',
  `alive_state` tinyint(4) NOT NULL DEFAULT '0' COMMENT '直播状态',
  `alive_mode` tinyint(4) NOT NULL DEFAULT '0' COMMENT '直播模式：0.无；1竖屏直播',
  `create_mode` tinyint(4) NOT NULL DEFAULT '0' COMMENT '创建类型：0-自创建，1-转播创建',
  `is_round_table_on` tinyint(1) NOT NULL DEFAULT '0' COMMENT '圆桌会议功能是否开启',
  `query_package_list` TEXT DEFAULT NULL COMMENT '关联商品',
  `alive_video_url` varchar(255) DEFAULT NULL COMMENT '直播视频URL',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_app_id` (`app_id`),
  KEY `idx_room_id` (`room_id`),
  KEY `idx_zb_start_at` (`zb_start_at`),
  KEY `idx_alive_state` (`alive_state`),
  KEY `idx_create_mode` (`create_mode`),
  KEY `idx_recycle_bin_state` (`recycle_bin_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播信息表 v2';

CREATE TABLE `alive_course_expire` (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alive_id` varchar(64) NOT NULL COMMENT '直播id',
  `period_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '有效期类型：0=永久有效，1=固定有效期，2=自定义有效期',
  `period_value` varchar(64) DEFAULT NULL COMMENT '自定义有效时长',
  `is_allow_repeat_purchase` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否允许重复购买：1=是，0=否',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_alive_id` (`alive_id`),
  CONSTRAINT `fk_alive_course_expire_alive_id` FOREIGN KEY (`alive_id`) REFERENCES `alive_v2` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播课程有效期表';
