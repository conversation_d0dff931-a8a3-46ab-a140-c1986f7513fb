{"openapi": "3.0.0", "info": {"title": "获取直播列表", "version": "1.0.0"}, "paths": {"/xe.alive.list.get/1.0.0": {"post": {"summary": "获取直播列表", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLiveBroadListRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLiveBroadListResponse"}}}}}}}}, "components": {"schemas": {"GetLiveBroadListRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "search_content": {"type": "string", "description": "搜索内容"}, "create_mode": {"type": "integer", "description": "直播课程类型：-1 全部；0 店铺课程； 1 转播课程；默认-1"}, "state": {"type": "integer", "description": "直播商品状态：-1 全部； 0 已上架； 1 已下架；2 待上架；默认-1"}, "search_alive_type": {"type": "integer", "description": "直播模式：-1 全部；10 横屏直播；11竖屏直播；12语音直播；13录播直播；默认-1"}, "alive_play_state": {"type": "integer", "description": "直播状态：-1全部；0未开始；1直播中；2已结束；默认-1"}, "page": {"type": "integer", "description": "页码，表示第几页，从1开始；默认1"}, "page_size": {"type": "integer", "description": "每页条数，最大50条；默认10"}}, "required": ["access_token"]}, "GetLiveBroadListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误描述"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "查询结果记录数"}, "page": {"type": "integer", "description": "当前页码"}, "page_count": {"type": "integer", "description": "总页数"}, "list": {"type": "array", "items": {"$ref": "#/components/schemas/LiveBroadInfo"}}}}}}, "LiveBroadInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "直播id"}, "alive_manual_stop_at": {"type": "string", "description": "直播手动结束时间"}, "alive_stop_at": {"type": "string", "description": "直播结束时间"}, "title": {"type": "string", "description": "直播名称"}, "view_count": {"type": "integer", "description": "直播观看人次，每5分钟同步一次数据"}, "alive_type": {"type": "integer", "description": "直播类型,10 横屏直播；11竖屏直播；12语音直播；13录播直播"}, "reward_sum": {"type": "string", "description": "打赏金额（元）"}, "recycle_bin_state": {"type": "integer", "description": "直播商品状态：-1 全部； 0 已上架； 1 已下架；2 待上架"}, "alive_start_at": {"type": "string", "description": "直播开始时间"}, "alive_state": {"type": "integer", "description": "直播状态：-1全部；0未开始；1直播中；2已结束"}, "live_state": {"type": "integer", "description": "直播间状态：0未开始；1直播中；2互动时间（仅录播有，直播视频推流完成并且在预设开播时间范围内 ）；3已结束、4讲师暂时离开（直播范围时间内未推流或断流后5分钟）"}, "resource_state": {"type": "integer", "description": "直播显隐状态：0-显示，1-隐藏"}, "page_url": {"type": "string", "description": "直播地址"}, "create_mode": {"type": "integer", "description": "直播课程类型：-1 全部；0 店铺课程； 1 转播课程"}, "img_url": {"type": "string", "description": "封面地址"}, "alive_img_url": {"type": "string", "description": "直播宣传图地址"}, "guest_list": {"type": "array", "items": {"$ref": "#/components/schemas/Teacher"}}}}, "Teacher": {"type": "object", "properties": {"user_id": {"type": "string", "description": "讲师id"}, "user_name": {"type": "string", "description": "讲师昵称"}, "phone": {"type": "string", "description": "讲师绑定手机号"}}}}}}