package com.wendao101.xiaoetong.livebroad.xealiveliveupdate;

import lombok.Data;

@Data
public class UpdateLiveRequest {
    /**
     * 专属token
     */
    private String accessToken;
    
    /**
     * 资源信息
     */
    private ResourceInfo resourceInfo;
    
    /**
     * 配置信息
     */
    private ModuleInfo moduleInfo;
    
    /**
     * 商品信息
     */
    private GoodsInfo goodsInfo;
    
    /**
     * 关联课程信息
     */
    private RelationInfo relationInfo;
    
    /**
     * 讲师信息列表
     */
    private RoleInfo[] roleInfo;

    @Data
    public static class ResourceInfo {
        /**
         * 直播id
         */
        private String id;
        
        /**
         * 直播标题（字符长度必须小于45）
         */
        private String title;
        
        /**
         * 直播简介（字符长度必须小于256，默认为空）
         */
        private String summary;
        
        /**
         * 直播详情（仅允许纯文本，不得超过5000个字符，默认为空）
         */
        private String descrb;
        
        /**
         * 直播类型：0-语音，1-录播直播，2-推流直播（默认为0-语音直播）
         */
        private Integer aliveType;
        
        /**
         * 预设直播开始时间（距离当前时间不能超过五年）
         */
        private String zbStartAt;
        
        /**
         * 预设直播时长，单位：秒（距离预设直播开始时间不能超过十年）
         */
        private Integer zbStopAt;
        
        /**
         * 详情封面图素材ID（不传则为B端默认详情封面图，图片大小不能超过5MB）
         */
        private String imgMaterialId;
        
        /**
         * 宣传封面图素材ID（不传则为B端默认宣传图，图片大小不能超过5MB）
         */
        private String aliveImgMaterialId;
        
        /**
         * 暖场图素材ID（当module_info.warm_up=1时有效，不传则为B端默认暖场图，图片大小不能超过5MB）
         */
        private String aliveroomImgMaterialId;
        
        /**
         * 暖场视频素材ID（当module_info.warm_up=2时必填，否则该字段无效，视频大小不能超过5GB）
         */
        private String warmUpVideoMaterialId;
        
        /**
         * 录播视频素材ID（当resource_info.alive_type=1时必填，否则该字段无效，视频大小不能超过5GB）
         */
        private String aliveVideoMaterialId;
        
        /**
         * 苹果端小程序信息调整（在ios微信小程序展示该直播时，会被替换为下面的信息）
         */
        private IosAppletDesc iosAppletDesc;
    }

    @Data
    public static class IosAppletDesc {
        /**
         * 直播标题（字符长度必须小于45，默认为空）
         */
        private String title;
        
        /**
         * 直播简介（字符长度必须小于45，默认为空）
         */
        private String summary;
        
        /**
         * 直播详情（仅允许纯文本，不得超过5000个字符，默认为空）
         */
        private String descrb;
        
        /**
         * 显示设置：0-在苹果小程序内显示该直播，1-在苹果小程序内不显示（默认为0-在苹果小程序内显示该直播）
         */
        private Integer state;
        
        /**
         * 直播封面图素材ID（不传则为B端默认详情封面图，图片大小不能超过5MB）
         */
        private String imgMaterialId;
        
        /**
         * 宣传封面图素材ID（不传则为B端默认宣传图，图片大小不能超过5MB）
         */
        private String aliveImgMaterialId;
    }

    @Data
    public static class ModuleInfo {
        /**
         * 是否开启回放：0-开启，1-关闭（默认为0-开启）
         */
        private Integer isLookback;
        
        /**
         * 回放是否允许倍速播放或快进：0-允许，1-禁止（默认为0-允许）
         */
        private Integer playFastStateSwitch;
        
        /**
         * 直播模式：0-横屏直播，1-竖屏直播（默认为0-横屏直播）
         */
        private Integer aliveMode;
        
        /**
         * 回放有效期设置：1-永久，2-限时（默认为1-永久）
         */
        private Integer expireType;
        
        /**
         * 回放过期时间（当expire_type=2时必填）
         */
        private String expire;
        
        /**
         * 暖场设置：1-暖场图，2-暖场视频（默认为1-暖场图）
         */
        private Integer warmUp;
        
        /**
         * 是否开启完成条件：0-关闭，1-开启（默认为0-关闭）
         */
        private Integer isOpenCompleteTime;
        
        /**
         * 设置最短学习时间（单位：分钟，当module_info.is_open_complete_time=1时必填，否则该字段无效，且最大不能超过300）
         */
        private Integer completeTime;
        
        /**
         * 是否开启联系学员：0-关闭，1-开启（默认为0-关闭）
         */
        private Integer isContactOn;
    }

    @Data
    public static class GoodsInfo {
        /**
         * 售卖类型：1-单独售卖、2-关联售卖
         */
        private Integer saleType;
        
        /**
         * 支付类型：1-免费，2-收费，3-加密，4-指定学员可用，5-仅关联上级资源，仅当goods_info.sale_type=2时才可用
         */
        private Integer paymentType;
        
        /**
         * 价格（单位：分，当payment_type=2时必填，否则该字段无效）
         */
        private Integer piecePrice;
        
        /**
         * 划线价格（单位：分，当payment_type=2时才可用，否则该字段无效，默认为0）
         */
        private Integer linePrice;
        
        /**
         * 密码（goods_info.payemnt_type为3时必填，否则该字段无效）
         */
        private String resourcePassword;
        
        /**
         * 上下架：1-下架，0-上架（默认为0-上架）
         */
        private Integer recycleBinState;
        
        /**
         * 定时上架时间（当recycle_bin_state=0且该字段有值时，为定时上架，默认为空
         */
        private String startAt;
        
        /**
         * 是否停售：0-否，1-是（默认为0-否）
         */
        private Integer isStopSell;
        
        /**
         * 商品编码（字符长度不能超过64，默认为空）
         */
        private String goodsSn;
        
        /**
         * 商品状态：0-可见，1-隐藏（默认为0-可见）
         */
        private Integer state;
    }

    @Data
    public static class RelationInfo {
        /**
         * 关联课程id列表（当goods_info.sale_type=2时必填，否则该字段无效）
         */
        private String[] packageIds;
    }

    @Data
    public static class RoleInfo {
        /**
         * 自定义身份标签（1-10个字符，当需要添加讲师时必填）
         */
        private String roleName;
        
        /**
         * 用户id（当需要添加讲师时必填）
         */
        private String userId;
        
        /**
         * 是否接受打赏：1-接受打赏，0-不接受打赏（默认为0-不接受打赏）
         */
        private Integer isCanExceptional;
    }
}
