{"openapi": "3.0.0", "info": {"title": "获取学习记录列表", "version": "1.0.0"}, "paths": {"/xe.user.learning.records.get/1.0.0": {"post": {"summary": "获取学习记录列表", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLearnRecordRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLearnRecordResponse"}}}}}}}}, "components": {"schemas": {"GetLearnRecordRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "user_id": {"type": "string", "description": "用户id"}, "data": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码"}, "page_size": {"type": "integer", "description": "页容量"}, "agent_type": {"type": "integer", "description": "数据来源"}}, "required": ["page", "page_size"]}}, "required": ["access_token", "user_id", "data"]}, "GetLearnRecordResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误描述"}, "data": {"type": "object", "properties": {"goods_list": {"type": "array", "items": {"$ref": "#/components/schemas/GoodsInfo"}, "description": "收藏列表"}, "total": {"type": "integer", "description": "总数"}}}}}, "GoodsInfo": {"type": "object", "properties": {"id": {"type": "string", "description": "自增主键"}, "resource_id": {"type": "string", "description": "内容的id"}, "resource_type": {"type": "integer", "description": "内容的类型"}, "learn_progress": {"type": "integer", "description": "最近一次学习进度 0-100（当前停留进度）"}, "org_learn_progress": {"type": "string", "description": "原始学习进度"}, "is_finish": {"type": "integer", "description": "是否已学完 1-已学完"}, "finished_at": {"type": "string", "description": "首次完成时间"}, "agent_type": {"type": "integer", "description": "终端来源(1-h5,2-app，4-小程序)"}, "max_learn_progress": {"type": "integer", "description": "历史最大学习进度（不一定是当前停留进度）"}, "created_at": {"type": "string", "description": "首次学习时间"}, "app_id": {"type": "string", "description": "店铺app_id"}, "product_id": {"type": "string", "description": "上级资源id"}, "last_learn_time": {"type": "string", "description": "最近一次学习时间，默认按该字段倒序排序"}, "stay_time": {"type": "integer", "description": "停留时长（学习时长），单位：秒"}, "content_app_id": {"type": "string", "description": "内容方店铺ID"}}}}}}