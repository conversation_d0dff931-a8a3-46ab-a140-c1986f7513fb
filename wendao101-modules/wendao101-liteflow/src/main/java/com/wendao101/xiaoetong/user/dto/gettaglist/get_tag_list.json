{"openapi": "3.0.0", "info": {"title": "获取标签列表", "version": "1.0.0"}, "paths": {"/xe.user.tag.list/1.0.0": {"post": {"summary": "获取标签列表", "description": "获取标签列表的接口", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTagListRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetTagListResponse"}}}}}}}}, "components": {"schemas": {"GetTagListRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "tag_type": {"type": "integer", "description": "标签类型，可选范围[0 - 2]，0-全部，1-手动，2-自动"}, "search_content": {"type": "string", "description": "筛选内容"}, "page": {"type": "integer", "description": "页数，可选范围[1 - 500]"}, "page_size": {"type": "integer", "description": "每页条数，可选范围[1 - 50]"}}, "required": ["access_token", "tag_type", "page", "page_size"]}, "GetTagListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码：0-成功，其他为错误"}, "msg": {"type": "string", "description": "对返回码的文本描述内容"}, "data": {"type": "object", "properties": {"total": {"type": "integer", "description": "查询结果总数"}, "tag_list": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer", "description": "标签id"}, "name": {"type": "string", "description": "标签名称"}, "type": {"type": "integer", "description": "标签类型"}, "created_at": {"type": "string", "description": "创建时间"}, "updated_at": {"type": "string", "description": "更新时间"}}}}}}}}}}}