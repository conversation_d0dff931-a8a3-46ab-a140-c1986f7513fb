package com.wendao101.xiaoetong.livebroad.xealivemsglistget2;

import lombok.Data;

@Data
public class GetLiveMsgList2Request {
    /**
     * 专属token
     */
    private String accessToken;

    /**
     * 直播的资源id
     */
    private String aliveId;

    /**
     * 评论id（非必填，用于分页，从第二页起，此参数值传上一页数据的最后一个comment_id）
     */
    private String commentId;

    /**
     * 条数，最大50条，不传则默认50
     */
    private Integer size;

    /**
     * 用户id，表示当前拉取消息的用户（非筛选作用），不同用户看到的消息会不一样，例如直播开启了打赏消息仅针对讲师可见
     */
    private String userId;
}
