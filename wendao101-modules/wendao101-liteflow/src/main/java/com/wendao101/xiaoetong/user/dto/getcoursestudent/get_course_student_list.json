{"openapi": "3.0.0", "info": {"title": "获取课程学员列表", "version": "1.0.0"}, "paths": {"/xe.course.student.list/1.0.0": {"post": {"summary": "获取课程学员列表", "description": "获取指定课程的学员列表", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCourseStudentListRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCourseStudentListResponse"}}}}}}}}, "components": {"schemas": {"GetCourseStudentListRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "buz_data": {"type": "object", "properties": {"resource_id": {"type": "string", "description": "资源id"}, "resource_type": {"type": "integer", "description": "资源类型 1：图文，2：音频，3：视频，4：直播，5：会员，6：专栏，8：大专栏 ，20：电子书，25：训练营"}, "data": {"type": "object", "properties": {"page": {"type": "integer", "description": "页码"}, "page_size": {"type": "integer", "description": "页长"}}}}, "required": ["resource_id", "resource_type", "data"]}}, "required": ["access_token", "buz_data"]}, "GetCourseStudentListResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误信息"}, "data": {"type": "object", "properties": {"count": {"type": "integer", "description": "总数"}, "user_list": {"type": "array", "items": {"type": "object", "properties": {"user_id": {"type": "string", "description": "学员id"}, "created_at": {"type": "integer", "description": "创建时间"}, "resource_type": {"type": "integer", "description": "资源类型"}, "wx_avatar": {"type": "string", "description": "微信头像"}, "wx_nickname": {"type": "string", "description": "微信昵称"}, "payment_type": {"type": "integer", "description": "付费类型：1-免费、2-单笔、3-付费产品包、4-团购、5-单笔的购买赠送、6-产品包的购买赠送、7-问答提问、8-问答偷听、9-购买会员、10-会员的购买赠送、11、活动付费报名 12、打赏 13、拼团单个资源 14、拼团产品包 15、超级会员 16-接入商兑换码"}, "generate_type": {"type": "integer", "description": "加入类型：1-活动领取、6--好友赠送、8-导入、其他情况-购买"}, "generate_type_name": {"type": "string", "description": "加入类型描述"}, "is_deleted": {"type": "integer", "description": "状态：0：正常"}, "product_id": {"type": "string", "description": "产品id"}, "app_id": {"type": "string", "description": "应用id"}}}}}}}}}}}