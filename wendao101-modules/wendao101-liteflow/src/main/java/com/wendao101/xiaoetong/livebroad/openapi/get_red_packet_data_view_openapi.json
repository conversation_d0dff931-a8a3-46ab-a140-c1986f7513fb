{"openapi": "3.0.0", "info": {"title": "获取红包数据概况", "version": "1.0.0"}, "paths": {"/xe.live.redpacket.analysis/1.0.0": {"post": {"summary": "获取红包数据概况", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRedPacketDataViewRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetRedPacketDataViewResponse"}}}}}}}}, "components": {"schemas": {"GetRedPacketDataViewRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "resource_id": {"type": "string", "description": "资源id"}, "resource_type": {"type": "integer", "description": "资源类型"}}, "required": ["access_token", "resource_id", "resource_type"]}, "GetRedPacketDataViewResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"type": "object", "properties": {"data": {"type": "object", "properties": {"receive_total": {"type": "integer", "description": "红包领取总数"}, "total_after_deduction_money": {"type": "integer", "description": "红包总金额(扣除手续费后)"}, "total_money": {"type": "integer", "description": "红包总金额(扣除手续费前)"}, "total_number": {"type": "integer", "description": "红包总数量"}, "total_rounds": {"type": "integer", "description": "红包总轮数"}}}}}}}}}}