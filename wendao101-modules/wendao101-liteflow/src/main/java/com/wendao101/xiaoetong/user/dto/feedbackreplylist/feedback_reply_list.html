
<!DOCTYPE HTML>
<html lang="" >
<head>
    <meta charset="UTF-8">
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type">
    <title>获取用户反馈回复列表 · 小鹅云</title>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="">
    <meta name="generator" content="GitBook 3.2.3">




    <link rel="stylesheet" href="../../../gitbook/style.css">




    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-highlight/website.css">



    <link rel="stylesheet" href="https://xiaoetong-1252524126.cdn.xiaoeknow.com/bootstrap/3.3.7/css/bootstrap.min.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-simpletabs/tabs.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-expandable-chapters-small/expandable-chapters-small.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-image-captions/image-captions.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-styled-blockquotes/plugin-styled-blockquotes.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-search-pro/search.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-back-to-top-button/plugin.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-splitter/splitter.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-lightbox/css/lightbox.min.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-xiaoe-header/header-element.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-chapter-fold/chapter-fold.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-xiaoe-api/api.css">



    <link rel="stylesheet" href="../../../gitbook/gitbook-plugin-fontsettings/website.css">
























    <meta name="HandheldFriendly" content="true"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="../../../gitbook/images/apple-touch-icon-precomposed-152.png">
    <link rel="shortcut icon" href="../../../gitbook/images/favicon.ico" type="image/x-icon">


    <link rel="next" href="reply.html" />


    <link rel="prev" href="list.html" />



    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon">


    <link rel="bookmark" href="favicon.ico" type="image/x-icon">


    <link rel="apple-touch-icon" sizes="152x152" href="favicon.png">


    <link rel="apple-touch-icon-precomposed" sizes="152x152" href="favicon.png">



    <link rel="apple-touch-icon" sizes="120x120" href="favicon.png">

    <link rel="apple-touch-icon" sizes="180x180" href="favicon.png">




    <link rel="apple-touch-icon-precomposed" sizes="120x120" href="favicon.png">

    <link rel="apple-touch-icon-precomposed" sizes="180x180" href="favicon.png">



</head>
<body>

<div class="book">
    <div class="book-summary">


        <div id="book-search-input" role="search">
            <input type="text" placeholder="Type to search" />
        </div>


        <nav role="navigation">



            <ul class="summary">









                <li class="chapter " data-level="1.1" data-path="../../../xiaoe_cloud_public.html">

                    <a href="../../../xiaoe_cloud_public.html">


                        小鹅云公告

                    </a>



                </li>

                <li class="chapter " data-level="1.2" data-path="../../../xiaoe_cloud_charge.html">

                    <a href="../../../xiaoe_cloud_charge.html">


                        小鹅云API收费公告

                    </a>



                </li>

                <li class="chapter " data-level="1.3" data-path="../../../read_before.html">

                    <a href="../../../read_before.html">


                        开发前必读

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.3.1" data-path="../../../">

                            <a href="../../../">


                                小鹅云介绍

                            </a>



                        </li>

                        <li class="chapter " data-level="1.3.2" data-path="../../../log.html">

                            <a href="../../../log.html">


                                更新日志

                            </a>



                        </li>

                        <li class="chapter " data-level="1.3.3" data-path="../../../read_before/access_guide.html">

                            <a href="../../../read_before/access_guide.html">


                                开始开发

                            </a>



                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.4" data-path="../../../develop_guide.html">

                    <a href="../../../develop_guide.html">


                        开发指南

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.4.1" data-path="../../../develop_guide/application_authorize.html">

                            <a href="../../../develop_guide/application_authorize.html">


                                创建并授权应用

                            </a>



                        </li>

                        <li class="chapter " data-level="1.4.2" data-path="../../../develop_guide/get_access_token.html">

                            <a href="../../../develop_guide/get_access_token.html">


                                获取access_token（含代码示例）

                            </a>



                        </li>

                        <li class="chapter " data-level="1.4.3" data-path="../../../develop_guide/open_permiss.html">

                            <a href="../../../develop_guide/open_permiss.html">


                                开通接口权限

                            </a>



                        </li>

                        <li class="chapter " data-level="1.4.4" data-path="../../../common_problem/code.html">

                            <a href="../../../common_problem/code.html">


                                全局返回码

                            </a>



                        </li>

                        <li class="chapter " data-level="1.4.5" data-path="../../../develop_guide/dev_tool.html">

                            <a href="../../../develop_guide/dev_tool.html">


                                常用工具

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.4.5.1" data-path="../../../develop_guide/dev_tool/log_dev_tool.html">

                                    <a href="../../../develop_guide/dev_tool/log_dev_tool.html">


                                        日志排查工具

                                    </a>



                                </li>


                            </ul>

                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.5" data-path="../../../recall_scene.html">

                    <a href="../../../recall_scene.html">


                        场景方案

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.5.1" data-path="../../../recall_scene/syn_user_info.html">

                            <a href="../../../recall_scene/syn_user_info.html">


                                同步用户场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.2" data-path="../../../recall_scene/syn_product_info.html">

                            <a href="../../../recall_scene/syn_product_info.html">


                                同步商品场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.3" data-path="../../../recall_scene/syn_order_relation.html">

                            <a href="../../../recall_scene/syn_order_relation.html">


                                同步订购关系场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.4" data-path="../../../recall_scene/point_scenario.html">

                            <a href="../../../recall_scene/point_scenario.html">


                                同步积分场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.5" data-path="../../../recall_scene/syn_channel_info.html">

                            <a href="../../../recall_scene/syn_channel_info.html">


                                同步页面统计场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.6" data-path="../../../recall_scene/learning_record.html">

                            <a href="../../../recall_scene/learning_record.html">


                                同步学习数据场景

                            </a>



                        </li>

                        <li class="chapter " data-level="1.5.7" data-path="../../../recall_scene/distributor.html">

                            <a href="../../../recall_scene/distributor.html">


                                同步推广员场景

                            </a>



                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.6" data-path="../../../recall_scene/lnline_sdk.html">

                    <a href="../../../recall_scene/lnline_sdk.html">


                        内嵌SDK服务场景

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.6.1" data-path="../../../recall_scene/lnline_sdk/introduce.html">

                            <a href="../../../recall_scene/lnline_sdk/introduce.html">


                                产品介绍

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.6.1.1" data-path="../../../recall_scene/lnline_sdk/introduce/summary.html">

                                    <a href="../../../recall_scene/lnline_sdk/introduce/summary.html">


                                        产品简介

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.1.2" data-path="../../../recall_scene/lnline_sdk/introduce/product_case.html">

                                    <a href="../../../recall_scene/lnline_sdk/introduce/product_case.html">


                                        客户案例

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.1.3" data-path="../../../recall_scene/lnline_sdk/introduce/access_process.html">

                                    <a href="../../../recall_scene/lnline_sdk/introduce/access_process.html">


                                        接入流程

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.6.2" data-path="../../../recall_scene/lnline_sdk/business.html">

                            <a href="../../../recall_scene/lnline_sdk/business.html">


                                业务文档

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.6.2.1" data-path="../../../recall_scene/lnline_sdk/business/account.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/account.html">


                                        账号打通

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.2" data-path="../../../recall_scene/lnline_sdk/business/pay.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/pay.html">


                                        支付

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.3" data-path="../../../recall_scene/lnline_sdk/business/livestreaming.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/livestreaming.html">


                                        直播带货

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.4" data-path="../../../recall_scene/lnline_sdk/business/goods.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/goods.html">


                                        商品同步

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.5" data-path="../../../recall_scene/lnline_sdk/business/order.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/order.html">


                                        订单同步

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.6" data-path="../../../recall_scene/lnline_sdk/business/learn_data.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/learn_data.html">


                                        学习数据同步

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.7" data-path="../../../recall_scene/lnline_sdk/business/training.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/training.html">


                                        企业内训

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.8" data-path="../../../recall_scene/lnline_sdk/business/pcweb.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/pcweb.html">


                                        PCweb

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.9" data-path="../../../recall_scene/lnline_sdk/business/h5web.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/h5web.html">


                                        H5web

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.10" data-path="../../../recall_scene/lnline_sdk/business/traffic_cash.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/traffic_cash.html">


                                        流量变现

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.11" data-path="../../../recall_scene/lnline_sdk/business/app_share.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/app_share.html">


                                        App分享

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.12" data-path="../../../recall_scene/lnline_sdk/business/micro_page.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/micro_page.html">


                                        微页面装修

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.2.13" data-path="../../../recall_scene/lnline_sdk/business/live_microphone.html">

                                    <a href="../../../recall_scene/lnline_sdk/business/live_microphone.html">


                                        直播连麦

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.6.3" data-path="../../../recall_scene/lnline_sdk/tech_doc.html">

                            <a href="../../../recall_scene/lnline_sdk/tech_doc.html">


                                技术文档

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.6.3.1" data-path="../../../recall_scene/lnline_sdk/tech_doc/info_statement.html">

                                    <a href="../../../recall_scene/lnline_sdk/tech_doc/info_statement.html">


                                        小鹅通SDK开发者声明

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.2" data-path="../../../recall_scene/lnline_sdk/tech_doc/info_protect_new.html">

                                    <a href="../../../recall_scene/lnline_sdk/tech_doc/info_protect_new.html">


                                        小鹅通SDK个人信息保护政策

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.3" data-path="../../../recall_scene/lnline_sdk/guide.html">

                                    <a href="../../../recall_scene/lnline_sdk/guide.html">


                                        开发指南

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.4" data-path="../../../recall_scene/lnline_sdk/sdk_login.html">

                                    <a href="../../../recall_scene/lnline_sdk/sdk_login.html">


                                        原生SDK登录态

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.5" data-path="../../../recall_scene/lnline_sdk/android_sdk.html">

                                    <a href="../../../recall_scene/lnline_sdk/android_sdk.html">


                                        Android-SDK 接入

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.6" data-path="../../../recall_scene/lnline_sdk/ios_sdk.html">

                                    <a href="../../../recall_scene/lnline_sdk/ios_sdk.html">


                                        iOS-SDK 接入

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.7" data-path="../../../recall_scene/lnline_sdk/h5_sdk.html">

                                    <a href="../../../recall_scene/lnline_sdk/h5_sdk.html">


                                        WebSDK接入

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.8" data-path="../../../recall_scene/lnline_sdk/mini_sdk.html">

                                    <a href="../../../recall_scene/lnline_sdk/mini_sdk.html">


                                        小程序接入

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.9" data-path="../../../recall_scene/lnline_sdk/mini_sdk_pay.html">

                                    <a href="../../../recall_scene/lnline_sdk/mini_sdk_pay.html">


                                        小程序支付接入

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.10" data-path="../../../recall_scene/lnline_sdk/algorithm.html">

                                    <a href="../../../recall_scene/lnline_sdk/algorithm.html">


                                        代币支付签名算法

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.11" data-path="../../../recall_scene/lnline_sdk/h5_sdk_detail.html">

                                    <a href="../../../recall_scene/lnline_sdk/h5_sdk_detail.html">


                                        SDK&H5账号详解

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.12" data-path="../../../recall_scene/lnline_sdk/ios_sdk_release.html">

                                    <a href="../../../recall_scene/lnline_sdk/ios_sdk_release.html">


                                        发布日志（iOS）

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.6.3.13" data-path="../../../recall_scene/lnline_sdk/android_sdk_release.html">

                                    <a href="../../../recall_scene/lnline_sdk/android_sdk_release.html">


                                        发布日志（Android）

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.6.4" data-path="../../../recall_scene/lnline_sdk/question.html">

                            <a href="../../../recall_scene/lnline_sdk/question.html">


                                常见问题

                            </a>



                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.7" data-path="../../../news_push.html">

                    <a href="../../../news_push.html">


                        消息推送

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.7.1" data-path="../../news_push/intruduction.html">

                            <a href="../../news_push/intruduction.html">


                                使用说明

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.2" data-path="../../news_push/encryp_decryp.html">

                            <a href="../../news_push/encryp_decryp.html">


                                消息加/解密

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.3" data-path="../../news_push/call_back_url_check.html">

                            <a href="../../news_push/call_back_url_check.html">


                                消息接收URL校验说明

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.4" data-path="../../news_push/order_change_state.html">

                            <a href="../../news_push/order_change_state.html">


                                订单状态推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.5" data-path="../../news_push/phone_update.html">

                            <a href="../../news_push/phone_update.html">


                                用户手机号变更推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.6" data-path="../../news_push/status_update.html">

                            <a href="../../news_push/status_update.html">


                                用户状态变更推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.7" data-path="../../news_push/point_push.html">

                            <a href="../../news_push/point_push.html">


                                积分变更消息推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.8" data-path="../../news_push/goods_change_push.html">

                            <a href="../../news_push/goods_change_push.html">


                                商品变更推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.9" data-path="../../news_push/examination_push.html">

                            <a href="../../news_push/examination_push.html">


                                考试消息推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.10" data-path="../../news_push/activity_sign_push.html">

                            <a href="../../news_push/activity_sign_push.html">


                                活动签到推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.11" data-path="../../news_push/order_push.html">

                            <a href="../../news_push/order_push.html">


                                订单支付推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.12" data-path="../../news_push/account_merge_push.html">

                            <a href="../../news_push/account_merge_push.html">


                                账号合并推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.13" data-path="../../news_push/user_register.html">

                            <a href="../../news_push/user_register.html">


                                用户注册推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.14" data-path="../../news_push/coupon_push.html">

                            <a href="../../news_push/coupon_push.html">


                                优惠券消息推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.15" data-path="../../news_push/info_collect_push.html">

                            <a href="../../news_push/info_collect_push.html">


                                信息采集推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.16" data-path="../../news_push/role_change_push.html">

                            <a href="../../news_push/role_change_push.html">


                                权益变更推送

                            </a>



                        </li>

                        <li class="chapter " data-level="1.7.17" data-path="../../news_push/user_tag_change.html">

                            <a href="../../news_push/user_tag_change.html">


                                用户标签变更消息推送

                            </a>



                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.8" data-path="../../../api_list.html">

                    <a href="../../../api_list.html">


                        API列表

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.8.1" data-path="../../get_access_token.html">

                            <a href="../../get_access_token.html">


                                获取access_token（含代码示例）

                            </a>



                        </li>

                        <li class="chapter " data-level="1.8.2" data-path="../../user.html">

                            <a href="../../user.html">


                                用户管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.2.1" data-path="../instruction.html">

                                    <a href="../instruction.html">


                                        使用说明

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.2" data-path="../register.html">

                                    <a href="../register.html">


                                        注册新用户

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.3" data-path="../update_user_instruction.html">

                                    <a href="../update_user_instruction.html">


                                        更新用户信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.4" data-path="../get_user_info_batch_2.html">

                                    <a href="../get_user_info_batch_2.html">


                                        获取用户列表2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.5" data-path="../get_user_info_batch.html">

                                    <a href="../get_user_info_batch.html">


                                        获取用户列表1.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.6" data-path="../get_user_info.html">

                                    <a href="../get_user_info.html">


                                        查询单个用户信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.7" data-path="../get_user_info_batch_by_user_id.html">

                                    <a href="../get_user_info_batch_by_user_id.html">


                                        批量查询用户信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.8" data-path="../get_user_id_and_consumption_info.html">

                                    <a href="../get_user_id_and_consumption_info.html">


                                        获取用户消费信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.9" data-path="../get_course_student_list.html">

                                    <a href="../get_course_student_list.html">


                                        获取课程学员列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../get_camp_pro_student_list.html">

                                    <a href="../get_camp_pro_student_list.html">


                                        获取训练营pro学员列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../get_svip_user_list.html">

                                    <a href="../get_svip_user_list.html">


                                        获取会员卡用户信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../get_user_label.html">

                                    <a href="../get_user_label.html">


                                        查询单个用户标签列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../get_user_merge_info_by_user_id.html">

                                    <a href="../get_user_merge_info_by_user_id.html">


                                        查询用户合并记录

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../get_tag_list.html">

                                    <a href="../get_tag_list.html">


                                        获取标签列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.15" data-path="../create_modify_tag.html">

                                    <a href="../create_modify_tag.html">


                                        添加/修改标签

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.2.16" data-path="../create_modify_user_tag.html">

                                    <a href="../create_modify_user_tag.html">


                                        添加/修改用户标签

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="list.html">

                                    <a href="list.html">


                                        获取用户反馈列表

                                    </a>



                                </li>

                                <li class="chapter active" data-level="********" data-path="reply_list.html">

                                    <a href="reply_list.html">


                                        获取用户反馈回复列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="reply.html">

                                    <a href="reply.html">


                                        回复用户反馈

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.3" data-path="../../learn_data.html">

                            <a href="../../learn_data.html">


                                学习数据管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.3.1" data-path="../get_learn_record.html">

                                    <a href="../get_learn_record.html">


                                        获取学习记录列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.3.2" data-path="../get_learn_record_daily.html">

                                    <a href="../get_learn_record_daily.html">


                                        获取每日学习记录

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.3.3" data-path="../get_learn_record_by_resource_id.html">

                                    <a href="../get_learn_record_by_resource_id.html">


                                        获取指定资源学习记录信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.3.4" data-path="../get_camp_user_progress.html">

                                    <a href="../get_camp_user_progress.html">


                                        获取营期下用户的学习情况

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.3.5" data-path="../get_camp_pro_user_task_list.html">

                                    <a href="../get_camp_pro_user_task_list.html">


                                        获取训练营pro学员任务详情

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.4" data-path="../../live_broad.html">

                            <a href="../../live_broad.html">


                                直播管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.4.1" data-path="../../live_broad/xe_alive_list_get.html">

                                    <a href="../../live_broad/xe_alive_list_get.html">


                                        获取直播列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.2" data-path="../../live_broad/xe_alive_list_getv2.html">

                                    <a href="../../live_broad/xe_alive_list_getv2.html">


                                        获取直播列表2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.3" data-path="../../live_broad/xe_alive_list_by_ids.html">

                                    <a href="../../live_broad/xe_alive_list_by_ids.html">


                                        批量查询指定直播间列表信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.4" data-path="../../big_class/xe_big_class_list_get.html">

                                    <a href="../../big_class/xe_big_class_list_get.html">


                                        获取班课列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.5" data-path="../../live_broad/sign_in_record_list.html">

                                    <a href="../../live_broad/sign_in_record_list.html">


                                        获取签到列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.6" data-path="../../live_broad/sign_in_trainee_list.html">

                                    <a href="../../live_broad/sign_in_trainee_list.html">


                                        获取签到学员列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.7" data-path="../../live_broad/redpacket_list.html">

                                    <a href="../../live_broad/redpacket_list.html">


                                        获取直播红包列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.8" data-path="../../live_broad/redpacket_record.html">

                                    <a href="../../live_broad/redpacket_record.html">


                                        获取红包领取记录

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.9" data-path="../../live_broad/overview.html">

                                    <a href="../../live_broad/overview.html">


                                        获取直播数据概览

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.10" data-path="../../live_broad/xe_alive_detail_get.html">

                                    <a href="../../live_broad/xe_alive_detail_get.html">


                                        获取直播详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.11" data-path="../../live_broad/xe_alive_detail_get_2.html">

                                    <a href="../../live_broad/xe_alive_detail_get_2.html">


                                        获取直播详情2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.12" data-path="../../live_broad/redpacket_dataview.html">

                                    <a href="../../live_broad/redpacket_dataview.html">


                                        获取红包数据概况

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.13" data-path="../../live_broad/xe_alive_msg_list_get.html">

                                    <a href="../../live_broad/xe_alive_msg_list_get.html">


                                        获取直播间讨论区消息列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.14" data-path="../../live_broad/xe_alive_msg_list_get2.html">

                                    <a href="../../live_broad/xe_alive_msg_list_get2.html">


                                        获取直播间讨论区消息列表2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.15" data-path="../../live_broad/xe_alive_push_record_get.html">

                                    <a href="../../live_broad/xe_alive_push_record_get.html">


                                        获取直播间多段推流记录

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.16" data-path="../../live_broad/xe_alive_live_create.html">

                                    <a href="../../live_broad/xe_alive_live_create.html">


                                        创建直播

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.17" data-path="../../live_broad/xe_alive_live_update.html">

                                    <a href="../../live_broad/xe_alive_live_update.html">


                                        编辑直播

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.18" data-path="../../live_broad/xe_alive_shelf_state_update.html">

                                    <a href="../../live_broad/xe_alive_shelf_state_update.html">


                                        上架/下架直播

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.19" data-path="../../live_broad/xe_alive_live_delete.html">

                                    <a href="../../live_broad/xe_alive_live_delete.html">


                                        删除直播

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.20" data-path="../../live_broad/xe_alive_user_list.html">

                                    <a href="../../live_broad/xe_alive_user_list.html">


                                        获取直播间用户列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.21" data-path="../../live_broad/xe_alive_invite_plan_list.html">

                                    <a href="../../live_broad/xe_alive_invite_plan_list.html">


                                        获取直播邀约计划列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.4.22" data-path="../../live_broad/xe_alive_invite_user_list.html">

                                    <a href="../../live_broad/xe_alive_invite_user_list.html">


                                        获取直播邀约用户列表

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.5" data-path="../../product.html">

                            <a href="../../product.html">


                                商品管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.5.1" data-path="../../product/get_goods_relations_2.html">

                                    <a href="../../product/get_goods_relations_2.html">


                                        查询商品列表2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.2" data-path="../../product/get_goods_list.html">

                                    <a href="../../product/get_goods_list.html">


                                        查询商品列表1.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.3" data-path="../../product/get_goods_relations4.0.0.html">

                                    <a href="../../product/get_goods_relations4.0.0.html">


                                        查询组合课资源列表4.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.4" data-path="../../product/get_goods_relations3.0.0.html">

                                    <a href="../../product/get_goods_relations3.0.0.html">


                                        查询组合课资源列表3.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.5" data-path="../../product/camp_task_list.html">

                                    <a href="../../product/camp_task_list.html">


                                        获取训练营营期任务

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.6" data-path="../../product/product_info_2.html">

                                    <a href="../../product/product_info_2.html">


                                        商品详情2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.7" data-path="../../product/product_info.html">

                                    <a href="../../product/product_info.html">


                                        商品详情1.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.8" data-path="../../product/course_relations.html">

                                    <a href="../../product/course_relations.html">


                                        获取课程关联的助学工具

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.9" data-path="../../product/commerce_list.html">

                                    <a href="../../product/commerce_list.html">


                                        获取直播带货商品列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.10" data-path="../../product/get_goods_comment_list.html">

                                    <a href="../../product/get_goods_comment_list.html">


                                        获取实物商品评论列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.11" data-path="../../product/add_stock.html">

                                    <a href="../../product/add_stock.html">


                                        商品增加库存

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.12" data-path="../../product/reduce_stock.html">

                                    <a href="../../product/reduce_stock.html">


                                        商品减少库存

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.13" data-path="../../product/insert_ent_goods.html">

                                    <a href="../../product/insert_ent_goods.html">


                                        新建实物商品

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.14" data-path="../../product/save_ent_goods.html">

                                    <a href="../../product/save_ent_goods.html">


                                        编辑实物商品

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.15" data-path="../../product/delete_ent_goods.html">

                                    <a href="../../product/delete_ent_goods.html">


                                        删除实物商品

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.16" data-path="../../product/get_ent_good_category_list.html">

                                    <a href="../../product/get_ent_good_category_list.html">


                                        获取实物商品类目列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.17" data-path="../../product/get_search_goods.html">

                                    <a href="../../product/get_search_goods.html">


                                        获取C端搜索商品列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.18" data-path="../../product/course_column_list.html">

                                    <a href="../../product/course_column_list.html">


                                        查询专栏、会员、大专栏目录小节列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.19" data-path="../../product/course_batch_remove.html">

                                    <a href="../../product/course_batch_remove.html">


                                        课程、专栏、大专栏、视频删除

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.20" data-path="../../product/course_batch_update_state.html">

                                    <a href="../../product/course_batch_update_state.html">


                                        课程、专栏、大专栏、视频上下架

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.21" data-path="../../product/course_course_list.html">

                                    <a href="../../product/course_course_list.html">


                                        查询课程列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.22" data-path="../../product/course_chapter_get.html">

                                    <a href="../../product/course_chapter_get.html">


                                        查询课程目录小节

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.5.23" data-path="../../product/course_camp_term_detail.html">

                                    <a href="../../product/course_camp_term_detail.html">


                                        查询训练营目录小节

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.6" data-path="../../resource_tags.html">

                            <a href="../../resource_tags.html">


                                商品分组管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.6.1" data-path="../../product/resource_tags_list.html">

                                    <a href="../../product/resource_tags_list.html">


                                        获取店铺商品分组列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.6.2" data-path="../../product/resource_tags_resources.html">

                                    <a href="../../product/resource_tags_resources.html">


                                        获取商品分组内商品列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.6.3" data-path="../../product/resource_tags_save.html">

                                    <a href="../../product/resource_tags_save.html">


                                        批量新建/更新商品分组

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.6.4" data-path="../../product/resource_tags_delete.html">

                                    <a href="../../product/resource_tags_delete.html">


                                        删除商品分组

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.7" data-path="../../material.html">

                            <a href="../../material.html">


                                素材管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.7.1" data-path="../../material/list.html">

                                    <a href="../../material/list.html">


                                        获取素材列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.7.2" data-path="../../material/category_list.html">

                                    <a href="../../material/category_list.html">


                                        获取素材分组列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.7.3" data-path="../../material/category_edit.html">

                                    <a href="../../material/category_edit.html">


                                        素材分组编辑

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.7.4" data-path="../../material/category_move.html">

                                    <a href="../../material/category_move.html">


                                        素材分组移动

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.7.5" data-path="../../material/category_delete.html">

                                    <a href="../../material/category_delete.html">


                                        素材分组删除

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.8" data-path="../../order.html">

                            <a href="../../order.html">


                                订单管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.8.1" data-path="../../order/get_order_list_1.0.2.html">

                                    <a href="../../order/get_order_list_1.0.2.html">


                                        获取订单列表2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.2" data-path="../../order/get_order_details_2.html">

                                    <a href="../../order/get_order_details_2.html">


                                        获取订单详情2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.3" data-path="../../order/get_order_list_1.0.1.html">

                                    <a href="../../order/get_order_list_1.0.1.html">


                                        获取订单列表1.1

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.4" data-path="../../order/get_order_list_1.0.0.html">

                                    <a href="../../order/get_order_list_1.0.0.html">


                                        获取订单列表1.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.5" data-path="../../order/get_user_order.html">

                                    <a href="../../order/get_user_order.html">


                                        查询用户订单列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.6" data-path="../../order/get_order_details.html">

                                    <a href="../../order/get_order_details.html">


                                        获取订单详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.7" data-path="../../order/get_order_gift_info.html">

                                    <a href="../../order/get_order_gift_info.html">


                                        获取买赠订单信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.8" data-path="../../order/change_price.html">

                                    <a href="../../order/change_price.html">


                                        待付款订单改价

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.9" data-path="../../order/get_aftersale_list.html">

                                    <a href="../../order/get_aftersale_list.html">


                                        获取售后订单列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.10" data-path="../../order/get_aftersale_detail.html">

                                    <a href="../../order/get_aftersale_detail.html">


                                        获取售后订单详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.8.11" data-path="../../order/order_refund.html">

                                    <a href="../../order/order_refund.html">


                                        订单退款

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.9" data-path="../../pay_through.html">

                            <a href="../../pay_through.html">


                                投诉管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.9.1" data-path="../../pay_through/complaint_record_list.html">

                                    <a href="../../pay_through/complaint_record_list.html">


                                        商户号投诉列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.9.2" data-path="../../pay_through/get_complaint_detail.html">

                                    <a href="../../pay_through/get_complaint_detail.html">


                                        查询投诉单详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.9.3" data-path="../../pay_through/response_complaint.html">

                                    <a href="../../pay_through/response_complaint.html">


                                        回复用户投诉

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.9.4" data-path="../../pay_through/get_complaint_negotiation_historys.html">

                                    <a href="../../pay_through/get_complaint_negotiation_historys.html">


                                        查询投诉单协商历史

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.9.5" data-path="../../pay_through/complete_complaint.html">

                                    <a href="../../pay_through/complete_complaint.html">


                                        反馈投诉处理

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.9.6" data-path="../../pay_through/get_complaint_info.html">

                                    <a href="../../pay_through/get_complaint_info.html">


                                        获取交易投诉单详情

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.10" data-path="../../buy.html">

                            <a href="../../buy.html">


                                订购管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.10.1" data-path="../../start_class/xe_order_delivery_2.0.html">

                                    <a href="../../start_class/xe_order_delivery_2.0.html">


                                        用户开通产品包权益2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.2" data-path="../../start_class/xe_order_delivery.html">

                                    <a href="../../start_class/xe_order_delivery.html">


                                        用户开通产品包权益

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.3" data-path="../../buy/xe_resource_purchase_get.html">

                                    <a href="../../buy/xe_resource_purchase_get.html">


                                        查询用户订购资源列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.4" data-path="../../buy/xe_product_available.html">

                                    <a href="../../buy/xe_product_available.html">


                                        用户购买关系查询

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.5" data-path="../../buy/xe_user_asset_check2.html">

                                    <a href="../../buy/xe_user_asset_check2.html">


                                        查询用户资源权益2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.6" data-path="../../buy/xe_user_asset_check.html">

                                    <a href="../../buy/xe_user_asset_check.html">


                                        查询用户资源权益1.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.7" data-path="../../buy/xe_purchase_delete.html">

                                    <a href="../../buy/xe_purchase_delete.html">


                                        取消订购

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.8" data-path="../../start_class/xe_order_delivery_create_task.html">

                                    <a href="../../start_class/xe_order_delivery_create_task.html">


                                        批量开通用户产品包权益-创建任务

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.9" data-path="../../start_class/xe_order_delivery_get_task_list.html">

                                    <a href="../../start_class/xe_order_delivery_get_task_list.html">


                                        批量开通用户产品包权益-查询任务列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.10.10" data-path="../../start_class/xe_order_delivery_get_task_detail.html">

                                    <a href="../../start_class/xe_order_delivery_get_task_detail.html">


                                        批量开通用户产品包权益-查询任务详情

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.11" data-path="../../logistics.html">

                            <a href="../../logistics.html">


                                物流管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.11.1" data-path="../../logistics/material_object_order.html">

                                    <a href="../../logistics/material_object_order.html">


                                        普通实物单发货(即将下线)

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.11.2" data-path="../../logistics/get_courier_company_list.html">

                                    <a href="../../logistics/get_courier_company_list.html">


                                        获取物流公司信息列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.11.3" data-path="../../logistics/send_all_resource.html">

                                    <a href="../../logistics/send_all_resource.html">


                                        订单全部商品发货

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.11.4" data-path="../../logistics/send_part_resource.html">

                                    <a href="../../logistics/send_part_resource.html">


                                        订单部分商品发货

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.11.5" data-path="../../logistics/modify_logistics.html">

                                    <a href="../../logistics/modify_logistics.html">


                                        修改已发货订单的物流信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.11.6" data-path="../../logistics/get_freight_template.html">

                                    <a href="../../logistics/get_freight_template.html">


                                        获取运费模板列表

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.12" data-path="../../promoter.html">

                            <a href="../../promoter.html">


                                推广员管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.12.1" data-path="../../promoter/xe_distributor_list_get.html">

                                    <a href="../../promoter/xe_distributor_list_get.html">


                                        查询推广员列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.2" data-path="../../promoter/xe_distributor_sub_customer.html">

                                    <a href="../../promoter/xe_distributor_sub_customer.html">


                                        查询推广员客户列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.3" data-path="../../promoter/xe_distributor_sub_list_get.html">

                                    <a href="../../promoter/xe_distributor_sub_list_get.html">


                                        查询推广员的下级推广员列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.4" data-path="../../promoter/xe_distributor_info_get.html">

                                    <a href="../../promoter/xe_distributor_info_get.html">


                                        查询指定推广员的信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.5" data-path="../../promoter/xe_distributor_customer_get.html">

                                    <a href="../../promoter/xe_distributor_customer_get.html">


                                        查询客户所属推广员

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.6" data-path="../../promoter/xe_distributor_sub_customer_get.html">

                                    <a href="../../promoter/xe_distributor_sub_customer_get.html">


                                        查询推广员名下新增/过期客户

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.7" data-path="../../promoter/xe_distributor_member_add.html">

                                    <a href="../../promoter/xe_distributor_member_add.html">


                                        新增推广员

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.8" data-path="../../promoter/xe_distributor_member_bind.html">

                                    <a href="../../promoter/xe_distributor_member_bind.html">


                                        推广员绑定客户

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.9" data-path="../../promoter/xe_distributor_member_change.html">

                                    <a href="../../promoter/xe_distributor_member_change.html">


                                        修改/解除绑定关系

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.10" data-path="../../promoter/xe_distributor_superior_set.html">

                                    <a href="../../promoter/xe_distributor_superior_set.html">


                                        设置推广员上级

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.11" data-path="../../promoter/xe_distributor_order_get.html">

                                    <a href="../../promoter/xe_distributor_order_get.html">


                                        推广订单详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.12" data-path="../../promoter/xe_distributor_performance_get.html">

                                    <a href="../../promoter/xe_distributor_performance_get.html">


                                        按日期查询推广员的收益和新增加的下级

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.12.13" data-path="../../promoter/xe_distributor_orders_get.html">

                                    <a href="../../promoter/xe_distributor_orders_get.html">


                                        推广订单详情(支持购物车订单返回多条记录)

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.13" data-path="../../info_collect.html">

                            <a href="../../info_collect.html">


                                信息采集/表单管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.13.1" data-path="../../info_collect/xe_information_form_get.html">

                                    <a href="../../info_collect/xe_information_form_get.html">


                                        获取信息采集列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.13.2" data-path="../../info_collect/xe_information_form_result_get.html">

                                    <a href="../../info_collect/xe_information_form_result_get.html">


                                        获取单个信息采集结果

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.13.3" data-path="../../info_collect/xe_information_user_result_get.html">

                                    <a href="../../info_collect/xe_information_user_result_get.html">


                                        获取用户信息采集结果

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.13.4" data-path="../../info_collect/xe_information_user_form_get.html">

                                    <a href="../../info_collect/xe_information_user_form_get.html">


                                        获取用户填写的信息采集数据

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.13.5" data-path="../../info_collect/form_get_form_list_by_user_id.html">

                                    <a href="../../info_collect/form_get_form_list_by_user_id.html">


                                        获取用户填写表单的信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.13.6" data-path="../../info_collect/xe_information_form_order_get.html">

                                    <a href="../../info_collect/xe_information_form_order_get.html">


                                        批量获取信息采集关联订单

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.14" data-path="../../coupon.html">

                            <a href="../../coupon.html">


                                营销码券管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.14.1" data-path="../../coupon/coupon_list.html">

                                    <a href="../../coupon/coupon_list.html">


                                        优惠券列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.2" data-path="../../coupon/coupon_detail_2.0.html">

                                    <a href="../../coupon/coupon_detail_2.0.html">


                                        获取优惠券详情2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.3" data-path="../../coupon/xe_coupon_info.html">

                                    <a href="../../coupon/xe_coupon_info.html">


                                        获取优惠券详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.4" data-path="../../coupon/coupon_user_list.html">

                                    <a href="../../coupon/coupon_user_list.html">


                                        获取优惠券领券用户

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.5" data-path="../../coupon/coupon_receive_2.0.html">

                                    <a href="../../coupon/coupon_receive_2.0.html">


                                        发放优惠券2.0

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.6" data-path="../../coupon/xe_user_coupon_add.html">

                                    <a href="../../coupon/xe_user_coupon_add.html">


                                        发放优惠券

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.7" data-path="../../coupon/coupon_set_use.html">

                                    <a href="../../coupon/coupon_set_use.html">


                                        优惠券核销

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.8" data-path="../../coupon/promo_code_list.html">

                                    <a href="../../coupon/promo_code_list.html">


                                        获取优惠码列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.9" data-path="../../coupon/promo_code_record.html">

                                    <a href="../../coupon/promo_code_record.html">


                                        获取优惠码码库列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.10" data-path="../../coupon/promo_code_effect_data.html">

                                    <a href="../../coupon/promo_code_effect_data.html">


                                        获取优惠码效果数据

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.11" data-path="../../coupon/promo_code_create_code.html">

                                    <a href="../../coupon/promo_code_create_code.html">


                                        创建优惠码

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.12" data-path="../../coupon/promo_code_add_codestore.html">

                                    <a href="../../coupon/promo_code_add_codestore.html">


                                        优惠码添加库存

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.13" data-path="../../coupon/promo_code_invalid.html">

                                    <a href="../../coupon/promo_code_invalid.html">


                                        优惠码失效

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.14" data-path="../../coupon/promo_code_edit.html">

                                    <a href="../../coupon/promo_code_edit.html">


                                        编辑优惠码

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.15" data-path="../../coupon/promo_code_delete.html">

                                    <a href="../../coupon/promo_code_delete.html">


                                        优惠码删除

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.16" data-path="../../coupon/redeem_code_list.html">

                                    <a href="../../coupon/redeem_code_list.html">


                                        获取兑换码码库列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.17" data-path="../../coupon/redeem_code_append.html">

                                    <a href="../../coupon/redeem_code_append.html">


                                        追加兑换码库存

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.18" data-path="../../coupon/redeem_code_create.html">

                                    <a href="../../coupon/redeem_code_create.html">


                                        创建兑换码

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.19" data-path="../../coupon/redeem_code_edit.html">

                                    <a href="../../coupon/redeem_code_edit.html">


                                        编辑兑换码

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.20" data-path="../../coupon/redeem_code_manual_stop.html">

                                    <a href="../../coupon/redeem_code_manual_stop.html">


                                        兑换码失效

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.21" data-path="../../coupon/redeem_code_exchange.html">

                                    <a href="../../coupon/redeem_code_exchange.html">


                                        用户使用兑换码

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.22" data-path="../../coupon/redeem_code_batch_list.html">

                                    <a href="../../coupon/redeem_code_batch_list.html">


                                        获取兑换码批次列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.14.23" data-path="../../coupon/redeem_code_storage_disable.html">

                                    <a href="../../coupon/redeem_code_storage_disable.html">


                                        作废单个兑换码

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.15" data-path="../../team_buy.html">

                            <a href="../../team_buy.html">


                                拼团管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.15.1" data-path="../../team_buy/list.html">

                                    <a href="../../team_buy/list.html">


                                        获取拼团活动列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.15.2" data-path="../../team_buy/get_details.html">

                                    <a href="../../team_buy/get_details.html">


                                        获取单个拼团明细列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../../team_buy/get_user_details.html">

                                    <a href="../../team_buy/get_user_details.html">


                                        拼团明细-参团人员详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../../team_buy/get_data_grip.html">

                                    <a href="../../team_buy/get_data_grip.html">


                                        单个拼团活动概况数据

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.16" data-path="../../point.html">

                            <a href="../../point.html">


                                积分管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="********" data-path="../../point/user_flow_details.html">

                                    <a href="../../point/user_flow_details.html">


                                        查询用户积分日志

                                    </a>



                                </li>

                                <li class="chapter " data-level="********" data-path="../../point/sigle_user_account_info.html">

                                    <a href="../../point/sigle_user_account_info.html">


                                        获取单个用户积分信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.3" data-path="../../point/user_point_list.html">

                                    <a href="../../point/user_point_list.html">


                                        批量获取用户积分列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.4" data-path="../../point/shop_point_summary.html">

                                    <a href="../../point/shop_point_summary.html">


                                        获取店铺积分信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.5" data-path="../../point/distribute_point.html">

                                    <a href="../../point/distribute_point.html">


                                        批量发放积分

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.6" data-path="../../point/deduct_point.html">

                                    <a href="../../point/deduct_point.html">


                                        批量扣减积分

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.7" data-path="../../point/unfreeze_point.html">

                                    <a href="../../point/unfreeze_point.html">


                                        解冻冻结积分

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.8" data-path="../../point/set_expire_time.html">

                                    <a href="../../point/set_expire_time.html">


                                        设置店铺积分过期时间

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.16.9" data-path="../../point/explain.html">

                                    <a href="../../point/explain.html">


                                        业务类型说明

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.17" data-path="../../page_stat.html">

                            <a href="../../page_stat.html">


                                页面统计管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.17.1" data-path="../../page_stat/save_page_stat.html">

                                    <a href="../../page_stat/save_page_stat.html">


                                        新建页面统计

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.2" data-path="../../page_stat/edit_page_stat.html">

                                    <a href="../../page_stat/edit_page_stat.html">


                                        编辑页面统计

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.3" data-path="../../page_stat/get_page_stat_list.html">

                                    <a href="../../page_stat/get_page_stat_list.html">


                                        获取页面统计列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.4" data-path="../../page_stat/get_page_stat_detail.html">

                                    <a href="../../page_stat/get_page_stat_detail.html">


                                        获取页面统计详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.5" data-path="../../page_stat/page_stat_order_list.html">

                                    <a href="../../page_stat/page_stat_order_list.html">


                                        获取页面统计订单列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.6" data-path="../../page_stat/set_new_channels.html">

                                    <a href="../../page_stat/set_new_channels.html">


                                        新建投放渠道

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.17.7" data-path="../../page_stat/get_all_channels.html">

                                    <a href="../../page_stat/get_all_channels.html">


                                        获取投放渠道列表

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.18" data-path="../../eclock.html">

                            <a href="../../eclock.html">


                                打卡管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.18.1" data-path="../../eclock/activity_list.html">

                                    <a href="../../eclock/activity_list.html">


                                        获取打卡列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.2" data-path="../../eclock/activity_actor_list.html">

                                    <a href="../../eclock/activity_actor_list.html">


                                        获取打卡参与用户

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.3" data-path="../../eclock/activity_actor_diary.html">

                                    <a href="../../eclock/activity_actor_diary.html">


                                        获取指定学员活动日记列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.4" data-path="../../eclock/activity_zan.html">

                                    <a href="../../eclock/activity_zan.html">


                                        获取活动点赞列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.5" data-path="../../eclock/activity_actor_diary_zan.html">

                                    <a href="../../eclock/activity_actor_diary_zan.html">


                                        获取指定学员被点赞总数和列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.6" data-path="../../eclock/activity_actor_diary_comment.html">

                                    <a href="../../eclock/activity_actor_diary_comment.html">


                                        获取用户日记被点评/评论列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.7" data-path="../../eclock/get_elock_homework_list.html">

                                    <a href="../../eclock/get_elock_homework_list.html">


                                        获取指定打卡的作业列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.8" data-path="../../eclock/get_activity_user_diary_list.html">

                                    <a href="../../eclock/get_activity_user_diary_list.html">


                                        获取学员的打卡日记列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.18.9" data-path="../../eclock/get_activity_related_resource.html">

                                    <a href="../../eclock/get_activity_related_resource.html">


                                        获取打卡关联的课程信息

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.19" data-path="../../practice.html">

                            <a href="../../practice.html">


                                练习管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.19.1" data-path="../../practice/practice_list.html">

                                    <a href="../../practice/practice_list.html">


                                        获取练习基本信息列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.19.2" data-path="../../practice/practice_result_list.html">

                                    <a href="../../practice/practice_result_list.html">


                                        获取练习结果列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.19.3" data-path="../../practice/practice_detail.html">

                                    <a href="../../practice/practice_detail.html">


                                        获取练习详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.19.4" data-path="../../practice/user_practice_detail.html">

                                    <a href="../../practice/user_practice_detail.html">


                                        获取学员练习答题详情

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.20" data-path="../../exercise.html">

                            <a href="../../exercise.html">


                                作业管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.20.1" data-path="../../exercise/exercise_book_list.html">

                                    <a href="../../exercise/exercise_book_list.html">


                                        获取作业本列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.20.2" data-path="../../exercise/exercise_list.html">

                                    <a href="../../exercise/exercise_list.html">


                                        获取作业基本信息列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.20.3" data-path="../../exercise/exercise_result_list.html">

                                    <a href="../../exercise/exercise_result_list.html">


                                        获取作业结果列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.20.4" data-path="../../exercise/exercise_detail.html">

                                    <a href="../../exercise/exercise_detail.html">


                                        获取作业详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.20.5" data-path="../../exercise/user_exercise_detail.html">

                                    <a href="../../exercise/user_exercise_detail.html">


                                        获取学员作业答题详情

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.21" data-path="../../certificate.html">

                            <a href="../../certificate.html">


                                证书管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.21.1" data-path="../../certificate/certificate_grant.html">

                                    <a href="../../certificate/certificate_grant.html">


                                        证书发放

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.22" data-path="../../community.html">

                            <a href="../../community.html">


                                圈子管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.22.1" data-path="../../community/xe_community_user_list.html">

                                    <a href="../../community/xe_community_user_list.html">


                                        获取用户参与的圈子

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.22.2" data-path="../../community/xe_community_user_list_2.html">

                                    <a href="../../community/xe_community_user_list_2.html">


                                        获取圈子成员列表

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.23" data-path="../../examination.html">

                            <a href="../../examination.html">


                                考试管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.23.1" data-path="../../examination/examination_list.html">

                                    <a href="../../examination/examination_list.html">


                                        获取考试列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.23.2" data-path="../../examination/examination_detail.html">

                                    <a href="../../examination/examination_detail.html">


                                        获取考试详情

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.23.3" data-path="../../examination/get_examination_list.html">

                                    <a href="../../examination/get_examination_list.html">


                                        获取考试结果列表信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.23.4" data-path="../../examination/get_examination_result.html">

                                    <a href="../../examination/get_examination_result.html">


                                        获取考试结果详细信息

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.24" data-path="../../activity.html">

                            <a href="../../activity.html">


                                活动管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.24.1" data-path="../../activity/get_activity_list.html">

                                    <a href="../../activity/get_activity_list.html">


                                        获取活动列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.24.2" data-path="../../activity/get_attendance_list.html">

                                    <a href="../../activity/get_attendance_list.html">


                                        获取签到列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.24.3" data-path="../../activity/get_enroll_list.html">

                                    <a href="../../activity/get_enroll_list.html">


                                        获取活动报名列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.24.4" data-path="../../activity/get_sign_info.html">

                                    <a href="../../activity/get_sign_info.html">


                                        获取活动签到信息

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.25" data-path="../../organization.html">

                            <a href="../../organization.html">


                                组织管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.25.1" data-path="../../organization/get_staff_list.html">

                                    <a href="../../organization/get_staff_list.html">


                                        获取员工列表

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.2" data-path="../../organization/get_staff_result.html">

                                    <a href="../../organization/get_staff_result.html">


                                        获取员工详细信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.3" data-path="../../organization/add_save_staff_info.html">

                                    <a href="../../organization/add_save_staff_info.html">


                                        添加或者保存员工信息

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.4" data-path="../../organization/user_add_auth.html">

                                    <a href="../../organization/user_add_auth.html">


                                        启用员工

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.5" data-path="../../organization/stop_staff_info.html">

                                    <a href="../../organization/stop_staff_info.html">


                                        停用员工

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.6" data-path="../../organization/user_remove.html">

                                    <a href="../../organization/user_remove.html">


                                        删除员工

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.7" data-path="../../organization/get_uid_by_account.html">

                                    <a href="../../organization/get_uid_by_account.html">


                                        根据员工账号获取用户ID

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.8" data-path="../../organization/get_organization_structure.html">

                                    <a href="../../organization/get_organization_structure.html">


                                        获取组织结构树

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.9" data-path="../../organization/add_update_organization_structure.html">

                                    <a href="../../organization/add_update_organization_structure.html">


                                        添加或修改组织架构

                                    </a>



                                </li>

                                <li class="chapter " data-level="1.8.25.10" data-path="../../organization/remove_organization_structure.html">

                                    <a href="../../organization/remove_organization_structure.html">


                                        移除组织架构

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.26" data-path="../../micro_page.html">

                            <a href="../../micro_page.html">


                                微页面管理

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.26.1" data-path="../../micro_page/xe_micro_list_get.html">

                                    <a href="../../micro_page/xe_micro_list_get.html">


                                        获取微页面列表

                                    </a>



                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.27" data-path="../../college.html">

                            <a href="../../college.html">


                                企学院专用(不对外开放)

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.27.1" data-path="../../college_student.html">

                                    <a href="../../college_student.html">


                                        学员管理（限企学院）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.27.1.1" data-path="../../college_student/employ_list.html">

                                            <a href="../../college_student/employ_list.html">


                                                企学院获取学员列表

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.2" data-path="../../college_student/employ_add.html">

                                            <a href="../../college_student/employ_add.html">


                                                企学院添加学员

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.3" data-path="../../college_student/employ_update.html">

                                            <a href="../../college_student/employ_update.html">


                                                企学院修改学员

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.4" data-path="../../college_student/employ_delete.html">

                                            <a href="../../college_student/employ_delete.html">


                                                企学院删除学员

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.5" data-path="../../college_student/department_add.html">

                                            <a href="../../college_student/department_add.html">


                                                企学院添加组织架构

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.6" data-path="../../college_student/department_delete.html">

                                            <a href="../../college_student/department_delete.html">


                                                企学院删除组织架构

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.7" data-path="../../college_student/department_update.html">

                                            <a href="../../college_student/department_update.html">


                                                企学院编辑组织架构

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.8" data-path="../../college_student/department_get.html">

                                            <a href="../../college_student/department_get.html">


                                                企学院获取组织架构

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.9" data-path="../../college_student/department_info.html">

                                            <a href="../../college_student/department_info.html">


                                                企学院获取部门信息

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.10" data-path="../../college_student/search_by_dept_level.html">

                                            <a href="../../college_student/search_by_dept_level.html">


                                                企学院根据部门层级获取最子级部门ID

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.1.11" data-path="../../college_student/employee_department_search.html">

                                            <a href="../../college_student/employee_department_search.html">


                                                企学院搜索学员、部门

                                            </a>



                                        </li>


                                    </ul>

                                </li>

                                <li class="chapter " data-level="1.8.27.2" data-path="../../activity_college.html">

                                    <a href="../../activity_college.html">


                                        活动管理（限企学院）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.27.2.1" data-path="../../activity_college/get_college_activity_sign.html">

                                            <a href="../../activity_college/get_college_activity_sign.html">


                                                企学院用户签到活动列表

                                            </a>



                                        </li>


                                    </ul>

                                </li>

                                <li class="chapter " data-level="1.8.27.3" data-path="../../college_training.html">

                                    <a href="../../college_training.html">


                                        培训计划管理（限企学院）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.27.3.1" data-path="../../college_training/training_plan_list.html">

                                            <a href="../../college_training/training_plan_list.html">


                                                获取培训计划列表

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.3.2" data-path="../../college_training/training_plan_detail.html">

                                            <a href="../../college_training/training_plan_detail.html">


                                                获取培训计划详情

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.3.3" data-path="../../college_training/user_training_study_data.html">

                                            <a href="../../college_training/user_training_study_data.html">


                                                获取用户培训学习数据

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.27.3.4" data-path="../../college_training/training_plan_user.html">

                                            <a href="../../college_training/training_plan_user.html">


                                                获取员工的培训计划列表

                                            </a>



                                        </li>


                                    </ul>

                                </li>


                            </ul>

                        </li>

                        <li class="chapter " data-level="1.8.28" data-path="../../provider.html">

                            <a href="../../provider.html">


                                服务商专用

                            </a>



                            <ul class="articles">


                                <li class="chapter " data-level="1.8.28.1" data-path="../../provider/login.html">

                                    <a href="../../provider/login.html">


                                        登录管理（限服务商）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.28.1.1" data-path="../../provider/login/b_login.html">

                                            <a href="../../provider/login/b_login.html">


                                                B端用户登录

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.1.2" data-path="../../provider/login/c_login.html">

                                            <a href="../../provider/login/c_login.html">


                                                C端用户登录

                                            </a>



                                        </li>


                                    </ul>

                                </li>

                                <li class="chapter " data-level="1.8.28.2" data-path="../../provider/live.html">

                                    <a href="../../provider/live.html">


                                        直播管理（限服务商）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.28.2.1" data-path="../../provider/live/user_data_preload.html">

                                            <a href="../../provider/live/user_data_preload.html">


                                                直播用户数据预热

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.2" data-path="../../provider/live/user_data_preload_status_get.html">

                                            <a href="../../provider/live/user_data_preload_status_get.html">


                                                查询预热数据状态

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.3" data-path="../../provider/live/user_market_data_get.html">

                                            <a href="../../provider/live/user_market_data_get.html">


                                                查询单个用户的营销数据

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.4" data-path="../../provider/live/user_interact_data_get.html">

                                            <a href="../../provider/live/user_interact_data_get.html">


                                                查询单个用户的互动数据

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.5" data-path="../../provider/live/user_watch_data_get.html">

                                            <a href="../../provider/live/user_watch_data_get.html">


                                                查询单个用户的观看数据(学习数据)

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.6" data-path="../../provider/live/live_interact_data_get.html">

                                            <a href="../../provider/live/live_interact_data_get.html">


                                                查询单场直播的互动数据

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.2.7" data-path="../../provider/live/live_watch_data_get.html">

                                            <a href="../../provider/live/live_watch_data_get.html">


                                                查询单场直播的观看数据(学习数据)

                                            </a>



                                        </li>


                                    </ul>

                                </li>

                                <li class="chapter " data-level="1.8.28.3" data-path="../../provider/message_push.html">

                                    <a href="../../provider/message_push.html">


                                        消息推送（限服务商）

                                    </a>



                                    <ul class="articles">


                                        <li class="chapter " data-level="1.8.28.3.1" data-path="../../provider/message_push/intruduction.html">

                                            <a href="../../provider/message_push/intruduction.html">


                                                使用说明

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.3.2" data-path="../../provider/message_push/alive_state_change.html">

                                            <a href="../../provider/message_push/alive_state_change.html">


                                                直播状态变更

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.3.3" data-path="../../provider/message_push/alive_create.html">

                                            <a href="../../provider/message_push/alive_create.html">


                                                直播创建推送

                                            </a>



                                        </li>

                                        <li class="chapter " data-level="1.8.28.3.4" data-path="../../provider/message_push/alive_im.html">

                                            <a href="../../provider/message_push/alive_im.html">


                                                直播间用户进离场推送

                                            </a>



                                        </li>


                                    </ul>

                                </li>


                            </ul>

                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.9" data-path="../../../common_problem.html">

                    <a href="../../../common_problem.html">


                        常见问题

                    </a>



                    <ul class="articles">


                        <li class="chapter " data-level="1.9.1" data-path="../../../common_problem/problem.html">

                            <a href="../../../common_problem/problem.html">


                                问题汇总

                            </a>



                        </li>

                        <li class="chapter " data-level="1.9.2" data-path="../../../common_problem/problem_feedback.html">

                            <a href="../../../common_problem/problem_feedback.html">


                                问题反馈

                            </a>



                        </li>

                        <li class="chapter " data-level="1.9.3" data-path="../../../common_problem/field_illustrate.html">

                            <a href="../../../common_problem/field_illustrate.html">


                                枚举字段说明

                            </a>



                        </li>


                    </ul>

                </li>

                <li class="chapter " data-level="1.10" data-path="../../../cloud_charge_protocol_page.html">

                    <a href="../../../cloud_charge_protocol_page.html">


                        小鹅通开放平台服务协议

                    </a>



                </li>




                <li class="divider"></li>

                <li>
                    <a href="https://www.gitbook.com" target="blank" class="gitbook-link">
                        Published with GitBook
                    </a>
                </li>
            </ul>


        </nav>


    </div>

    <div class="book-body">

        <div class="body-inner">



            <div class="book-header" role="navigation">


                <!-- Title -->
                <h1>
                    <i class="fa fa-circle-o-notch fa-spin"></i>
                    <a href="../../.." >获取用户反馈回复列表</a>
                </h1>
            </div>




            <div class="page-wrapper" tabindex="-1" role="main">
                <div class="page-inner">

                    <div id="book-search-results">
                        <div class="search-noresults">

                            <section class="normal markdown-section">

                                <h1 id="&#x83B7;&#x53D6;&#x7528;&#x6237;&#x53CD;&#x9988;&#x56DE;&#x590D;&#x5217;&#x8868;">&#x83B7;&#x53D6;&#x7528;&#x6237;&#x53CD;&#x9988;&#x56DE;&#x590D;&#x5217;&#x8868;</h1>
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="text-align:left">&#x62E5;&#x6709;&#x6B64;API&#x7684;&#x6743;&#x9650;&#x96C6;</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td style="text-align:left">&#x7528;&#x6237;&#x7BA1;&#x7406;-&#x67E5;&#x8BE2;</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p><strong>&#x8BF7;&#x6C42;&#x65B9;&#x5F0F;&#x53CA;url</strong> </p>
                                <ul>
                                    <li>&#x8BF7;&#x6C42;&#x65B9;&#x5F0F;&#xFF1A;<code>POST</code></li>
                                    <li>&#x8BF7;&#x6C42;&#x5934;&#xFF1A;<code>Content-Type:application/json</code></li>
                                    <li>&#x63A5;&#x53E3;&#x5730;&#x5740;&#xFF1A;<code>https://api.xiaoe-tech.com/xe.user.feedback.reply_list/1.0.0</code></li>
                                    <li>&#x9891;&#x7387;&#x9650;&#x5236;: <code>10&#x79D2;3000&#x6B21;</code></li>
                                </ul>
                                <p></p><div class="api-container"><div class="api-header">&#x5728;&#x7EBF;&#x8C03;&#x8BD5;&#x5DE5;&#x5177;<span class="icon-btn-right">
            <svg t="1626403766502" class="icon" viewbox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="4538" width="20" height="20" style="margin-top:8px;">
              <path d="M411.562667 271.104l225.834666 225.792a21.333333 21.333333 0 0 1 0 30.208l-225.834666 225.792a21.333333 21.333333 0 1 0 30.208 30.208l225.792-225.834667a64 64 0 0 0 0-90.538666l-225.792-225.834667a21.333333 21.333333 0 0 0-30.208 30.208z" p-id="4539" fill="#1472FF"/>

            </svg></span></div><input class="post" id="method" type="hidden" value="POST"><input id="apiUrl" type="hidden" value="https://api-doc.xiaoe-tech.com/_agent/forward?url=https://api.xiaoe-tech.com/xe.user.feedback.reply_list/1.0.0"><input id="initialValue" type="hidden" value="{
    &quot;access_token&quot;: &quot;xxxxxxxx&quot;,
    &quot;feedback_id&quot;: &quot;704915&quot;,
    &quot;user_id&quot;: &quot;u_62c7e2511913c_JVbs2Jplce&quot;
}"><div class="api-content"><div class="params-container"><h4 class="params-title" id="&#x8BF7;&#x6C42;">&#x8BF7;&#x6C42;</h4><textarea class="params-content" id="data">{
    &quot;access_token&quot;: &quot;xxxxxxxx&quot;,
    &quot;feedback_id&quot;: &quot;704915&quot;,
    &quot;user_id&quot;: &quot;u_62c7e2511913c_JVbs2Jplce&quot;
}</textarea></div><div class="response-container"><h4 class="params-title" id="&#x54CD;&#x5E94;">&#x54CD;&#x5E94;</h4><div class="response-content-wrapper"><div class="response-loading">
                                <svg t="1628496016953" class="icon loading-content" viewbox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5877" width="16" height="16">
                                    <path d="M1023.849566 529.032144C1022.533495 457.744999 1007.544916 386.64064 979.907438 321.641387 952.343075 256.605575 912.349158 197.674868 863.252422 148.980264 814.192243 100.249102 755.992686 61.717486 693.004095 36.310016 630.052062 10.792874 562.347552-1.380777 495.483865 0.081523 428.620178 1.470709 362.012394 16.495846 301.144139 44.206439 240.202769 71.807359 185.000928 111.874391 139.377154 161.044242 93.753381 210.177537 57.707676 268.450209 33.945294 331.475357 10.073239 394.463948-1.296147 462.1319 0.166154 529.032144 1.482224 595.968946 15.593423 662.503615 41.549256 723.371871 67.468531 784.240126 105.013094 839.405409 151.075558 884.956067 197.101464 930.579841 251.645269 966.552431 310.612534 990.241698 369.543241 1014.040637 432.860849 1025.336908 495.483865 1023.874608 558.143438 1022.485422 620.291206 1008.337666 677.174693 982.381833 734.094737 956.462558 785.677384 918.954552 828.230327 872.892089 870.819826 826.902741 904.416179 772.395492 926.533473 713.5379 939.986637 677.85777 949.089457 640.605667 953.915048 602.841758 955.194561 602.951431 956.510631 602.987988 957.790144 602.987988 994.27454 602.987988 1023.849566 572.425909 1023.849566 534.735116 1023.849566 532.834125 1023.739893 530.933135 1023.593663 529.032144L1023.849566 529.032144 1023.849566 529.032144ZM918.892953 710.284282C894.691881 767.021538 859.596671 818.421398 816.568481 860.82811 773.540291 903.307938 722.652236 936.75806 667.706298 958.729124 612.760359 980.773303 553.902767 991.192193 495.483865 989.729893 437.064963 988.377265 379.304096 975.106889 326.441936 950.832702 273.543218 926.668187 225.616322 891.682649 186.097653 848.764132 146.542426 805.91873 115.35887 755.176905 94.959779 700.486869 74.451015 645.796833 64.799833 587.195144 66.189018 529.032144 67.541646 470.869145 79.934642 413.437296 102.563741 360.867595 125.119725 308.297895 157.765582 260.663459 197.759499 221.364135 237.716858 182.064811 284.985719 151.137157 335.910331 130.884296 386.834944 110.55832 441.305634 101.01681 495.483865 102.47911 549.662096 103.868296 603.036061 116.261292 651.876895 138.780718 700.754287 161.22703 745.025432 193.690099 781.509828 233.428113 818.067339 273.166127 846.764984 320.142529 865.518987 370.665008 884.346105 421.224045 893.156465 475.256046 891.76728 529.032144L891.986625 529.032144C891.840395 530.933135 891.76728 532.797568 891.76728 534.735116 891.76728 569.939999 917.540325 598.893547 950.66143 602.585856 944.227308 639.728286 933.589072 675.956779 918.892953 710.284282Z" p-id="5878" fill="#ffffff"/>

                                </svg>
                            </div><textarea class="response-content" id="result" readonly="true"></textarea></div></div><footer class="api-footer"><div class="footer-inner"><button class="footer-button reset-btn">&#x91CD;&#x7F6E;</button><button class="footer-button request-btn">&#x63D0;&#x4EA4;&#x8BF7;&#x6C42;</button></div></footer></div></div><p></p>
                                <p><strong>&#x8BF7;&#x6C42;&#x53C2;&#x6570;</strong> </p>
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="text-align:left">&#x53C2;&#x6570;&#x540D;</th>
                                        <th style="text-align:left">&#x5FC5;&#x9009;</th>
                                        <th style="text-align:left">&#x7C7B;&#x578B;</th>
                                        <th>&#x8BF4;&#x660E;</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td style="text-align:left">access_token</td>
                                        <td style="text-align:left">&#x662F;</td>
                                        <td style="text-align:left">string</td>
                                        <td><a href="../../get_access_token.html">&#x4E13;&#x5C5E;token</a></td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">user_id</td>
                                        <td style="text-align:left">&#x662F;</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x7528;&#x6237;id</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">feedback_id</td>
                                        <td style="text-align:left">&#x662F;</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x7528;&#x6237;&#x53CD;&#x9988;&#x5BF9;&#x5E94;&#x7684;id</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p><strong>&#x8BF7;&#x6C42;&#x793A;&#x4F8B;</strong></p>
                                <pre><code> {
    &quot;access_token&quot;: &quot;xxxxxxxx&quot;,
    &quot;feedback_id&quot;: &quot;704915&quot;,
    &quot;user_id&quot;: &quot;u_62c7e2511913c_JVbs2Jplce&quot;
}
</code></pre><p> <strong>&#x8FD4;&#x56DE;&#x53C2;&#x6570;</strong> </p>
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="text-align:left">&#x53C2;&#x6570;&#x540D;</th>
                                        <th style="text-align:left">&#x7C7B;&#x578B;</th>
                                        <th>&#x8BF4;&#x660E;</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td style="text-align:left">code</td>
                                        <td style="text-align:left">int</td>
                                        <td>&#x72B6;&#x6001;&#x7801;&#xFF1A;0-&#x6210;&#x529F;&#xFF0C;&#x5176;&#x4ED6;&#x4E3A;&#x9519;&#x8BEF;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">message</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x5BF9;&#x8FD4;&#x56DE;&#x7801;&#x7684;&#x6587;&#x672C;&#x63CF;&#x8FF0;&#x5185;&#x5BB9;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">data.reply_list</td>
                                        <td style="text-align:left">array</td>
                                        <td>&#x8FD4;&#x56DE;&#x7684;&#x56DE;&#x590D;&#x5217;&#x8868;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">data.reply_list_count</td>
                                        <td style="text-align:left">int</td>
                                        <td>&#x603B;&#x6570;</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p><code>data.reply_list</code>&#x56DE;&#x590D;&#x5217;&#x8868;</p>
                                <table>
                                    <thead>
                                    <tr>
                                        <th style="text-align:left">&#x53C2;&#x6570;&#x540D;</th>
                                        <th style="text-align:left">&#x7C7B;&#x578B;</th>
                                        <th>&#x8BF4;&#x660E;</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr>
                                        <td style="text-align:left">reply_list[].user_id</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x53CD;&#x9988;&#x4EBA;id</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">reply_list[].send_user_phone</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x56DE;&#x590D;&#x4EBA;&#x8054;&#x7CFB;&#x65B9;&#x5F0F;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">reply_list[].content</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x56DE;&#x590D;&#x5185;&#x5BB9;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">reply_list[].state</td>
                                        <td style="text-align:left">int</td>
                                        <td>&#x56DE;&#x590D;&#x72B6;&#x6001;&#xFF1B;0&#xFF1A;&#x6B63;&#x5E38;&#xFF0C;1&#xFF1A;&#x64A4;&#x56DE;</td>
                                    </tr>
                                    <tr>
                                        <td style="text-align:left">reply_list[].send_at</td>
                                        <td style="text-align:left">string</td>
                                        <td>&#x56DE;&#x590D;&#x65F6;&#x95F4;</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p><strong>&#x8FD4;&#x56DE;&#x793A;&#x4F8B;</strong></p>
                                <pre><code>{
    &quot;code&quot;: 0,
    &quot;msg&quot;: &quot;success&quot;,
    &quot;data&quot;: {
        &quot;reply_list&quot;: [
            {
                &quot;content&quot;: &quot;&#x6D4B;&#x8BD5;&quot;,
                &quot;send_at&quot;: &quot;2022-12-07 17:14:07&quot;,
                &quot;send_user_phone&quot;: &quot;13333333333&quot;,
                &quot;state&quot;: 0,
                &quot;user_id&quot;: &quot;u_62c7e2511913c_JVbs2Jplce&quot;
            }
        ],
        &quot;reply_list_count&quot;: 81
    }
}
</code></pre>
                                <script>console.log("plugin-popup....");document.onclick = function(e){ e.target.tagName === "IMG" && window.open(e.target.src,e.target.src)}</script><style>img{cursor:pointer}</style>

                            </section>

                        </div>
                        <div class="search-results">
                            <div class="has-results">

                                <h1 class="search-results-title"><span class='search-results-count'></span> results matching "<span class='search-query'></span>"</h1>
                                <ul class="search-results-list"></ul>

                            </div>
                            <div class="no-results">

                                <h1 class="search-results-title">No results matching "<span class='search-query'></span>"</h1>

                            </div>
                        </div>
                    </div>

                </div>
            </div>

        </div>



        <a href="list.html" class="navigation navigation-prev " aria-label="Previous page: 获取用户反馈列表">
            <i class="fa fa-angle-left"></i>
        </a>


        <a href="reply.html" class="navigation navigation-next " aria-label="Next page: 回复用户反馈">
            <i class="fa fa-angle-right"></i>
        </a>



    </div>

    <script>
        var gitbook = gitbook || [];
        gitbook.push(function() {
            gitbook.page.hasChanged({"page":{"title":"获取用户反馈回复列表","level":"********","depth":3,"next":{"title":"回复用户反馈","level":"********","depth":3,"path":"api_list/user/feedback/reply.md","ref":"api_list/user/feedback/reply.md","articles":[]},"previous":{"title":"获取用户反馈列表","level":"********","depth":3,"path":"api_list/user/feedback/list.md","ref":"api_list/user/feedback/list.md","articles":[]},"dir":"ltr"},"config":{"plugins":["-sharing","highlight","simpletabs","expandable-chapters-small","image-captions","styled-blockquotes","-lunr","-search","search-pro","back-to-top-button","theme-default","hide-element","splitter","popup","lightbox","xiaoe-header","chapter-fold","-livereload","favicon-absolute","xiaoe-api","livereload"],"styles":{"ebook":"styles/ebook.css","epub":"styles/epub.css","mobi":"styles/mobi.css","pdf":"styles/pdf.css","print":"styles/print.css","website":"styles/website.css"},"pluginsConfig":{"chapter-fold":{},"splitter":{},"search-pro":{},"styled-blockquotes":{},"popup":{},"simpletabs":{},"favicon-absolute":{"appleTouchIconMore":{"120x120":"favicon.png","180x180":"favicon.png"},"appleTouchIconPrecomposed152":"favicon.png","appleTouchIconPrecomposedMore":{"120x120":"favicon.png","180x180":"favicon.png"},"favicon":"favicon.ico","bookmark":"favicon.ico","appleTouchIcon152":"favicon.png"},"hide-element":{"elements":[".gitbook-link"]},"fontsettings":{"theme":"white","family":"sans","size":2},"xiaoe-api":{},"highlight":{},"xiaoe-header":{},"anchor-navigation-ex":{"multipleH1":false,"showLevel":false,"float":{"tocLevel1Icon":"fa fa-hand-o-right","tocLevel2Icon":"fa fa-hand-o-right","tocLevel3Icon":"fa fa-hand-o-right"},"toolTipMode":"click"},"lightbox":{"jquery":true,"sameUuid":false},"back-to-top-button":{},"expandable-chapters-small":{},"theme-default":{"showLevel":false,"styles":{"ebook":"styles/ebook.css","epub":"styles/epub.css","mobi":"styles/mobi.css","pdf":"styles/pdf.css","print":"styles/print.css","website":"styles/website.css"}},"image-captions":{"variable_name":"_pictures"}},"theme":"default","pdf":{"pageNumbers":true,"fontSize":12,"fontFamily":"Arial","paperSize":"a4","chapterMark":"pagebreak","pageBreaksBefore":"/","margin":{"right":62,"left":62,"top":56,"bottom":56}},"structure":{"langs":"LANGS.md","readme":"README.md","glossary":"GLOSSARY.md","summary":"SUMMARY.md"},"variables":{"_pictures":[]},"title":"小鹅云","gitbook":"*"},"file":{"path":"api_list/user/feedback/reply_list.md","mtime":"2023-12-11T01:58:42.009Z","type":"markdown"},"gitbook":{"version":"3.2.3","time":"2024-09-11T02:56:32.748Z"},"basePath":"../../..","book":{"language":""}});
        });
    </script>
</div>


<script src="../../../gitbook/gitbook.js"></script>
<script src="../../../gitbook/theme.js"></script>


<script src="https://xiaoetong-1252524126.cdn.xiaoeknow.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>



<script src="../../../gitbook/gitbook-plugin-simpletabs/tabs.js"></script>



<script src="../../../gitbook/gitbook-plugin-expandable-chapters-small/expandable-chapters-small.js"></script>



<script src="../../../gitbook/gitbook-plugin-search-pro/jquery.mark.min.js"></script>



<script src="../../../gitbook/gitbook-plugin-search-pro/search.js"></script>



<script src="../../../gitbook/gitbook-plugin-back-to-top-button/plugin.js"></script>



<script src="../../../gitbook/gitbook-plugin-hide-element/plugin.js"></script>



<script src="../../../gitbook/gitbook-plugin-splitter/splitter.js"></script>



<script src="../../../gitbook/gitbook-plugin-lightbox/js/lightbox.min.js"></script>



<script src="../../../gitbook/gitbook-plugin-xiaoe-header/handler.js"></script>



<script src="../../../gitbook/gitbook-plugin-chapter-fold/chapter-fold.js"></script>



<script src="../../../gitbook/gitbook-plugin-xiaoe-api/api.js"></script>



<script src="../../../gitbook/gitbook-plugin-fontsettings/fontsettings.js"></script>



</body>
</html>

