package com.wendao101.xiaoetong.livebroad.signintraineelist;

import lombok.Data;
import java.util.List;

@Data
public class GetSignInTraineeListResponse {
    /**
     * 错误码
     */
    private int code;

    /**
     * 错误描述
     */
    private String msg;

    /**
     * 返回数据
     */
    private ResponseData data;

    @Data
    public static class ResponseData {
        /**
         * 查询结果记录数
         */
        private int total;

        /**
         * 当前页码
         */
        private int page;

        /**
         * 当前页大小
         */
        private int pageSize;

        /**
         * 用户签到信息列表
         */
        private List<SignInInfo> list;
    }

    @Data
    public static class SignInInfo {
        /**
         * 店铺id
         */
        private String appId;

        /**
         * 签到id
         */
        private String signId;

        /**
         * 用户id
         */
        private String userId;

        /**
         * 签到时间戳
         */
        private long signTime;

        /**
         * 昵称
         */
        private String wxNickname;
    }
}
