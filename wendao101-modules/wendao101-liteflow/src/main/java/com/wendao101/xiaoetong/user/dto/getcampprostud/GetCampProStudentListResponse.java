package com.wendao101.xiaoetong.user.dto.getcampprostud;

import lombok.Data;
import java.util.List;

@Data
public class GetCampProStudentListResponse {
    private Integer code;
    private String msg;
    private ResponseData data;

    @Data
    public static class ResponseData {
        private List<UserInfo> userList;
        private String courseId;
        private Integer total;
    }

    @Data
    public static class UserInfo {
        private String userId;
        private String wxNickname;
        private String wxAvatar;
        private String phone;
        private Integer isSeal;
        private String commentName;
        private String endAtStr;
        private Integer authState;
        private Integer finishCount;
        private String finishRate;
        private String requiredCourseFinishRate;
        private String progress;
        private String subscribeSource;
        private String subscribeTime;
    }
}
