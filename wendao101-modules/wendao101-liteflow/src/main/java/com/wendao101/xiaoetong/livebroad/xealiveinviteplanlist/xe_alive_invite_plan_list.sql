CREATE TABLE xe_alive_invite_plan_list (
  id INT PRIMARY KEY AUTO_INCREMENT,
  invite_id VARCHAR(50) NOT NULL COMMENT '邀约计划ID',
  plan_name VARCHAR(100) NOT NULL COMMENT '计划名',
  alive_id VARCHAR(50) NOT NULL COMMENT '直播ID',
  plan_state TINYINT NOT NULL COMMENT '计划状态: 0-创建计划中 1-计划执行中 2-创建计划失败 3-计划结束',
  created_at DATETIME NOT NULL COMMENT '计划创建时间',
  KEY idx_alive_id (alive_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播邀约计划列表';