package com.wendao101.xiaoetong.learndata.getlearnrecordbyresourceid;

import lombok.Getter;

@Getter
public enum GetLearnRecordByResourceIdErrorCode {
    SUCCESS(0, "成功"),
    INVALID_PARAMETER(400, "参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    UNKNOWN_ERROR(-1, "未知错误");

    private final int code;
    private final String message;

    GetLearnRecordByResourceIdErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static GetLearnRecordByResourceIdErrorCode getByCode(int code) {
        for (GetLearnRecordByResourceIdErrorCode errorCode : values()) {
            if (errorCode.getCode() == code) {
                return errorCode;
            }
        }
        return UNKNOWN_ERROR;
    }
}
