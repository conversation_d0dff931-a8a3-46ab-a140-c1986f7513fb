{"openapi": "3.0.0", "info": {"title": "添加/修改标签", "version": "1.0.0"}, "paths": {"/xe.user.tag.create_modify_tag/1.0.0": {"post": {"summary": "添加/修改标签", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateModifyTagRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateModifyTagResponse"}}}}}}}}, "components": {"schemas": {"CreateModifyTagRequest": {"type": "object", "required": ["accessToken", "tagName", "tagType"], "properties": {"accessToken": {"type": "string", "description": "专属token"}, "tagId": {"type": "integer", "description": "标签id,当tag_id未传时,默认为新建标签,当传入的tag_id不存在时,会有报错提示"}, "tagName": {"type": "string", "description": "标签名称"}, "tagType": {"type": "integer", "description": "标签类型,可选范围[1],1-手动"}}}, "CreateModifyTagResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码:0-成功,其他为错误"}, "msg": {"type": "string", "description": "对返回码的文本描述内容"}}}}}}