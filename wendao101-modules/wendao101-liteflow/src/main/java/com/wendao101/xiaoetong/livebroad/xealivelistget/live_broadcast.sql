CREATE TABLE `alive` (
  `id` varchar(64) NOT NULL COMMENT '直播id',
  `alive_manual_stop_at` datetime DEFAULT NULL COMMENT '直播手动结束时间',
  `alive_stop_at` datetime DEFAULT NULL COMMENT '直播结束时间',
  `title` varchar(255) NOT NULL COMMENT '直播名称',
  `view_count` int(11) NOT NULL DEFAULT '0' COMMENT '直播观看人次',
  `alive_type` tinyint(4) NOT NULL COMMENT '直播类型,10 横屏直播；11竖屏直播；12语音直播；13录播直播',
  `reward_sum` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '打赏金额（元）',
  `recycle_bin_state` tinyint(4) NOT NULL COMMENT '直播商品状态：0 已上架； 1 已下架；2 待上架',
  `alive_start_at` datetime NOT NULL COMMENT '直播开始时间',
  `alive_state` tinyint(4) NOT NULL COMMENT '直播状态：0未开始；1直播中；2已结束',
  `live_state` tinyint(4) NOT NULL COMMENT '直播间状态：0未开始；1直播中；2互动时间；3已结束、4讲师暂时离开',
  `resource_state` tinyint(1) NOT NULL DEFAULT '0' COMMENT '直播显隐状态：0-显示，1-隐藏',
  `page_url` varchar(255) DEFAULT NULL COMMENT '直播地址',
  `create_mode` tinyint(4) NOT NULL COMMENT '直播课程类型：0 店铺课程； 1 转播课程',
  `img_url` varchar(255) DEFAULT NULL COMMENT '封面地址',
  `alive_img_url` varchar(255) DEFAULT NULL COMMENT '直播宣传图地址',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_alive_start_at` (`alive_start_at`),
  KEY `idx_alive_state` (`alive_state`),
  KEY `idx_create_mode` (`create_mode`),
  KEY `idx_recycle_bin_state` (`recycle_bin_state`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播信息表';

CREATE TABLE `alive_guest` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `alive_id` varchar(64) NOT NULL COMMENT '直播id',
  `user_id` varchar(64) NOT NULL COMMENT '讲师id',
  `user_name` varchar(255) NOT NULL COMMENT '讲师昵称',
  `phone` varchar(20) DEFAULT NULL COMMENT '讲师绑定手机号',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '记录创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '记录更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_alive_id` (`alive_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='直播讲师关联表';
