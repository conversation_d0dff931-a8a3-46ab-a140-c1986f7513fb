package com.wendao101.xiaoetong.user.dto.feedback;

import lombok.Data;
import java.util.List;

@Data
public class GetFeedbackListResponse {
    /**
     * 状态码：0-成功，其他为错误
     */
    private int code;

    /**
     * 对返回码的文本描述内容
     */
    private String message;

    /**
     * 返回数据
     */
    private FeedbackData data;

    @Data
    public static class FeedbackData {
        /**
         * 返回的反馈列表
         */
        private List<FeedbackInfo> feedbackList;

        /**
         * 总数
         */
        private int total;
    }

    @Data
    public static class FeedbackInfo {
        /**
         * 反馈id
         */
        private int feedbackId;

        /**
         * 店铺id
         */
        private String appId;

        /**
         * 反馈人昵称
         */
        private String feedbackUserNickname;

        /**
         * 反馈人id
         */
        private String feedbackUserId;

        /**
         * 反馈所在终端
         */
        private String feedbackType;

        /**
         * 反馈带有的图片
         */
        private List<String> feedbackImgs;

        /**
         * 反馈内容
         */
        private String feedbackMsg;

        /**
         * 反馈时间
         */
        private String feedbackTime;
    }
}
