package com.wendao101.xiaoetong.user.dto.getusermergeinfo;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GetUserMergeInfoErrorCode {

    SUCCESS(0, "成功"),
    INVALID_ACCESS_TOKEN(1001, "无效的access_token"),
    INVALID_USER_ID(1002, "无效的用户ID"),
    USER_NOT_MERGED(1003, "用户未进行过合并"),
    SYSTEM_ERROR(9999, "系统错误");

    private final int code;
    private final String message;
}
