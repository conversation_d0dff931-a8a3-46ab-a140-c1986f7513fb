{"openapi": "3.0.0", "info": {"title": "获取直播详情", "version": "1.0.0"}, "paths": {"/xe.alive.detail.get/1.0.0": {"post": {"summary": "获取直播详情", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLiveBroadDetailRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLiveBroadDetailResponse"}}}}}}}}, "components": {"schemas": {"GetLiveBroadDetailRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "id": {"type": "string", "description": "直播ID"}}, "required": ["access_token", "id"]}, "GetLiveBroadDetailResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "返回码"}, "msg": {"type": "string", "description": "返回信息"}, "data": {"$ref": "#/components/schemas/LiveBroadDetail"}}}, "LiveBroadDetail": {"type": "object", "properties": {"id": {"type": "string", "description": "直播ID"}, "title": {"type": "string", "description": "直播标题"}, "cover_img": {"type": "string", "description": "直播封面"}, "start_time": {"type": "integer", "format": "int64", "description": "直播开始时间"}, "end_time": {"type": "integer", "format": "int64", "description": "直播结束时间"}, "status": {"type": "integer", "description": "直播状态"}, "type": {"type": "integer", "description": "直播间类型"}, "is_playback": {"type": "boolean", "description": "是否开启回放"}, "is_record": {"type": "boolean", "description": "是否开启录制"}, "is_forbid_chat": {"type": "boolean", "description": "是否开启禁言"}, "is_goods": {"type": "boolean", "description": "是否开启商品"}, "is_sign_in": {"type": "boolean", "description": "是否开启签到"}, "is_lottery": {"type": "boolean", "description": "是否开启抽奖"}, "is_questionnaire": {"type": "boolean", "description": "是否开启问卷"}, "is_red_packet": {"type": "boolean", "description": "是否开启红包"}, "is_notice": {"type": "boolean", "description": "是否开启公告"}, "is_evaluate": {"type": "boolean", "description": "是否开启评价"}, "is_exam": {"type": "boolean", "description": "是否开启考试"}, "is_vote": {"type": "boolean", "description": "是否开启投票"}, "is_qa": {"type": "boolean", "description": "是否开启问答"}, "is_chat": {"type": "boolean", "description": "是否开启聊天"}, "is_like": {"type": "boolean", "description": "是否开启点赞"}, "is_share": {"type": "boolean", "description": "是否开启分享"}, "is_invite": {"type": "boolean", "description": "是否开启邀请"}, "is_appointment": {"type": "boolean", "description": "是否开启预约"}, "is_remind": {"type": "boolean", "description": "是否开启提醒"}, "is_gift": {"type": "boolean", "description": "是否开启礼物"}, "is_danmaku": {"type": "boolean", "description": "是否开启弹幕"}, "is_link_mic": {"type": "boolean", "description": "是否开启连麦"}, "is_pk": {"type": "boolean", "description": "是否开启PK"}}}}}}