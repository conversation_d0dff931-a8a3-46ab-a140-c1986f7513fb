package com.wendao101.xiaoetong.user.dto.gettaglist;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GetTagListErrorCode {

    SUCCESS(0, "成功"),
    INVALID_ACCESS_TOKEN(1001, "无效的access_token"),
    INVALID_TAG_TYPE(1002, "无效的标签类型"),
    INVALID_PAGE(1003, "无效的页数"),
    INVALID_PAGE_SIZE(1004, "无效的每页条数"),
    SYSTEM_ERROR(9999, "系统错误");

    private final int code;
    private final String message;
}
