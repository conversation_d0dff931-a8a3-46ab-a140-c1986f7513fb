{"openapi": "3.0.0", "info": {"title": "注册新用户 API", "version": "1.0.0", "description": "此 API 用于注册新用户"}, "servers": [{"url": "https://api.xiaoe-tech.com"}], "paths": {"/xe.user.register/1.0.0": {"post": {"summary": "注册新用户", "description": "频率限制: 10秒5000次", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterUserRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterUserResponse"}}}}}}}}, "components": {"schemas": {"RegisterUserRequest": {"type": "object", "required": ["access_token", "data"], "properties": {"access_token": {"type": "string", "description": "专属token"}, "data": {"type": "object", "properties": {"wx_union_id": {"type": "string", "description": "微信 union_id"}, "phone": {"type": "string", "description": "联系方式,手机号格式：[+区号-]手机号，海外手机号区号必传，如 +1-643xxxx"}, "sdk_user_id": {"type": "string", "description": "sdk用户id"}, "sdk_app_id": {"type": "string", "description": "sdk_app_id"}, "avatar": {"type": "string", "description": "用户头像链接"}, "nickname": {"type": "string", "description": "微信 用户昵称"}, "country": {"type": "string", "description": "国家"}, "province": {"type": "string", "description": "省份"}, "city": {"type": "string", "description": "城市"}, "gender": {"type": "integer", "enum": [0, 1, 2], "description": "性别: 0-无 1-男 2-女"}, "wx_name": {"type": "string", "description": "真实姓名"}, "company": {"type": "string", "description": "公司"}, "industry": {"type": "string", "description": "行业"}, "job": {"type": "string", "description": "工作"}, "wx_email": {"type": "string", "description": "邮箱"}}}}}, "RegisterUserResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "请求结果码: 0-成功, 其他为错误码"}, "msg": {"type": "string", "description": "描述信息"}, "data": {"type": "object", "properties": {"user_id": {"type": "string", "description": "小鹅通用户id"}, "user_exists": {"type": "integer", "enum": [0, 1, 2], "description": "用户状态: 0-新注册用户；1-用户手机号存在；2-用户union_id存在"}}}}}}}}