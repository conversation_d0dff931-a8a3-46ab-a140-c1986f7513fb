{"openapi": "3.0.0", "info": {"title": "获取每日学习记录", "version": "1.0.0"}, "paths": {"/xe.learning_records.daily.get/1.0.0": {"post": {"summary": "获取每日学习记录", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLearnRecordDailyRequest"}}}}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLearnRecordDailyResponse"}}}}}}}}, "components": {"schemas": {"GetLearnRecordDailyRequest": {"type": "object", "properties": {"access_token": {"type": "string", "description": "专属token"}, "date": {"type": "string", "description": "日期 yyyy-MM-dd"}, "user_id_list": {"type": "array", "items": {"type": "string"}, "description": "用户id列表；最多传1000条，列表为空时，查询整个店铺用户。"}, "page": {"type": "integer", "description": "页码"}, "page_size": {"type": "integer", "description": "每页条数，最大为1000"}}, "required": ["access_token", "date", "page", "page_size"]}, "GetLearnRecordDailyResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}, "msg": {"type": "string", "description": "错误描述"}, "data": {"type": "object", "properties": {"list": {"type": "array", "items": {"$ref": "#/components/schemas/LearnRecord"}, "description": "信息列表"}, "total_count": {"type": "integer", "description": "总数"}}}}}, "LearnRecord": {"type": "object", "properties": {"user_id": {"type": "string", "description": "用户 id"}, "resource_type": {"type": "integer", "description": "资源类型"}, "resource_id": {"type": "string", "description": "资源 id"}, "count_date": {"type": "string", "description": "统计时间"}, "app_id": {"type": "string", "description": "店铺 id"}, "stay_time": {"type": "integer", "description": "学习时长"}}}}}}