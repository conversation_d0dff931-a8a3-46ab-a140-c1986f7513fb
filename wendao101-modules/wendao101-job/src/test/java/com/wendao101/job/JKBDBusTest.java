package com.wendao101.job;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.wendao101.common.core.utils.http.HttpUtils;

/**
 * 驾考宝典抓取
 */
public class JKBDBusTest {
    public static void main客车A1A3B1科目1(String[] args) {
        String r = getR();
        String carType = "bus";
        //
        //http://api2.jiakaobaodian.com/api/open/exercise/sequence.htm?_r=17801703702540802070&cityCode=511300&page=1&limit=25&course=kemu1&carType=car&_=0.7121756361682074
        //科目1
        String kemu1 = "http://api2.jiakaobaodian.com/api/open/exercise/sequence.htm?_r=" + r + "&cityCode=511300&page=1&limit=25&course=kemu1&carType="+carType+"&_=0.7121756361682074";

        String kemu1Result = HttpUtils.sendGet(kemu1);

        JSONObject jsonObject = JSON.parseObject(kemu1Result);
        JSONArray data = jsonObject.getJSONArray("data");

        for (int i = 0; i < data.size(); i++) {
            String number = data.getString(i);
            System.out.println(number+"---------------------------------------------------------------");
            r = getR();
            String str = HttpUtils.sendGet("http://api2.jiakaobaodian.com/api/open/question/question-list.htm?_r=" + r + "&page=1&limit=25&questionIds="+number);
            System.out.println(str);
            System.out.println(number+"---------------------------------------------------------------");
            //写入文件夹C:\Users\<USER>\Desktop\科目1
            boolean exist = FileUtil.exist("C:\\Users\\<USER>\\Desktop\\客车A1A3B1科目1\\" + number + ".js");
            if(!exist){
                FileUtil.writeString(str,"C:\\Users\\<USER>\\Desktop\\客车A1A3B1科目1\\"+number+".js", "UTF-8");
            }

        }
    }

    public static void main(String[] args) {
        String r = getR();
        String carType = "bus";
        //
        //http://api2.jiakaobaodian.com/api/open/exercise/sequence.htm?_r=17801703702540802070&cityCode=511300&page=1&limit=25&course=kemu1&carType=car&_=0.7121756361682074
        //科目1
        String kemu1 = "http://api2.jiakaobaodian.com/api/open/exercise/sequence.htm?_r=" + r + "&cityCode=511300&page=1&limit=25&course=kemu3&carType="+carType+"&_=0.7121756361682074";

        String kemu1Result = HttpUtils.sendGet(kemu1);

        JSONObject jsonObject = JSON.parseObject(kemu1Result);
        JSONArray data = jsonObject.getJSONArray("data");

        for (int i = 0; i < data.size(); i++) {
            String number = data.getString(i);
            System.out.println(number+"---------------------------------------------------------------");
            r = getR();
            String str = HttpUtils.sendGet("http://api2.jiakaobaodian.com/api/open/question/question-list.htm?_r=" + r + "&page=1&limit=25&questionIds="+number);
            System.out.println(str);
            System.out.println(number+"---------------------------------------------------------------");
            //写入文件夹C:\Users\<USER>\Desktop\科目1
            boolean exist = FileUtil.exist("C:\\Users\\<USER>\\Desktop\\客车A1A3B1科目3\\" + number + ".js");
            if(!exist){
                FileUtil.writeString(str,"C:\\Users\\<USER>\\Desktop\\客车A1A3B1科目3\\"+number+".js", "UTF-8");
            }

        }
    }

    private static String getR() {
        String getR = HttpUtils.sendGet("http://localhost:9754/get_r");
        JSONObject jsonObject0 = JSON.parseObject(getR);
        String r = jsonObject0.getString("r");
        return r;
    }
}
