package com.wendao101.job;

import com.wendao101.teacher.domain.CourseOrder;
import com.wendao101.teacher.domain.CourseRefund;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class OrderTest {
//    public static void main(String[] args) {
//
//    }

    public static void main(String[] args) {
        CourseOrder courseOrder = new CourseOrder();
        courseOrder.setStudyDuration(1054L);
        if (courseOrder.getStudyDuration() == 1054L) {
            System.out.println("ok");
        }
//        long studyDuration = courseOrder.getStudyDuration() == null ? 0L : courseOrder.getStudyDuration();
//        // 学习时间小于10分钟直接退
//        if (studyDuration < 600L) {
//            System.out.println("studyDuration < 600L");
//        } else {
//            System.out.println("studyDuration >= 600L");
//        }
    }

    public static void main333(String[] args) throws ParseException {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date refundTime = sdf.parse("2024-02-18 7:07:00");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(refundTime);

        calendar.add(Calendar.HOUR_OF_DAY, 2);
        if (calendar.getTime().compareTo(new Date()) < 0) {
            System.out.println("执行退款");
        } else {
            System.out.println("未到时间");
        }
    }
}
