<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TAudioMapper">
    
    <resultMap type="TAudio" id="TAudioResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="pathUrl"    column="path_url"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileAfterTransSize"    column="file_after_trans_size"    />
        <result property="fileOriginalSize"    column="file_original_size"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="transcodeStatus"    column="transcode_status"    />
        <result property="tcvodFileId"    column="tcvod_file_id"    />
        <result property="tcvodMediaUrlBeforeTrans"    column="tcvod_media_url_before_trans"    />
        <result property="tcvodMediaUrlAfterTrans"    column="tcvod_media_url_after_trans"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="procedureErrorMsg"    column="procedure_error_msg"    />
        <result property="transcodeErrorMsg"    column="transcode_error_msg"    />
        <result property="duration"    column="duration"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="sourceMaterialId"    column="source_material_id"    />
        <result property="oldId"    column="old_id"    />
        <result property="md5"    column="md5"    />
        <result property="sha1"    column="sha1"    />
    </resultMap>

    <sql id="selectTAudioVo">
        select id, teacher_id, path_url, file_name, file_after_trans_size, file_original_size, is_delete, transcode_status, tcvod_file_id, tcvod_media_url_before_trans, tcvod_media_url_after_trans, create_time, update_time, procedure_error_msg, transcode_error_msg, duration, app_name_type, source_material_id, old_id, md5, sha1 from t_audio
    </sql>

    <select id="selectTAudioList" parameterType="TAudio" resultMap="TAudioResult">
        <include refid="selectTAudioVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="pathUrl != null  and pathUrl != ''"> and path_url = #{pathUrl}</if>
            <if test="fileName != null  and fileName != ''"> and file_name like concat('%', #{fileName}, '%')</if>
            <if test="fileAfterTransSize != null "> and file_after_trans_size = #{fileAfterTransSize}</if>
            <if test="fileOriginalSize != null "> and file_original_size = #{fileOriginalSize}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="transcodeStatus != null "> and transcode_status = #{transcodeStatus}</if>
            <if test="tcvodFileId != null  and tcvodFileId != ''"> and tcvod_file_id = #{tcvodFileId}</if>
            <if test="tcvodMediaUrlBeforeTrans != null  and tcvodMediaUrlBeforeTrans != ''"> and tcvod_media_url_before_trans = #{tcvodMediaUrlBeforeTrans}</if>
            <if test="tcvodMediaUrlAfterTrans != null  and tcvodMediaUrlAfterTrans != ''"> and tcvod_media_url_after_trans = #{tcvodMediaUrlAfterTrans}</if>
            <if test="procedureErrorMsg != null  and procedureErrorMsg != ''"> and procedure_error_msg = #{procedureErrorMsg}</if>
            <if test="transcodeErrorMsg != null  and transcodeErrorMsg != ''"> and transcode_error_msg = #{transcodeErrorMsg}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="sourceMaterialId != null "> and source_material_id = #{sourceMaterialId}</if>
            <if test="oldId != null "> and old_id = #{oldId}</if>
            <if test="md5 != null  and md5 != ''"> and md5 = #{md5}</if>
            <if test="sha1 != null  and sha1 != ''"> and sha1 = #{sha1}</if>
        </where>
    </select>
    
    <select id="selectTAudioById" parameterType="Long" resultMap="TAudioResult">
        <include refid="selectTAudioVo"/>
        where id = #{id}
    </select>
    <select id="selectTAudioListByMaxId" parameterType="Long" resultMap="TAudioResult">
        <include refid="selectTAudioVo"/>
        where id > #{maxId} and app_name_type=1 and md5 is null order by id limit 1000
    </select>
    <select id="selectTAudioByUrl" parameterType="String" resultMap="TAudioResult">
            <include refid="selectTAudioVo"/>
            where transcode_status=1 and path_url = #{url} limit 1
    </select>
    <select id="selectByMd5AndTeacherId" resultMap="TAudioResult">
        <include refid="selectTAudioVo"/>
        where md5 = #{md5} and teacher_id != #{teacherId} and app_name_type=#{appNameType}
    </select>

    <select id="selectAudioByUrlOrOriginalUrl" resultMap="TAudioResult">
        <include refid="selectTAudioVo"/>
        where path_url = #{url} or tcvod_media_url_before_trans = #{url} limit 1
    </select>

    <insert id="insertTAudio" parameterType="TAudio" useGeneratedKeys="true" keyProperty="id">
        insert into t_audio
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="pathUrl != null">path_url,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileAfterTransSize != null">file_after_trans_size,</if>
            <if test="fileOriginalSize != null">file_original_size,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="transcodeStatus != null">transcode_status,</if>
            <if test="tcvodFileId != null">tcvod_file_id,</if>
            <if test="tcvodMediaUrlBeforeTrans != null">tcvod_media_url_before_trans,</if>
            <if test="tcvodMediaUrlAfterTrans != null">tcvod_media_url_after_trans,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="procedureErrorMsg != null">procedure_error_msg,</if>
            <if test="transcodeErrorMsg != null">transcode_error_msg,</if>
            <if test="duration != null">duration,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="sourceMaterialId != null">source_material_id,</if>
            <if test="oldId != null">old_id,</if>
            <if test="md5 != null">md5,</if>
            <if test="sha1 != null">sha1,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="pathUrl != null">#{pathUrl},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileAfterTransSize != null">#{fileAfterTransSize},</if>
            <if test="fileOriginalSize != null">#{fileOriginalSize},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="transcodeStatus != null">#{transcodeStatus},</if>
            <if test="tcvodFileId != null">#{tcvodFileId},</if>
            <if test="tcvodMediaUrlBeforeTrans != null">#{tcvodMediaUrlBeforeTrans},</if>
            <if test="tcvodMediaUrlAfterTrans != null">#{tcvodMediaUrlAfterTrans},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="procedureErrorMsg != null">#{procedureErrorMsg},</if>
            <if test="transcodeErrorMsg != null">#{transcodeErrorMsg},</if>
            <if test="duration != null">#{duration},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="sourceMaterialId != null">#{sourceMaterialId},</if>
            <if test="oldId != null">#{oldId},</if>
            <if test="md5 != null">#{md5},</if>
            <if test="sha1 != null">#{sha1},</if>
         </trim>
    </insert>

    <update id="updateTAudio" parameterType="TAudio">
        update t_audio
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="pathUrl != null">path_url = #{pathUrl},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileAfterTransSize != null">file_after_trans_size = #{fileAfterTransSize},</if>
            <if test="fileOriginalSize != null">file_original_size = #{fileOriginalSize},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="transcodeStatus != null">transcode_status = #{transcodeStatus},</if>
            <if test="tcvodFileId != null">tcvod_file_id = #{tcvodFileId},</if>
            <if test="tcvodMediaUrlBeforeTrans != null">tcvod_media_url_before_trans = #{tcvodMediaUrlBeforeTrans},</if>
            <if test="tcvodMediaUrlAfterTrans != null">tcvod_media_url_after_trans = #{tcvodMediaUrlAfterTrans},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="procedureErrorMsg != null">procedure_error_msg = #{procedureErrorMsg},</if>
            <if test="transcodeErrorMsg != null">transcode_error_msg = #{transcodeErrorMsg},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="sourceMaterialId != null">source_material_id = #{sourceMaterialId},</if>
            <if test="oldId != null">old_id = #{oldId},</if>
            <if test="md5 != null">md5 = #{md5},</if>
            <if test="sha1 != null">sha1 = #{sha1},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTAudioById" parameterType="Long">
        delete from t_audio where id = #{id}
    </delete>

    <delete id="deleteTAudioByIds" parameterType="String">
        delete from t_audio where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>