<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.CourseOrderPushRecordMapper">
    
    <resultMap type="CourseOrderPushRecord" id="CourseOrderPushRecordResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="payTime"    column="pay_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="errorMsg"    column="error_msg"    />
    </resultMap>

    <sql id="selectCourseOrderPushRecordVo">
        select id, order_id, push_status, pay_time, create_time, update_time,error_msg from `wendao101-order`.course_order_push_record
    </sql>

    <select id="selectCourseOrderPushRecordList" parameterType="CourseOrderPushRecord" resultMap="CourseOrderPushRecordResult">
        <include refid="selectCourseOrderPushRecordVo"/>
        <where>  
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="pushStatus != null "> and push_status = #{pushStatus}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="errorMsg != null "> and error_msg = #{errorMsg}</if>
        </where>
    </select>
    
    <select id="selectCourseOrderPushRecordById" parameterType="Long" resultMap="CourseOrderPushRecordResult">
        <include refid="selectCourseOrderPushRecordVo"/>
        where id = #{id}
    </select>
    <select id="selectCourseOrderPushRecordByOrderId" resultMap="CourseOrderPushRecordResult">
        <include refid="selectCourseOrderPushRecordVo"/>
        where order_id = #{orderId} limit 1
    </select>

    <insert id="insertCourseOrderPushRecord" parameterType="CourseOrderPushRecord" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.course_order_push_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="errorMsg != null">error_msg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
         </trim>
    </insert>

    <update id="updateCourseOrderPushRecord" parameterType="CourseOrderPushRecord">
        update `wendao101-order`.course_order_push_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseOrderPushRecordById" parameterType="Long">
        delete from `wendao101-order`.course_order_push_record where id = #{id}
    </delete>

    <delete id="deleteCourseOrderPushRecordByIds" parameterType="String">
        delete from `wendao101-order`.course_order_push_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>