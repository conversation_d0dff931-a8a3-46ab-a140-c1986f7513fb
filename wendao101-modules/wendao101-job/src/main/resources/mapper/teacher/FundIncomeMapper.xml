<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.FundIncomeMapper">

    <resultMap type="FundIncome" id="FundIncomeResult">
        <result property="id" column="id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="promoterId" column="promoter_id"/>
        <result property="orderId" column="order_id"/>
        <result property="incomePlatform" column="income_platform"/>
        <result property="incomeType" column="income_type"/>
        <result property="orderNum" column="order_num"/>
        <result property="fundsType" column="funds_type"/>
        <result property="incomePrice" column="income_price"/>
        <result property="accountPriceType" column="account_price_type"/>
        <result property="storeIncomePrice" column="store_income_price"/>
        <result property="storeName" column="store_name"/>
        <result property="servicePrice" column="service_price"/>
        <result property="servicePriceRatio" column="service_price_ratio"/>
        <result property="remark" column="remark"/>
        <result property="orderTime" column="order_time"/>
        <result property="incomePriceTime" column="income_price_time"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>

        <result property="realIncomePrice" column="real_income_price"/>
        <result property="publicationRate" column="publication_rate"/>
        <result property="publicationFee" column="publication_fee"/>
        <result property="logisticsFee" column="logistics_fee"/>
        <result property="liveCommerceRate" column="live_commerce_rate"/>
        <result property="liveCommerceFee" column="live_commerce_fee"/>
        <result property="payPrice" column="pay_price"/>
    </resultMap>

    <sql id="selectFundIncomeVo">
        select id,
               teacher_id,
               promoter_id,
               order_id,
               income_platform,
               income_type,
               order_num,
               funds_type,
               income_price,
               account_price_type,
               store_income_price,
               store_name,
               service_price,
               service_price_ratio,
               remark,
               order_time,
               income_price_time,
               is_delete,
               create_time,
               update_time,
               real_income_price,
               publication_rate,
               publication_fee,
               logistics_fee,
               live_commerce_rate,
               live_commerce_fee,
               pay_price
        from `wendao101-order`.fund_income
    </sql>

    <select id="selectFundIncomeList" parameterType="FundIncome" resultMap="FundIncomeResult">
        <include refid="selectFundIncomeVo"/>
        <where>
            <if test="teacherId != null ">and teacher_id = #{teacherId}</if>
            <if test="promoterId != null ">and promoter_id = #{promoterId}</if>
            <if test="orderId != null  and orderId != ''">and order_id = #{orderId}</if>
            <if test="incomePlatform != null ">and income_platform = #{incomePlatform}</if>
            <if test="incomeType != null ">and income_type = #{incomeType}</if>
            <if test="orderNum != null ">and order_num = #{orderNum}</if>
            <if test="fundsType != null ">and funds_type = #{fundsType}</if>
            <if test="incomePrice != null ">and income_price = #{incomePrice}</if>
            <if test="accountPriceType != null ">and account_price_type = #{accountPriceType}</if>
            <if test="storeIncomePrice != null ">and store_income_price = #{storeIncomePrice}</if>
            <if test="storeName != null  and storeName != ''">and store_name like concat('%', #{storeName}, '%')</if>
            <if test="servicePrice != null ">and service_price = #{servicePrice}</if>
            <if test="servicePriceRatio != null ">and service_price_ratio = #{servicePriceRatio}</if>
            <if test="remark != null ">and remark = #{remark}</if>
            <if test="orderTime != null ">and order_time = #{orderTime}</if>
            <if test="incomePriceTime != null ">and income_price_time = #{incomePriceTime}</if>
            <if test="isDelete != null ">and is_delete = #{isDelete}</if>
        </where>
    </select>

    <select id="getFundIncomeList" parameterType="FundIncome" resultMap="FundIncomeResult">
        <include refid="selectFundIncomeVo"/>
        where income_type != 4 and is_delete = 0
    </select>

    <select id="selectFundIncomeById" parameterType="Long" resultMap="FundIncomeResult">
        <include refid="selectFundIncomeVo"/>
        where id = #{id}
    </select>

    <insert id="insertFundIncome" parameterType="FundIncome" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.fund_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="promoterId != null">promoter_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="incomePlatform != null">income_platform,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="fundsType != null">funds_type,</if>
            <if test="incomePrice != null">income_price,</if>
            <if test="accountPriceType != null">account_price_type,</if>
            <if test="storeIncomePrice != null">store_income_price,</if>
            <if test="storeName != null">store_name,</if>
            <if test="servicePrice != null">service_price,</if>
            <if test="servicePriceRatio != null">service_price_ratio,</if>
            <if test="remark != null">remark,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="incomePriceTime != null">income_price_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="promoterId != null">#{promoterId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="incomePlatform != null">#{incomePlatform},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="fundsType != null">#{fundsType},</if>
            <if test="incomePrice != null">#{incomePrice},</if>
            <if test="accountPriceType != null">#{accountPriceType},</if>
            <if test="storeIncomePrice != null">#{storeIncomePrice},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="servicePrice != null">#{servicePrice},</if>
            <if test="servicePriceRatio != null">#{servicePriceRatio},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="incomePriceTime != null">#{incomePriceTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertFundIncome1" parameterType="FundIncome" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order1`.fund_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="promoterId != null">promoter_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="incomePlatform != null">income_platform,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="fundsType != null">funds_type,</if>
            <if test="incomePrice != null">income_price,</if>
            <if test="accountPriceType != null">account_price_type,</if>
            <if test="storeIncomePrice != null">store_income_price,</if>
            <if test="storeName != null">store_name,</if>
            <if test="servicePrice != null">service_price,</if>
            <if test="servicePriceRatio != null">service_price_ratio,</if>
            <if test="remark != null">remark,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="incomePriceTime != null">income_price_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="promoterId != null">#{promoterId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="incomePlatform != null">#{incomePlatform},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="fundsType != null">#{fundsType},</if>
            <if test="incomePrice != null">#{incomePrice},</if>
            <if test="accountPriceType != null">#{accountPriceType},</if>
            <if test="storeIncomePrice != null">#{storeIncomePrice},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="servicePrice != null">#{servicePrice},</if>
            <if test="servicePriceRatio != null">#{servicePriceRatio},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="incomePriceTime != null">#{incomePriceTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateFundIncome" parameterType="FundIncome">
        update `wendao101-order`.fund_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="incomePlatform != null">income_platform = #{incomePlatform},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="fundsType != null">funds_type = #{fundsType},</if>
            <if test="incomePrice != null">income_price = #{incomePrice},</if>
            <if test="accountPriceType != null">account_price_type = #{accountPriceType},</if>
            <if test="storeIncomePrice != null">store_income_price = #{storeIncomePrice},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
            <if test="servicePriceRatio != null">service_price_ratio = #{servicePriceRatio},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="incomePriceTime != null">income_price_time = #{incomePriceTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateFundIncomeByOrderId">
        <![CDATA[
        UPDATE `wendao101-order`.fund_income
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND order_id IN (
            SELECT
                order_id
            FROM
                `wendao101-order`.course_order
            WHERE
                order_status IN ( 1, 4, 6 )
              AND order_platform != 0 and order_platform != 2
          AND pay_time <= DATE_SUB( NOW(), INTERVAL 7 DAY ) AND pay_time > DATE_SUB( NOW(), INTERVAL 31 DAY )
            )
        ]]>
    </update>
    <update id="updatePromoterFundIncomeByOrderId">
        <![CDATA[
        UPDATE `wendao101-order`.promoter_fund_income
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND is_delete = 0
          AND order_id IN (
            SELECT
                order_id
            FROM
                `wendao101-order`.course_order
            WHERE
                order_status IN ( 1, 4, 6 )
              AND order_platform != 0 and order_platform != 2
          AND pay_time <= DATE_SUB( NOW(), INTERVAL 7 DAY ) AND pay_time > DATE_SUB( NOW(), INTERVAL 31 DAY )
            )
        ]]>
    </update>
    <update id="updateTeacherFlowByOrderId">
        <![CDATA[
        UPDATE `wendao101-order`.teacher_flow_record
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND order_id IN (
            SELECT
                order_id
            FROM
                `wendao101-order`.course_order
            WHERE
                order_status IN ( 1, 4, 6 )
              AND order_platform != 0 and order_platform != 2
          AND pay_time <= DATE_SUB( NOW(), INTERVAL 7 DAY ) AND pay_time > DATE_SUB( NOW(), INTERVAL 31 DAY )
            )
        ]]>
    </update>
    <update id="updateFundIncomeRefundStatus">
        update `wendao101-order`.fund_income set income_type = 4 where order_id = #{orderId}
    </update>
    <update id="updateFundIncomeByOrderIdInOne">
        UPDATE `wendao101-order`.fund_income
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND order_id = #{orderId}
    </update>
    <update id="updatePromoterFundIncomeByOrderIdInOne">
        UPDATE `wendao101-order`.promoter_fund_income
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND is_delete = 0
          AND order_id = #{orderId}
    </update>
    <update id="updateTeacherFlowByOrderIdInOne">
        UPDATE `wendao101-order`.teacher_flow_record
        SET account_price_type = 1
        WHERE
            account_price_type = 0
          AND order_id = #{orderId}
    </update>
    <update id="updateKuaishouAccountPriceTypeFinished">
        update `wendao101-order`.fund_income set account_price_type = 1 where id = #{id}
    </update>

    <delete id="deleteFundIncomeById" parameterType="Long">
        delete
        from `wendao101-order`.fund_income
        where id = #{id}
    </delete>

    <delete id="deleteFundIncomeByIds" parameterType="String">
        delete from `wendao101-order`.fund_income where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getByOrderId" parameterType="String" resultType="Long">
        select id
        from `wendao101-order`.fund_income
        where order_id = #{orderId}
    </select>
    <select id="selectOrderIdListNotSettle" resultType="java.lang.String">
        <![CDATA[
        SELECT
            order_id
        FROM
            `wendao101-order`.fund_income
        WHERE
            account_price_type = 0
          AND order_id IN (
            SELECT
                order_id
            FROM
                `wendao101-order`.course_order
            WHERE
                order_status IN ( 1, 4, 6 )
              AND order_platform = 0
              AND pay_time <= DATE_SUB( NOW(), INTERVAL 7 DAY )
        )
        ]]>
    </select>
    <select id="selectFundIncomeKuaishouNotSettleList" resultMap="FundIncomeResult">
        <include refid="selectFundIncomeVo"/>
         where income_platform=2 and account_price_type=0 and income_type!=4 and income_type!=3 and income_type!=11
    </select>
    <select id="selectSumFundIncomeByTeacherId" resultType="java.math.BigDecimal">
        select sum(income_price) from `wendao101-order`.fund_income where teacher_id=#{teacherId} and funds_type=0 and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
    </select>
    <select id="selectSumFundIncomeForInAccountByTeacherId" resultType="java.math.BigDecimal">
        select sum(income_price) from `wendao101-order`.fund_income where teacher_id=#{teacherId} and funds_type=1 and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
    </select>
    <select id="selectSumFundIncomeForPlatformByTeacherId" resultType="com.wendao101.job.dto.PlatformMoneyDTO">
        select sum(income_price) as totalMoney, income_platform as platform from `wendao101-order`.fund_income where teacher_id=#{teacherId} and income_type !=3 and income_type!=4 group by income_platform
    </select>
    <select id="selectSumFundIncomeAllByTeacherId" resultType="java.math.BigDecimal">
        select sum(income_price) from `wendao101-order`.fund_income where teacher_id=#{teacherId} and income_type !=3 and income_type!=4
    </select>
    <select id="selectSumFundIncomeForPromoterIds" resultType="java.math.BigDecimal">
        select sum(income_price) from `wendao101-order`.fund_income where promoter_id in
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
         and funds_type=0 and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
    </select>
    <select id="selectSumFundIncomeForInAccountByPromoterIds" resultType="java.math.BigDecimal">
        select sum(income_price) from `wendao101-order`.fund_income where promoter_id in
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
         and funds_type=1 and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
    </select>
    <select id="queryMoneyByPlatform" resultType="com.wendao101.job.dto.TeacherMoneyDTO">
        select sum(income_price) as totalMoney, income_platform as incomePlatform from `wendao101-order`.fund_income where teacher_id=#{teacherId} and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
         GROUP BY income_platform
    </select>
    <select id="queryPromoterMoneyByPlatform" resultType="com.wendao101.job.dto.TeacherMoneyDTO">
        select sum(income_price) as totalMoney,promoter_id as promoterId, income_platform as incomePlatform from `wendao101-order`.fund_income where promoter_id in
        <foreach item="promoterId" collection="list" open="(" separator="," close=")">
            #{promoterId}
        </foreach>
         and income_type !=3 and income_type!=4
        <if test="beginTime != null">
            <![CDATA[ and create_time>=#{beginTime} ]]>
        </if>
        <if test="endTime != null">
            <![CDATA[ and create_time<=#{endTime} ]]>
        </if>
        GROUP BY income_platform,promoter_id
    </select>

    <delete id="deleteFundIncomeByOrderIds" parameterType="Long">
        delete from `wendao101-order`.fund_income where id in
        <foreach item="item" collection="orderIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>
</mapper>