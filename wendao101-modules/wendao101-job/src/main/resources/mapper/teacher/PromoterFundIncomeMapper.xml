<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PromoterFundIncomeMapper">
    
    <resultMap type="PromoterFundIncome" id="PromoterFundIncomeResult">
        <result property="id"    column="id"    />
        <result property="promoterId"    column="promoter_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="refundId"    column="refund_id"    />
        <result property="withdrawOrderNumber"    column="withdraw_order_number"    />
        <result property="courseTitle"    column="course_title"    />
        <result property="incomePlatform"    column="income_platform"    />
        <result property="incomeType"    column="income_type"    />
        <result property="fundsType"    column="funds_type"    />
        <result property="orderPayPrice"    column="order_pay_price"    />
        <result property="refundPrice"    column="refund_price"    />
        <result property="withdrawPrice"    column="withdraw_price"    />
        <result property="incomePrice"    column="income_price"    />
        <result property="accountPriceType"    column="account_price_type"    />
        <result property="promoterRate"    column="promoter_rate"    />
        <result property="servicePrice"    column="service_price"    />
        <result property="servicePriceRatio"    column="service_price_ratio"    />
        <result property="incomePriceTime"    column="income_price_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectPromoterFundIncomeVo">
        select id, promoter_id, order_id, refund_id, withdraw_order_number, course_title, income_platform, income_type, funds_type, order_pay_price, refund_price, withdraw_price, income_price, account_price_type, promoter_rate, service_price, service_price_ratio,income_price_time, is_delete, create_time, update_time from `wendao101-order`.promoter_fund_income
    </sql>

    <select id="selectPromoterFundIncomeList" parameterType="PromoterFundIncome" resultMap="PromoterFundIncomeResult">
        <include refid="selectPromoterFundIncomeVo"/>
        <where>
            <if test="promoterId != null "> and promoter_id = #{promoterId}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="refundId != null  and refundId != ''"> and refund_id = #{refundId}</if>
            <if test="withdrawOrderNumber != null  and withdrawOrderNumber != ''"> and withdraw_order_number = #{withdrawOrderNumber}</if>
            <if test="courseTitle != null  and courseTitle != ''"> and course_title = #{courseTitle}</if>
            <if test="incomePlatform != null "> and income_platform = #{incomePlatform}</if>
            <if test="incomeType != null "> and income_type = #{incomeType}</if>
            <if test="fundsType != null "> and funds_type = #{fundsType}</if>
            <if test="orderPayPrice != null "> and order_pay_price = #{orderPayPrice}</if>
            <if test="refundPrice != null "> and refund_price = #{refundPrice}</if>
            <if test="withdrawPrice != null "> and withdraw_price = #{withdrawPrice}</if>
            <if test="incomePrice != null "> and income_price = #{incomePrice}</if>
            <if test="accountPriceType != null "> and account_price_type = #{accountPriceType}</if>
            <if test="promoterRate != null "> and promoter_rate = #{promoterRate}</if>
            <if test="servicePrice != null "> and service_price = #{servicePrice}</if>
            <if test="servicePriceRatio != null "> and service_price_ratio = #{servicePriceRatio}</if>
            <if test="incomePriceTime != null "> and income_price_time = #{incomePriceTime}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectPromoterFundIncomeById" parameterType="Long" resultMap="PromoterFundIncomeResult">
        <include refid="selectPromoterFundIncomeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPromoterFundIncome" parameterType="PromoterFundIncome" useGeneratedKeys="true" keyProperty="id">
        insert into `wendao101-order`.promoter_fund_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="promoterId != null">promoter_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="refundId != null">refund_id,</if>
            <if test="withdrawOrderNumber != null">withdraw_order_number,</if>
            <if test="courseTitle != null">course_title,</if>
            <if test="incomePlatform != null">income_platform,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="fundsType != null">funds_type,</if>
            <if test="orderPayPrice != null">order_pay_price,</if>
            <if test="refundPrice != null">refund_price,</if>
            <if test="withdrawPrice != null">withdraw_price,</if>
            <if test="incomePrice != null">income_price,</if>
            <if test="accountPriceType != null">account_price_type,</if>
            <if test="promoterRate != null">promoter_rate,</if>
            <if test="servicePrice != null">service_price,</if>
            <if test="servicePriceRatio != null">service_price_ratio,</if>
            <if test="incomePriceTime != null">income_price_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="promoterId != null">#{promoterId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="refundId != null">#{refundId},</if>
            <if test="withdrawOrderNumber != null">#{withdrawOrderNumber},</if>
            <if test="courseTitle != null">#{courseTitle},</if>
            <if test="incomePlatform != null">#{incomePlatform},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="fundsType != null">#{fundsType},</if>
            <if test="orderPayPrice != null">#{orderPayPrice},</if>
            <if test="refundPrice != null">#{refundPrice},</if>
            <if test="withdrawPrice != null">#{withdrawPrice},</if>
            <if test="incomePrice != null">#{incomePrice},</if>
            <if test="accountPriceType != null">#{accountPriceType},</if>
            <if test="promoterRate != null">#{promoterRate},</if>
            <if test="servicePrice != null">#{servicePrice},</if>
            <if test="servicePriceRatio != null">#{servicePriceRatio},</if>
            <if test="incomePriceTime != null">#{incomePriceTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updatePromoterFundIncome" parameterType="PromoterFundIncome">
        update `wendao101-order`.promoter_fund_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="refundId != null">refund_id = #{refundId},</if>
            <if test="withdrawOrderNumber != null">withdraw_order_number = #{withdrawOrderNumber},</if>
            <if test="courseTitle != null">course_title = #{courseTitle},</if>
            <if test="incomePlatform != null">income_platform = #{incomePlatform},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="fundsType != null">funds_type = #{fundsType},</if>
            <if test="orderPayPrice != null">order_pay_price = #{orderPayPrice},</if>
            <if test="refundPrice != null">refund_price = #{refundPrice},</if>
            <if test="withdrawPrice != null">withdraw_price = #{withdrawPrice},</if>
            <if test="incomePrice != null">income_price = #{incomePrice},</if>
            <if test="accountPriceType != null">account_price_type = #{accountPriceType},</if>
            <if test="promoterRate != null">promoter_rate = #{promoterRate},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
            <if test="servicePriceRatio != null">service_price_ratio = #{servicePriceRatio},</if>
            <if test="incomePriceTime != null">income_price_time = #{incomePriceTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updatePromoterFundIncomeByFix">
        update `wendao101-order`.promoter_fund_income set refund_price = #{promoterEarningsPrice},refund_id = #{refundId},income_type = 3 
        where order_id = #{orderId} and is_delete = 0
    </update>

    <delete id="deletePromoterFundIncomeById" parameterType="Long">
        delete from `wendao101-order`.promoter_fund_income where id = #{id}
    </delete>

    <delete id="deletePromoterFundIncomeByIds" parameterType="String">
        delete from `wendao101-order`.promoter_fund_income where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>