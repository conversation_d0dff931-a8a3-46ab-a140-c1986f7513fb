<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.UserMapper">

    <resultMap type="User" id="UserResult">
        <result property="id"    column="id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="platform"    column="platform"    />
        <result property="openId"    column="open_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="telNumber"    column="tel_number"    />
        <result property="isAdmin"    column="is_admin"    />
        <result property="gender"    column="gender"    />
        <result property="city"    column="city"    />
        <result property="province"    column="province"    />
        <result property="country"    column="country"    />
        <result property="userName"    column="user_name"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectUserVo">
        select id, nick_name, platform, open_id, union_id,
               avatar_url, tel_number, is_admin, gender,
               city, province, country, user_name,app_name_type
        from wendao_user
    </sql>

    <select id="selectUserList" parameterType="User" resultMap="UserResult">
        <include refid="selectUserVo"/>
        <where>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="platform != null "> and platform = #{platform}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="telNumber != null  and telNumber != ''"> and tel_number = #{telNumber}</if>
            <if test="id != null"> and id = #{id}</if>
            <if test="isAdmin != null "> and is_admin = #{isAdmin}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="appNameType != null"> and app_name_type =#{appNameType}</if>
        </where>
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where id = #{id}
    </select>

    <select id="selectUserByOpenId" parameterType="String" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where open_id = #{openId}
    </select>

    <insert id="insertUser" parameterType="User" useGeneratedKeys="true" keyProperty="id">
        insert into wendao_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nickName != null">nick_name,</if>
            <if test="platform != null">platform,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="telNumber != null">tel_number,</if>
            <if test="isAdmin != null">is_admin,</if>
            <if test="gender != null">gender,</if>
            <if test="city != null">city,</if>
            <if test="province != null">province,</if>
            <if test="country != null">country,</if>
            <if test="userName != null">user_name,</if>
            <if test="appNameType != null">app_name_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nickName != null">#{nickName},</if>
            <if test="platform != null">#{platform},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="telNumber != null">#{telNumber},</if>
            <if test="isAdmin != null">#{isAdmin},</if>
            <if test="gender != null">#{gender},</if>
            <if test="city != null">#{city},</if>
            <if test="province != null">#{province},</if>
            <if test="country != null">#{country},</if>
            <if test="userName != null">#{userName},</if>
            <if test="appNameType != null">#{appNameType},</if>
        </trim>
        <selectKey resultType="Long" keyProperty="id" order="AFTER">
            select LAST_INSERT_ID()
        </selectKey>
    </insert>

    <update id="updateUser" parameterType="User">
        update wendao_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="telNumber != null">tel_number = #{telNumber},</if>
            <if test="isAdmin != null">is_admin = #{isAdmin},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="city != null">city = #{city},</if>
            <if test="province != null">province = #{province},</if>
            <if test="country != null">country = #{country},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectUserByPhone" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where tel_number = #{phone}
    </select>
    <select id="selectUserByPhoneAndAppNameType" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where tel_number = #{phone} and app_name_type = #{appNameType}
    </select>

    <select id="selectUserByPhoneAndPlatform" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where tel_number = #{phone} and platform = #{platform} and app_name_type = #{appNameType}
    </select>

    <select id="getUserByPhone" resultMap="UserResult">
        <include refid="selectUserVo"/>
        where tel_number = #{phone}
    </select>



    <delete id="deleteUserById" parameterType="Long">
        delete from wendao_user where id = #{id}
    </delete>

    <delete id="deleteUserByIds" parameterType="String">
        delete from wendao_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>