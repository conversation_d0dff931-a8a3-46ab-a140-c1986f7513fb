<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MDiscountsCodeMapMapper">

    <resultMap type="MDiscountsCodeMap" id="MDiscountsCodeMapResult">
        <result property="id"    column="id"    />
        <result property="discountsId"    column="discounts_id"    />
        <result property="discountsCode"    column="discounts_code"    />
        <result property="useType"    column="use_type"    />
        <result property="userId"    column="user_id"    />
        <result property="nickName"    column="nick_name"    />
        <result property="invalidationTime"    column="invalidation_time"    />
        <result property="getTime"    column="get_time"    />
        <result property="useTime"    column="use_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMDiscountsCodeMapVo">
        select id, discounts_id, discounts_code, use_type, user_id, nick_name, invalidation_time, get_time, use_time, create_time, update_time from m_discounts_code_map
    </sql>

    <select id="selectMDiscountsCodeMapList" parameterType="MDiscountsCodeMap" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        <where>
            <if test="discountsId != null "> and discounts_id = #{discountsId}</if>
            <if test="discountsCode != null  and discountsCode != ''"> and discounts_code like concat('%', #{discountsCode}, '%')</if>
            <if test="useType != null "> and use_type = #{useType}</if>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="invalidationTime != null "> and invalidation_time = #{invalidationTime}</if>
            <if test="getTime != null "> and get_time = #{getTime}</if>
            <if test="useTime != null "> and use_time = #{useTime}</if>
        </where>
    </select>

    <select id="selectMDiscountsCodeMapById" parameterType="Long" resultMap="MDiscountsCodeMapResult">
        <include refid="selectMDiscountsCodeMapVo"/>
        where id = #{id}
    </select>

    <insert id="insertMDiscountsCodeMap" parameterType="MDiscountsCodeMap" useGeneratedKeys="true" keyProperty="id">
        insert into m_discounts_code_map
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">discounts_id,</if>
            <if test="discountsCode != null">discounts_code,</if>
            <if test="useType != null">use_type,</if>
            <if test="userId != null">user_id,</if>
            <if test="nickName != null">nick_name,</if>
            <if test="invalidationTime != null">invalidation_time,</if>
            <if test="getTime != null">get_time,</if>
            <if test="useTime != null">use_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="discountsId != null">#{discountsId},</if>
            <if test="discountsCode != null">#{discountsCode},</if>
            <if test="useType != null">#{useType},</if>
            <if test="userId != null">#{userId},</if>
            <if test="nickName != null">#{nickName},</if>
            <if test="invalidationTime != null">#{invalidationTime},</if>
            <if test="getTime != null">#{getTime},</if>
            <if test="useTime != null">#{useTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateMDiscountsCodeMap" parameterType="MDiscountsCodeMap">
        update m_discounts_code_map
        <trim prefix="SET" suffixOverrides=",">
            <if test="discountsId != null">discounts_id = #{discountsId},</if>
            <if test="discountsCode != null">discounts_code = #{discountsCode},</if>
            <if test="useType != null">use_type = #{useType},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="nickName != null">nick_name = #{nickName},</if>
            <if test="invalidationTime != null">invalidation_time = #{invalidationTime},</if>
            <if test="getTime != null">get_time = #{getTime},</if>
            <if test="useTime != null">use_time = #{useTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateMDiscountsCodeMapStatus">
        update m_discounts_code_map
        set use_type = 2, invalidation_time = NOW()
        where discounts_id = #{discountsId} and (use_type = 0 or use_type = 3)
    </update>

    <update id="updateMDiscountsCodeMapValidTimeStatus">
        update m_discounts_code_map
        set use_type = 2, invalidation_time = NOW()
        where discounts_id = #{discountsId} and (use_type = 0 or use_type = 3) and  DATE_ADD(get_time, INTERVAL #{validTime} SECOND) &lt; NOW()
    </update>

    <select id="selectAll" resultType="java.lang.Integer">
        select count(1) from m_discounts_code_map
        where discounts_id = #{discountsId} and use_type != 3
    </select>

    <delete id="deleteMDiscountsCodeMapById" parameterType="Long">
        delete from m_discounts_code_map where id = #{id}
    </delete>

    <delete id="deleteMDiscountsCodeMapByIds" parameterType="String">
        delete from m_discounts_code_map where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>