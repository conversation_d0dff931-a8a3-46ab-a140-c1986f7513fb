package com.wendao101.teacher.service;


import com.wendao101.common.core.douyin.clock.request.QueryClockRecordRequest;
import com.wendao101.teacher.domain.ClockInQuest;

import java.util.List;

/**
 * 打卡任务Service接口
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
public interface IClockInQuestTaskService {
    /**
     * 查询打卡任务
     *
     * @param id 打卡任务主键
     * @return 打卡任务
     */
    public ClockInQuest selectClockInQuestById(Long id);

    /**
     * 查询打卡任务列表
     *
     * @param clockInQuest 打卡任务
     * @return 打卡任务集合
     */
    public List<ClockInQuest> selectClockInQuestList(ClockInQuest clockInQuest);

    /**
     * 新增打卡任务
     *
     * @param clockInQuest 打卡任务
     * @return 结果
     */
    public int insertClockInQuest(ClockInQuest clockInQuest);

    /**
     * 修改打卡任务
     *
     * @param clockInQuest 打卡任务
     * @return 结果
     */
    public int updateClockInQuest(ClockInQuest clockInQuest);

    /**
     * 批量删除打卡任务
     *
     * @param ids 需要删除的打卡任务主键集合
     * @return 结果
     */
    public int deleteClockInQuestByIds(Long[] ids);

    /**
     * 删除打卡任务信息
     *
     * @param id 打卡任务主键
     * @return 结果
     */
    public int deleteClockInQuestById(Long id);

//    /**
//     * 新增打卡任务
//     *
//     * @param clockInQuestVO
//     * @return
//     */
//    void addClockInQuest(ClockInQuestVO clockInQuestVO);
//
//    /**
//     * 打卡信息详情
//     *
//     * @param id
//     * @return
//     */
//    ClockInQuestDTO getById(Long id);
//
//    /**
//     * 修改打卡任务
//     *
//     * @param clockInQuestVO
//     */
//    void updateQuest(ClockInQuestVO clockInQuestVO);
//
//    /**
//     * 打卡任务列表
//     *
//     * @param clockInQuestAllVO
//     * @return
//     */
//    PageInfo<ClockInQuestAllDTO> selectAll(ClockInQuestAllVO clockInQuestAllVO);

    /**
     * 查询该老师下的课程是否已经有打卡任务
     *
     * @param recordRequest
     * @return
     */
    int getByCourseId(QueryClockRecordRequest recordRequest);

    /**
     * 根据课程id查询打卡任务
     *
     * @param courseId
     * @return
     */
    com.wendao101.common.core.douyin.clock.response.ClockInQuestDTO getClockInQuestByCourseId(Long courseId);

    /**
     * 修改打卡任务状态
     */
    void updateQuestStatus();
}
