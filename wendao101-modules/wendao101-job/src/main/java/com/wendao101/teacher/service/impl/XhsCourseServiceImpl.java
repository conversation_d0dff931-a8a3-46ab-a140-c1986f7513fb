package com.wendao101.teacher.service.impl;

import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.XhsCourse;
import com.wendao101.teacher.mapper.XhsCourseMapper;
import com.wendao101.teacher.service.IXhsCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小红书课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-01
 */
@Service
public class XhsCourseServiceImpl implements IXhsCourseService 
{
    @Autowired
    private XhsCourseMapper xhsCourseMapper;

    /**
     * 查询小红书课程
     * 
     * @param id 小红书课程主键
     * @return 小红书课程
     */
    @Override
    public XhsCourse selectXhsCourseById(Long id)
    {
        return xhsCourseMapper.selectXhsCourseById(id);
    }

    /**
     * 查询小红书课程列表
     * 
     * @param xhsCourse 小红书课程
     * @return 小红书课程
     */
    @Override
    public List<XhsCourse> selectXhsCourseList(XhsCourse xhsCourse)
    {
        return xhsCourseMapper.selectXhsCourseList(xhsCourse);
    }

    /**
     * 新增小红书课程
     * 
     * @param xhsCourse 小红书课程
     * @return 结果
     */
    @Override
    public int insertXhsCourse(XhsCourse xhsCourse)
    {
        xhsCourse.setCreateTime(DateUtils.getNowDate());
        return xhsCourseMapper.insertXhsCourse(xhsCourse);
    }

    /**
     * 修改小红书课程
     * 
     * @param xhsCourse 小红书课程
     * @return 结果
     */
    @Override
    public int updateXhsCourse(XhsCourse xhsCourse)
    {
        xhsCourse.setUpdateTime(DateUtils.getNowDate());
        return xhsCourseMapper.updateXhsCourse(xhsCourse);
    }

    /**
     * 批量删除小红书课程
     * 
     * @param ids 需要删除的小红书课程主键
     * @return 结果
     */
    @Override
    public int deleteXhsCourseByIds(List<Long> idList,Long teacherId)
    {
        return xhsCourseMapper.deleteXhsCourseByIds(idList,teacherId);
    }

    /**
     * 删除小红书课程信息
     * 
     * @param id 小红书课程主键
     * @return 结果
     */
    @Override
    public int deleteXhsCourseById(Long id)
    {
        return xhsCourseMapper.deleteXhsCourseById(id);
    }

    @Override
    public XhsCourse selectXhsCourseByIdAndTeacherId(Long id, Long teacherId) {
        return xhsCourseMapper.selectXhsCourseByIdAndTeacherId(id,teacherId);
    }

    @Override
    public XhsCourse selectXhsCourseByItemId(String itemId) {
        return xhsCourseMapper.selectXhsCourseByItemId(itemId);
    }

    @Override
    public XhsCourse selectXhsCourseBySkuId(String skuId) {
        return xhsCourseMapper.selectXhsCourseBySkuId(skuId);
    }

    @Override
    public List<XhsCourse> selectXhsCourseInAuditList() {
        return xhsCourseMapper.selectXhsCourseInAuditList();
    }
}
