package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.TStatisticsReport;

/**
 * 首页数据报统计数据Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITStatisticsReportService 
{
    /**
     * 查询首页数据报统计数据
     * 
     * @param id 首页数据报统计数据主键
     * @return 首页数据报统计数据
     */
    public TStatisticsReport selectTStatisticsReportById(Long id);

    /**
     * 查询首页数据报统计数据列表
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 首页数据报统计数据集合
     */
    public List<TStatisticsReport> selectTStatisticsReportList(TStatisticsReport tStatisticsReport);

    /**
     * 新增首页数据报统计数据
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 结果
     */
    public int insertTStatisticsReport(TStatisticsReport tStatisticsReport);

    /**
     * 修改首页数据报统计数据
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 结果
     */
    public int updateTStatisticsReport(TStatisticsReport tStatisticsReport);

    /**
     * 批量删除首页数据报统计数据
     * 
     * @param ids 需要删除的首页数据报统计数据主键集合
     * @return 结果
     */
    public int deleteTStatisticsReportByIds(Long[] ids);

    /**
     * 删除首页数据报统计数据信息
     * 
     * @param id 首页数据报统计数据主键
     * @return 结果
     */
    public int deleteTStatisticsReportById(Long id);
}
