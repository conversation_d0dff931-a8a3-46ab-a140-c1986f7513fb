package com.wendao101.teacher.mapper;

import com.wendao101.teacher.domain.User;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问到用户Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface UserMapper 
{
    /**
     * 查询问到用户
     * 
     * @param id 问到用户主键
     * @return 问到用户
     */
    public User selectUserById(Long id);

    /**
     * 查询问到用户列表
     * 
     * @param user 问到用户
     * @return 问到用户集合
     */
    public List<User> selectUserList(User user);

    /**
     * 新增问到用户
     * 
     * @param user 问到用户
     * @return 结果
     */
    public int insertUser(User user);

    /**
     * 修改问到用户
     * 
     * @param user 问到用户
     * @return 结果
     */
    public int updateUser(User user);

    /**
     * 删除问到用户
     * 
     * @param id 问到用户主键
     * @return 结果
     */
    public int deleteUserById(Long id);

    List<User> selectUserByPhone(String phone);

    List<User> selectUserByPhoneAndAppNameType(@Param("phone") String phone,@Param("appNameType")Integer appNameType);

    User selectUserByPhoneAndPlatform(@Param("phone") String phone,@Param("platform")Integer platform,@Param("appNameType")Integer appNameType);

    User getUserByPhone(String phone);


    /**
     * 批量删除问到用户
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteUserByIds(Long[] ids);


    User selectUserByOpenId(@Param("openId") String openId);
}
