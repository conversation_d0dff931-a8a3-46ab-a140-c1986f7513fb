package com.wendao101.teacher.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现记录对象 withdraw_record
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class WithdrawRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    private Long teacherId;

    /** 推广员id */
    private Long promoterId;

    /** 提现订单号 */
    private String withdrawOrderNumber;

    /** 提现人名称 */
    private String withdrawName;

    /** 提现人头像 */
    private String withdrawNameImg;

    /** 在途金额 */
    private BigDecimal fundsTransitPrice;

    /** 可提现金额 */
    private BigDecimal mayWithdrawPrice;

    /** 提现金额 */
    private BigDecimal withdrawPrice;

    /** 提现账户 */
    private String accountPhone;

    /** 到账金额 */
    private BigDecimal accountPrice;

    /** 服务费比例 */
    private Integer servicePriceRatio;

    /** 服务费 */
    private BigDecimal servicePrice;

    /** 全部平台 0抖音 1微信 2快手 3视频号 */
    private Integer incomePlatform;

    /** 提现账户 0支付宝提现 1公司账户提现 2余额划转 */
    private Integer accountType;

    /** 申请提现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawApplyTime;

    /** 提现到账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawAccountTime;

    /** 财务处理时间（取通过，驳回时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date processingTime;

    /** 第三方处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tripartiteProcessingTime;

    /** 打款流水号 */
    private String paymentSequenceNumber;

    /** 支付宝转账订单号 */
    private String zfbOrderId;

    /** 提现状态 0提现成功 1提现中 2提现失败 */
    private Integer withdrawStatus;

    /** 提现审核状态,0审核成功，1待审核，2审核驳回，3打款失败,4暂不处理 */
    private Integer withdrawAuditStatus;

    /** 实体类型 1: 个人 2:机构 3:公共资质 */
    private Integer entityType;

    /** 备注信息 */
    private String remarkMessage;

    /** 凭证 */
    private String evidence;

    /** 是否删除 0否 1是 */
    private Integer isDelete;
} 