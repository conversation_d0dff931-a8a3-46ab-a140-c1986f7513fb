package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.TVideo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.TPicMapper;
import com.wendao101.teacher.domain.TPic;
import com.wendao101.teacher.service.ITPicService;

/**
 * 素材图片Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-12-28
 */
@Service
public class TPicServiceImpl implements ITPicService 
{
    @Autowired
    private TPicMapper tPicMapper;

    /**
     * 查询素材图片
     * 
     * @param id 素材图片主键
     * @return 素材图片
     */
    @Override
    public TPic selectTPicById(Long id)
    {
        return tPicMapper.selectTPicById(id);
    }

    /**
     * 查询素材图片列表
     * 
     * @param tPic 素材图片
     * @return 素材图片
     */
    @Override
    public List<TPic> selectTPicList(TPic tPic)
    {
        return tPicMapper.selectTPicList(tPic);
    }

    /**
     * 新增素材图片
     * 
     * @param tPic 素材图片
     * @return 结果
     */
    @Override
    public int insertTPic(TPic tPic)
    {
        tPic.setCreateTime(DateUtils.getNowDate());
        return tPicMapper.insertTPic(tPic);
    }

    /**
     * 修改素材图片
     * 
     * @param tPic 素材图片
     * @return 结果
     */
    @Override
    public int updateTPic(TPic tPic)
    {
        tPic.setUpdateTime(DateUtils.getNowDate());
        return tPicMapper.updateTPic(tPic);
    }

    /**
     * 批量删除素材图片
     * 
     * @param ids 需要删除的素材图片主键
     * @return 结果
     */
    @Override
    public int deleteTPicByIds(Long[] ids)
    {
        return tPicMapper.deleteTPicByIds(ids);
    }

    /**
     * 删除素材图片信息
     * 
     * @param id 素材图片主键
     * @return 结果
     */
    @Override
    public int deleteTPicById(Long id)
    {
        return tPicMapper.deleteTPicById(id);
    }

    @Override
    public List<TPic> selectTPicListByMaxId(Long maxId) {
        return tPicMapper.selectTPicListByMaxId(maxId);
    }

    @Override
    public TPic selectTPicByUrl(String courseDirectoryUrl) {
        return tPicMapper.selectTPicByUrl(courseDirectoryUrl);
    }

    @Override
    public List<TPic> selectByMd5AndTeacherId(String md5, Long teacherId,Integer appNameType) {
         return tPicMapper.selectByMd5AndTeacherId(md5,teacherId,appNameType);
    }
}
