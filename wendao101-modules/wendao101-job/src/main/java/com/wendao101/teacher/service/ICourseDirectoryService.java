package com.wendao101.teacher.service;

import com.wendao101.teacher.domain.CourseDirectory;

import java.util.ArrayList;
import java.util.List;

/**
 * 目录Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-29
 */
public interface ICourseDirectoryService
{
    /**
     * 查询目录
     * 
     * @param id 目录主键
     * @return 目录
     */
    public CourseDirectory selectCourseDirectoryById(Long id);

    /**
     * 查询目录列表
     * 
     * @param courseDirectory 目录
     * @return 目录集合
     */
    public List<CourseDirectory> selectCourseDirectoryList(CourseDirectory courseDirectory);

    /**
     * 新增目录
     * 
     * @param courseDirectory 目录
     * @return 结果
     */
    public int insertCourseDirectory(CourseDirectory courseDirectory);

    /**
     * 修改目录
     * 
     * @param courseDirectory 目录
     * @return 结果
     */
    public int updateCourseDirectory(CourseDirectory courseDirectory);

    /**
     * 批量删除目录
     * 
     * @param ids 需要删除的目录主键集合
     * @return 结果
     */
    public int deleteCourseDirectoryByIds(Long[] ids);

    /**
     * 删除目录信息
     * 
     * @param id 目录主键
     * @return 结果
     */
    public int deleteCourseDirectoryById(Long id);


    List<CourseDirectory> selectCourseDirectoryDyByCourseDirectoryUrl(String tcvodMediaUrlBeforeTrans);

    void deleteCourseDirectoryWhereChapterIdIsNull(Long courseId);

    void deleteCourseDirectoryByChapterIds(Long[] chapterIds);

    int updateCourseDirectorySetChapterIdNull(CourseDirectory courseDirectory);

    List<CourseDirectory> selectCourseDirectoryListByChapterId(Long id);

    List<CourseDirectory> selectCourseDirectoryWhereChapterIdIsNull(CourseDirectory courseDirectoryQuery);

    List<CourseDirectory> selectCourseDirectoryListByMaxId(Long maxId);

    int deleteCourseDirectoryByCourseId(Long ktCourseId);

    void batchInsert(List<CourseDirectory> list);

    void updateCourseDirectoryByOldUrl(String key, String value);
}
