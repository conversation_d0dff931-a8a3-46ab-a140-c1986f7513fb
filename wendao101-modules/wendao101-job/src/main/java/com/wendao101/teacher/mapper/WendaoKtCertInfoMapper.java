package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.WendaoKtCertInfo;

/**
 * 问到课堂资质中心信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-20
 */
public interface WendaoKtCertInfoMapper 
{
    /**
     * 查询问到课堂资质中心信息
     * 
     * @param id 问到课堂资质中心信息主键
     * @return 问到课堂资质中心信息
     */
    public WendaoKtCertInfo selectWendaoKtCertInfoById(Long id);

    /**
     * 查询问到课堂资质中心信息列表
     * 
     * @param wendaoKtCertInfo 问到课堂资质中心信息
     * @return 问到课堂资质中心信息集合
     */
    public List<WendaoKtCertInfo> selectWendaoKtCertInfoList(WendaoKtCertInfo wendaoKtCertInfo);

    /**
     * 新增问到课堂资质中心信息
     * 
     * @param wendaoKtCertInfo 问到课堂资质中心信息
     * @return 结果
     */
    public int insertWendaoKtCertInfo(WendaoKtCertInfo wendaoKtCertInfo);

    /**
     * 修改问到课堂资质中心信息
     * 
     * @param wendaoKtCertInfo 问到课堂资质中心信息
     * @return 结果
     */
    public int updateWendaoKtCertInfo(WendaoKtCertInfo wendaoKtCertInfo);

    /**
     * 删除问到课堂资质中心信息
     * 
     * @param id 问到课堂资质中心信息主键
     * @return 结果
     */
    public int deleteWendaoKtCertInfoById(Long id);

    /**
     * 批量删除问到课堂资质中心信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoKtCertInfoByIds(Long[] ids);
}
