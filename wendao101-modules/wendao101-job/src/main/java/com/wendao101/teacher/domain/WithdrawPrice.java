package com.wendao101.teacher.domain;

import java.math.BigDecimal;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 提现金额对象 withdraw_price
 * 
 * <AUTHOR>
 * @date 2023-09-02
 */
@Data
public class WithdrawPrice extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 在途金额 */
    @Excel(name = "在途金额")
    private BigDecimal fundsTransitPrice;

    /** 抖音在途金额*/
    @Excel(name = "抖音在途金额")
    private BigDecimal dyFundsTransitPrice;

    /** 快手在途金额*/
    @Excel(name = "快手在途金额")
    private BigDecimal ksFundsTransitPrice;

    /** 微信在途金额*/
    @Excel(name = "微信在途金额")
    private BigDecimal wxFundsTransitPrice;

    /** 视频号在途金额*/
    @Excel(name = "视频号在途金额")
    private BigDecimal sphFundsTransitPrice;

    /** 抖音可提现金额 */
    @Excel(name = "抖音可提现金额")
    private BigDecimal dyMayWithdrawPrice;

    /** 抖音已提现金额 */
    @Excel(name = "抖音已提现金额")
    private BigDecimal dyWithdrawnPrice;

    /** 快手可提现金额 */
    @Excel(name = "快手可提现金额")
    private BigDecimal ksMayWithdrawPrice;

    /** 快手已提现金额 */
    @Excel(name = "快手已提现金额")
    private BigDecimal ksWithdrawnPrice;

    /** 微信可提现金额 */
    @Excel(name = "微信可提现金额")
    private BigDecimal wxMayWithdrawPrice;

    /** 微信已提现金额 */
    @Excel(name = "微信已提现金额")
    private BigDecimal wxWithdrawnPrice;

    /** 视频号可提现金额 */
    @Excel(name = "视频号可提现金额")
    private BigDecimal sphMayWithdrawPrice;

    /** 视频号已提现金额 */
    @Excel(name = "视频号已提现金额")
    private BigDecimal sphWithdrawnPrice;

    /** 可提现金额 */
    @Excel(name = "可提现金额")
    private BigDecimal mayWithdrawPrice;

    /** 累计提现总金额 */
    @Excel(name = "累计提现总金额")
    private BigDecimal totalWithdrawPrice;

    /** 抖音冻结状态 */
    @Excel(name = "抖音冻结状态")
    private Integer dyFreezeStatus;

    /** 快手冻结状态 */
    @Excel(name = "快手冻结状态")
    private Integer ksFreezeStatus;

    /** 微信冻结状态 */
    @Excel(name = "微信冻结状态")
    private Integer wxFreezeStatus;

    /** 视频号冻结状态 */
    @Excel(name = "视频号冻结状态")
    private Integer sphFreezeStatus;

    /** 抖音备注 */
    @Excel(name = "抖音备注")
    private String dyRemark;

    /** 快手备注 */
    @Excel(name = "快手备注")
    private String ksRemark;

    /** 微信备注 */
    @Excel(name = "微信备注")
    private String wxRemark;

    /** 视频号备注 */
    @Excel(name = "视频号备注")
    private String sphRemark;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /** 知识店铺可提现金额 */
    @Excel(name = "知识店铺可提现金额")
    private BigDecimal zsdpMayWithdrawPrice;

    /** 知识店铺已提现金额 */
    @Excel(name = "知识店铺已提现金额")
    private BigDecimal zsdpWithdrawnPrice;

    /** 知识店铺在途金额 */
    @Excel(name = "知识店铺在途金额")
    private BigDecimal zsdpFundsTransitPrice;

    /** 知识店铺冻结状态  0正常  1冻结 */
    @Excel(name = "知识店铺冻结状态  0正常  1冻结")
    private Integer zsdpFreezeStatus;

    /** 知识店铺备注 */
    @Excel(name = "知识店铺备注")
    private String zsdpRemark;

    @Excel(name = "小红书可提现金额")
    private BigDecimal xhsMayWithdrawPrice;
    @Excel(name = "小红书已提现金额")
    private BigDecimal xhsWithdrawnPrice;
    @Excel(name = "小红书在途金额")
    private BigDecimal xhsFundsTransitPrice;
    @Excel(name = "小红书冻结状态  0正常  1冻结")
    private Integer   xhsFreezeStatus;
    @Excel(name = "小红书备注")
    private String xhsRemark;

    @Excel(name = "小红书可提现金额")
    private BigDecimal pcMayWithdrawPrice;
    @Excel(name = "小红书已提现金额")
    private BigDecimal pcWithdrawnPrice;
    @Excel(name = "小红书在途金额")
    private BigDecimal pcFundsTransitPrice;
    @Excel(name = "小红书冻结状态  0正常  1冻结")
    private Integer   pcFreezeStatus;
    @Excel(name = "小红书备注")
    private String pcRemark;

    @Excel(name = "H5可提现金额")
    private BigDecimal wapMayWithdrawPrice;
    @Excel(name = "H5已提现金额")
    private BigDecimal wapWithdrawnPrice;
    @Excel(name = "H5在途金额")
    private BigDecimal wapFundsTransitPrice;
    @Excel(name = "H5冻结状态  0正常  1冻结")
    private Integer   wapFreezeStatus;
    @Excel(name = "H5备注")
    private String wapRemark;

    //抖店
    @Excel(name = "抖店可提现金额")
    private BigDecimal ddMayWithdrawPrice;
    @Excel(name = "抖店已提现金额")
    private BigDecimal ddWithdrawnPrice;
    @Excel(name = "抖店在途金额")
    private BigDecimal ddFundsTransitPrice;
    @Excel(name = "抖店冻结状态  0正常  1冻结")
    private Integer   ddFreezeStatus;
    @Excel(name = "抖店备注")
    private String ddRemark;







    public BigDecimal getDyFundsTransitPrice() {
        return dyFundsTransitPrice;
    }

    public void setDyFundsTransitPrice(BigDecimal dyFundsTransitPrice) {
        this.dyFundsTransitPrice = dyFundsTransitPrice;
    }

    public BigDecimal getKsFundsTransitPrice() {
        return ksFundsTransitPrice;
    }

    public void setKsFundsTransitPrice(BigDecimal ksFundsTransitPrice) {
        this.ksFundsTransitPrice = ksFundsTransitPrice;
    }

    public BigDecimal getWxFundsTransitPrice() {
        return wxFundsTransitPrice;
    }

    public void setWxFundsTransitPrice(BigDecimal wxFundsTransitPrice) {
        this.wxFundsTransitPrice = wxFundsTransitPrice;
    }

    public BigDecimal getSphFundsTransitPrice() {
        return sphFundsTransitPrice;
    }

    public void setSphFundsTransitPrice(BigDecimal sphFundsTransitPrice) {
        this.sphFundsTransitPrice = sphFundsTransitPrice;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setPromoterId(Long promoterId) 
    {
        this.promoterId = promoterId;
    }

    public Long getPromoterId() 
    {
        return promoterId;
    }
    public void setFundsTransitPrice(BigDecimal fundsTransitPrice) 
    {
        this.fundsTransitPrice = fundsTransitPrice;
    }

    public BigDecimal getFundsTransitPrice() 
    {
        return fundsTransitPrice;
    }
    public void setDyMayWithdrawPrice(BigDecimal dyMayWithdrawPrice) 
    {
        this.dyMayWithdrawPrice = dyMayWithdrawPrice;
    }

    public BigDecimal getDyMayWithdrawPrice() 
    {
        return dyMayWithdrawPrice;
    }
    public void setDyWithdrawnPrice(BigDecimal dyWithdrawnPrice) 
    {
        this.dyWithdrawnPrice = dyWithdrawnPrice;
    }

    public BigDecimal getDyWithdrawnPrice() 
    {
        return dyWithdrawnPrice;
    }
    public void setKsMayWithdrawPrice(BigDecimal ksMayWithdrawPrice) 
    {
        this.ksMayWithdrawPrice = ksMayWithdrawPrice;
    }

    public BigDecimal getKsMayWithdrawPrice() 
    {
        return ksMayWithdrawPrice;
    }
    public void setKsWithdrawnPrice(BigDecimal ksWithdrawnPrice) 
    {
        this.ksWithdrawnPrice = ksWithdrawnPrice;
    }

    public BigDecimal getKsWithdrawnPrice() 
    {
        return ksWithdrawnPrice;
    }
    public void setWxMayWithdrawPrice(BigDecimal wxMayWithdrawPrice) 
    {
        this.wxMayWithdrawPrice = wxMayWithdrawPrice;
    }

    public BigDecimal getWxMayWithdrawPrice() 
    {
        return wxMayWithdrawPrice;
    }
    public void setWxWithdrawnPrice(BigDecimal wxWithdrawnPrice) 
    {
        this.wxWithdrawnPrice = wxWithdrawnPrice;
    }

    public BigDecimal getWxWithdrawnPrice() 
    {
        return wxWithdrawnPrice;
    }
    public void setSphMayWithdrawPrice(BigDecimal sphMayWithdrawPrice) 
    {
        this.sphMayWithdrawPrice = sphMayWithdrawPrice;
    }

    public BigDecimal getSphMayWithdrawPrice() 
    {
        return sphMayWithdrawPrice;
    }
    public void setSphWithdrawnPrice(BigDecimal sphWithdrawnPrice) 
    {
        this.sphWithdrawnPrice = sphWithdrawnPrice;
    }

    public BigDecimal getSphWithdrawnPrice() 
    {
        return sphWithdrawnPrice;
    }
    public void setMayWithdrawPrice(BigDecimal mayWithdrawPrice) 
    {
        this.mayWithdrawPrice = mayWithdrawPrice;
    }

    public BigDecimal getMayWithdrawPrice() 
    {
        return mayWithdrawPrice;
    }
    public void setTotalWithdrawPrice(BigDecimal totalWithdrawPrice) 
    {
        this.totalWithdrawPrice = totalWithdrawPrice;
    }

    public BigDecimal getTotalWithdrawPrice() 
    {
        return totalWithdrawPrice;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("promoterId", getPromoterId())
            .append("fundsTransitPrice", getFundsTransitPrice())
            .append("dyMayWithdrawPrice", getDyMayWithdrawPrice())
            .append("dyWithdrawnPrice", getDyWithdrawnPrice())
            .append("ksMayWithdrawPrice", getKsMayWithdrawPrice())
            .append("ksWithdrawnPrice", getKsWithdrawnPrice())
            .append("wxMayWithdrawPrice", getWxMayWithdrawPrice())
            .append("wxWithdrawnPrice", getWxWithdrawnPrice())
            .append("sphMayWithdrawPrice", getSphMayWithdrawPrice())
            .append("sphWithdrawnPrice", getSphWithdrawnPrice())
            .append("mayWithdrawPrice", getMayWithdrawPrice())
            .append("totalWithdrawPrice", getTotalWithdrawPrice())
            .append("isDelete", getIsDelete())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
