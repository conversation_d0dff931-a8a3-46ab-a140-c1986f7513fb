package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.DyAccessToken;

/**
 * 抖音access_tokenService接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface IDyAccessTokenService 
{
    /**
     * 查询抖音access_token
     * 
     * @param id 抖音access_token主键
     * @return 抖音access_token
     */
    public DyAccessToken selectDyAccessTokenById(Long id);

    /**
     * 查询抖音access_token列表
     * 
     * @param dyAccessToken 抖音access_token
     * @return 抖音access_token集合
     */
    public List<DyAccessToken> selectDyAccessTokenList(DyAccessToken dyAccessToken);

    /**
     * 新增抖音access_token
     * 
     * @param dyAccessToken 抖音access_token
     * @return 结果
     */
    public int insertDyAccessToken(DyAccessToken dyAccessToken);

    /**
     * 修改抖音access_token
     * 
     * @param dyAccessToken 抖音access_token
     * @return 结果
     */
    public int updateDyAccessToken(DyAccessToken dyAccessToken);

    /**
     * 批量删除抖音access_token
     * 
     * @param ids 需要删除的抖音access_token主键集合
     * @return 结果
     */
    public int deleteDyAccessTokenByIds(Long[] ids);

    /**
     * 删除抖音access_token信息
     * 
     * @param id 抖音access_token主键
     * @return 结果
     */
    public int deleteDyAccessTokenById(Long id);

    DyAccessToken selectDyAccessTokenByAppNameType(Integer appNameType);
}
