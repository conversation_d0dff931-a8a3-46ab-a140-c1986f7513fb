package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.TCourseSaleStatistics;

/**
 * 首页课程售卖数量统计Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-23
 */
public interface ITCourseSaleStatisticsService 
{
    /**
     * 查询首页课程售卖数量统计
     * 
     * @param id 首页课程售卖数量统计主键
     * @return 首页课程售卖数量统计
     */
    public TCourseSaleStatistics selectTCourseSaleStatisticsById(Long id);

    /**
     * 查询首页课程售卖数量统计列表
     * 
     * @param tCourseSaleStatistics 首页课程售卖数量统计
     * @return 首页课程售卖数量统计集合
     */
    public List<TCourseSaleStatistics> selectTCourseSaleStatisticsList(TCourseSaleStatistics tCourseSaleStatistics);

    /**
     * 新增首页课程售卖数量统计
     * 
     * @param tCourseSaleStatistics 首页课程售卖数量统计
     * @return 结果
     */
    public int insertTCourseSaleStatistics(TCourseSaleStatistics tCourseSaleStatistics);

    /**
     * 修改首页课程售卖数量统计
     * 
     * @param tCourseSaleStatistics 首页课程售卖数量统计
     * @return 结果
     */
    public int updateTCourseSaleStatistics(TCourseSaleStatistics tCourseSaleStatistics);

    /**
     * 批量删除首页课程售卖数量统计
     * 
     * @param ids 需要删除的首页课程售卖数量统计主键集合
     * @return 结果
     */
    public int deleteTCourseSaleStatisticsByIds(Long[] ids);

    /**
     * 删除首页课程售卖数量统计信息
     * 
     * @param id 首页课程售卖数量统计主键
     * @return 结果
     */
    public int deleteTCourseSaleStatisticsById(Long id);
}
