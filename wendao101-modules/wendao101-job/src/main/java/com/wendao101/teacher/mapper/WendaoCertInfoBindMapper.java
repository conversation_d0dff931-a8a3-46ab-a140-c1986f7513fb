package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.WendaoCertInfoBind;
import org.apache.ibatis.annotations.Param;

/**
 * 资质绑定抖音账号Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-05
 */
public interface WendaoCertInfoBindMapper 
{
    /**
     * 查询资质绑定抖音账号
     * 
     * @param id 资质绑定抖音账号主键
     * @return 资质绑定抖音账号
     */
    public WendaoCertInfoBind selectWendaoCertInfoBindById(Long id);

    /**
     * 查询资质绑定抖音账号列表
     * 
     * @param wendaoCertInfoBind 资质绑定抖音账号
     * @return 资质绑定抖音账号集合
     */
    public List<WendaoCertInfoBind> selectWendaoCertInfoBindList(WendaoCertInfoBind wendaoCertInfoBind);

    /**
     * 新增资质绑定抖音账号
     * 
     * @param wendaoCertInfoBind 资质绑定抖音账号
     * @return 结果
     */
    public int insertWendaoCertInfoBind(WendaoCertInfoBind wendaoCertInfoBind);

    /**
     * 修改资质绑定抖音账号
     * 
     * @param wendaoCertInfoBind 资质绑定抖音账号
     * @return 结果
     */
    public int updateWendaoCertInfoBind(WendaoCertInfoBind wendaoCertInfoBind);

    /**
     * 删除资质绑定抖音账号
     * 
     * @param id 资质绑定抖音账号主键
     * @return 结果
     */
    public int deleteWendaoCertInfoBindById(Long id);

    /**
     * 批量删除资质绑定抖音账号
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoCertInfoBindByIds(Long[] ids);

    List<Long> selectWendaoCertInfoBindTeacherIdList();

    Integer querySuccessOrderByTeacherId(@Param("teacherId") Long teacherId);

    List<WendaoCertInfoBind> selectWendaoCertInfoBindByTeacherId(@Param("teacherId") Long teacherId);

    void deleteWendaoCertInfoBindByTeacherId(@Param("teacherId")Long teacherId);

    List<WendaoCertInfoBind> selectWendaoCertInfoBindListByStatus();
}
