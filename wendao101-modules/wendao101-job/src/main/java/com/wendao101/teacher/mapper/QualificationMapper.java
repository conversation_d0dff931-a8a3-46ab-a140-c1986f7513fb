package com.wendao101.teacher.mapper;

import com.wendao101.job.dto.QualificationDTO;
import com.wendao101.job.dto.TeacherQualificationDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/25
 */
public interface QualificationMapper {
    List<QualificationDTO> selectTeacherIdList();

    List<TeacherQualificationDTO> selectTeacherIdQualification(Long teacherId);

    int  selectTeacherIdCount(Long teacherId);

    int updateCourseQualification(TeacherQualificationDTO teacherQualificationDTOS);

    int updateCourseAuditQualification(TeacherQualificationDTO teacherQualificationDTOS);
}
