package com.wendao101.teacher.service.impl;

import java.util.List;

import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.DdCouponVerify;
import com.wendao101.teacher.mapper.DdCouponVerifyMapper;
import com.wendao101.teacher.service.IDdCouponVerifyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 核销异常订单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-12-14
 */
@Service
public class DdCouponVerifyServiceImpl implements IDdCouponVerifyService
{
    @Autowired
    private DdCouponVerifyMapper ddCouponVerifyMapper;

    /**
     * 查询核销异常订单
     * 
     * @param id 核销异常订单主键
     * @return 核销异常订单
     */
    @Override
    public DdCouponVerify selectDdCouponVerifyById(Long id)
    {
        return ddCouponVerifyMapper.selectDdCouponVerifyById(id);
    }

    /**
     * 查询核销异常订单列表
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 核销异常订单
     */
    @Override
    public List<DdCouponVerify> selectDdCouponVerifyList(DdCouponVerify ddCouponVerify)
    {
        return ddCouponVerifyMapper.selectDdCouponVerifyList(ddCouponVerify);
    }

    /**
     * 新增核销异常订单
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 结果
     */
    @Override
    public int insertDdCouponVerify(DdCouponVerify ddCouponVerify)
    {
        ddCouponVerify.setCreateTime(DateUtils.getNowDate());
        return ddCouponVerifyMapper.insertDdCouponVerify(ddCouponVerify);
    }

    /**
     * 修改核销异常订单
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 结果
     */
    @Override
    public int updateDdCouponVerify(DdCouponVerify ddCouponVerify)
    {
        ddCouponVerify.setUpdateTime(DateUtils.getNowDate());
        return ddCouponVerifyMapper.updateDdCouponVerify(ddCouponVerify);
    }

    /**
     * 批量删除核销异常订单
     * 
     * @param ids 需要删除的核销异常订单主键
     * @return 结果
     */
    @Override
    public int deleteDdCouponVerifyByIds(Long[] ids)
    {
        return ddCouponVerifyMapper.deleteDdCouponVerifyByIds(ids);
    }

    /**
     * 删除核销异常订单信息
     * 
     * @param id 核销异常订单主键
     * @return 结果
     */
    @Override
    public int deleteDdCouponVerifyById(Long id)
    {
        return ddCouponVerifyMapper.deleteDdCouponVerifyById(id);
    }
}
