package com.wendao101.teacher.mapper;

import java.util.List;

import com.wendao101.common.core.wendao.CourseAuditDTO;
import com.wendao101.teacher.domain.CourseAudit;
import org.apache.ibatis.annotations.Param;

/**
 * 课程待审核Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-20
 */
public interface CourseAuditMapper 
{
    /**
     * 查询课程待审核
     * 
     * @param id 课程待审核主键
     * @return 课程待审核
     */
    public CourseAudit selectCourseAuditById(Long id);

    /**
     * 查询课程待审核列表
     * 
     * @param courseAudit 课程待审核
     * @return 课程待审核集合
     */
    public List<CourseAudit> selectCourseAuditList(CourseAudit courseAudit);

    public List<CourseAudit> selectCourseAuditByTeacherId(Long teacherId);

    /**
     * 新增课程待审核
     * 
     * @param courseAudit 课程待审核
     * @return 结果
     */
    public int insertCourseAudit(CourseAudit courseAudit);

    /**
     * 修改课程待审核
     * 
     * @param courseAudit 课程待审核
     * @return 结果
     */
    public int updateCourseAudit(CourseAudit courseAudit);

    /**
     * 删除课程待审核
     * 
     * @param id 课程待审核主键
     * @return 结果
     */
    public int deleteCourseAuditById(Long id);

    /**
     * 批量删除课程待审核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseAuditByIds(Long[] ids);

    void deleteCourseByTeacherId(@Param("teacherId") Long teacherId);

    List<CourseAudit> selectCourseAuditByMaxId(@Param("maxId") Long maxId);

    List<CourseAudit> selectTrySeeCourse(@Param("appNameType")Integer appNameType, @Param("publishPlatform")String publishPlatform);

    List<CourseAuditDTO> selectHotSaleCourse(@Param("appNameType")Integer appNameType, @Param("platform")Integer platform);

    List<CourseAudit> selectNewCourse(@Param("appNameType")Integer appNameType, @Param("publishPlatform")String platform);

    Long selectSaleValuesByCourseId(@Param("courseId")Long courseId);

    List<CourseAudit> selectNewCourseForWap();

    List<CourseAuditDTO> selectHotSaleCourseForWap();

    List<CourseAudit> selectTrySeeCourseForWap();
}
