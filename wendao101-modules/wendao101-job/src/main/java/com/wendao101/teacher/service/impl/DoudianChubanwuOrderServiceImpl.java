package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.DoudianChubanwuOrderMapper;
import com.wendao101.teacher.domain.DoudianChubanwuOrder;
import com.wendao101.teacher.service.IDoudianChubanwuOrderService;

/**
 * 抖店出版物订单扣点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-10-30
 */
@Service
public class DoudianChubanwuOrderServiceImpl implements IDoudianChubanwuOrderService 
{
    @Autowired
    private DoudianChubanwuOrderMapper doudianChubanwuOrderMapper;

    /**
     * 查询抖店出版物订单扣点
     * 
     * @param id 抖店出版物订单扣点主键
     * @return 抖店出版物订单扣点
     */
    @Override
    public DoudianChubanwuOrder selectDoudianChubanwuOrderById(Long id)
    {
        return doudianChubanwuOrderMapper.selectDoudianChubanwuOrderById(id);
    }

    /**
     * 查询抖店出版物订单扣点列表
     * 
     * @param doudianChubanwuOrder 抖店出版物订单扣点
     * @return 抖店出版物订单扣点
     */
    @Override
    public List<DoudianChubanwuOrder> selectDoudianChubanwuOrderList(DoudianChubanwuOrder doudianChubanwuOrder)
    {
        return doudianChubanwuOrderMapper.selectDoudianChubanwuOrderList(doudianChubanwuOrder);
    }

    /**
     * 新增抖店出版物订单扣点
     * 
     * @param doudianChubanwuOrder 抖店出版物订单扣点
     * @return 结果
     */
    @Override
    public int insertDoudianChubanwuOrder(DoudianChubanwuOrder doudianChubanwuOrder)
    {
        doudianChubanwuOrder.setCreateTime(DateUtils.getNowDate());
        return doudianChubanwuOrderMapper.insertDoudianChubanwuOrder(doudianChubanwuOrder);
    }

    /**
     * 修改抖店出版物订单扣点
     * 
     * @param doudianChubanwuOrder 抖店出版物订单扣点
     * @return 结果
     */
    @Override
    public int updateDoudianChubanwuOrder(DoudianChubanwuOrder doudianChubanwuOrder)
    {
        doudianChubanwuOrder.setUpdateTime(DateUtils.getNowDate());
        return doudianChubanwuOrderMapper.updateDoudianChubanwuOrder(doudianChubanwuOrder);
    }

    /**
     * 批量删除抖店出版物订单扣点
     * 
     * @param ids 需要删除的抖店出版物订单扣点主键
     * @return 结果
     */
    @Override
    public int deleteDoudianChubanwuOrderByIds(Long[] ids)
    {
        return doudianChubanwuOrderMapper.deleteDoudianChubanwuOrderByIds(ids);
    }

    /**
     * 删除抖店出版物订单扣点信息
     * 
     * @param id 抖店出版物订单扣点主键
     * @return 结果
     */
    @Override
    public int deleteDoudianChubanwuOrderById(Long id)
    {
        return doudianChubanwuOrderMapper.deleteDoudianChubanwuOrderById(id);
    }

    @Override
    public DoudianChubanwuOrder selectDoudianChubanwuOrderByOrderId(String orderId) {
        return doudianChubanwuOrderMapper.selectDoudianChubanwuOrderByOrderId(orderId);
    }
}
