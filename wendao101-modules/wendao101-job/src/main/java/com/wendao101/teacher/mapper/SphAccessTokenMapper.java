package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.SphAccessToken;
import org.apache.ibatis.annotations.Param;

/**
 * 问到微信视频号小店access_tokenMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface SphAccessTokenMapper 
{
    /**
     * 查询问到微信视频号小店access_token
     * 
     * @param id 问到微信视频号小店access_token主键
     * @return 问到微信视频号小店access_token
     */
    public SphAccessToken selectSphAccessTokenById(Long id);

    /**
     * 查询问到微信视频号小店access_token列表
     * 
     * @param sphAccessToken 问到微信视频号小店access_token
     * @return 问到微信视频号小店access_token集合
     */
    public List<SphAccessToken> selectSphAccessTokenList(SphAccessToken sphAccessToken);

    /**
     * 新增问到微信视频号小店access_token
     * 
     * @param sphAccessToken 问到微信视频号小店access_token
     * @return 结果
     */
    public int insertSphAccessToken(SphAccessToken sphAccessToken);

    /**
     * 修改问到微信视频号小店access_token
     * 
     * @param sphAccessToken 问到微信视频号小店access_token
     * @return 结果
     */
    public int updateSphAccessToken(SphAccessToken sphAccessToken);

    /**
     * 删除问到微信视频号小店access_token
     * 
     * @param id 问到微信视频号小店access_token主键
     * @return 结果
     */
    public int deleteSphAccessTokenById(Long id);

    /**
     * 批量删除问到微信视频号小店access_token
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSphAccessTokenByIds(Long[] ids);

    SphAccessToken selectSphAccessTokenByAppNameType(@Param("appNameType") Integer appNameType);
}
