package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.KsAccessToken;
import org.apache.ibatis.annotations.Param;

/**
 * 问到快手小程序access_tokenMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface KsAccessTokenMapper 
{
    /**
     * 查询问到快手小程序access_token
     * 
     * @param id 问到快手小程序access_token主键
     * @return 问到快手小程序access_token
     */
    public KsAccessToken selectKsAccessTokenById(Long id);

    /**
     * 查询问到快手小程序access_token列表
     * 
     * @param ksAccessToken 问到快手小程序access_token
     * @return 问到快手小程序access_token集合
     */
    public List<KsAccessToken> selectKsAccessTokenList(KsAccessToken ksAccessToken);

    /**
     * 新增问到快手小程序access_token
     * 
     * @param ksAccessToken 问到快手小程序access_token
     * @return 结果
     */
    public int insertKsAccessToken(KsAccessToken ksAccessToken);

    /**
     * 修改问到快手小程序access_token
     * 
     * @param ksAccessToken 问到快手小程序access_token
     * @return 结果
     */
    public int updateKsAccessToken(KsAccessToken ksAccessToken);

    /**
     * 删除问到快手小程序access_token
     * 
     * @param id 问到快手小程序access_token主键
     * @return 结果
     */
    public int deleteKsAccessTokenById(Long id);

    /**
     * 批量删除问到快手小程序access_token
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKsAccessTokenByIds(Long[] ids);

    KsAccessToken selectKsAccessTokenByAppNameTypeAndAppId(@Param("appNameType")Integer appNameType, @Param("appId")String appId);
}
