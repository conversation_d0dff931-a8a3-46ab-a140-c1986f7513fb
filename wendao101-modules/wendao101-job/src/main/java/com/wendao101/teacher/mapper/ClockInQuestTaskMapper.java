package com.wendao101.teacher.mapper;

import com.wendao101.common.core.douyin.clock.request.QueryClockRecordRequest;
import com.wendao101.teacher.domain.ClockInQuest;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 打卡任务Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
public interface ClockInQuestTaskMapper {
    /**
     * 查询打卡任务
     *
     * @param id 打卡任务主键
     * @return 打卡任务
     */
    public ClockInQuest selectClockInQuestById(Long id);

    /**
     * 查询打卡任务列表
     *
     * @param clockInQuest 打卡任务
     * @return 打卡任务集合
     */
    public List<ClockInQuest> selectClockInQuestList(ClockInQuest clockInQuest);

    /**
     * 新增打卡任务
     *
     * @param clockInQuest 打卡任务
     * @return 结果
     */
    public int insertClockInQuest(ClockInQuest clockInQuest);

    /**
     * 修改打卡任务
     *
     * @param clockInQuest 打卡任务
     * @return 结果
     */
    public int updateClockInQuest(ClockInQuest clockInQuest);

    /**
     * 删除打卡任务
     *
     * @param id 打卡任务主键
     * @return 结果
     */
    public int deleteClockInQuestById(Long id);

    /**
     * 批量删除打卡任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteClockInQuestByIds(Long[] ids);

//    /**
//     * 根据主键id 获取详情
//     *
//     * @param id
//     * @return
//     */
//    ClockInQuestDTO getById(Long id);
//
//    /**
//     * 打卡任务列表
//     *
//     * @param clockInQuestAllVO
//     * @return
//     */
//    List<ClockInQuestAllDTO> selectAll(ClockInQuestAllVO clockInQuestAllVO);


    ClockInQuest getByCourseId(QueryClockRecordRequest recordRequest);

    /**
     * 根据课程id获取打卡任务
     *
     * @param courseId
     * @return
     */
    com.wendao101.common.core.douyin.clock.response.ClockInQuestDTO getClockInQuestByCourseId(@Param("courseId") Long courseId);

    /**
     * 课程打卡参与人数
     *
     * @param courseId
     * @return
     */
    Long getNumber(Long courseId);

    /**
     * 修改打卡任务状态
     */
    void updateQuestStatus();
}
