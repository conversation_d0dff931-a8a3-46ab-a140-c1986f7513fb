package com.wendao101.teacher.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 抖音access_token对象 dy_access_token
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public class DyAccessToken extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 抖音小程序appid */
    @Excel(name = "抖音小程序appid")
    private String appid;

    /** 抖音小程序secret */
    @Excel(name = "抖音小程序secret")
    private String secret;

    /** 抖音小程序access_token */
    @Excel(name = "抖音小程序access_token")
    private String accessToken;

    /** access_token过期秒数 */
    @Excel(name = "access_token过期秒数")
    private Long expiresIn;

    /** 1好课,2课堂 */
    @Excel(name = "1好课,2课堂")
    private Integer appNameType;

    /** 备注信息 */
    @Excel(name = "备注信息")
    private String appName;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setAppid(String appid) 
    {
        this.appid = appid;
    }

    public String getAppid() 
    {
        return appid;
    }
    public void setSecret(String secret) 
    {
        this.secret = secret;
    }

    public String getSecret() 
    {
        return secret;
    }
    public void setAccessToken(String accessToken) 
    {
        this.accessToken = accessToken;
    }

    public String getAccessToken() 
    {
        return accessToken;
    }
    public void setExpiresIn(Long expiresIn) 
    {
        this.expiresIn = expiresIn;
    }

    public Long getExpiresIn() 
    {
        return expiresIn;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }
    public void setAppName(String appName) 
    {
        this.appName = appName;
    }

    public String getAppName() 
    {
        return appName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("appid", getAppid())
            .append("secret", getSecret())
            .append("accessToken", getAccessToken())
            .append("expiresIn", getExpiresIn())
            .append("appNameType", getAppNameType())
            .append("appName", getAppName())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
