package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.KsResourceUploadMapper;
import com.wendao101.teacher.domain.KsResourceUpload;
import com.wendao101.teacher.service.IKsResourceUploadService;

/**
 * 快手课程资源上传Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-15
 */
@Service
public class KsResourceUploadServiceImpl implements IKsResourceUploadService 
{
    @Autowired
    private KsResourceUploadMapper ksResourceUploadMapper;

    /**
     * 查询快手课程资源上传
     * 
     * @param resourceId 快手课程资源上传主键
     * @return 快手课程资源上传
     */
    @Override
    public KsResourceUpload selectKsResourceUploadByResourceId(Long resourceId)
    {
        return ksResourceUploadMapper.selectKsResourceUploadByResourceId(resourceId);
    }

    /**
     * 查询快手课程资源上传列表
     * 
     * @param ksResourceUpload 快手课程资源上传
     * @return 快手课程资源上传
     */
    @Override
    public List<KsResourceUpload> selectKsResourceUploadList(KsResourceUpload ksResourceUpload)
    {
        return ksResourceUploadMapper.selectKsResourceUploadList(ksResourceUpload);
    }

    /**
     * 新增快手课程资源上传
     * 
     * @param ksResourceUpload 快手课程资源上传
     * @return 结果
     */
    @Override
    public int insertKsResourceUpload(KsResourceUpload ksResourceUpload)
    {
        ksResourceUpload.setCreateTime(DateUtils.getNowDate());
        return ksResourceUploadMapper.insertKsResourceUpload(ksResourceUpload);
    }

    /**
     * 修改快手课程资源上传
     * 
     * @param ksResourceUpload 快手课程资源上传
     * @return 结果
     */
    @Override
    public int updateKsResourceUpload(KsResourceUpload ksResourceUpload)
    {
        ksResourceUpload.setUpdateTime(DateUtils.getNowDate());
        return ksResourceUploadMapper.updateKsResourceUpload(ksResourceUpload);
    }

    /**
     * 批量删除快手课程资源上传
     * 
     * @param resourceIds 需要删除的快手课程资源上传主键
     * @return 结果
     */
    @Override
    public int deleteKsResourceUploadByResourceIds(Long[] resourceIds)
    {
        return ksResourceUploadMapper.deleteKsResourceUploadByResourceIds(resourceIds);
    }

    /**
     * 删除快手课程资源上传信息
     * 
     * @param resourceId 快手课程资源上传主键
     * @return 结果
     */
    @Override
    public int deleteKsResourceUploadByResourceId(Long resourceId)
    {
        return ksResourceUploadMapper.deleteKsResourceUploadByResourceId(resourceId);
    }
}
