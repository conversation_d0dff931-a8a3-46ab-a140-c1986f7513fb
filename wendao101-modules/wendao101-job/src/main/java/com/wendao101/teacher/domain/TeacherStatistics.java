package com.wendao101.teacher.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 教师统计对象 teacher_statistics
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TeacherStatistics extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 统计日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date statisticsDate;

    /** 老师id */
    private Long teacherId;

    /** 老师手机号 */
    private String mobile;

    /** 店铺名称 */
    private String shopName;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    private Integer appNameType;

    /** 交易金额 */
    private BigDecimal dealAmount;

    /** 已提现金额 */
    private BigDecimal withdrawnAmount;

    /** 在途资金 */
    private BigDecimal moneyInTransit;

    /** 可提现金额 */
    private BigDecimal withdrawableAmount;

    /** 服务费 */
    private BigDecimal serviceFee;

    /** 服务费率 */
    private BigDecimal serviceFeeRate;

    private String timeQueryStr;
} 