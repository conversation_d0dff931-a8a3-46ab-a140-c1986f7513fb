package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.TAudio;
import org.apache.ibatis.annotations.Param;

/**
 * 素材音频Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface TAudioMapper 
{
    /**
     * 查询素材音频
     * 
     * @param id 素材音频主键
     * @return 素材音频
     */
    public TAudio selectTAudioById(Long id);

    /**
     * 查询素材音频列表
     * 
     * @param tAudio 素材音频
     * @return 素材音频集合
     */
    public List<TAudio> selectTAudioList(TAudio tAudio);

    /**
     * 新增素材音频
     * 
     * @param tAudio 素材音频
     * @return 结果
     */
    public int insertTAudio(TAudio tAudio);

    /**
     * 修改素材音频
     * 
     * @param tAudio 素材音频
     * @return 结果
     */
    public int updateTAudio(TAudio tAudio);

    /**
     * 删除素材音频
     * 
     * @param id 素材音频主键
     * @return 结果
     */
    public int deleteTAudioById(Long id);

    /**
     * 批量删除素材音频
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTAudioByIds(Long[] ids);

    List<TAudio> selectTAudioListByMaxId(@Param("maxId") Long maxId);

    TAudio selectTAudioByUrl(String courseDirectoryUrl);

    List<TAudio> selectByMd5AndTeacherId(@Param("md5")String md5, @Param("teacherId")Long teacherId, @Param("appNameType")Integer appNameType);

    TAudio selectAudioByUrlOrOriginalUrl(@Param("url")String courseDirectoryUrl);

}
