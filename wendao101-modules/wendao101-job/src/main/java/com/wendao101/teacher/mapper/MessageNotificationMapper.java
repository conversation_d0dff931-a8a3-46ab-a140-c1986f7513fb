package com.wendao101.teacher.mapper;

import com.wendao101.teacher.domain.MessageNotification;

import java.util.List;

/**
 * 消息通知Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-28
 */
public interface MessageNotificationMapper 
{
    /**
     * 查询消息通知
     * 
     * @param id 消息通知主键
     * @return 消息通知
     */
    public MessageNotification selectMessageNotificationById(Long id);

    /**
     * 查询消息通知列表
     * 
     * @param messageNotification 消息通知
     * @return 消息通知集合
     */
    public List<MessageNotification> selectMessageNotificationList(MessageNotification messageNotification);

    /**
     * 统计通知数量
     * @param messageNotification
     * @return
     */
    public Long selectMessageNotificationListCount(MessageNotification messageNotification);

    /**
     * 新增消息通知
     * 
     * @param messageNotification 消息通知
     * @return 结果
     */
    public int insertMessageNotification(MessageNotification messageNotification);

    /**
     * 修改消息通知
     * 
     * @param messageNotification 消息通知
     * @return 结果
     */
    public int updateMessageNotification(MessageNotification messageNotification);

    /**
     * 删除消息通知
     * 
     * @param id 消息通知主键
     * @return 结果
     */
    public int deleteMessageNotificationById(Long id);

    /**
     * 批量删除消息通知
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMessageNotificationByIds(Long[] ids);
}
