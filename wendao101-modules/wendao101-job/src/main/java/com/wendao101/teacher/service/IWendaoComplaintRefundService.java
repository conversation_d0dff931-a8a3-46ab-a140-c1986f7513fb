package com.wendao101.teacher.service;


import com.wendao101.job.dto.WendaoComplaintRefundDTO;

import java.util.List;

/**
 * 问到投诉退款管理Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-09
 */
public interface IWendaoComplaintRefundService {

    List<Integer> selectWendaoComplaintRefundList();
    List<String> selectWendaoComplaintRefundOrderId();


    int updateComplaintStatus(List<Integer> complaintIds);

    int updateOrderStatus(List<String> orderId);
    int updateComplaint(List<String> orderIds);

}
