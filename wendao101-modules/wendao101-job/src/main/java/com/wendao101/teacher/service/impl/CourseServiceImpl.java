package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.CourseMapper;
import com.wendao101.teacher.domain.Course;
import com.wendao101.teacher.service.ICourseService;

/**
 * 课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-19
 */
@Service
public class CourseServiceImpl implements ICourseService 
{
    @Autowired
    private CourseMapper courseMapper;

    /**
     * 查询课程
     * 
     * @param id 课程主键
     * @return 课程
     */
    @Override
    public Course selectCourseById(Long id)
    {
        return courseMapper.selectCourseById(id);
    }

    /**
     * 查询课程列表
     * 
     * @param course 课程
     * @return 课程
     */
    @Override
    public List<Course> selectCourseList(Course course)
    {
        return courseMapper.selectCourseList(course);
    }

    /**
     * 新增课程
     * 
     * @param course 课程
     * @return 结果
     */
    @Override
    public int insertCourse(Course course)
    {
        course.setCreateTime(DateUtils.getNowDate());
        return courseMapper.insertCourse(course);
    }

    /**
     * 修改课程
     * 
     * @param course 课程
     * @return 结果
     */
    @Override
    public int updateCourse(Course course)
    {
        if(course.getUpdateTime()==null){
            course.setUpdateTime(DateUtils.getNowDate());
        }
        return courseMapper.updateCourse(course);
    }

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseByIds(Long[] ids)
    {
        return courseMapper.deleteCourseByIds(ids);
    }

    /**
     * 删除课程信息
     * 
     * @param id 课程主键
     * @return 结果
     */
    @Override
    public int deleteCourseById(Long id)
    {
        return courseMapper.deleteCourseById(id);
    }

    @Override
    public Course selectCourseByProductId(String productId) {
        return courseMapper.selectCourseByProductId(productId);
    }

    @Override
    public List<Course> selectCourseByAuditStatus(Integer auditStatus) {
        return courseMapper.selectCourseByAuditStatus(auditStatus);
    }

    @Override
    public Course selectCourseByTeacherIdAndId(Long id, Long teacherId) {
        return courseMapper.selectCourseByTeacherIdAndId(id,teacherId);
    }

    @Override
    public List<Course> selectCourseListByMaxId(Long maxId) {
        return courseMapper.selectCourseListByMaxId(maxId);
    }

    @Override
    public void deleteCourseByTeacherId(Long teacherId) {
        courseMapper.deleteCourseByTeacherId(teacherId);
    }
}
