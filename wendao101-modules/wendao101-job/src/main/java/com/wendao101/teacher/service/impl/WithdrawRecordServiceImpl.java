package com.wendao101.teacher.service.impl;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import com.wendao101.job.dto.WithDrawTotalDTO;
import com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.WithdrawRecordMapper;
import com.wendao101.teacher.domain.WithdrawRecord;
import com.wendao101.teacher.service.IWithdrawRecordService;

/**
 * 提现记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WithdrawRecordServiceImpl implements IWithdrawRecordService {
    @Autowired
    private WithdrawRecordMapper withdrawRecordMapper;

    /**
     * 查询提现记录
     * 
     * @param id 提现记录主键
     * @return 提现记录
     */
    @Override
    public WithdrawRecord selectWithdrawRecordById(Long id) {
        return withdrawRecordMapper.selectWithdrawRecordById(id);
    }

    /**
     * 查询提现记录列表
     * 
     * @param withdrawRecord 提现记录
     * @return 提现记录
     */
    @Override
    public List<WithdrawRecord> selectWithdrawRecordList(WithdrawRecord withdrawRecord) {
        return withdrawRecordMapper.selectWithdrawRecordList(withdrawRecord);
    }

    /**
     * 新增提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    @Override
    public int insertWithdrawRecord(WithdrawRecord withdrawRecord) {
        return withdrawRecordMapper.insertWithdrawRecord(withdrawRecord);
    }

    /**
     * 修改提现记录
     * 
     * @param withdrawRecord 提现记录
     * @return 结果
     */
    @Override
    public int updateWithdrawRecord(WithdrawRecord withdrawRecord) {
        return withdrawRecordMapper.updateWithdrawRecord(withdrawRecord);
    }

    /**
     * 批量删除提现记录
     * 
     * @param ids 需要删除的提现记录主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawRecordByIds(Long[] ids) {
        return withdrawRecordMapper.deleteWithdrawRecordByIds(ids);
    }

    /**
     * 删除提现记录信息
     * 
     * @param id 提现记录主键
     * @return 结果
     */
    @Override
    public int deleteWithdrawRecordById(Long id) {
        return withdrawRecordMapper.deleteWithdrawRecordById(id);
    }

    @Override
    public List<WithDrawTotalDTO> selectTotalWithdrawRecordList(Long teacherId,Date beginTime, Date endTime) {
        return withdrawRecordMapper.selectTotalWithdrawRecordList(teacherId,beginTime, endTime);
    }

    @Override
    public List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordInPromoterList(List<Long> promoterIdList,Date beginTime, Date endTime) {
        return withdrawRecordMapper.selectPromoterTotalWithdrawRecordInPromoterList(promoterIdList,beginTime, endTime);
    }

    @Override
    public List<WithdrawRecord> selectBackupWithdrawRecordList() {
        return withdrawRecordMapper.selectBackupWithdrawRecordList();
    }

    @Override
    public int updateWithdrawRecordBackup(WithdrawRecord withdrawRecord) {
        return withdrawRecordMapper.updateWithdrawRecordBackup(withdrawRecord);
    }

    @Override
    public WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityType(Long teacherId) {
        return withdrawRecordMapper.getServicePriceRatioAndEntityType(teacherId);
    }

    @Override
    public WithdrawRecordAndReteEntityDTO getServicePriceRatioAndEntityTypeBackUp(Long teacherId) {
        return withdrawRecordMapper.getServicePriceRatioAndEntityTypeBackUp(teacherId);
    }

    @Override
    public BigDecimal queryTotalMoneyByPlatform(Long teacherId, Integer incomePlatform, Date oldChangeNetTime) {
        return withdrawRecordMapper.queryTotalMoneyByPlatform(teacherId,incomePlatform,oldChangeNetTime);
    }

    @Override
    public int deleteServicePriceRatioAndEntityTypeBackUp(Long id) {
        return withdrawRecordMapper.deleteServicePriceRatioAndEntityTypeBackUp(id);
    }

    @Override
    public List<WithDrawTotalDTO> selectPromoterTotalWithdrawRecordForPromoterList(List<Long> promoterIdList, Date beginTime, Date endTime) {
        return withdrawRecordMapper.selectPromoterTotalWithdrawRecordForPromoterList(promoterIdList,beginTime,endTime);
    }
} 