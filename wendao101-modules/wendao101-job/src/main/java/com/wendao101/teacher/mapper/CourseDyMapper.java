package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.CourseDy;
import org.apache.ibatis.annotations.Param;

/**
 * 课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-19
 */
public interface CourseDyMapper 
{
    /**
     * 查询课程
     * 
     * @param id 课程主键
     * @return 课程
     */
    public CourseDy selectCourseDyById(Long id);

    /**
     * 查询课程列表
     * 
     * @param courseDy 课程
     * @return 课程集合
     */
    public List<CourseDy> selectCourseDyList(CourseDy courseDy);

    /**
     * 新增课程
     * 
     * @param courseDy 课程
     * @return 结果
     */
    public int insertCourseDy(CourseDy courseDy);

    /**
     * 修改课程
     * 
     * @param courseDy 课程
     * @return 结果
     */
    public int updateCourseDy(CourseDy courseDy);

    /**
     * 删除课程
     * 
     * @param id 课程主键
     * @return 结果
     */
    public int deleteCourseDyById(Long id);

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseDyByIds(Long[] ids);

    CourseDy selectCourseDyByProductId(@Param("productId") String productId);

    List<CourseDy> selectInAuditDyCoursesByAppNameType(@Param("appNameType") Integer appNameType);

    List<CourseDy> selectWendaoAuditDyCoursesByAppNameType();

    CourseDy selectCourseDyByCourseIdNumber(@Param("courseIdNumber")Long courseIdNumber);

    void deleteCourseByTeacherId(@Param("teacherId")Long teacherId);
}
