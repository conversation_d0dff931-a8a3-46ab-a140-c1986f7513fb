package com.wendao101.teacher.mapper;

import com.wendao101.teacher.domain.DoudianCourse;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 抖店课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-08-28
 */
public interface DoudianCourseMapper 
{
    /**
     * 查询抖店课程
     * 
     * @param id 抖店课程主键
     * @return 抖店课程
     */
    public DoudianCourse selectDoudianCourseById(Long id);

    /**
     * 查询抖店课程列表
     * 
     * @param doudianCourse 抖店课程
     * @return 抖店课程集合
     */
    public List<DoudianCourse> selectDoudianCourseList(DoudianCourse doudianCourse);

    /**
     * 新增抖店课程
     * 
     * @param doudianCourse 抖店课程
     * @return 结果
     */
    public int insertDoudianCourse(DoudianCourse doudianCourse);

    /**
     * 修改抖店课程
     * 
     * @param doudianCourse 抖店课程
     * @return 结果
     */
    public int updateDoudianCourse(DoudianCourse doudianCourse);

    /**
     * 删除抖店课程
     * 
     * @param id 抖店课程主键
     * @return 结果
     */
    public int deleteDoudianCourseById(Long id);

    /**
     * 批量删除抖店课程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDoudianCourseByIds(Long[] ids);

    DoudianCourse selectDoudianCourseByCourseId(@Param("courseId") Long courseId);

    List<DoudianCourse> selectDoudianCourseNoChannelList(DoudianCourse courseQuery);

    /**
     * 查询更新时间大于指定时间的抖店课程列表
     *
     * @param updateTime 更新时间
     * @return 抖店课程集合
     */
    public List<DoudianCourse> selectDoudianCourseByUpdateTime(Date updateTime);
}
