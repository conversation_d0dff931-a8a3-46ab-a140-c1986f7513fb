package com.wendao101.teacher.service;

import java.util.List;

import com.wendao101.common.core.wendao.HotTeacherDTO;
import com.wendao101.teacher.domain.CourseAudit;
import com.wendao101.teacher.domain.TTeacher;

/**
 * 老师Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-24
 */
public interface ITTeacherService 
{
    /**
     * 查询老师
     * 
     * @param teacherId 老师主键
     * @return 老师
     */
    public TTeacher selectTTeacherByTeacherId(Long teacherId);

    /**
     * 查询老师列表
     * 
     * @param tTeacher 老师
     * @return 老师集合
     */
    public List<TTeacher> selectTTeacherList(TTeacher tTeacher);

    /**
     * 根据老师id查询课程
     *
     * @return 老师集合
     */
    public List<CourseAudit> selectCourseAuditByTeacherId(Long teacherId);

    /**
     * 新增老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    public int insertTTeacher(TTeacher tTeacher);

    /**
     * 修改老师
     * 
     * @param tTeacher 老师
     * @return 结果
     */
    public int updateTTeacher(TTeacher tTeacher);

    /**
     * 批量删除老师
     * 
     * @param teacherIds 需要删除的老师主键集合
     * @return 结果
     */
    public int deleteTTeacherByTeacherIds(Long[] teacherIds);

    /**
     * 删除老师信息
     * 
     * @param teacherId 老师主键
     * @return 结果
     */
    public int deleteTTeacherByTeacherId(Long teacherId);

    List<TTeacher> selectTTeacherListByMaxId(Long maxId);

    int deleteEnterInfoByTeacherId(Long teacherId);

    int updateSysUser(Long teacherId);

    int deleteSysUser(Long teacherId);

    List<HotTeacherDTO> selectHotTeacher(Integer appNameType, Integer platform);

    List<HotTeacherDTO> selectHotTeacherForWap();

    List<TTeacher> selectTTeacherListByTeacherInfo(String teacherInfo);
}
