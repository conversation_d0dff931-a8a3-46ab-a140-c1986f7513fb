package com.wendao101.teacher.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 推广员资金收益对象 promoter_fund_income
 * 
 * <AUTHOR>
 * @date 2023-09-26
 */
public class PromoterFundIncome extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 退款订单编号 */
    @Excel(name = "退款订单编号")
    private String refundId;

    /** 提现订单号 */
    @Excel(name = "提现订单号")
    private String withdrawOrderNumber;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseTitle;

    /** 收入来源 0抖音 1微信 2快手  3视频号 */
    @Excel(name = "收入来源 0抖音 1微信 2快手  3视频号")
    private Integer incomePlatform;

    /** 资金类型  1推广收入 2平台提现 3退款  */
    @Excel(name = "资金类型  1推广收入 2平台提现 3退款 ")
    private Integer incomeType;

    /** 资金状态 0 在途 1已入账  */
    @Excel(name = "资金状态 0 在途 1已入账 ")
    private Integer fundsType;

    /** 订单支付金额 */
    @Excel(name = "订单支付金额")
    private BigDecimal orderPayPrice;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundPrice;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal withdrawPrice;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal incomePrice;

    /** 账号金额是否分账 0否 1是 */
    @Excel(name = "账号金额是否分账 0否 1是")
    private Integer accountPriceType;

    /** 分销比例 */
    @Excel(name = "分销比例")
    private Long promoterRate;

    /** 服务费 */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;

    /** 服务比率 */
    @Excel(name = "服务比率")
    private Integer servicePriceRatio;

    /**
     * 入账时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date incomePriceTime;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public Date getIncomePriceTime() {
        return incomePriceTime;
    }

    public void setIncomePriceTime(Date incomePriceTime) {
        this.incomePriceTime = incomePriceTime;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setPromoterId(Long promoterId) 
    {
        this.promoterId = promoterId;
    }

    public Long getPromoterId() 
    {
        return promoterId;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setRefundId(String refundId) 
    {
        this.refundId = refundId;
    }

    public String getRefundId() 
    {
        return refundId;
    }
    public void setWithdrawOrderNumber(String withdrawOrderNumber) 
    {
        this.withdrawOrderNumber = withdrawOrderNumber;
    }

    public String getWithdrawOrderNumber() 
    {
        return withdrawOrderNumber;
    }
    public void setCourseTitle(String courseTitle) 
    {
        this.courseTitle = courseTitle;
    }

    public String getCourseTitle() 
    {
        return courseTitle;
    }
    public void setIncomePlatform(Integer incomePlatform) 
    {
        this.incomePlatform = incomePlatform;
    }

    public Integer getIncomePlatform() 
    {
        return incomePlatform;
    }
    public void setIncomeType(Integer incomeType) 
    {
        this.incomeType = incomeType;
    }

    public Integer getIncomeType() 
    {
        return incomeType;
    }
    public void setFundsType(Integer fundsType) 
    {
        this.fundsType = fundsType;
    }

    public Integer getFundsType() 
    {
        return fundsType;
    }
    public void setOrderPayPrice(BigDecimal orderPayPrice) 
    {
        this.orderPayPrice = orderPayPrice;
    }

    public BigDecimal getOrderPayPrice() 
    {
        return orderPayPrice;
    }
    public void setRefundPrice(BigDecimal refundPrice) 
    {
        this.refundPrice = refundPrice;
    }

    public BigDecimal getRefundPrice() 
    {
        return refundPrice;
    }
    public void setWithdrawPrice(BigDecimal withdrawPrice) 
    {
        this.withdrawPrice = withdrawPrice;
    }

    public BigDecimal getWithdrawPrice() 
    {
        return withdrawPrice;
    }
    public void setIncomePrice(BigDecimal incomePrice) 
    {
        this.incomePrice = incomePrice;
    }

    public BigDecimal getIncomePrice() 
    {
        return incomePrice;
    }
    public void setAccountPriceType(Integer accountPriceType) 
    {
        this.accountPriceType = accountPriceType;
    }

    public Integer getAccountPriceType() 
    {
        return accountPriceType;
    }
    public void setPromoterRate(Long promoterRate) 
    {
        this.promoterRate = promoterRate;
    }

    public Long getPromoterRate() 
    {
        return promoterRate;
    }
    public void setServicePrice(BigDecimal servicePrice) 
    {
        this.servicePrice = servicePrice;
    }

    public BigDecimal getServicePrice() 
    {
        return servicePrice;
    }

    public Integer getServicePriceRatio() {
        return servicePriceRatio;
    }

    public void setServicePriceRatio(Integer servicePriceRatio) {
        this.servicePriceRatio = servicePriceRatio;
    }

    public void setIsDelete(Integer isDelete)
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("promoterId", getPromoterId())
            .append("orderId", getOrderId())
            .append("refundId", getRefundId())
            .append("withdrawOrderNumber", getWithdrawOrderNumber())
            .append("courseTitle", getCourseTitle())
            .append("incomePlatform", getIncomePlatform())
            .append("incomeType", getIncomeType())
            .append("fundsType", getFundsType())
            .append("orderPayPrice", getOrderPayPrice())
            .append("refundPrice", getRefundPrice())
            .append("withdrawPrice", getWithdrawPrice())
            .append("incomePrice", getIncomePrice())
            .append("accountPriceType", getAccountPriceType())
            .append("promoterRate", getPromoterRate())
            .append("servicePrice", getServicePrice())
            .append("servicePriceRatio", getServicePriceRatio())
            .append("isDelete", getIsDelete())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
