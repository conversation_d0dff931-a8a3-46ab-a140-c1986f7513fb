package com.wendao101.teacher.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 机器审核对象 course_machine_audit
 * 
 * <AUTHOR>
 * @date 2024-01-05
 */
public class CourseMachineAudit extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id主键自增 */
    private Long id;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseIdNumber;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 总课时，是几节课 */
    @Excel(name = "总课时，是几节课")
    private Long totalCourseCount;

    /** 店铺id,对应老师id */
    @Excel(name = "店铺id,对应老师id")
    private Long shopId;

    /** 店铺名称同时也是登录显示名，例如“淘气包” */
    @Excel(name = "店铺名称同时也是登录显示名，例如“淘气包”")
    private String shopName;

    /** 资质名称,公共资质名称,个人或企业名称 */
    @Excel(name = "资质名称,公共资质名称,个人或企业名称")
    private String certName;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 课程删除0未删除，1已删除 */
    @Excel(name = "课程删除0未删除，1已删除")
    private Integer isDelete;

    /** 上传时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date uploadTime;

    /** 最近审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最近审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date lastAuditTime;

    /** 机器审核结果,-1 审核驳回,0审核中,1审核通过 */
    @Excel(name = "机器审核结果,-1 审核驳回,0审核中,1审核通过")
    private Integer auditStatus;

    /** 相同课程,相同课程数 */
    @Excel(name = "相同课程,相同课程数")
    private Integer sameCourseCount;

    /** 相同店铺数,涉及店铺数 */
    @Excel(name = "相同店铺数,涉及店铺数")
    private Integer sameShopCount;

    /** -1 审核驳回,0审核中,1审核通过 */
    @Excel(name = "-1 审核驳回,0审核中,1审核通过")
    private Integer humanAuditStatus;

    /** 人工审核驳回原因 */
    @Excel(name = "人工审核驳回原因")
    private String humanAuditReason;

    /** 类型,1人审,2是机审 */
    @Excel(name = "类型,1人审,2是机审")
    private Integer type;

    /** 课程类型,图文/音视频 */
    @Excel(name = "课程类型,图文/音视频")
    private String courseType;

    private Integer appNameType;

    public Integer getAppNameType() {
        return appNameType;
    }

    public void setAppNameType(Integer appNameType) {
        this.appNameType = appNameType;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCourseIdNumber(Long courseIdNumber) 
    {
        this.courseIdNumber = courseIdNumber;
    }

    public Long getCourseIdNumber() 
    {
        return courseIdNumber;
    }
    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }
    public void setTotalCourseCount(Long totalCourseCount) 
    {
        this.totalCourseCount = totalCourseCount;
    }

    public Long getTotalCourseCount() 
    {
        return totalCourseCount;
    }
    public void setShopId(Long shopId) 
    {
        this.shopId = shopId;
    }

    public Long getShopId() 
    {
        return shopId;
    }
    public void setShopName(String shopName) 
    {
        this.shopName = shopName;
    }

    public String getShopName() 
    {
        return shopName;
    }
    public void setCertName(String certName) 
    {
        this.certName = certName;
    }

    public String getCertName() 
    {
        return certName;
    }
    public void setFirstClassDouyinClassId(Long firstClassDouyinClassId) 
    {
        this.firstClassDouyinClassId = firstClassDouyinClassId;
    }

    public Long getFirstClassDouyinClassId() 
    {
        return firstClassDouyinClassId;
    }
    public void setFirstClassTitle(String firstClassTitle) 
    {
        this.firstClassTitle = firstClassTitle;
    }

    public String getFirstClassTitle() 
    {
        return firstClassTitle;
    }
    public void setSecondClassDouyinClassId(Long secondClassDouyinClassId) 
    {
        this.secondClassDouyinClassId = secondClassDouyinClassId;
    }

    public Long getSecondClassDouyinClassId() 
    {
        return secondClassDouyinClassId;
    }
    public void setSecondClassTitle(String secondClassTitle) 
    {
        this.secondClassTitle = secondClassTitle;
    }

    public String getSecondClassTitle() 
    {
        return secondClassTitle;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setUploadTime(Date uploadTime) 
    {
        this.uploadTime = uploadTime;
    }

    public Date getUploadTime() 
    {
        return uploadTime;
    }
    public void setLastAuditTime(Date lastAuditTime) 
    {
        this.lastAuditTime = lastAuditTime;
    }

    public Date getLastAuditTime() 
    {
        return lastAuditTime;
    }
    public void setAuditStatus(Integer auditStatus) 
    {
        this.auditStatus = auditStatus;
    }

    public Integer getAuditStatus() 
    {
        return auditStatus;
    }
    public void setSameCourseCount(Integer sameCourseCount) 
    {
        this.sameCourseCount = sameCourseCount;
    }

    public Integer getSameCourseCount() 
    {
        return sameCourseCount;
    }
    public void setSameShopCount(Integer sameShopCount) 
    {
        this.sameShopCount = sameShopCount;
    }

    public Integer getSameShopCount() 
    {
        return sameShopCount;
    }
    public void setHumanAuditStatus(Integer humanAuditStatus) 
    {
        this.humanAuditStatus = humanAuditStatus;
    }

    public Integer getHumanAuditStatus() 
    {
        return humanAuditStatus;
    }
    public void setHumanAuditReason(String humanAuditReason) 
    {
        this.humanAuditReason = humanAuditReason;
    }

    public String getHumanAuditReason() 
    {
        return humanAuditReason;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setCourseType(String courseType) 
    {
        this.courseType = courseType;
    }

    public String getCourseType() 
    {
        return courseType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("courseIdNumber", getCourseIdNumber())
            .append("courseName", getCourseName())
            .append("totalCourseCount", getTotalCourseCount())
            .append("shopId", getShopId())
            .append("shopName", getShopName())
            .append("certName", getCertName())
            .append("firstClassDouyinClassId", getFirstClassDouyinClassId())
            .append("firstClassTitle", getFirstClassTitle())
            .append("secondClassDouyinClassId", getSecondClassDouyinClassId())
            .append("secondClassTitle", getSecondClassTitle())
            .append("isDelete", getIsDelete())
            .append("uploadTime", getUploadTime())
            .append("lastAuditTime", getLastAuditTime())
            .append("auditStatus", getAuditStatus())
            .append("sameCourseCount", getSameCourseCount())
            .append("sameShopCount", getSameShopCount())
            .append("humanAuditStatus", getHumanAuditStatus())
            .append("humanAuditReason", getHumanAuditReason())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("type", getType())
            .append("courseType", getCourseType())
            .toString();
    }
}
