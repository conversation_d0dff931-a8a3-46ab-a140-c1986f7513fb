package com.wendao101.teacher.service.impl;

import com.wendao101.teacher.domain.TeacherStatistics;
import com.wendao101.teacher.mapper.TeacherStatisticsMapper;
import com.wendao101.teacher.service.ITeacherStatisticsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 教师统计Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class TeacherStatisticsServiceImpl implements ITeacherStatisticsService {
    @Autowired
    private TeacherStatisticsMapper teacherStatisticsMapper;

    /**
     * 查询教师统计
     * 
     * @param id 教师统计主键
     * @return 教师统计
     */
    @Override
    public TeacherStatistics selectTeacherStatisticsById(Long id) {
        return teacherStatisticsMapper.selectTeacherStatisticsById(id);
    }

    /**
     * 查询教师统计列表
     * 
     * @param teacherStatistics 教师统计
     * @return 教师统计
     */
    @Override
    public List<TeacherStatistics> selectTeacherStatisticsList(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.selectTeacherStatisticsList(teacherStatistics);
    }

    /**
     * 新增教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    @Override
    public int insertTeacherStatistics(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.insertTeacherStatistics(teacherStatistics);
    }

    /**
     * 修改教师统计
     * 
     * @param teacherStatistics 教师统计
     * @return 结果
     */
    @Override
    public int updateTeacherStatistics(TeacherStatistics teacherStatistics) {
        return teacherStatisticsMapper.updateTeacherStatistics(teacherStatistics);
    }

    /**
     * 批量删除教师统计
     * 
     * @param ids 需要删除的教师统计主键
     * @return 结果
     */
    @Override
    public int deleteTeacherStatisticsByIds(Long[] ids) {
        return teacherStatisticsMapper.deleteTeacherStatisticsByIds(ids);
    }

    /**
     * 删除教师统计信息
     * 
     * @param id 教师统计主键
     * @return 结果
     */
    @Override
    public int deleteTeacherStatisticsById(Long id) {
        return teacherStatisticsMapper.deleteTeacherStatisticsById(id);
    }

    @Override
    public TeacherStatistics selectTeacherStatisticsByTeacherId(Long teacherId) {
        return teacherStatisticsMapper.selectTeacherStatisticsByTeacherId(teacherId);
    }

    @Override
    public List<Long> selectPromoterIdsByTeacherId(Long teacherId) {
        return teacherStatisticsMapper.selectPromoterIdsByTeacherId(teacherId);
    }

    @Override
    public int deleteTeacherStatisticsBigerThanId(Long id,String timeRange) {
        return teacherStatisticsMapper.deleteTeacherStatisticsBigerThanId(id,timeRange);
    }

    @Override
    public int deleteTeacherStatisticsBigerThanIdAndQueryStringIsNull(Long id) {
        return teacherStatisticsMapper.deleteTeacherStatisticsBigerThanIdAndQueryStringIsNull(id);
    }
} 