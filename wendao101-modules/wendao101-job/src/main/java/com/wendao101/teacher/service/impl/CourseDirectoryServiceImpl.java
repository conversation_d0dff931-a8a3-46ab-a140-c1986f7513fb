package com.wendao101.teacher.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.teacher.domain.CourseDirectory;
import com.wendao101.teacher.mapper.CourseDirectoryMapper;
import com.wendao101.teacher.service.ICourseDirectoryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@Service
public class CourseDirectoryServiceImpl implements ICourseDirectoryService {
    @Resource
    private CourseDirectoryMapper courseDirectoryMapper;

    /**
     * 查询目录
     *
     * @param id 目录主键
     * @return 目录
     */
    @Override
    public CourseDirectory selectCourseDirectoryById(Long id) {
        return courseDirectoryMapper.selectCourseDirectoryById(id);
    }

    /**
     * 查询目录列表
     *
     * @param courseDirectory 目录
     * @return 目录
     */
    @Override
    public List<CourseDirectory> selectCourseDirectoryList(CourseDirectory courseDirectory) {
        return courseDirectoryMapper.selectCourseDirectoryList(courseDirectory);
    }

    /**
     * 新增目录
     *
     * @param couseDirectory 目录
     * @return 结果
     */
    @Override
    public int insertCourseDirectory(CourseDirectory couseDirectory) {
        couseDirectory.setCreateTime(DateUtils.getNowDate());
        return courseDirectoryMapper.insertCourseDirectory(couseDirectory);
    }

    /**
     * 修改目录
     *
     * @param courseDirectory 目录
     * @return 结果
     */
    @Override
    public int updateCourseDirectory(CourseDirectory courseDirectory) {
        courseDirectory.setUpdateTime(DateUtils.getNowDate());
        return courseDirectoryMapper.updateCourseDirectory(courseDirectory);
    }

    /**
     * 批量删除目录
     *
     * @param ids 需要删除的目录主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryByIds(Long[] ids) {
        return courseDirectoryMapper.deleteCourseDirectoryByIds(ids);
    }

    /**
     * 删除目录信息
     *
     * @param id 目录主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryById(Long id) {
        return courseDirectoryMapper.deleteCourseDirectoryById(id);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryDyByCourseDirectoryUrl(String tcvodMediaUrlBeforeTrans) {
        return courseDirectoryMapper.selectCourseDirectoryDyByCourseDirectoryUrl(tcvodMediaUrlBeforeTrans);
    }

    @Override
    public void deleteCourseDirectoryWhereChapterIdIsNull(Long courseId) {
         courseDirectoryMapper.deleteCourseDirectoryWhereChapterIdIsNull(courseId);
    }

    @Override
    public void deleteCourseDirectoryByChapterIds(Long[] chapterIds) {
        courseDirectoryMapper.deleteCourseDirectoryByChapterIds(chapterIds);
    }

    @Override
    public int updateCourseDirectorySetChapterIdNull(CourseDirectory courseDirectory) {
        return courseDirectoryMapper.updateCourseDirectorySetChapterIdNull(courseDirectory);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryListByChapterId(Long id) {
        return courseDirectoryMapper.selectCourseDirectoryListByChapterId(id);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryWhereChapterIdIsNull(CourseDirectory courseDirectoryQuery) {
        return courseDirectoryMapper.selectCourseDirectoryWhereChapterIdIsNull(courseDirectoryQuery);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryListByMaxId(Long maxId) {
        return courseDirectoryMapper.selectCourseDirectoryListByMaxId(maxId);
    }

    @Override
    public int deleteCourseDirectoryByCourseId(Long ktCourseId) {
        return courseDirectoryMapper.deleteCourseDirectoryByCourseId(ktCourseId);
    }

    @Override
    public void batchInsert(List<CourseDirectory> list) {
        courseDirectoryMapper.batchInsert(list);
    }

    @Override
    public void updateCourseDirectoryByOldUrl(String key, String value) {
        if(StringUtils.isNotBlank(key)&&StringUtils.isNotBlank(value)){
            courseDirectoryMapper.updateCourseDirectoryByOldUrl(key,value);
        }
    }


}
