package com.wendao101.teacher.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 优惠券对象 m_discounts
 * 
 * <AUTHOR>
 * @date 2023-08-18
 */
public class MDiscounts extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String discountsName;

    /** 优惠券类型, 0普通优惠券，1分享优惠券 */
    @Excel(name = "优惠券类型, 0普通优惠券，1分享优惠券")
    private Integer discountsType;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id(主键) */
    @Excel(name = "课程id(主键)")
    private Long courseId;

    /** 优惠券码id */
    @Excel(name = "优惠券码id")
    private Long discountsCodeId;

    /** 优惠券金额 */
    @Excel(name = "优惠券金额")
    private BigDecimal discountsMoney;

    /** 最多领取数 */
    @Excel(name = "最多领取数")
    private Long receiveMax;

    /** 优惠券使用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsStartTime;

    /** 优惠券使用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsEndTime;

    /** 优惠券总数 */
    @Excel(name = "优惠券总数")
    private Integer discountsSum;

    /** 领取优惠券总数 */
    @Excel(name = "领取优惠券总数")
    private Integer receiveCouponSum;

    /** 优惠券是否无限，0 不是 ， 1 是 */
    @Excel(name = "优惠券是否无限，0 不是 ， 1 是")
    private Integer discountsNumberType;

    /** 优惠券状态，0 未开始，1进行中，2已结束 */
    @Excel(name = "优惠券状态，0 未开始，1进行中，2已结束")
    private Integer discountsStatus;

    /** 限制时间状态；0限制时间范围；1限制有效时间 */
    @Excel(name = "限制时间状态；0限制时间范围；1限制有效时间")
    private Integer timeStatus;

    /** 限制有效时间 */
    @Excel(name = "限制有效时间")
    private Integer validTime;

    /** 是否删除，0不删除，1删除 */
    @Excel(name = "是否删除，0不删除，1删除")
    private Integer isDelete;

    /** 是否允许继续使用，0不允许，1允许 */
    @Excel(name = "是否允许继续使用，0不允许，1允许")
    private Integer isKeepUsing;

    /** 是否停止活动，0不停止，1停止 */
    @Excel(name = "是否停止活动，0不停止，1停止")
    private Integer isStopAction;

    /** 优惠券开始领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券开始领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponStartTime;

    /** 优惠券结束领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券结束领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponEndTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDiscountsName(String discountsName) 
    {
        this.discountsName = discountsName;
    }

    public String getDiscountsName() 
    {
        return discountsName;
    }
    public void setDiscountsType(Integer discountsType) 
    {
        this.discountsType = discountsType;
    }

    public Integer getDiscountsType() 
    {
        return discountsType;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setDiscountsCodeId(Long discountsCodeId) 
    {
        this.discountsCodeId = discountsCodeId;
    }

    public Long getDiscountsCodeId() 
    {
        return discountsCodeId;
    }
    public void setDiscountsMoney(BigDecimal discountsMoney) 
    {
        this.discountsMoney = discountsMoney;
    }

    public BigDecimal getDiscountsMoney() 
    {
        return discountsMoney;
    }
    public void setReceiveMax(Long receiveMax) 
    {
        this.receiveMax = receiveMax;
    }

    public Long getReceiveMax() 
    {
        return receiveMax;
    }
    public void setDiscountsStartTime(Date discountsStartTime) 
    {
        this.discountsStartTime = discountsStartTime;
    }

    public Date getDiscountsStartTime() 
    {
        return discountsStartTime;
    }
    public void setDiscountsEndTime(Date discountsEndTime) 
    {
        this.discountsEndTime = discountsEndTime;
    }

    public Date getDiscountsEndTime() 
    {
        return discountsEndTime;
    }
    public void setDiscountsNumberType(Integer discountsNumberType) 
    {
        this.discountsNumberType = discountsNumberType;
    }

    public Integer getDiscountsNumberType() 
    {
        return discountsNumberType;
    }
    public void setDiscountsStatus(Integer discountsStatus) 
    {
        this.discountsStatus = discountsStatus;
    }

    public Integer getDiscountsStatus() 
    {
        return discountsStatus;
    }
    public void setTimeStatus(Integer timeStatus) 
    {
        this.timeStatus = timeStatus;
    }

    public Integer getTimeStatus() 
    {
        return timeStatus;
    }

    public Integer getValidTime() {
        return validTime;
    }

    public void setValidTime(Integer validTime) {
        this.validTime = validTime;
    }

    public void setIsDelete(Integer isDelete)
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setIsKeepUsing(Integer isKeepUsing) 
    {
        this.isKeepUsing = isKeepUsing;
    }

    public Integer getIsKeepUsing() 
    {
        return isKeepUsing;
    }
    public void setIsStopAction(Integer isStopAction) 
    {
        this.isStopAction = isStopAction;
    }

    public Integer getIsStopAction() 
    {
        return isStopAction;
    }
    public void setReceiveCouponStartTime(Date receiveCouponStartTime) 
    {
        this.receiveCouponStartTime = receiveCouponStartTime;
    }

    public Date getReceiveCouponStartTime() 
    {
        return receiveCouponStartTime;
    }
    public void setReceiveCouponEndTime(Date receiveCouponEndTime) 
    {
        this.receiveCouponEndTime = receiveCouponEndTime;
    }

    public Date getReceiveCouponEndTime() 
    {
        return receiveCouponEndTime;
    }

    public Integer getDiscountsSum() {
        return discountsSum;
    }

    public void setDiscountsSum(Integer discountsSum) {
        this.discountsSum = discountsSum;
    }

    public Integer getReceiveCouponSum() {
        return receiveCouponSum;
    }

    public void setReceiveCouponSum(Integer receiveCouponSum) {
        this.receiveCouponSum = receiveCouponSum;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("discountsName", getDiscountsName())
            .append("discountsType", getDiscountsType())
            .append("teacherId", getTeacherId())
            .append("courseId", getCourseId())
            .append("discountsCodeId", getDiscountsCodeId())
            .append("discountsMoney", getDiscountsMoney())
            .append("receiveMax", getReceiveMax())
            .append("discountsStartTime", getDiscountsStartTime())
            .append("discountsEndTime", getDiscountsEndTime())
            .append("discountsNumberType", getDiscountsNumberType())
            .append("discountsStatus", getDiscountsStatus())
            .append("timeStatus", getTimeStatus())
            .append("validTime", getValidTime())
            .append("isDelete", getIsDelete())
            .append("isKeepUsing", getIsKeepUsing())
            .append("isStopAction", getIsStopAction())
            .append("receiveCouponStartTime", getReceiveCouponStartTime())
            .append("receiveCouponEndTime", getReceiveCouponEndTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
