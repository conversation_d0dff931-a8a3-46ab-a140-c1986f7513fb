package com.wendao101.job.task;

import com.wendao101.job.dto.WithdrawRecordAndReteEntityDTO;
import com.wendao101.teacher.domain.WithdrawPrice;
import com.wendao101.teacher.domain.WithdrawRecord;
import com.wendao101.teacher.service.IWithdrawPriceService;
import com.wendao101.teacher.service.IWithdrawRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component("reCalculateTeacherWithdrawTask")
public class ReCalculateTeacherWithdrawTask {

    @Autowired
    private IWithdrawRecordService withdrawRecordService;

    @Autowired
    private IWithdrawPriceService withdrawPriceService;

    public void reCalculate() {
        //查询备份表
        List<WithdrawRecord> list = withdrawRecordService.selectBackupWithdrawRecordList();
        for (WithdrawRecord withdrawRecord : list) {
            Integer incomePlatform = withdrawRecord.getIncomePlatform();
            if (incomePlatform == null) {
                System.out.println("incomePlatform为空");
                continue;
            }
            Long teacherId = withdrawRecord.getTeacherId();
            if (teacherId == null) {
                System.out.println("teacherId为空");
                continue;
            }
            WithdrawRecordAndReteEntityDTO servicePriceRatioAndEntityType = withdrawRecordService.getServicePriceRatioAndEntityType(teacherId);
            WithdrawRecordAndReteEntityDTO servicePriceRatioAndEntityTypeBackUp = withdrawRecordService.getServicePriceRatioAndEntityTypeBackUp(teacherId);

            if (servicePriceRatioAndEntityTypeBackUp == null) {
                System.out.println("servicePriceRatioAndEntityTypeBackUp为空");
                continue;
            }
            BigDecimal newRate = null;
            BigDecimal oldRate = null;

            boolean reCalculate = false;
            //withdrawRecordAddVO.getIncomePlatform()提现平台，0抖音 1微信 2快手 3视频号 8知识店铺 9pc端 5h5端(wap端) 7小红书,11抖店
            switch (incomePlatform) {
                case 0: // 抖音
                    if (!Objects.equals(servicePriceRatioAndEntityType.getDyRate(), servicePriceRatioAndEntityTypeBackUp.getDyRate())) {
                        newRate = servicePriceRatioAndEntityType.getDyRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getDyRate();
                        reCalculate = true;
                    }
                    break;
                case 1: // 微信
                    if (!Objects.equals(servicePriceRatioAndEntityType.getWxRate(), servicePriceRatioAndEntityTypeBackUp.getWxRate())) {
                        newRate = servicePriceRatioAndEntityType.getWxRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getWxRate();
                        reCalculate = true;
                    }
                    break;
                case 2: // 快手
                    if (!Objects.equals(servicePriceRatioAndEntityType.getKsRate(), servicePriceRatioAndEntityTypeBackUp.getKsRate())) {
                        newRate = servicePriceRatioAndEntityType.getKsRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getKsRate();
                        reCalculate = true;
                    }
                    break;
                case 3: // 视频号
                    if (!Objects.equals(servicePriceRatioAndEntityType.getSphRate(), servicePriceRatioAndEntityTypeBackUp.getSphRate())) {
                        newRate = servicePriceRatioAndEntityType.getSphRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getSphRate();
                        reCalculate = true;
                    }
                    break;
                case 5: // h5端(wap端)
                    if (!Objects.equals(servicePriceRatioAndEntityType.getH5Rate(), servicePriceRatioAndEntityTypeBackUp.getH5Rate())) {
                        newRate = servicePriceRatioAndEntityType.getH5Rate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getH5Rate();
                        reCalculate = true;
                    }
                    break;
                case 7: // 小红书
                    if (!Objects.equals(servicePriceRatioAndEntityType.getXhsRate(), servicePriceRatioAndEntityTypeBackUp.getXhsRate())) {
                        newRate = servicePriceRatioAndEntityType.getXhsRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getXhsRate();
                        reCalculate = true;
                    }
                    break;
                case 8: // 知识店铺
                    if (!Objects.equals(servicePriceRatioAndEntityType.getZsdpRate(), servicePriceRatioAndEntityTypeBackUp.getZsdpRate())) {
                        newRate = servicePriceRatioAndEntityType.getZsdpRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getZsdpRate();
                        reCalculate = true;
                    }
                    break;
                case 9: // pc端
                    if (!Objects.equals(servicePriceRatioAndEntityType.getPcRate(), servicePriceRatioAndEntityTypeBackUp.getPcRate())) {
                        newRate = servicePriceRatioAndEntityType.getPcRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getPcRate();
                        reCalculate = true;
                    }
                    break;
                case 11: // 抖店
                    if (!Objects.equals(servicePriceRatioAndEntityType.getDdRate(), servicePriceRatioAndEntityTypeBackUp.getDdRate())) {
                        newRate = servicePriceRatioAndEntityType.getDdRate();
                        oldRate = servicePriceRatioAndEntityTypeBackUp.getDdRate();
                        reCalculate = true;
                    }
                    break;
                default:
                    break;
            }
            if (!reCalculate) {
                System.out.println("费率相等不需要计算");
                updateDeleteWithdrawRecord(withdrawRecord);
                continue;
            }
            if (newRate == null) {
                System.out.println("新费率newRate为空");
                updateDeleteWithdrawRecord(withdrawRecord);
                continue;
            }
            if (oldRate == null) {
                System.out.println("旧费率oldRate为空");
                updateDeleteWithdrawRecord(withdrawRecord);
                continue;
            }
            //修改时间
            Date oldChangeNetTime = servicePriceRatioAndEntityTypeBackUp.getCreateTime();
            //修改两次怎么办?目前不支持
            BigDecimal totalMoneyByPlatform = withdrawRecordService.queryTotalMoneyByPlatform(teacherId, incomePlatform, oldChangeNetTime);

            if (totalMoneyByPlatform == null) {
                System.out.println("该教师没有订单记录");
                updateDeleteWithdrawRecord(withdrawRecord);
                if (servicePriceRatioAndEntityTypeBackUp.getId() != null) {
                    int row = withdrawRecordService.deleteServicePriceRatioAndEntityTypeBackUp(servicePriceRatioAndEntityTypeBackUp.getId());
                }
                continue;
            }
            //查出提现总计额度
            WithdrawPrice withdrawPrice = withdrawPriceService.selectWithdrawPriceByTeacherIdAndpromoterId(teacherId, null);
            BigDecimal totalWithdrawMoney = null;
            switch (incomePlatform) {
                case 0: // 抖音
                    totalWithdrawMoney = withdrawPrice.getDyWithdrawnPrice();
                    break;
                case 1: // 微信
                    totalWithdrawMoney = withdrawPrice.getWxWithdrawnPrice();
                    break;
                case 2: // 快手
                    totalWithdrawMoney = withdrawPrice.getKsWithdrawnPrice();
                    break;
                case 3: // 视频号
                    totalWithdrawMoney = withdrawPrice.getSphWithdrawnPrice();
                    break;
                case 5: // h5端(wap端)
                    totalWithdrawMoney = withdrawPrice.getWapWithdrawnPrice();
                    break;
                case 7: // 小红书
                    totalWithdrawMoney = withdrawPrice.getXhsWithdrawnPrice();
                    break;
                case 8: // 知识店铺
                    totalWithdrawMoney = withdrawPrice.getZsdpWithdrawnPrice();
                    break;
                case 9: // pc端
                    totalWithdrawMoney = withdrawPrice.getPcWithdrawnPrice();
                    break;
                case 11: // 抖店
                    totalWithdrawMoney = withdrawPrice.getDdWithdrawnPrice();
                    break;
                default:
                    break;
            }
            if (totalWithdrawMoney == null) {
                totalWithdrawMoney = BigDecimal.ZERO;
            }
            totalWithdrawMoney = totalWithdrawMoney.subtract(withdrawRecord.getWithdrawPrice());//减去该笔提现金额
            if (totalWithdrawMoney.compareTo(totalMoneyByPlatform) >= 0) {
                System.out.println("提现额度已经全部结算完毕");
                updateDeleteWithdrawRecord(withdrawRecord);
                if (servicePriceRatioAndEntityTypeBackUp.getId() != null) {
                    int row = withdrawRecordService.deleteServicePriceRatioAndEntityTypeBackUp(servicePriceRatioAndEntityTypeBackUp.getId());
                }
            } else {
                BigDecimal add = totalWithdrawMoney.add(withdrawRecord.getWithdrawPrice());
                if (add.compareTo(totalMoneyByPlatform) <= 0) {
                    //第一种情况,totalWithdrawMoney+withdrawRecord.getWithdrawPrice()<=totalMoneyByPlatform(按老的费率算)
                    BigDecimal serviceFee = withdrawRecord.getWithdrawPrice().multiply(oldRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    WithdrawRecord withdrawRecord1 = withdrawRecordService.selectWithdrawRecordById(withdrawRecord.getId());
                    withdrawRecord1.setServicePrice(serviceFee);
                    withdrawRecord1.setServicePriceRatio(oldRate.intValue());
                    withdrawRecord1.setRemarkMessage("修改抽佣费率之前的订单入账金额提现,使用老的抽佣费率:"+oldRate);
                    withdrawRecordService.updateWithdrawRecord(withdrawRecord1);
                    updateDeleteWithdrawRecord(withdrawRecord);
                    //如果刚好相等则特殊处理
                    if (add.compareTo(totalMoneyByPlatform) == 0) {
                        if (servicePriceRatioAndEntityTypeBackUp.getId() != null) {
                            int row = withdrawRecordService.deleteServicePriceRatioAndEntityTypeBackUp(servicePriceRatioAndEntityTypeBackUp.getId());
                        }
                    }
                } else {
                    //第二种情况,totalWithdrawMoney+withdrawRecord.getWithdrawPrice()>totalMoneyByPlatform(到totalMoneyByPlatform的数额按老的费率算,剩余的按新的费率算)
                    //老的部分:
                    BigDecimal subtract = totalMoneyByPlatform.subtract(totalWithdrawMoney);
                    BigDecimal oldRateMoney = subtract.multiply(oldRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                    //新的部分
                    BigDecimal subtract1 = add.subtract(totalMoneyByPlatform);
                    BigDecimal newRateMoney = subtract1.multiply(newRate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

                    BigDecimal totalFee = oldRateMoney.add(newRateMoney);

                    BigDecimal calculatedRate = totalFee.divide(withdrawRecord.getWithdrawPrice(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100));

                    int finalRate = calculatedRate.setScale(0, RoundingMode.HALF_UP).intValue();

                    WithdrawRecord withdrawRecord1 = withdrawRecordService.selectWithdrawRecordById(withdrawRecord.getId());
                    withdrawRecord1.setServicePrice(totalFee);
                    withdrawRecord1.setServicePriceRatio(finalRate);
                    withdrawRecord1.setRemarkMessage("修改抽佣费率之前入账金额提现:"+oldRateMoney+",费率:"+oldRate+",修改抽佣费率之后入账金额提现:"+newRateMoney+",费率:"+newRate);
                    withdrawRecordService.updateWithdrawRecord(withdrawRecord1);
                    updateDeleteWithdrawRecord(withdrawRecord);
                    if (servicePriceRatioAndEntityTypeBackUp.getId() != null) {
                        int row = withdrawRecordService.deleteServicePriceRatioAndEntityTypeBackUp(servicePriceRatioAndEntityTypeBackUp.getId());
                    }
                }

            }
        }
    }

    private void updateDeleteWithdrawRecord(WithdrawRecord withdrawRecord) {
        withdrawRecord.setIsDelete(1);
        withdrawRecord.setUpdateTime(new Date());
        int i = withdrawRecordService.updateWithdrawRecordBackup(withdrawRecord);
    }
}
