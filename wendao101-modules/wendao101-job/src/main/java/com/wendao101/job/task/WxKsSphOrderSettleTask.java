package com.wendao101.job.task;

import com.wendao101.teacher.service.ICourseOrderService;
import com.wendao101.teacher.service.IFundIncomeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 微信视频号,7天分账任务(不包含抖音和快手)
 */
@Component("wxKsSphOrderSettleTask")
public class WxKsSphOrderSettleTask {


    @Autowired
    private IFundIncomeService fundIncomeService;

    @Autowired
    private ICourseOrderService courseOrderService;

    public void settleOrder() {
        System.out.println("执行非抖音分账!");
        fundIncomeService.updateFundIncomeByOrderId();
        fundIncomeService.updatePromoterFundIncomeByOrderId();
        fundIncomeService.updateTeacherFlowByOrderId();

        courseOrderService.updateCourseOrderSettleStatus();

        System.out.println("非抖音分账执行完毕!");
    }
}
