package com.wendao101.job.task;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.teacher.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Component("dyCancelOrderTask")
public class DyCancelOrderTask {

    @Autowired
    private ICourseOrderService courseOrderService;

    /**
     * 取消订单定时任务
     */
    public void cancelOrder() {
        //获取所有已关闭订单
        List<Long> ids = courseOrderService.getCourseOrderId();
        if (CollectionUtils.isNotEmpty(ids)) {
            //修改已关闭订单状态
            courseOrderService.updateOrderStatus(ids);
        }

    }
}
