package com.wendao101.job.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 快手课程审核日志对象 ks_course_audit_log
 */
@Data
public class KsCourseAuditLog {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 课程ID */
    @Excel(name = "课程ID")
    private Long courseId;

    /** 审核状态 */
    @Excel(name = "审核状态")
    private Integer auditStatus;

    /** 操作描述 */
    @Excel(name = "操作描述")
    private String operation;

    /** 操作结果 */
    @Excel(name = "操作结果", readConverterExp = "1=成功,0=失败")
    private Integer result;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    /** 请求数据 */
    private String requestData;

    /** 响应数据 */
    private String responseData;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
package com.wendao101.job.service;

import com.wendao101.job.domain.KsCourseAuditTask;
import java.util.List;

/**
 * 快手课程审核任务Service接口
 */
public interface IKsCourseAuditTaskService {

    /**
     * 新增快手课程审核任务
     */
    int insertKsCourseAuditTask(KsCourseAuditTask ksCourseAuditTask);

    /**
     * 修改快手课程审核任务
     */
    int updateKsCourseAuditTask(KsCourseAuditTask ksCourseAuditTask);

    /**
     * 根据课程ID查询任务
     */
    KsCourseAuditTask selectByCourseId(Long courseId);

    /**
     * 查询待处理的任务
     */
    List<KsCourseAuditTask> selectPendingTasks();

    /**
     * 查询超时任务
     */
    List<KsCourseAuditTask> selectTimeoutTasks(int hours);
}

