package com.wendao101.job.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.common.core.kuaishou.mpcourse.*;
import com.wendao101.common.core.kuaishou.mpcourse.child.*;
import com.wendao101.common.core.utils.http.HttpUtils;
import com.wendao101.common.core.utils.qvideo.KeyValidateUtils;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.dto.KuaishouMpCourseAudioDTO;
import com.wendao101.douyin.api.dto.KuaishouMpCourseVideoDTO;
import com.wendao101.douyin.api.feign.KuaishouMpCourseService;
import com.wendao101.job.dto.StudyDataDTO1;
import com.wendao101.job.service.DouyinAuditService;
import com.wendao101.job.service.KsAccessTokenService;
import com.wendao101.job.service.SmsService;
import com.wendao101.teacher.domain.*;
import com.wendao101.teacher.service.*;
import com.wendao101.teacher.vo.MGiveEntityAndTextbookVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component("ksCourseAuditTask")
public class KsCourseAuditTask {

    /**
     * 48小时的秒数
     */
    private static final long FORTY_EIGHT_HOURS_IN_SECONDS = 48 * 60 * 60;

    private static final String COURSE_KEY_PREFIX = "WENDAO_COURSE:";
    public static final String ks_callback_url = "https://goodminiapp.wendao101.com/ks_upload_callback/video_audio_result";
    public static final String mini_app_course_detail_path = "pages_details/details/details";
    public static final String ks_course_callback_url = "https://goodminiapp.wendao101.com/ks_upload_callback/course_update_callback";
    public static final String ks_course_submit_prefix = "ks_course_submit_prefix:";
    public static final String ks_course_id_prefix = "ks_course_id_prefix:";
    @Autowired
    private ICourseAuditService courseAuditService;
    @Autowired
    private IChapterAuditService chapterAuditService;
    @Autowired
    private ICourseDirectoryAuditService courseDirectoryAuditService;
    @Autowired
    private ITVideoService videoService;
    /**
     * 资源上传服务
     */
    @Autowired
    private IKsResourceUploadService ksResourceUploadService;
    /**
     * 调用快手接口服务
     */
    @Autowired
    private KuaishouMpCourseService kuaishouMpCourseService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private ICourseDyService courseDyService;
    @Autowired
    private KsAccessTokenService ksAccessTokenService;
    @Autowired
    private ITTeacherService teacherService;
    @Autowired
    private ICourseDirectoryDyService courseDirectoryDyService;
    @Autowired
    private IChapterDyService chapterDyService;
    @Autowired
    private ICourseService courseService;
    @Autowired
    private IChapterService chapterService;
    @Autowired
    private ICourseDirectoryService courseDirectoryService;
    @Autowired
    private IMGiveEntityService giveEntityService;
    @Autowired
    private SmsService smsService;
    @Autowired
    private DouyinAuditService douyinAuditService;

    public void checkKsResourceStatus1() {
        List<String> appIdList = new ArrayList<>();
        appIdList.add("ks699183842184529589");
        appIdList.add("ks696650569772164426");
        for (String appId : appIdList) {
            KsResourceUpload ksResourceUpload = new KsResourceUpload();
            ksResourceUpload.setKsAppId(appId);
            ksResourceUpload.setKsStatus(1);
            List<KsResourceUpload> ksResourceUploads = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
            for (KsResourceUpload ksResourceUpload1 : ksResourceUploads) {
                String resourceId = ksResourceUpload1.getAudioId();
                if (StringUtils.isBlank(resourceId)) {
                    resourceId = ksResourceUpload1.getVideoId();
                }
                if (StringUtils.isBlank(resourceId)) {
                    continue;
                }
                String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(ksResourceUpload1.getAppNameType(), appId);
                String checkUrl = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/resource/info?app_id=%s&access_token=%s";
                checkUrl = String.format(checkUrl, appId, accessToken);
                MpCourseResourceInfo mpCourseResourceInfo = new MpCourseResourceInfo();
                mpCourseResourceInfo.setResourceId(resourceId);
                MpCourseResourceInfoResult mpCourseResourceInfoResult = getUploadDyCourseResult(mpCourseResourceInfo, checkUrl, MpCourseResourceInfoResult.class);
                if (mpCourseResourceInfoResult != null && mpCourseResourceInfoResult.getResult() == 1) {
                    if (mpCourseResourceInfoResult.getData() != null) {
                        if (mpCourseResourceInfoResult.getData().getStatus() == 2) {
                            ksResourceUpload1.setKsStatus(2);
                            ksResourceUpload1.setResult(1);
                            ksResourceUploadService.updateKsResourceUpload(ksResourceUpload1);
                        }
                        if (mpCourseResourceInfoResult.getData().getStatus() == 3) {
                            ksResourceUploadService.deleteKsResourceUploadByResourceId(ksResourceUpload1.getResourceId());
                        }
                        if (mpCourseResourceInfoResult.getData().getStatus() == 4) {
                            ksResourceUploadService.deleteKsResourceUploadByResourceId(ksResourceUpload1.getResourceId());
                        }
                    }
                }
            }
        }
    }

    public void uploadResourceToKs() {
        //查询课程
        System.out.println("课程资源上传开始!!");
        CourseDy query = new CourseDy();
        //快手提审状态为ks_audit_status
        //快手 0未提审 1审核中 2审核通过 3审核驳回
        //还没有快手审核的时候为2,默认通过,现在有审核了,课程修改后应该为0
        query.setKsAuditStatus(0);
        query.setIsDelete(0);
        //查询的发布平台为2,快手
        query.setPublishPlatform("2");
        //这个是问到边审核状态,一般为1,默认通过,因为问到侧基本不审核
        query.setAuditStatus(1);
        List<CourseDy> list = courseDyService.selectCourseDyList(query);
        //上传资源

        //检测资源状态
        //全部检测通过,上传课程内容
        //或者更新课程
        //或者追加课程履约内容
        if (CollectionUtils.isEmpty(list)) {
            System.out.println("没有需要审核的课程!!");
            return;
        }
        for (CourseDy courseDy : list) {
            if (courseDy.getKuaishouClassId() != null && courseDy.getKuaishouClassId().intValue() > 0) {
                System.out.println("符合条件");
            } else {
                System.out.println("不符合条件");
                continue;
            }
            System.out.println("开始单门课程审核");
            //获取图片资源并上传
            Integer appNameType = courseDy.getAppNameType();
            if (appNameType == null) {
                System.out.println("数据不完整,跳过");
                continue;
            }
            String appId = null;
            if (appNameType == 1) {
                //问到好课
                appId = "ks699183842184529589";
            }
            if (appNameType == 2) {
                //问到课堂
                appId = "ks696650569772164426";
            }
            if (StringUtils.isBlank(appId)) {
                System.out.println("错误的appNameType!");
                continue;
            }
            String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(appNameType, appId);
            //准备课程的图片  1.课程封面  2.老师头像
            //获取课程封面
            String coverPicUrl = courseDy.getCoverPicUrl();
            if (StringUtils.isBlank(coverPicUrl)) {
                System.out.println("课程封面为空,跳过,课程主键Id为:" + courseDy.getId());
                continue;
            }
            uploadKsImage(appId, coverPicUrl, accessToken, appNameType);
            //查询老师信息
            if (courseDy.getTeacherId() == null || courseDy.getTeacherId() <= 0L) {
                System.out.println("老师Id错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(courseDy.getTeacherId());
            if (tTeacher == null) {
                System.out.println("老师信息错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() == null) {
                System.out.println("老师状态错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() != null && tTeacher.getStatus() == 1) {
                System.out.println("老师账号已被停用,课程主键Id为:" + courseDy.getId());
                continue;
            }
            String avatarUrl = tTeacher.getAvatarUrl();
            if (StringUtils.isBlank(avatarUrl)) {
                System.out.println("老师头像为空,跳过,课程主键Id为:" + courseDy.getId());
                continue;
            }
            uploadKsImage(appId, avatarUrl, accessToken, appNameType);
            //紧接着就是上传课程资源
            Course courseInDB = getCourse(courseDy.getId(), courseDy.getTeacherId(), courseDy.getCourseType());
            if (courseInDB.getCourseType() == 1) {
                List<Chapter> chapterList = courseInDB.getChapterList();
                if (CollectionUtils.isEmpty(chapterList)) {
                    System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                for (Chapter chapter : chapterList) {
                    List<CourseDirectory> courseDirectoryList = chapter.getCourseDirectoryList();
                    if (CollectionUtils.isEmpty(courseDirectoryList)) {
                        System.out.println("课程目录为空,跳过,课程主键Id为:" + courseDy.getId() + ",章节主键Id:" + chapter.getId());
                        continue;
                    }
                    processUploadResource(appId, courseDirectoryList, accessToken, appNameType);
                }
            } else {
                List<CourseDirectory> allCourseDirectoryList = courseInDB.getAllCourseDirectoryList();
                if (CollectionUtils.isEmpty(allCourseDirectoryList)) {
                    System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                processUploadResource(appId, allCourseDirectoryList, accessToken, appNameType);
            }
            //说明上传完毕,直接更新指定Id的课程快手状态
            CourseDy courseDyUpdate = new CourseDy();
            courseDyUpdate.setId(courseDy.getId());
            // 状态4为课程资源上传完毕待检查
            courseDyUpdate.setKsAuditStatus(4);
            courseDyService.updateCourseDy(courseDyUpdate);
        }
    }

    public void checkResourceToKs() {
        //查询课程
        System.out.println("检查课程资源上传是否成功!!");
        CourseDy query = new CourseDy();
        //快手提审状态为ks_audit_status
        //快手 0未提审 1审核中 2审核通过 3审核驳回
        //还没有快手审核的时候为2,默认通过,现在有审核了,课程修改后应该为0
        query.setKsAuditStatus(4);
        query.setIsDelete(0);
        //查询的发布平台为2,快手
        query.setPublishPlatform("2");
        //这个是问到边审核状态,一般为1,默认通过,因为问到侧基本不审核
        query.setAuditStatus(1);
        List<CourseDy> list = courseDyService.selectCourseDyList(query);
        //上传资源

        //上传图片资源
        //检测资源状态
        //全部检测通过,上传课程内容
        //或者更新课程
        //或者追加课程履约内容
        if (CollectionUtils.isEmpty(list)) {
            System.out.println("没有需要审核的课程!!");
            return;
        }
        for (CourseDy courseDy : list) {
            if (courseDy.getKuaishouClassId() != null && courseDy.getKuaishouClassId().intValue() > 0) {
                System.out.println("符合条件");
            } else {
                System.out.println("不符合条件");
                continue;
            }
            //检测状态以单门课程为一个整体
            System.out.println("开始单门课程审核");
            //获取图片资源并上传
            Integer appNameType = courseDy.getAppNameType();
            if (appNameType == null) {
                System.out.println("数据不完整,跳过");
                continue;
            }
            String appId = null;
            if (appNameType == 1) {
                //问到好课
                appId = "ks699183842184529589";
            }
            if (appNameType == 2) {
                //问到课堂
                appId = "ks696650569772164426";
            }
            if (StringUtils.isBlank(appId)) {
                System.out.println("错误的appNameType!");
                continue;
            }
            //准备课程的图片  1.课程封面  2.老师头像
            //获取课程封面
            String coverPicUrl = courseDy.getCoverPicUrl();
            if (StringUtils.isBlank(coverPicUrl)) {
                System.out.println("课程封面为空,跳过,课程主键Id为:" + courseDy.getId());
                continue;
            }
            boolean checkOK = checkKsImage(appId, coverPicUrl, appNameType);
            //查询老师信息
            if (courseDy.getTeacherId() == null || courseDy.getTeacherId() <= 0L) {
                System.out.println("老师Id错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(courseDy.getTeacherId());
            if (tTeacher == null) {
                System.out.println("老师信息错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() == null) {
                System.out.println("老师状态错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() != null && tTeacher.getStatus() == 1) {
                System.out.println("老师账号已被停用,课程主键Id为:" + courseDy.getId());
                continue;
            }
            String avatarUrl = tTeacher.getAvatarUrl();
            if (StringUtils.isBlank(avatarUrl)) {
                System.out.println("老师头像为空,跳过,课程主键Id为:" + courseDy.getId());
                continue;
            }
            checkOK = (checkOK && checkKsImage(appId, avatarUrl, appNameType));
            //紧接着就是上传课程资源
            Course courseInDB = getCourse(courseDy.getId(), courseDy.getTeacherId(), courseDy.getCourseType());
            if (courseInDB.getCourseType() == 1) {
                List<Chapter> chapterList = courseInDB.getChapterList();
                if (CollectionUtils.isEmpty(chapterList)) {
                    System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                for (Chapter chapter : chapterList) {
                    List<CourseDirectory> courseDirectoryList = chapter.getCourseDirectoryList();
                    if (CollectionUtils.isEmpty(courseDirectoryList)) {
                        System.out.println("课程目录为空,跳过,课程主键Id为:" + courseDy.getId() + ",章节主键Id:" + chapter.getId());
                        continue;
                    }
                    checkOK = (checkOK && checkUploadResource(appId, courseDirectoryList, appNameType));
                }
            } else {
                List<CourseDirectory> allCourseDirectoryList = courseInDB.getAllCourseDirectoryList();
                if (CollectionUtils.isEmpty(allCourseDirectoryList)) {
                    System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                checkOK = (checkOK && checkUploadResource(appId, allCourseDirectoryList, appNameType));
            }
            //检查全部正常后,设置课程状态为待提交到快手审核
            if (checkOK) {
                //说明上传完毕,直接更新指定Id的课程快手状态
                CourseDy courseDyUpdate = new CourseDy();
                courseDyUpdate.setId(courseDy.getId());
                // 状态5为课程资源上传全部检查成功,待提交到快手审核
                courseDyUpdate.setKsAuditStatus(5);
                courseDyService.updateCourseDy(courseDyUpdate);
            }
        }
    }

    public void createKsCourseAudit() {
        Map<String, MpCourseCreateResult> resultMap = new HashMap<>();
        //查询课程
        System.out.println("资源检查完毕的课程内容快手上传开始!!");
        CourseDy query = new CourseDy();
        //快手提审状态为ks_audit_status
        //快手 0未提审 1审核中 2审核通过 3审核驳回
        //还没有快手审核的时候为2,默认通过,现在有审核了,课程修改后应该为0
        query.setKsAuditStatus(5);
        query.setIsDelete(0);
        //查询的发布平台为2,快手
        query.setPublishPlatform("2");
        //这个是问到边审核状态,一般为1,默认通过,因为问到侧基本不审核
        query.setAuditStatus(1);
        List<CourseDy> list = courseDyService.selectCourseDyList(query);
        //上传资源

        //上传图片资源
        //检测资源状态
        //全部检测通过,上传课程内容
        //或者更新课程
        //或者追加课程履约内容
        if (CollectionUtils.isEmpty(list)) {
            System.out.println("没有需要审核的课程!!");
            return;
        }
        for (CourseDy courseDy : list) {
            if (courseDy.getKuaishouClassId() != null && courseDy.getKuaishouClassId().intValue() > 0) {
                System.out.println("符合条件");
            } else {
                System.out.println("不符合条件");
                continue;
            }
            System.out.println("开始单门课程审核");
            //获取图片资源并上传
            Integer appNameType = courseDy.getAppNameType();
            if (appNameType == null) {
                System.out.println("数据不完整,跳过");
                continue;
            }
            String appId = null;
            if (appNameType == 1) {
                //问到好课
                appId = "ks699183842184529589";
            }
            if (appNameType == 2) {
                //问到课堂
                appId = "ks696650569772164426";
            }
            if (StringUtils.isBlank(appId)) {
                System.out.println("错误的appNameType!");
                continue;
            }
            boolean submitToKsOk;
            {
                CreateCourseInfo createCourseInfo = new CreateCourseInfo();
                MpCourseAlbumInfo mpCourseAlbumInfo = new MpCourseAlbumInfo();

                //每个appId执行一次上传
                String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(appNameType, appId);
                //准备课程的图片  1.课程封面  2.老师头像
                //获取课程封面
                String coverPicUrl = courseDy.getCoverPicUrl();
                if (StringUtils.isBlank(coverPicUrl)) {
                    System.out.println("课程封面为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                KsResourceUpload ksResourceUpload = queryKsImage(appId, coverPicUrl, appNameType);
                if (ksResourceUpload != null) {
                    String imgKey = ksResourceUpload.getImgKey();
                    mpCourseAlbumInfo.setCover(imgKey);
                }
                //查询老师信息
                if (courseDy.getTeacherId() == null || courseDy.getTeacherId() <= 0L) {
                    System.out.println("老师Id错误,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(courseDy.getTeacherId());
                if (tTeacher == null) {
                    System.out.println("老师信息错误,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                if (tTeacher.getStatus() == null) {
                    System.out.println("老师状态错误,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                if (tTeacher.getStatus() != null && tTeacher.getStatus() == 1) {
                    System.out.println("老师账号已被停用,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                String avatarUrl = tTeacher.getAvatarUrl();
                if (StringUtils.isBlank(avatarUrl)) {
                    System.out.println("老师头像为空,跳过,课程主键Id为:" + courseDy.getId());
                    continue;
                }
                KsResourceUpload avatarUploadResult = queryKsImage(appId, avatarUrl, appNameType);
                TeacherInfo teacherInfo = new TeacherInfo();
                teacherInfo.setName(tTeacher.getTeacherName());
                teacherInfo.setIntroduce(tTeacher.getTeacherDesc());
                if (avatarUploadResult != null) {
                    teacherInfo.setImgKey(avatarUploadResult.getImgKey());
                }
                mpCourseAlbumInfo.setTeacherInfo(teacherInfo);
                /**
                 * 添加课程信息
                 */
                mpCourseAlbumInfo.setTitle(courseDy.getTitle());
                mpCourseAlbumInfo.setTotalEpisodeNumber(0);
                mpCourseAlbumInfo.setPath(mini_app_course_detail_path);
                //添加快手课程审核，如果有文本
                Course courseFromRedis = redisService.getCacheObject(COURSE_KEY_PREFIX + courseDy.getId());
                if (courseFromRedis != null && StringUtils.isNotBlank(courseFromRedis.getKsAuditPlainText())) {
                    //截断为500字,如果不到500则全部
                    if (courseFromRedis.getKsAuditPlainText().length() > 500) {
                        mpCourseAlbumInfo.setIntroduce(courseFromRedis.getKsAuditPlainText().substring(0, 500));
                    }else {
                        mpCourseAlbumInfo.setIntroduce(courseFromRedis.getKsAuditPlainText());
                    }
                } else {
                    mpCourseAlbumInfo.setIntroduce(courseDy.getTitle());
                }
                mpCourseAlbumInfo.setChargeOrNot(true);
                mpCourseAlbumInfo.setUpdateDoneOrNot(false);
                mpCourseAlbumInfo.setNotifyUrl(ks_course_callback_url);
                //26.其他职业
                mpCourseAlbumInfo.setSecondCategory(courseDy.getKuaishouClassId() == null ? 26 : courseDy.getKuaishouClassId().intValue());
                mpCourseAlbumInfo.setPurchasePrecation("购买须知:七天内观看不超过5%可以申请退款!");
                //教学类型：
                //1.线上
                //2.线下
                mpCourseAlbumInfo.setTeachType(1);
//                服务形式
//                1. 1对1教学
//                2. 1对多教学
//                3. 无后续服务提供
                mpCourseAlbumInfo.setServiceType(3);
                //永久有效
                mpCourseAlbumInfo.setValidPeriod(3);
                mpCourseAlbumInfo.setSupportTrial(false);
                //赠送实物优先
                MGiveEntity mGiveEntity = giveEntityService.selectMGiveEntityByCourseId(courseDy.getId());
                if (mGiveEntity != null && mGiveEntity.getIsGetAdress() != null && mGiveEntity.getIsGetAdress() == 1) {
                    String materials = mGiveEntity.getMaterials();
                    List<MGiveEntityAndTextbookVO> mGiveEntityList = JSON.parseArray(materials, MGiveEntityAndTextbookVO.class);
                    if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(mGiveEntityList)) {
                        MGiveEntityAndTextbookVO mGiveEntityAndTextbookVO = mGiveEntityList.get(0);
                        GiftInfo giftInfo = new GiftInfo();
                        giftInfo.setName(mGiveEntityAndTextbookVO.getTextBook());
                        giftInfo.setAmount(1);
                        giftInfo.setRefundPolicy("课程退款时需要退还赠品!");
                        giftInfo.setType(1);
                        mpCourseAlbumInfo.setGiftInfo(giftInfo);
                    }
                }
                //学习资料在后
                if (mpCourseAlbumInfo.getGiftInfo() == null) {
                    StudyDataDTO1 studyData = giveEntityService.selectStudyByCourseId(courseDy.getId());
                    if (studyData != null) {
                        GiftInfo giftInfo = new GiftInfo();
                        giftInfo.setName(studyData.getStudyFileName());
                        giftInfo.setAmount(1);
                        giftInfo.setRefundPolicy("课程退款无需退还!");
                        giftInfo.setType(2);
                        mpCourseAlbumInfo.setGiftInfo(giftInfo);
                    }
                }
                //所有课程的目录
                List<CourseDirectory> cdCheckList = new ArrayList<>();
//              紧接着就是查询上传的课节
                Course courseInDB = getCourse(courseDy.getId(), courseDy.getTeacherId(), courseDy.getCourseType());
                if (courseInDB.getCourseType() == 1) {
                    List<Chapter> chapterList = courseInDB.getChapterList();
                    if (CollectionUtils.isEmpty(chapterList)) {
                        System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                        continue;
                    }
                    for (Chapter chapter : chapterList) {
                        List<CourseDirectory> courseDirectoryList = chapter.getCourseDirectoryList();
                        if (CollectionUtils.isEmpty(courseDirectoryList)) {
                            System.out.println("课程目录为空,跳过,课程主键Id为:" + courseDy.getId() + ",章节主键Id:" + chapter.getId());
                            continue;
                        }
                        cdCheckList.addAll(courseDirectoryList);
                    }
                } else {
                    cdCheckList = courseInDB.getAllCourseDirectoryList();
                    if (CollectionUtils.isEmpty(cdCheckList)) {
                        System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                        continue;
                    }
                }
                List<EpisodeInfo> episodeInfoList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(cdCheckList)) {
                    //directory_type==4 文件
                    cdCheckList.removeIf(cd -> cd.getDirectoryType()!=null&&cd.getDirectoryType() == 4);
                    //directory_type==5 图文
                    cdCheckList.removeIf(cd -> cd.getDirectoryType()!=null&&cd.getDirectoryType() == 5);
                    //directory_type==6 题库
                    cdCheckList.removeIf(cd -> cd.getDirectoryType()!=null&&cd.getDirectoryType() == 6);
                }
                if (CollectionUtils.isEmpty(cdCheckList)) {
                    System.out.println("课程章节为空,跳过,课程主键Id为:" + courseDy.getId());
                    String emptyErrorMsg = "课程章节或目录必须包含图片或视频,图文课程不能提交,请检查课程目录或章节.";
                    //保存审核失败消息
                    douyinAuditService.saveDyAuditFailResult(courseDy.getId(), emptyErrorMsg);
                    //同时更新审核状态为失败
                    //审核驳回
                    CourseDy courseDyUpdate = new CourseDy();
                    courseDyUpdate.setId(courseDy.getId());
                    courseDyUpdate.setKsAuditStatus(3);
                    courseDyService.updateCourseDy(courseDyUpdate);

                    Course courseResult = new Course();
                    courseResult.setId(courseDy.getId());
                    courseResult.setKsAuditStatus(3);
                    courseResult.setKsCourseId(courseDy.getKsCourseId());
                    courseResult.setKsCourseVersion(courseDy.getKsCourseVersion());
                    courseResult.setTeacherId(courseDy.getTeacherId());
                    courseService.updateCourse(courseResult);
                    //新增通知
                    String noticeTitle = "「 审核通知 」 您的课程 《" + courseDy.getTitle() + "》已被快手驳回";
                    String noticeContent = "您的课程 《" + courseDy.getTitle() + "》已被快手驳回,原因:" + emptyErrorMsg;
                    douyinAuditService.insetCourseAuditNotify(noticeTitle, noticeContent, courseResult.getTeacherId());
                    continue;
                }
                if (CollectionUtils.isNotEmpty(cdCheckList)) {
                    int size = cdCheckList.size();
                    mpCourseAlbumInfo.setTotalEpisodeNumber(size);
                    //整体课程是否有试看
                    boolean isAllCourseHaveIsTrySee = false;
                    for (int i = 0; i < cdCheckList.size(); i++) {
                        CourseDirectory cd = cdCheckList.get(i);
                        EpisodeInfo episodeInfo = new EpisodeInfo();
                        episodeInfo.setTitle(cd.getDirectoryName());
                        episodeInfo.setEpisodeNumber(i + 1);
                        //目录类型 1视频，2音频，3图片
                        if (cd.getDirectoryType() != null) {
                            if (cd.getDirectoryType() == 1) {
                                KsResourceUpload videoUploadResult = queryKsVideo(appId, cd.getCourseDirectoryUrl(), appNameType);
                                if (videoUploadResult != null) {
                                    episodeInfo.setVideoId(videoUploadResult.getVideoId());
                                }
                            } else if (cd.getDirectoryType() == 2) {
                                KsResourceUpload audioUploadResult = queryKsAudio(appId, cd.getCourseDirectoryUrl(), appNameType);
                                if (audioUploadResult != null) {
                                    episodeInfo.setAudioId(audioUploadResult.getAudioId());
                                }
                            } else if (cd.getDirectoryType() == 3) {
                                KsResourceUpload imgUploadResult = queryKsImage(appId, cd.getCourseDirectoryUrl(), appNameType);
                                if (imgUploadResult != null) {
                                    episodeInfo.setImageIdList(Collections.singletonList(imgUploadResult.getImgKey()));
                                }
                            }
                        }
                        Integer isTrySee = cd.getIsTrySee();
                        episodeInfo.setSupportTrial(isTrySee != null && isTrySee == 1);
                        if (!isAllCourseHaveIsTrySee) {
                            isAllCourseHaveIsTrySee = isTrySee != null && isTrySee == 1;
                        }
                        episodeInfo.setPath(mini_app_course_detail_path);
                        episodeInfoList.add(episodeInfo);
                    }
                    mpCourseAlbumInfo.setSupportTrial(isAllCourseHaveIsTrySee);
                }
                createCourseInfo.setMpCourseAlbumInfo(mpCourseAlbumInfo);
                if (CollectionUtils.isNotEmpty(episodeInfoList)) {
                    createCourseInfo.setEpisodeInfoList(episodeInfoList);
                }
                MpCourseCreateResult mpCourseCreateResult = null;
                String createUrl = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/create?app_id=" + appId + "&access_token=" + accessToken;
                String editUrl = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/edit?app_id=" + appId + "&access_token=" + accessToken;
                if (StringUtils.isNotBlank(courseDy.getKsCourseId())) {
                    //编辑
                    EditCourseInfo editCourseInfo = new EditCourseInfo();
                    BeanUtils.copyProperties(createCourseInfo, editCourseInfo);
                    editCourseInfo.setCourseId(courseDy.getKsCourseId());
                    mpCourseCreateResult = getUploadDyCourseResult(editCourseInfo, editUrl, MpCourseCreateResult.class);
                } else {
                    //创建
                    mpCourseCreateResult = getUploadDyCourseResult(createCourseInfo, createUrl, MpCourseCreateResult.class);
                }
                
                // 检查是否是因为已有审核中的记录
                if (mpCourseCreateResult != null && 
                    mpCourseCreateResult.getResult() != null && 
                    mpCourseCreateResult.getResult() == 10000431 && 
                    StringUtils.isNotBlank(mpCourseCreateResult.getError_msg()) && 
                    mpCourseCreateResult.getError_msg().contains("已有审核中的记录，禁止修改")) {
                    
                    System.out.println("课程创建/编辑失败，原因：已有审核中的记录，开始查询版本信息，课程ID:" + courseDy.getId());
                    handleAuditInProgressError(courseDy, appId, accessToken);
                    continue;
                }
                
                //课程创建成功后提交审核
                if (mpCourseCreateResult != null && mpCourseCreateResult.getResult() == 1 && StringUtils.isNotBlank(mpCourseCreateResult.getData().getCourseId()) && mpCourseCreateResult.getData().getVersion() != null) {
                    System.out.println("创建课程成功,课程Id:" + mpCourseCreateResult.getData().getCourseId() + ",问到课程Id:" + courseDy.getId());
                    MpCourseDetail mpCourseDetail1 = new MpCourseDetail();
                    mpCourseDetail1.setCourseId(mpCourseCreateResult.getData().getCourseId());
                    mpCourseDetail1.setVersion(mpCourseCreateResult.getData().getVersion());
                    MpCourseSubmitResult mpCourseSubmitResult = kuaishouMpCourseService.mpCourseSubmit(appId, accessToken, mpCourseDetail1);
                    if (mpCourseSubmitResult.getResult() == 1) {
                        System.out.println("提交审核成功,课程Id:" + mpCourseCreateResult.getData().getCourseId() + ",问到课程Id:" + courseDy.getId());
                    } else {
//                        if (mpCourseSubmitResult.getResult() == 10000200 && StringUtils.isNotBlank(mpCourseSubmitResult.getError_msg()) && mpCourseSubmitResult.getError_msg().contains("信息相同")) {
//                            //TODO:提交了相同内容/忽略审核
//                            //提交了相同内容
//                            //忽略审核
//                            //continue;
//                        }
                        //如果失败直接审核失败
                        String auditErrorMsg = mpCourseSubmitResult.getError_msg();
                        if (StringUtils.isBlank(auditErrorMsg)) {
                            auditErrorMsg = "快手审核驳回!";
                        }
                        //保存审核失败消息
                        douyinAuditService.saveDyAuditFailResult(courseDy.getId(), auditErrorMsg);
                        //同时更新审核状态为失败
                        //审核驳回
                        CourseDy courseDyUpdate = new CourseDy();
                        courseDyUpdate.setId(courseDy.getId());
                        courseDyUpdate.setKsAuditStatus(3);
                        courseDyService.updateCourseDy(courseDyUpdate);

                        Course courseResult = new Course();
                        courseResult.setId(courseDy.getId());
                        courseResult.setKsAuditStatus(3);
                        courseResult.setKsCourseId(courseDy.getKsCourseId());
                        courseResult.setKsCourseVersion(courseDy.getKsCourseVersion());
                        courseResult.setTeacherId(courseDy.getTeacherId());
                        courseService.updateCourse(courseResult);
                        //新增通知
                        String noticeTitle = "「 审核通知 」 您的课程 《" + courseDy.getTitle() + "》已被快手驳回";
                        String noticeContent = "您的课程 《" + courseDy.getTitle() + "》已被快手驳回";
                        douyinAuditService.insetCourseAuditNotify(noticeTitle, noticeContent, courseResult.getTeacherId());
                        System.out.println("提交审核失败,课程Id:" + mpCourseCreateResult.getData().getCourseId() + ",问到课程Id:" + courseDy.getId() + ",错误信息:" + JSON.toJSONString(mpCourseSubmitResult));
                        continue;
                    }
                } else {
                    System.out.println("创建课程失败,问到课程Id:" + courseDy.getId() + ",错误信息:" + JSON.toJSONString(mpCourseCreateResult));
                    System.out.println("提交的数据为:" + JSON.toJSONString(createCourseInfo));
                }
                //缓存数据
                String key = ks_course_submit_prefix + appId + "_" + courseDy.getId();
                resultMap.put(key, mpCourseCreateResult);
                redisService.setCacheObject(key, mpCourseCreateResult);
                submitToKsOk = mpCourseCreateResult != null && mpCourseCreateResult.getResult() == 1;
            }
            //课程创建成功,加入数据库
            // 判断提交成功
            if (submitToKsOk) {
                String key = ks_course_submit_prefix + appId + "_" + courseDy.getId();
                MpCourseCreateResult mpCourseCreateResult = resultMap.get(key);
                if (mpCourseCreateResult != null) {
                    //此时课程状态被设置为快手审核中,等待快手审核回调
                    CourseDy courseDyUpdate = new CourseDy();
                    courseDyUpdate.setId(courseDy.getId());
                    // 状态5为课程资源上传全部检查成功,待提交到快手审核
                    courseDyUpdate.setKsAuditStatus(1);
                    courseDyUpdate.setKsCourseId(mpCourseCreateResult.getData().getCourseId());
                    courseDyUpdate.setKsCourseVersion(mpCourseCreateResult.getData().getVersion());
                    courseDyService.updateCourseDy(courseDyUpdate);

                    Course courseUpdate0 = new Course();
                    courseUpdate0.setId(courseDy.getId());
                    courseUpdate0.setKsAuditStatus(1);
                    courseUpdate0.setKsCourseId(mpCourseCreateResult.getData().getCourseId());
                    courseUpdate0.setKsCourseVersion(mpCourseCreateResult.getData().getVersion());
                    courseService.updateCourse(courseUpdate0);
                    if (StringUtils.isNotBlank(mpCourseCreateResult.getData().getCourseId())) {
                        redisService.setCacheObject(ks_course_id_prefix + mpCourseCreateResult.getData().getCourseId(), courseDy.getId());
                    }
                }
            }
        }
    }

    public void queryKsCourseAudit() {
        //查询课程
        System.out.println("查询课程审核结果!!");
        CourseDy query = new CourseDy();
        query.setKsAuditStatus(1);
        query.setIsDelete(0);
        //查询的发布平台为2,快手
        query.setPublishPlatform("2");
        //这个是问到边审核状态,一般为1,默认通过,因为问到侧基本不审核
        query.setAuditStatus(1);
        List<CourseDy> list = courseDyService.selectCourseDyList(query);
        if (CollectionUtils.isEmpty(list)) {
            System.out.println("没有需要审核的课程!!");
            return;
        }
        for (CourseDy courseDy : list) {
            if (courseDy.getKuaishouClassId() != null && courseDy.getKuaishouClassId().intValue() > 0) {
                System.out.println("符合条件");
            } else {
                System.out.println("不符合条件");
                continue;
            }
            System.out.println("开始单门课程审核");
            //获取图片资源并上传
            Integer appNameType = courseDy.getAppNameType();
            if (appNameType == null) {
                System.out.println("数据不完整,跳过");
                continue;
            }
            String appId = null;
            if (appNameType == 1) {
                //问到好课
                appId = "ks699183842184529589";
            }
            if (appNameType == 2) {
                //问到课堂
                appId = "ks696650569772164426";
            }
            if (StringUtils.isBlank(appId)) {
                System.out.println("错误的appNameType!");
                continue;
            }
            String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(appNameType, appId);
            if (courseDy.getTeacherId() == null || courseDy.getTeacherId() <= 0L) {
                System.out.println("老师Id错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(courseDy.getTeacherId());
            if (tTeacher == null) {
                System.out.println("老师信息错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() == null) {
                System.out.println("老师状态错误,课程主键Id为:" + courseDy.getId());
                continue;
            }
            if (tTeacher.getStatus() != null && tTeacher.getStatus() == 1) {
                System.out.println("老师账号已被停用,课程主键Id为:" + courseDy.getId());
                continue;
            }
            MpCourseDetail mpCourseDetail = new MpCourseDetail();
            mpCourseDetail.setCourseId(courseDy.getKsCourseId());
            mpCourseDetail.setVersion(courseDy.getKsCourseVersion());
            String queryUrl = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/detail?app_id=" + appId + "&access_token=" + accessToken;
            MpCourseDetailResult mpCourseDetailResult = getUploadDyCourseResult(mpCourseDetail, queryUrl, MpCourseDetailResult.class);
            if (mpCourseDetailResult != null && mpCourseDetailResult.getResult() == 1 && mpCourseDetailResult.getData() != null) {
                MpCourseVersionResultData versionInfo = mpCourseDetailResult.getData().getVersionInfo();
                if (versionInfo.getAuditStatus() == 2) {
                    //审核通过,上架
                    MpCourseCreateResult mpCourseCreateResult = kuaishouMpCourseService.mpCourseOnline(appId, accessToken, mpCourseDetail);
                    if (mpCourseCreateResult.getResult() == 1) {
                        CourseDy courseDyUpdate = new CourseDy();
                        courseDyUpdate.setId(courseDy.getId());
                        courseDyUpdate.setKsAuditStatus(2);
                        courseDyUpdate.setCourseOnShelfStatus(1);
                        courseDy.setKsAuditStatus(2);
                        courseDy.setCourseOnShelfStatus(1);
                        courseDyService.updateCourseDy(courseDyUpdate);

                        Course courseResult = new Course();
                        courseResult.setId(courseDy.getId());
                        courseResult.setKsAuditStatus(2);
                        courseResult.setCourseOnShelfStatus(1);
                        courseResult.setKsCourseId(courseDy.getKsCourseId());
                        courseResult.setKsCourseVersion(courseDy.getKsCourseVersion());
                        courseService.updateCourse(courseResult);
                    } else {
                        System.out.println("课程上线失败,课程主键Id为:" + courseDy.getId() + ",快手课程主键Id:" + courseDy.getKsCourseId());
                        continue;
                    }
                    try {
                        smsService.sendKsCourseAuditSuccessMessage(tTeacher.getMobile(), courseDy.getTitle());
                    } catch (Exception e) {
                        log.error(e.getMessage());
                    }
                    {
                        //同步到course_audit,chapter_audit,course_dir_audit
                        CourseAudit courseAudit = courseAuditService.selectCourseAuditById(courseDy.getId());

                        CourseAudit courseAudit1 = new CourseAudit();
                        BeanUtils.copyProperties(courseDy, courseAudit1);
                        if (courseAudit == null) {
                            courseAuditService.insertCourseAudit(courseAudit1);
                            //章节
                            ChapterDy chapter = new ChapterDy();
                            chapter.setCourseId(courseDy.getId());
                            List<ChapterDy> chapters = chapterDyService.selectChapterDyList(chapter);
                            for (ChapterDy chapterItem : chapters) {
                                ChapterAudit chapterAudit = new ChapterAudit();
                                BeanUtils.copyProperties(chapterItem, chapterAudit);
                                chapterAuditService.insertChapterAudit(chapterAudit);
                            }
                            //目录
                            CourseDirectoryDy cd = new CourseDirectoryDy();
                            cd.setCourseId(courseDy.getId());
                            List<CourseDirectoryDy> courseDirectories = courseDirectoryDyService.selectCourseDirectoryDyList(cd);
                            for (CourseDirectoryDy cdItem : courseDirectories) {
                                CourseDirectoryAudit courseDirectoryAudit = new CourseDirectoryAudit();
                                BeanUtils.copyProperties(cdItem, courseDirectoryAudit);
                                //插入视频的宽高
                                if (courseDirectoryAudit.getDirectoryType() == 1) {
                                    TVideo video = videoService.selectTVideoByUrl(courseDirectoryAudit.getCourseDirectoryUrl());
                                    if (video != null) {
                                        courseDirectoryAudit.setVideoHeight(video.getVideoHeight());
                                        courseDirectoryAudit.setVideoWidth(video.getVideoWidth());
                                    }
                                }
                                courseDirectoryAuditService.insertCourseDirectoryAudit(courseDirectoryAudit);
                            }
                        } else {
                            Integer dyAuditStatus = courseAudit.getDyAuditStatus();
                            courseAudit1.setDyAuditStatus(dyAuditStatus);
                            courseAuditService.updateCourseAudit(courseAudit1);
                            //已经存在课程更新课程
                            //章节
                            ChapterDy chapter = new ChapterDy();
                            chapter.setCourseId(courseDy.getId());
                            List<ChapterDy> chapters = chapterDyService.selectChapterDyList(chapter);
                            List<Long> collect0 = chapters.stream().map(ChapterDy::getId).collect(Collectors.toList());
                            ChapterAudit chapterAudit = new ChapterAudit();
                            chapterAudit.setCourseId(courseDy.getId());
                            List<ChapterAudit> chapterAuditList = chapterAuditService.selectChapterAuditList(chapterAudit);
                            List<Long> collect1 = chapterAuditList.stream().map(ChapterAudit::getId).collect(Collectors.toList());

                            List<Long> subtract = ListUtils.subtract(collect1, collect0);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(subtract)) {
                                Long[] array2 = subtract.toArray(new Long[0]);
                                chapterAuditService.deleteChapterAuditByIds(array2);
                            }
                            //有则更新,没有插入
                            for (ChapterDy c : chapters) {
                                ChapterAudit chapterAudit1 = chapterAuditService.selectChapterAuditById(c.getId());
                                ChapterAudit chapterAudit2 = new ChapterAudit();
                                BeanUtils.copyProperties(c, chapterAudit2);
                                if (chapterAudit1 == null) {
                                    chapterAuditService.insertChapterAudit(chapterAudit2);
                                } else {
                                    chapterAuditService.updateChapterAudit(chapterAudit2);
                                }
                            }
                            //目录
                            CourseDirectoryDy cd = new CourseDirectoryDy();
                            cd.setCourseId(courseDy.getId());
                            List<CourseDirectoryDy> courseDirectories = courseDirectoryDyService.selectCourseDirectoryDyList(cd);
                            List<Long> collect2 = courseDirectories.stream().map(CourseDirectoryDy::getId).collect(Collectors.toList());
                            CourseDirectoryAudit cddy = new CourseDirectoryAudit();
                            cddy.setCourseId(courseDy.getId());
                            List<CourseDirectoryAudit> courseDirectoryAuditList = courseDirectoryAuditService.selectCourseDirectoryAuditList(cddy);
                            List<Long> collect3 = courseDirectoryAuditList.stream().map(CourseDirectoryAudit::getId).collect(Collectors.toList());

                            List<Long> subtract1 = ListUtils.subtract(collect3, collect2);
                            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(subtract1)) {
                                Long[] array2 = subtract1.toArray(new Long[0]);
                                courseDirectoryAuditService.deleteCourseDirectoryAuditByIds(array2);
                            }
                            //有则更新,没有插入
                            for (CourseDirectoryDy cdItem : courseDirectories) {
                                CourseDirectoryAudit courseDirectoryAudit = courseDirectoryAuditService.selectCourseDirectoryAuditById(cdItem.getId());
                                CourseDirectoryAudit courseDirectoryAudit2 = new CourseDirectoryAudit();
                                BeanUtils.copyProperties(cdItem, courseDirectoryAudit2);
                                //插入视频的宽高
                                if (courseDirectoryAudit2.getDirectoryType() == 1) {
                                    TVideo video = videoService.selectTVideoByUrl(courseDirectoryAudit2.getCourseDirectoryUrl());
                                    if (video != null) {
                                        courseDirectoryAudit2.setVideoHeight(video.getVideoHeight());
                                        courseDirectoryAudit2.setVideoWidth(video.getVideoWidth());
                                    }
                                }
                                if (courseDirectoryAudit == null) {
                                    courseDirectoryAuditService.insertCourseDirectoryAudit(courseDirectoryAudit2);
                                } else {
                                    courseDirectoryAuditService.updateCourseDirectoryAudit(courseDirectoryAudit2);
                                    if (courseDirectoryAudit2.getChapterId() == null) {
                                        courseDirectoryAuditService.updateCourseDirectoryChapterIdNull(courseDirectoryAudit2.getId());
                                    }
                                }
                            }
                        }
                    }
                    //更新缓存
                    String updateCacheResult = HttpUtils.sendGet("https://goodminiapp.wendao101.com/course_detail/update_detail_cache", "courseId=" + courseDy.getId() + "&key=fkasdjfkjfkjdsfeiiuiu39984fddf");
                    if (StringUtils.isNotBlank(updateCacheResult) && updateCacheResult.contains("200")) {
                        log.info("更新缓存成功");
                        System.out.println("更新缓存成功");
                    } else {
                        log.info("更新缓存失败,课程id:" + courseDy.getId());
                        System.out.println("更新缓存失败,课程id:" + courseDy.getId());
                    }
                } else if (versionInfo.getAuditStatus() == 3) {
                    StringBuilder failReason = new StringBuilder(versionInfo.getAuditStatusDesc());
                    if (StringUtils.isBlank(failReason.toString())) {
                        failReason = new StringBuilder("快手审核驳回!");
                    }
                    MpCourseDetailResultData dataRefuse = mpCourseDetailResult.getData();
                    MpCourseAlbumInfoDetail mpCourseAlbumInfo = dataRefuse.getMpCourseAlbumInfo();
                    String failReason1 = mpCourseAlbumInfo.getFailReason();
                    if (StringUtils.isNotBlank(failReason1)) {
                        failReason.append("原因:").append(failReason1);
                    }
                    List<EpisodeInfoDetail> episodeInfoList = dataRefuse.getEpisodeInfoList();
                    if (CollectionUtils.isNotEmpty(episodeInfoList)) {
                        for (EpisodeInfoDetail episodeInfoDetail : episodeInfoList) {
                            if (episodeInfoDetail.getAuditStatus() == 3) {
                                String failReason2 = episodeInfoDetail.getFailReason();
                                if (StringUtils.isNotBlank(failReason2)) {
                                    failReason.append(";具体视频驳回原因:").append(episodeInfoDetail.getTitle()).append(":").append(failReason2);
                                }
                            }
                        }
                    }
                    //保存审核失败消息
                    douyinAuditService.saveDyAuditFailResult(courseDy.getId(), failReason.toString());
                    //同时更新审核状态为失败
                    //审核驳回
                    CourseDy courseDyUpdate = new CourseDy();
                    courseDyUpdate.setId(courseDy.getId());
                    courseDyUpdate.setKsAuditStatus(3);
                    courseDyService.updateCourseDy(courseDyUpdate);

                    Course courseResult = new Course();
                    courseResult.setId(courseDy.getId());
                    courseResult.setKsAuditStatus(3);
                    courseResult.setKsCourseId(courseDy.getKsCourseId());
                    courseResult.setKsCourseVersion(courseDy.getKsCourseVersion());
                    courseService.updateCourse(courseResult);

                    //新增通知
                    String noticeTitle = "「 审核通知 」 您的课程 《" + courseDy.getTitle() + "》快手已被驳回";
                    String noticeContent = "您的课程 《" + courseDy.getTitle() + "》快手已被驳回";
                    douyinAuditService.insetCourseAuditNotify(noticeTitle, noticeContent, courseResult.getTeacherId());
                }
            }
        }
    }

    private boolean checkUploadResource(String appId, List<CourseDirectory> courseDirectoryList, Integer appNameType) {
        boolean success = true;
        for (CourseDirectory cd : courseDirectoryList) {
            //目录类型1视频，2音频，3图片
            if (cd.getDirectoryType() != null) {
                if (cd.getDirectoryType() == 1) {
                    success = (success && checkVideoToKs(appId, cd, appNameType));
                } else if (cd.getDirectoryType() == 2) {
                    success = (success && checkAudioToKs(appId, cd, appNameType));
                } else if (cd.getDirectoryType() == 3) {
                    //上传图片
                    success = (success && checkKsImage(appId, cd.getCourseDirectoryUrl(), appNameType));
                }
            }
        }
        return success;
    }

    private boolean checkAudioToKs(String appId, CourseDirectory cd, Integer appNameType) {
        //检测是否上传成功,并存入数据
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        //2、音频履约内容;
        ksResourceUpload.setResourceType(2);
        ksResourceUpload.setResourceUrl(cd.getCourseDirectoryUrl());
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        //设置状态为快手回调的转码成功状态
        //ksResourceUpload.setKsStatus(2);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            List<KsResourceUpload> successList = list.stream().filter(item -> item.getKsStatus() == 2).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(successList)) {
                //上传成功
                return true;
            } else {
                KsResourceUpload ksResourceUpload1 = list.get(0);
                String accessToken = ksAccessTokenService.getKsAccessTokenByAppNameType(appNameType, appId);
                String checkUrl = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/resource/info?app_id=%s&access_token=%s";
                checkUrl = String.format(checkUrl, appId, accessToken);
                if (StringUtils.isNotBlank(ksResourceUpload1.getAudioId())) {
                    MpCourseResourceInfo mpCourseResourceInfo = new MpCourseResourceInfo();
                    mpCourseResourceInfo.setResourceId(ksResourceUpload1.getAudioId());
                    MpCourseResourceInfoResult mpCourseResourceInfoResult = getUploadDyCourseResult(mpCourseResourceInfo, checkUrl, MpCourseResourceInfoResult.class);
                    if (mpCourseResourceInfoResult != null && mpCourseResourceInfoResult.getResult() == 1) {
                        if (mpCourseResourceInfoResult.getData() != null) {
                            if (mpCourseResourceInfoResult.getData().getStatus() == 2) {
                                ksResourceUpload1.setKsStatus(2);
                                ksResourceUpload1.setResult(1);
                                ksResourceUploadService.updateKsResourceUpload(ksResourceUpload1);
                                return true;
                            }
                            if (mpCourseResourceInfoResult.getData().getStatus() == 3) {
                                ksResourceUploadService.deleteKsResourceUploadByResourceId(ksResourceUpload1.getResourceId());
                                return false;
                            }
                            if (mpCourseResourceInfoResult.getData().getStatus() == 4) {
                                ksResourceUploadService.deleteKsResourceUploadByResourceId(ksResourceUpload1.getResourceId());
                                return false;
                            }
                        }
                    }
                }else{
                    ksResourceUploadService.deleteKsResourceUploadByResourceId(ksResourceUpload1.getResourceId());
                }
                //上传失败
                return false;
            }
        }
        System.out.println("未找到成功记录或已经上传过了,但是失败了!音频地址:" + ksResourceUpload.getResourceUrl()
                + ",appId:" + ksResourceUpload.getKsAppId()
                + ",appNameType:" + appNameType);
        return false;
    }

    private boolean checkVideoToKs(String appId, CourseDirectory cd, Integer appNameType) {
        //检测是否上传成功
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        //1、视频履约内容;
        ksResourceUpload.setResourceType(1);
        ksResourceUpload.setResourceUrl(cd.getCourseDirectoryUrl());
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        //快手回调处理成功状态
        ksResourceUpload.setKsStatus(2);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        System.out.println("未找到成功记录或已经上传过了,但是失败了!视频地址:" + ksResourceUpload.getResourceUrl()
                + ",appId:" + ksResourceUpload.getKsAppId()
                + ",appNameType:" + appNameType);
        return false;
    }

    private boolean checkKsImage(String appId, String imageUrl, Integer appNameType) {
        // 获取文件名
        String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
        String encodedFileName = fileName; // 默认编码后的文件名为原文件名
        // 判断文件名是否包含空格
        try {
            if (fileName.contains(" ")) {
                // URL编码
                encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            System.out.println(e.getMessage());
        }
        // 重新拼回去
        imageUrl = imageUrl.substring(0, imageUrl.lastIndexOf('/') + 1) + encodedFileName;
        //检测是否上传成功,并存入数据
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        ksResourceUpload.setResourceType(0);
        ksResourceUpload.setResourceUrl(imageUrl);
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        ksResourceUpload.setKsStatus(2);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            return true;
        }
        System.out.println("未找到成功记录或已经上传过了,但是失败了!图片地址:" + ksResourceUpload.getResourceUrl()
                + ",appId:" + ksResourceUpload.getKsAppId()
                + ",appNameType:" + appNameType);
        return false;
    }


    private KsResourceUpload queryKsAudio(String appId, String url, Integer appNameType) {
        //检测是否上传成功
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        ksResourceUpload.setResourceType(2);
        ksResourceUpload.setResourceUrl(url);
        //状态必须是快手回调后的转码成功状态
        ksResourceUpload.setKsStatus(2);
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }

    private KsResourceUpload queryKsVideo(String appId, String url, Integer appNameType) {
        //检测是否上传成功
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        ksResourceUpload.setResourceType(1);
        ksResourceUpload.setResourceUrl(url);
        //状态必须是快手回调后的转码成功状态
        ksResourceUpload.setKsStatus(2);
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    private KsResourceUpload queryKsImage(String appId, String imageUrl, Integer appNameType) {
        // 获取文件名
        String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
        String encodedFileName = fileName; // 默认编码后的文件名为原文件名
        // 判断文件名是否包含空格
        try {
            if (fileName.contains(" ")) {
                // URL编码
                encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            System.out.println(e.getMessage());
        }
        // 重新拼回去
        imageUrl = imageUrl.substring(0, imageUrl.lastIndexOf('/') + 1) + encodedFileName;
        //检测是否上传成功
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        ksResourceUpload.setResourceType(0);
        ksResourceUpload.setResourceUrl(imageUrl);
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        //此查询必须是快手处理成功的状态才可以
        ksResourceUpload.setKsStatus(2);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            return list.get(0);
        }
        return null;
    }


    private void processUploadResource(String appId, List<CourseDirectory> courseDirectoryList, String accessToken, Integer appNameType) {
        for (CourseDirectory cd : courseDirectoryList) {
            //目录类型1视频，2音频，3图片
            if (cd.getDirectoryType() != null) {
                if (cd.getDirectoryType() == 1) {
                    uploadVideoToKs(appId, cd, accessToken, appNameType);
                } else if (cd.getDirectoryType() == 2) {
                    uploadAudioToKs(appId, cd, accessToken, appNameType);
                } else if (cd.getDirectoryType() == 3) {
                    //上传图片
                    uploadKsImage(appId, cd.getCourseDirectoryUrl(), accessToken, appNameType);
                }
            }
        }
    }

    /**
     * 上传音频
     *
     * @param appId
     * @param cd
     * @param accessToken
     * @param appNameType
     */
    private void uploadAudioToKs(String appId, CourseDirectory cd, String accessToken, Integer appNameType) {
        //检测是否上传过
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        //2、音频履约内容;
        ksResourceUpload.setResourceType(2);
        ksResourceUpload.setResourceUrl(cd.getCourseDirectoryUrl());
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            System.out.println("已经上传过了!音频地址:" + ksResourceUpload.getResourceUrl()
                    + ",appId:" + ksResourceUpload.getKsAppId()
                    + ",上传文件地址为:" + cd.getCourseDirectoryUrl());
            return;
        }
        if(StringUtils.isBlank(cd.getCourseDirectoryUrl())){
            System.out.println("音频地址为空");
            return;
        }
        //检查出确实没有上传过则上传
        //上传音频
        KuaishouMpCourseAudioDTO kuaishouMpCourseAudioDTO = new KuaishouMpCourseAudioDTO();
        kuaishouMpCourseAudioDTO.setUrl(convertUrl(cd.getCourseDirectoryUrl()));
        //设置回调地址
        kuaishouMpCourseAudioDTO.setCallbackUrl(ks_callback_url);
        MpCourseAudioUploadResult mpCourseAudioUploadResult = kuaishouMpCourseService.mpCourseAudioUpload(appId, accessToken, kuaishouMpCourseAudioDTO);
        //插入数据库
        ksResourceUpload.setResult(mpCourseAudioUploadResult.getResult());
        ksResourceUpload.setErrorMsg(mpCourseAudioUploadResult.getError_msg());
        if (mpCourseAudioUploadResult.getResult() == 1) {
            //视频上传请求提交成功
            ksResourceUpload.setAudioId(mpCourseAudioUploadResult.getData().getAudioId());
            //刚上传设置为处理中,等待快手处理后回调
            ksResourceUpload.setKsStatus(1);
        } else {
            ksResourceUpload.setKsStatus(4);
        }
        ksResourceUploadService.insertKsResourceUpload(ksResourceUpload);
    }

    /**
     * 上传视频
     *
     * @param appId
     * @param cd
     * @param accessToken
     * @param appNameType
     */
    private void uploadVideoToKs(String appId, CourseDirectory cd, String accessToken, Integer appNameType) {
        //检测是否上传成功,并存入数据
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        //1、视频履约内容;
        ksResourceUpload.setResourceType(1);
        ksResourceUpload.setResourceUrl(cd.getCourseDirectoryUrl());
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            System.out.println("已经上传过了!视频地址:" + ksResourceUpload.getResourceUrl()
                    + ",appId:" + ksResourceUpload.getKsAppId()
                    + ",上传文件地址:" + cd.getCourseDirectoryUrl());
            return;
        }
        if(StringUtils.isBlank(cd.getCourseDirectoryUrl())){
            System.out.println("视频地址为空");
            return;
        }
        //检查出确实没有上传过则上传
        //上传视频
        KuaishouMpCourseVideoDTO kuaishouMpCourseVideoDTO = new KuaishouMpCourseVideoDTO();
        kuaishouMpCourseVideoDTO.setUrl(convertUrl(cd.getCourseDirectoryUrl()));
        //设置回调地址
        kuaishouMpCourseVideoDTO.setCallbackUrl(ks_callback_url);
        MpCourseVideoUploadResult mpCourseVideoUploadResult = kuaishouMpCourseService.mpCourseVideoUpload(appId, accessToken, kuaishouMpCourseVideoDTO);
        //检测是否上传成功,并存入数据
        ksResourceUpload.setResult(mpCourseVideoUploadResult.getResult());
        ksResourceUpload.setErrorMsg(mpCourseVideoUploadResult.getError_msg());
        if (mpCourseVideoUploadResult.getResult() == 1) {
            //视频上传请求提交成功
            ksResourceUpload.setVideoId(mpCourseVideoUploadResult.getData().getVideoId());
            //刚上传设置为处理中,等待快手处理后回调
            ksResourceUpload.setKsStatus(1);
        } else {
            ksResourceUpload.setKsStatus(4);
        }
        ksResourceUploadService.insertKsResourceUpload(ksResourceUpload);
    }

    private String convertUrl(String courseDirectoryUrl) {
        if (courseDirectoryUrl.contains("?")) {
            courseDirectoryUrl = courseDirectoryUrl.split("\\?")[0];
        }
        long expireTime = Instant.now().getEpochSecond() + FORTY_EIGHT_HOURS_IN_SECONDS;
        return KeyValidateUtils.generateAntiLeechUrl(courseDirectoryUrl,expireTime);
    }

    /**
     * 获取课程资源数据
     *
     * @param id
     * @param teacherId
     * @param courseType
     * @return
     */
    private Course getCourse(Long id, Long teacherId, Integer courseType) {
        Course course = courseService.selectCourseByTeacherIdAndId(id, teacherId);
        if (courseType == 1) {
            List<Chapter> chapters = chapterService.selectChapterListByCourseId(course.getId());
            course.setChapterList(chapters);
            for (Chapter chapter : chapters) {
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryListByChapterId(chapter.getId());
                chapter.setCourseDirectoryList(courseDirectories);
            }
        } else {
            CourseDirectory courseDirectoryQuery = new CourseDirectory();
            courseDirectoryQuery.setCourseId(id);
            courseDirectoryQuery.setTeacherId(teacherId);
            courseDirectoryQuery.setIsDelete(0);
            List<CourseDirectory> cdList = courseDirectoryService.selectCourseDirectoryWhereChapterIdIsNull(courseDirectoryQuery);
            course.setAllCourseDirectoryList(cdList);
        }
        return course;
    }

    /**
     * 上传图片
     *
     * @param appId
     * @param imageUrl
     * @param accessToken
     * @param appNameType
     */

    private void uploadKsImage(String appId, String imageUrl, String accessToken, Integer appNameType) {
        // 获取文件名
        String fileName = imageUrl.substring(imageUrl.lastIndexOf('/') + 1);
        String encodedFileName = fileName; // 默认编码后的文件名为原文件名
        // 判断文件名是否包含空格
        try {
            if (fileName.contains(" ")) {
                // URL编码
                encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            }
        } catch (UnsupportedEncodingException e) {
            System.out.println(e.getMessage());
        }
        // 重新拼回去
        imageUrl = imageUrl.substring(0, imageUrl.lastIndexOf('/') + 1) + encodedFileName;
        //检测是否上传成功,并存入数据
        KsResourceUpload ksResourceUpload = new KsResourceUpload();
        ksResourceUpload.setResourceType(0);
        ksResourceUpload.setResourceUrl(imageUrl);
        ksResourceUpload.setAppNameType(appNameType);
        ksResourceUpload.setKsAppId(appId);
        List<KsResourceUpload> list = ksResourceUploadService.selectKsResourceUploadList(ksResourceUpload);
        if (CollectionUtils.isNotEmpty(list)) {
            System.out.println("已经上传过了!图片地址:" + ksResourceUpload.getResourceUrl() + ",appId:" + ksResourceUpload.getKsAppId());
            return;
        }
        //确实没有上传过则上传
        MpCourseImgUpload mpCourseImgUpload = new MpCourseImgUpload();
        mpCourseImgUpload.setImgUrl(imageUrl);
        //MpCourseImgUploadResult mpCourseImgUploadResult = kuaishouMpCourseService.mpCourseImgUpload(appId, accessToken, mpCourseImgUpload);
        String url = "https://open.kuaishou.com/openapi/mp/developer/mpcourse/img/upload?app_id=%s&access_token=%s";
        url = String.format(url, appId, accessToken);
        MpCourseImgUploadResult mpCourseImgUploadResult = getUploadDyCourseResult(mpCourseImgUpload, url, MpCourseImgUploadResult.class);
        if(mpCourseImgUploadResult!=null){
            //更新数据库
            ksResourceUpload.setResult(mpCourseImgUploadResult.getResult());
            ksResourceUpload.setErrorMsg(mpCourseImgUploadResult.getError_msg());
            if (mpCourseImgUploadResult.getResult() == 1) {
                //图片上传成功
                ksResourceUpload.setImgKey(mpCourseImgUploadResult.getData().getImgKey());
                //图片上传成功则直接设置为成功
                ksResourceUpload.setKsStatus(2);
            } else {
                System.out.println("mpCourseImgUploadResult.getResult()不为1,快手上传图片失败,地址为:"+imageUrl);
                ksResourceUpload.setKsStatus(4);
            }
            ksResourceUploadService.insertKsResourceUpload(ksResourceUpload);
        }else{
            System.out.println("快手上传图片失败,地址为:"+imageUrl);
        }
    }

    private <T, K> T getUploadDyCourseResult(K dto, String url, Class<T> clazz) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

    private void handleAuditInProgressError(CourseDy courseDy, String appId, String accessToken) {
        if(StringUtils.isBlank(courseDy.getKsCourseId())){
            return;
        }
        try {
            System.out.println("检测到已有审核中的记录，开始查询课程版本信息，课程ID:" + courseDy.getKsCourseId());
            
            MpCourseVersion mpCourseVersion = new MpCourseVersion();
            mpCourseVersion.setCourseId(courseDy.getKsCourseId());
            
            MpCourseVersionResult versionResult = kuaishouMpCourseService.mpCourseVersion(appId, accessToken, mpCourseVersion);
            
            if (versionResult != null && versionResult.getResult() == 1 && versionResult.getData() != null) {
                // 获取最大版本号的对象
                MpCourseVersionResultData maxVersionData = versionResult.getData().stream()
                    .max(Comparator.comparing(MpCourseVersionResultData::getVersion))
                    .orElse(null);
                
                if (maxVersionData != null) {
                    Integer maxVersion = maxVersionData.getVersion();
                    Integer auditStatus = maxVersionData.getAuditStatus();
                    
                    System.out.println("查询成功，最大版本号:" + maxVersion + 
                                     ", 审核状态:" + auditStatus + 
                                     ", 课程ID:" + courseDy.getKsCourseId());
                    
                    // 根据审核状态设置不同的状态值
                    int newAuditStatus;
                    String statusDesc = "";
                    
                    if (auditStatus == 4) {
                        // 审核中状态
                        newAuditStatus = 1;
                        statusDesc = "审核中";
                    } else if (auditStatus == 3) {
                        // 审核拒绝状态
                        newAuditStatus = 3;
                        statusDesc = "审核拒绝";
                    } else {
                        // 其他状态，默认设置为审核中
                        newAuditStatus = 1;
                        statusDesc = "其他状态(" + auditStatus + ")";
                    }
                    
                    // 更新CourseDy状态
                    CourseDy courseDyUpdate = new CourseDy();
                    courseDyUpdate.setId(courseDy.getId());
                    courseDyUpdate.setKsAuditStatus(newAuditStatus);
                    courseDyUpdate.setKsCourseVersion(maxVersion);
                    courseDyService.updateCourseDy(courseDyUpdate);
                    
                    // 同时更新Course表
                    Course courseUpdate = new Course();
                    courseUpdate.setId(courseDy.getId());
                    courseUpdate.setKsAuditStatus(newAuditStatus);
                    courseUpdate.setKsCourseVersion(maxVersion);
                    courseService.updateCourse(courseUpdate);
                    
                    System.out.println("已更新课程状态为" + statusDesc + "，版本号:" + maxVersion);
                } else {
                    System.err.println("未找到有效的版本数据");
                }
            } else {
                System.err.println("查询课程版本信息失败:" + (versionResult != null ? versionResult.getError_msg() : "未知错误"));
            }
        } catch (Exception e) {
            System.err.println("处理审核中记录时发生异常:" + e.getMessage());
            e.printStackTrace();
        }
    }
}
