package com.wendao101.job.task;

import com.alibaba.fastjson2.JSON;
import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenDTO;
import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenResult;
import com.wendao101.common.core.kuaishou.login.AccessTokenResult;
import com.wendao101.common.core.utils.http.HttpUtils;
import com.wendao101.douyin.api.config.DouyinConfig;
import com.wendao101.douyin.api.feign.KuaishouAccessTokenService;
import com.wendao101.job.dto.wxminiapp.AccessTokenFailResultDTO;
import com.wendao101.job.dto.wxminiapp.AccessTokenResultDTO;
import com.wendao101.teacher.domain.DyAccessToken;
import com.wendao101.teacher.domain.KsAccessToken;
import com.wendao101.teacher.domain.SphAccessToken;
import com.wendao101.teacher.domain.WxAccessToken;
import com.wendao101.teacher.service.IDyAccessTokenService;
import com.wendao101.teacher.service.IKsAccessTokenService;
import com.wendao101.teacher.service.ISphAccessTokenService;
import com.wendao101.teacher.service.IWxAccessTokenService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 课堂各种token任务!!迁移下线使用
 */
@Slf4j
@Component("ktTokeTask")
public class KtTokeTask {
    @Autowired
    private KuaishouAccessTokenService kuaishouAccessTokenService;
    @Autowired
    private ISphAccessTokenService sphAccessTokenService;
    @Autowired
    private IDyAccessTokenService dyAccessTokenService;
    @Autowired
    private IWxAccessTokenService wxAccessTokenService;
    @Autowired
    private IKsAccessTokenService ksAccessTokenService;
    @Autowired
    private DouyinConfig dyConfig;

    /**
     * 问到课堂抖音accessToken获取,更新
     * 地址:<a href="https://developer.toutiao.com/api/apps/v2/token">获取地址为:https://developer.toutiao.com/api/apps/v2/token</a>
     * 执行时间为每个小时的45分
     * 0 45 0/1 * * ?
     */
    public void ktDyAccessTokenUpdate() {
        System.out.println("执行课堂刷新accessToken开始");
        //查询问到课堂的
        Integer appNameType = 2;
        DyAccessToken dyAccessToken = dyAccessTokenService.selectDyAccessTokenByAppNameType(appNameType);
        String uri = "/api/apps/v2/token";
        //从数据库获取appid和secret
        GetAccessTokenDTO getAccessTokenDTO = new GetAccessTokenDTO();
        getAccessTokenDTO.setAppid(dyAccessToken.getAppid());
        getAccessTokenDTO.setSecret(dyAccessToken.getSecret());
        getAccessTokenDTO.setGrantType("client_credential");

        GetAccessTokenResult getAccessTokenResult = postRequest(getAccessTokenDTO, uri, GetAccessTokenResult.class);
        //数据存入数据库
        if (getAccessTokenResult != null && getAccessTokenResult.getErrNo() != null && getAccessTokenResult.getErrNo().intValue() == 0) {
            //AccessToken
            String accessToken = getAccessTokenResult.getData().getAccessToken();
            //过期秒数,默认7200秒
            Long expiresIn = getAccessTokenResult.getData().getExpiresIn();
            dyAccessToken.setAccessToken(accessToken);
            dyAccessToken.setExpiresIn(expiresIn);
            dyAccessTokenService.updateDyAccessToken(dyAccessToken);
        } else {
            System.out.println("课堂accessToken获取失败,错误内容为:" + JSON.toJSONString(getAccessTokenResult));
        }
        System.out.println("执行课堂刷新accessToken结束");
    }

    /**
     * 问到微信视频号小店获取token,问到课堂和问到好课通用
     * 执行时间为每个小时的45分
     * 0 45 0/1 * * ?
     */
//    public void wxSphAccessToken() {
//        Integer appNameType = 2;
//        SphAccessToken sphAccessToken = sphAccessTokenService.selectSphAccessTokenByAppNameType(appNameType);
//        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + sphAccessToken.getAppid() + "&secret=" + sphAccessToken.getSecret();
//        String result = HttpUtils.sendGet(requestUrl);
//        if (StringUtils.isNotBlank(result)) {
//            if (result.contains("access_token")) {
//                //成功:保存token
//                AccessTokenResultDTO accessTokenResult = JSON.parseObject(result, AccessTokenResultDTO.class);
//                sphAccessToken.setAccessToken(accessTokenResult.getAccess_token());
//                sphAccessToken.setExpiresIn(accessTokenResult.getExpires_in());
//                sphAccessTokenService.updateSphAccessToken(sphAccessToken);
//            } else {
//                //获取失败的各种情况
//                if (result.contains("errcode")) {
//                    //文档格式{"errcode": 40013,"errmsg": "invalid appid rid: 65b9ed76-64015495-7d5872df"}
//                    AccessTokenFailResultDTO accessTokenFailResult = JSON.parseObject(result, AccessTokenFailResultDTO.class);
//                    System.out.println("视频号accessToken获取失败," +
//                            "错误码为:" + accessTokenFailResult.getErrcode() +
//                            ",错误消息为:" + accessTokenFailResult.getErrmsg());
//                } else {
//                    //非文档格式(bad gateway)
//                    System.out.println("视频号accessToken获取失败,错误内容为:" + result);
//                }
//            }
//
//        } else {
//            //获取失败
//            System.out.println("视频号accessToken获取失败,获取内容为空");
//        }
//    }

    /**
     * 问到课堂微信小程序获取token
     * 执行时间为每个小时的45分
     * 0 45 0/1 * * ?
     */
    public void ktWxMiniAppAccessTokenUpdate() {
        Integer appNameType = 2;
        WxAccessToken wxAccessToken = wxAccessTokenService.selectWxAccessTokenByAppNameType(appNameType);
        String requestUrl = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=" + wxAccessToken.getAppid() + "&secret=" + wxAccessToken.getSecret();
        String result = HttpUtils.sendGet(requestUrl);
        if (StringUtils.isNotBlank(result)) {
            if (result.contains("access_token")) {
                //成功:保存token
                AccessTokenResultDTO accessTokenResult = JSON.parseObject(result, AccessTokenResultDTO.class);
                wxAccessToken.setAccessToken(accessTokenResult.getAccess_token());
                wxAccessToken.setExpiresIn(accessTokenResult.getExpires_in());
                wxAccessTokenService.updateWxAccessToken(wxAccessToken);
            } else {
                //获取失败的各种情况
                if (result.contains("errcode")) {
                    //文档格式{"errcode": 40013,"errmsg": "invalid appid rid: 65b9ed76-64015495-7d5872df"}
                    AccessTokenFailResultDTO accessTokenFailResult = JSON.parseObject(result, AccessTokenFailResultDTO.class);
                    System.out.println("小程序accessToken获取失败," +
                            "错误码为:" + accessTokenFailResult.getErrcode() +
                            ",错误消息为:" + accessTokenFailResult.getErrmsg());
                } else {
                    //非文档格式(bad gateway)
                    System.out.println("小程序accessToken获取失败,错误内容为:" + result);
                }
            }

        } else {
            //获取失败
            System.out.println("小程序accessToken获取失败,获取内容为空");
        }
    }

    /**
     * 问道课堂获取快手Token
     * 执行时间为每天的0点
     * 0 0 0 * * ?
     */
    public void ktKsAccessTokenUpdate() {
        String webdaoKtAppId = "ks685954519696767360";
        String webdaoZkAppId = "ks696650569772164426";
        List<String> appIdList = new ArrayList<>();
        appIdList.add(webdaoKtAppId);
        appIdList.add(webdaoZkAppId);
        Integer appNameType = 2;
        /**
         * 需要刷新两个,一个问到课堂,一个问到智库
         */
        for (String appId : appIdList) {
            KsAccessToken ksAccessToken = ksAccessTokenService.selectKsAccessTokenByAppNameTypeAndAppId(appNameType, appId);
            AccessTokenResult accessTokenResult = kuaishouAccessTokenService.getAccessToken(ksAccessToken.getAppid(), ksAccessToken.getSecret(), "client_credentials");
            if (accessTokenResult.getResult() == 1) {
                Long expiresIn = accessTokenResult.getExpires_in();
                String accessToken = accessTokenResult.getAccess_token();
                ksAccessToken.setAccessToken(accessToken);
                ksAccessToken.setExpiresIn(expiresIn);
                ksAccessTokenService.updateKsAccessToken(ksAccessToken);
            } else {
                if (accessTokenResult.getResult() == 100200100) {
                    System.out.println("参数有误，需要检查参数是否为空或有误");
                }
                if (accessTokenResult.getResult() == 100200101) {
                    System.out.println("验证出错，需要检查app_id和app_secret");
                }
                System.out.println("获取快手token错误:" + JSON.toJSONString(accessTokenResult));
            }
        }
    }

    ////////////////////////////////////////////////////////////////////privet method//////////////////////////////////////////////////////////////////////////////////////////////////
    private <T, K> T postRequest(K dto, String uri, Class<T> clazz) {
        String url = dyConfig.getDomain() + uri;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }
}
