package com.wendao101.job.task;

import com.alibaba.fastjson2.JSON;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.*;
import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenDTO;
import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenResult;
import com.wendao101.common.core.douyin.accesstoken.GetClientTokenDTO;
import com.wendao101.common.core.douyin.accesstoken.GetClientTokenResult;
import com.wendao101.common.core.douyin.accesstoken.child.AccessTokenData;
import com.wendao101.common.core.douyin.accesstoken.child.ClientTokenResult;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.api.config.DouyinConfig;
import com.wendao101.job.service.TcvodDescribeFileAttributesService;
import com.wendao101.teacher.domain.DyAccessToken;
import com.wendao101.teacher.domain.TAudio;
import com.wendao101.teacher.domain.TPic;
import com.wendao101.teacher.domain.TVideo;
import com.wendao101.teacher.service.IDyAccessTokenService;
import com.wendao101.teacher.service.ITAudioService;
import com.wendao101.teacher.service.ITPicService;
import com.wendao101.teacher.service.ITVideoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Slf4j
@Component("ryTask")
public class RyTask {
    @Value("${wendao.video.vodsubAppid}")
    private Long vodsubAppid;
    @Value("${wendao.video.procedure}")
    private String procedure;
    @Value("${wendao.video.secretId}")
    private String secretId;
    @Value("${wendao.video.secretKey}")
    private String secretKey;
    @Autowired
    private DouyinConfig dyConfig;

    @Autowired
    private ITVideoService videoService;

    @Autowired
    private ITAudioService audioService;

    @Autowired
    private ITPicService picService;

    @Autowired
    private RedisService redisService;

    @Autowired
    private TcvodDescribeFileAttributesService tcvodDescribeFileAttributesService;

    @Autowired
    private IDyAccessTokenService dyAccessTokenServiceForDB;

    private static final String CLIENT_URL = "/oauth/client_token/";


    public void refreshAccessToken() {
        {
            System.out.println("执行刷新accessToken开始");
            String uri = "/api/apps/v2/token";
            GetAccessTokenDTO getAccessTokenDTO = new GetAccessTokenDTO();
            getAccessTokenDTO.setAppid(dyConfig.getAppid());
            getAccessTokenDTO.setSecret(dyConfig.getSecret());
            getAccessTokenDTO.setGrantType("client_credential");
            GetAccessTokenResult accessTokenResult = postRequest(getAccessTokenDTO, uri, GetAccessTokenResult.class, dyConfig.getDomain());
            if (accessTokenResult != null && accessTokenResult.getErrNo() != null && accessTokenResult.getErrNo().intValue() == 0) {
                AccessTokenData data = accessTokenResult.getData();
                Long expireIn = data.getExpiresIn() - 300L;
                redisService.setCacheObject(dyConfig.getAccessTokenKey(), accessTokenResult.getData(), expireIn, TimeUnit.SECONDS);
                AccessTokenData checkData = redisService.getCacheObject(dyConfig.getAccessTokenKey());
                //查询问到好课的
                Integer appNameType = 1;
                DyAccessToken dyAccessToken = dyAccessTokenServiceForDB.selectDyAccessTokenByAppNameType(appNameType);
                String accessToken = accessTokenResult.getData().getAccessToken();
                Long expiresIn = accessTokenResult.getData().getExpiresIn();
                dyAccessToken.setAccessToken(accessToken);
                dyAccessToken.setExpiresIn(expiresIn);
                dyAccessTokenServiceForDB.updateDyAccessToken(dyAccessToken);
                System.out.println("AccessToken:" + JSON.toJSONString(checkData));
            }
            System.out.println("执行刷新accessToken结束");
        }
        {
            System.out.println("执行刷新好课应用授权调用凭证开始");
            int appNameType = 1;
            //好课
            String redisKey = dyConfig.getClientAccessTokenKey() + ":" + appNameType;
            GetClientTokenDTO getClientTokenDTO = new GetClientTokenDTO();
            getClientTokenDTO.setClientKey(dyConfig.getAppid());
            getClientTokenDTO.setClientSecret(dyConfig.getSecret());
            getClientTokenDTO.setGrantType("client_credential");
            GetClientTokenResult clientTokenResult = postRequest(getClientTokenDTO, CLIENT_URL, GetClientTokenResult.class, dyConfig.getOpenDouyinDomain());
            if (clientTokenResult != null) {
                ClientTokenResult tokenResult = clientTokenResult.getData();
                if (tokenResult.getErrorCode() != null && tokenResult.getErrorCode().intValue() == 0) {
                    Long expireIn = tokenResult.getExpiresIn() - 300L;
                    redisService.setCacheObject(redisKey, tokenResult.getAccessToken(), expireIn, TimeUnit.SECONDS);
                    redisService.setCacheObject(dyConfig.getClientAccessTokenKey(), tokenResult, expireIn, TimeUnit.SECONDS);
                }
            }
            System.out.println("执行刷新好课应用授权调用凭证结束");
        }
        {
            System.out.println("执行刷新课堂应用授权调用凭证开始");
            //课堂
            Integer appNameType = 2;
            DyAccessToken dyAccessToken = dyAccessTokenServiceForDB.selectDyAccessTokenByAppNameType(appNameType);
            String redisKey = dyConfig.getClientAccessTokenKey() + ":" + appNameType;
            GetClientTokenDTO getClientTokenDTO = new GetClientTokenDTO();
            getClientTokenDTO.setClientKey(dyAccessToken.getAppid());
            getClientTokenDTO.setClientSecret(dyAccessToken.getSecret());
            getClientTokenDTO.setGrantType("client_credential");
            GetClientTokenResult clientTokenResult = postRequest(getClientTokenDTO, CLIENT_URL, GetClientTokenResult.class, dyConfig.getOpenDouyinDomain());
            if (clientTokenResult != null) {
                ClientTokenResult tokenResult = clientTokenResult.getData();
                if (tokenResult.getErrorCode() != null && tokenResult.getErrorCode().intValue() == 0) {
                    Long expireIn = tokenResult.getExpiresIn() - 300L;
                    redisService.setCacheObject(redisKey, tokenResult.getAccessToken(), expireIn, TimeUnit.SECONDS);
                }
            }
            System.out.println("执行刷新课堂应用授权调用凭证结束");
        }
    }

    /**
     * 回调视频接口，获取截图和转码后的数据
     */
    public void ryNoParams() {
        System.out.println("执行无参方法开始" + secretId);
        //TVideo tVideo = videoService.selectTVideoById(1L);
        //System.out.println("执行无参方法结束"+ JSON.toJSONString(tVideo));
        //拉取事件
        List<String> eventHandleList = new ArrayList<>();
        try {
            // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
            // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
            // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
            Credential cred = new Credential(secretId, secretKey);
            // 实例化一个http选项，可选的，没有特殊需求可以跳过
            HttpProfile httpProfile = new HttpProfile();
            httpProfile.setEndpoint("vod.tencentcloudapi.com");
            // 实例化一个client选项，可选的，没有特殊需求可以跳过
            ClientProfile clientProfile = new ClientProfile();
            clientProfile.setHttpProfile(httpProfile);
            // 实例化要请求产品的client对象,clientProfile是可选的
            VodClient client = new VodClient(cred, "", clientProfile);
            // 实例化一个请求对象,每个接口都会对应一个request对象
            PullEventsRequest req = new PullEventsRequest();
            req.setSubAppId(vodsubAppid);
            // 返回的resp是一个PullEventsResponse的实例，与请求对象对应
            PullEventsResponse resp = client.PullEvents(req);
            //处理任务打出来
            System.out.println(PullEventsResponse.toJsonString(resp));
            // 获取事件列表
            EventContent[] eventSet = resp.getEventSet();
            if (eventSet != null && eventSet.length > 0) {
                System.out.println("有" + String.valueOf(eventSet.length) + "条任务要处理！");
                for (EventContent eventContent : eventSet) {
                    //对事件进行处理
                    boolean isOk = process(eventContent);
                    if (isOk) {
                        eventHandleList.add(eventContent.getEventHandle());
                    }
                }
            }
            // 输出json格式的字符串回包
            // System.out.println(PullEventsResponse.toJsonString(resp));
        } catch (TencentCloudSDKException e) {
            System.out.println(e.toString());
        }

        // 确认事件
        if (CollectionUtils.isNotEmpty(eventHandleList)) {
            try {
                // 实例化一个认证对象，入参需要传入腾讯云账户 SecretId 和 SecretKey，此处还需注意密钥对的保密
                // 代码泄露可能会导致 SecretId 和 SecretKey 泄露，并威胁账号下所有资源的安全性。以下代码示例仅供参考，建议采用更安全的方式来使用密钥，请参见：https://cloud.tencent.com/document/product/1278/85305
                // 密钥可前往官网控制台 https://console.cloud.tencent.com/cam/capi 进行获取
                Credential cred = new Credential(secretId, secretKey);
                // 实例化一个http选项，可选的，没有特殊需求可以跳过
                HttpProfile httpProfile = new HttpProfile();
                httpProfile.setEndpoint("vod.tencentcloudapi.com");
                // 实例化一个client选项，可选的，没有特殊需求可以跳过
                ClientProfile clientProfile = new ClientProfile();
                clientProfile.setHttpProfile(httpProfile);
                // 实例化要请求产品的client对象,clientProfile是可选的
                VodClient client = new VodClient(cred, "", clientProfile);
                // 实例化一个请求对象,每个接口都会对应一个request对象
                ConfirmEventsRequest req = new ConfirmEventsRequest();
                //String[] eventHandles1 = eventHandleList.toArray();//{"tdmq830895251351591957", "tdmq830895251351594360"};
                String[] eventHandles1 = eventHandleList.toArray(new String[0]);
                req.setEventHandles(eventHandles1);

                req.setSubAppId(vodsubAppid);
                // 返回的resp是一个ConfirmEventsResponse的实例，与请求对象对应
                ConfirmEventsResponse resp = client.ConfirmEvents(req);
                // 输出json格式的字符串回包
                System.out.println(ConfirmEventsResponse.toJsonString(resp));
            } catch (TencentCloudSDKException e) {
                System.out.println(e.toString());
            }
        }
    }

    /**
     * EventType
     * 支持事件类型：
     * NewFileUpload：视频上传完成；
     * ProcedureStateChanged：任务流状态变更；
     * FileDeleted：视频删除完成；
     * RestoreMediaComplete：视频取回完成；
     * PullComplete：视频转拉完成；
     * EditMediaComplete：视频编辑完成；
     * SplitMediaComplete：视频拆分完成；
     * ComposeMediaComplete：制作媒体文件完成；
     * WechatMiniProgramPublishComplete：微信小程序发布完成。
     * RemoveWatermark：智能去除水印完成。
     * RebuildMediaComplete：音画质重生完成事件。
     * ReviewAudioVideoComplete：音视频审核完成；
     * ExtractTraceWatermarkComplete：提取溯源水印完成；
     * ExtractCopyRightWatermarkComplete：提取版权水印完成；
     * DescribeFileAttributesComplete：获取文件属性完成；
     * QualityInspectComplete：音画质检测完成。
     *
     * @param eventContent
     * @return
     */
    private boolean process(EventContent eventContent) {
        //处理视频上传完成响应EventType=NewFileUpload
        String eventType = eventContent.getEventType();
        if (StringUtils.equals("DescribeFileAttributesComplete", eventType)) {
            DescribeFileAttributesTask task = eventContent.getDescribeFileAttributesCompleteEvent();
            String fileId = task.getFileId();
            if(StringUtils.equals(task.getStatus(),"SUCCESS")){
                DescribeFileAttributesTaskOutput output = task.getOutput();
                String md5 = output.getMd5();
                String sha1 = output.getSha1();
                TVideo video = findVideoByFileId(fileId);
                if(video!=null){
                    video.setMd5(md5);
                    video.setSha1(sha1);
                    videoService.updateTVideo(video);
                }
                TAudio audio = findAudioByFileId(fileId);
                if(audio!=null){
                    audio.setMd5(md5);
                    audio.setSha1(sha1);
                    audioService.updateTAudio(audio);
                }
                TPic pic = findPicByFileId(fileId);
                if(pic!=null){
                    pic.setMd5(md5);
                    pic.setSha1(sha1);
                    picService.updateTPic(pic);
                }
            }
        }
        //音视频文件上传
            if (StringUtils.equals("NewFileUpload", eventType)) {
                //文件上传完成!
                FileUploadTask fileUploadEvent = eventContent.getFileUploadEvent();
                String fileId = fileUploadEvent.getFileId();

                //新文件上传后,执行获取文件摘要任务
                tcvodDescribeFileAttributesService.createDescribeFileAttributesTask(fileId);

                MediaBasicInfo mediaBasicInfo = fileUploadEvent.getMediaBasicInfo();
                String fileName = mediaBasicInfo.getName();
                String coverUrl = mediaBasicInfo.getCoverUrl();
                String mediaUrl = mediaBasicInfo.getMediaUrl();
                //获取文件大小
                MediaMetaData metaData = fileUploadEvent.getMetaData();
                Long fileSize = null;
                Float duration = null;
                Long bitrate = null;
                Long height = null;
                Long width = null;
                if(metaData!=null){
                    fileSize = metaData.getSize();
                    duration = metaData.getDuration();
                    bitrate = metaData.getBitrate();
                    height = metaData.getHeight();
                    width = metaData.getWidth();
                }

                //可能空，视频信息,需要判断空，否则抛空指针
                //MediaVideoStreamItem mediaVideoStreamItem = metaData.getVideoStreamSet()[0];
                //可能空，音频信息,需要判断空，否则抛空指针
                //MediaAudioStreamItem mediaAudioStreamItem = metaData.getAudioStreamSet()[0];
                //视频时长
                //视频码率
                //除以1000就是码率
                TVideo video = findVideoByFileId(fileId);
                TAudio audio = findAudioByFileId(fileId);
                if (audio != null) {
                    Long teacherId = audio.getTeacherId();
                    // TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
                    //upload dy resource抖音资源上传.
                    //修改为提交课程审核时在上传到抖音
                    //dyUploadResService.uploadDyResource(mediaUrl, DyResourceType.audioContent,tTeacher.getAppNameType());
                    if(audio.getDuration()==null){
                        audio.setDuration(duration);
                    }
                    audioService.updateTAudio(audio);
                }

                if (video != null) {
                    if (StringUtils.isBlank(video.getVideoPicUrl()) && StringUtils.isNotBlank(coverUrl)) {
                        video.setVideoPicUrl(coverUrl);
                    }
                    if (StringUtils.isBlank(video.getFileName()) && StringUtils.isNotBlank(fileName)) {
                        video.setFileName(fileName);
                    }
                    if (StringUtils.isBlank(video.getTcvodMediaUrlBeforeTrans()) && StringUtils.isNotBlank(mediaUrl)) {
                        video.setTcvodMediaUrlBeforeTrans(mediaUrl);
                    }
                    if ((video.getFileOriginalSize() == null || video.getFileOriginalSize() == 0) && fileSize != null) {
                        video.setFileOriginalSize(fileSize);
                    }
                    video.setDuration(duration);
                    video.setVideoHeight(height);
                    video.setVideoWidth(width);
                    videoService.updateTVideo(video);
                    System.out.println("更新了一条数据fileId为" + video.getTcvodFileId());
                }
                return true;
            }
        //转码结果解析
        if (StringUtils.equals("ProcedureStateChanged", eventType)) {
            ProcedureTask procedureStateChangeEvent = eventContent.getProcedureStateChangeEvent();
            //"Status": "FINISH",
            String status = procedureStateChangeEvent.getStatus();
            Long errCode = procedureStateChangeEvent.getErrCode();
            String message = procedureStateChangeEvent.getMessage();
            String procedureFileId = procedureStateChangeEvent.getFileId();
            TVideo video = findVideoByFileId(procedureFileId);
            // 如果数据库中此视频不存在则直接返回true
            if (video == null) {
                {
                    //音频转码
                    processAudioTransResult(procedureFileId, errCode, message, status, procedureStateChangeEvent);
                }
                return true;
            }
            boolean change = false;
            if (errCode != 0) {
                System.out.println("任务流失败，原因：" + message);
                video.setProcedureErrorMsg(message);
                change = true;
            }
            //必须是已经完成的，errorCode可以不判断，有可能部分成功，比如说截图成功，转码失败
            if (StringUtils.equals(status, "FINISH")) {
                MediaProcessTaskResult[] mediaProcessResultSet = procedureStateChangeEvent.getMediaProcessResultSet();
                if (mediaProcessResultSet == null || mediaProcessResultSet.length == 0) {
                    return true;
                }
                //type:Transcode 为转码
                //type:CoverBySnapshot 为截取封面
                for (MediaProcessTaskResult mediaProcessTaskResult : mediaProcessResultSet) {
                    String type = mediaProcessTaskResult.getType();
                    // 转码
                    if (StringUtils.equals("Transcode", type)) {
                        MediaProcessTaskTranscodeResult transcodeTask = mediaProcessTaskResult.getTranscodeTask();
                        Long transcodeTaskErrCode = transcodeTask.getErrCode();
                        String transcodeTaskMessage = transcodeTask.getMessage();
                        String transcodeTaskStatus = transcodeTask.getStatus();
                        if (transcodeTaskErrCode != 0 || StringUtils.equals("FAIL", transcodeTaskStatus)) {
                            if (StringUtils.equals("SUCCESS", transcodeTaskMessage)) {
                                transcodeTaskMessage = "未知";
                            }
                            //TODO:转码失败需要再次提交转码任务
                            System.out.println("转码失败，原因：" + transcodeTaskMessage);
                            video.setTranscodeErrorMsg(transcodeTaskMessage);
                            video.setTranscodeStatus(-1);
                            change = true;
                        } else {
                            //成功，获取转码url
                            MediaTranscodeItem output = transcodeTask.getOutput();
                            String url = output.getUrl();
                            //上传抖音视频资源，异步
                            Long teacherId = video.getTeacherId();
                            System.out.println("video内容:"+JSON.toJSONString(video));
                            //TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
                            //TODO:修改转码完毕后上传到抖音为提交课程时上传
                            //dyUploadResService.uploadDyResource(url,DyResourceType.videoContent,tTeacher.getAppNameType());
                            Long fileSize = output.getSize();
                            Float duration = output.getDuration();
                            Long height = output.getHeight();
                            Long width = output.getWidth();
                            if (StringUtils.isBlank(video.getPathUrl()) && StringUtils.isNotBlank(url)) {
                                video.setPathUrl(url);
                                video.setTcvodMediaUrl720p(url);
                                video.setTranscodeStatus(1);
                                video.setDuration(duration);
                                change = true;
                            }
                            if(height!=null&&width!=null){
                                video.setVideoHeight(height);
                                video.setVideoWidth(width);
                                change = true;
                            }
                            if ((video.getFileAfterTransSize() == null || video.getFileAfterTransSize() == 0) && fileSize != null) {
                                video.setFileAfterTransSize(fileSize);
                                change = true;
                            }
                            //修改课程里的数据,让课程可以播放
//                            {
//                                String tcvodMediaUrlBeforeTrans = video.getTcvodMediaUrlBeforeTrans();
//                                if(StringUtils.isNotBlank(tcvodMediaUrlBeforeTrans)){
//                                    List<CourseDirectoryDy> courseDirectoryDyList = courseDirectoryDyService.selectCourseDirectoryDyByCourseDirectoryUrl(tcvodMediaUrlBeforeTrans);
//                                    if(CollectionUtils.isNotEmpty(courseDirectoryDyList)){
//                                        for(CourseDirectoryDy courseDirectoryDy:courseDirectoryDyList){
//                                            String pathUrl = video.getPathUrl();
//                                            if(StringUtils.isNotBlank(pathUrl)){
//                                                courseDirectoryDy.setCourseDirectoryUrl(pathUrl);
//                                                courseDirectoryDy.setDuration(video.getDuration()==null?0L:video.getDuration().longValue());
//                                                courseDirectoryDy.setDuration(video.getVideoHeight());
//                                                courseDirectoryDy.setDuration(video.getVideoWidth());
//                                                courseDirectoryDyService.updateCourseDirectoryDy(courseDirectoryDy);
//                                            }
//                                        }
//                                    }
//                                    List<CourseDirectory> courseDirectoriesList = courseDirectoryService.selectCourseDirectoryDyByCourseDirectoryUrl(tcvodMediaUrlBeforeTrans);
//                                    if(CollectionUtils.isNotEmpty(courseDirectoriesList)){
//                                        for(CourseDirectory courseDirectory:courseDirectoriesList){
//                                            String pathUrl = video.getPathUrl();
//                                            if(StringUtils.isNotBlank(pathUrl)) {
//                                                courseDirectory.setCourseDirectoryUrl(pathUrl);
//                                                courseDirectory.setDuration(video.getDuration()==null?0L:video.getDuration().longValue());
//                                                courseDirectory.setDuration(video.getVideoHeight());
//                                                courseDirectory.setDuration(video.getVideoWidth());
//                                                courseDirectoryService.updateCourseDirectory(courseDirectory);
//                                            }
//                                        }
//                                    }
//                                    List<CourseDirectoryAudit> courseDirectoryAuditsList = courseDirectoryAuditService.selectCourseDirectoryDyByCourseDirectoryUrl1(tcvodMediaUrlBeforeTrans);
//                                    if(CollectionUtils.isNotEmpty(courseDirectoryAuditsList)){
//                                        for(CourseDirectoryAudit courseDirectoryAudit:courseDirectoryAuditsList){
//                                            String pathUrl = video.getPathUrl();
//                                            if(StringUtils.isNotBlank(pathUrl)) {
//                                                courseDirectoryAudit.setCourseDirectoryUrl(pathUrl);
//                                                courseDirectoryAudit.setDuration(video.getDuration()==null?0L:video.getDuration().longValue());
//                                                courseDirectoryAudit.setDuration(video.getVideoHeight());
//                                                courseDirectoryAudit.setDuration(video.getVideoWidth());
//                                                courseDirectoryAuditService.updateCourseDirectoryAudit(courseDirectoryAudit);
//                                            }
//                                        }
//                                    }
//                                }
//                            }
                        }
                    }
                    // 截取封面
                    if (StringUtils.equals("CoverBySnapshot", type)) {
                        MediaProcessTaskCoverBySnapshotResult coverBySnapshotTask = mediaProcessTaskResult.getCoverBySnapshotTask();
                        Long coverBySnapshotTaskErrCode = coverBySnapshotTask.getErrCode();
                        String coverBySnapshotTaskMessage = coverBySnapshotTask.getMessage();
                        if (coverBySnapshotTaskErrCode != 0) {
                            System.out.println("截取封面失败，原因：" + coverBySnapshotTaskMessage);
                            video.setCoverBySnapshotErrorMsg(coverBySnapshotTaskMessage);
                            change = true;
                        } else {
                            //成功，获取截图url
                            CoverBySnapshotTaskOutput output = coverBySnapshotTask.getOutput();
                            String coverUrl = output.getCoverUrl();
                            if (StringUtils.isBlank(video.getVideoPicUrl()) && StringUtils.isNotBlank(coverUrl)) {
                                video.setVideoPicUrl(coverUrl);
                                change = true;
                            }
                        }
                    }
                }
            }
            if (change) {
                videoService.updateTVideo(video);
            }
            return true;
        }

        return true;
    }

    private TPic findPicByFileId(String fileId) {
        TPic tpic = new TPic();
        tpic.setTcvodFileId(fileId);
        List<TPic> tPics = picService.selectTPicList(tpic);
        if (CollectionUtils.isNotEmpty(tPics)) {
            return tPics.get(0);
        }
        return null;
    }

    private void processAudioTransResult(String procedureFileId, Long errCode, String message, String status, ProcedureTask procedureStateChangeEvent) {
        TAudio audio = findAudioByFileId(procedureFileId);
        if (audio == null) {
            return;
        }
        boolean change = false;
        if (errCode.longValue() != 0) {
            System.out.println("任务流失败，原因：" + message);
            audio.setProcedureErrorMsg(message);
            change = true;
        }
        //必须是已经完成的，errorCode可以不判断，有可能部分成功，比如说截图成功，转码失败
        if (StringUtils.equals(status, "FINISH")) {
            MediaProcessTaskResult[] mediaProcessResultSet = procedureStateChangeEvent.getMediaProcessResultSet();
            if (mediaProcessResultSet == null || mediaProcessResultSet.length == 0) {
                return;
            }
            //type:Transcode 为转码
            //type:CoverBySnapshot 为截取封面
            for (MediaProcessTaskResult mediaProcessTaskResult : mediaProcessResultSet) {
                String type = mediaProcessTaskResult.getType();
                // 转码
                if (StringUtils.equals("Transcode", type)) {
                    MediaProcessTaskTranscodeResult transcodeTask = mediaProcessTaskResult.getTranscodeTask();
                    Long transcodeTaskErrCode = transcodeTask.getErrCode();
                    String transcodeTaskMessage = transcodeTask.getMessage();
                    String transcodeTaskStatus = transcodeTask.getStatus();
                    if (transcodeTaskErrCode.longValue() != 0 || StringUtils.equals("FAIL", transcodeTaskStatus)) {
                        if (StringUtils.equals("SUCCESS", transcodeTaskMessage)) {
                            transcodeTaskMessage = "未知";
                        }
                        //TODO:转码失败需要再次提交转码任务
                        System.out.println("转码失败，原因：" + transcodeTaskMessage);
                        audio.setTranscodeErrorMsg(transcodeTaskMessage);
                        audio.setTranscodeStatus(-1);
                        change = true;
                    } else {
                        //成功，获取转码url
                        MediaTranscodeItem output = transcodeTask.getOutput();
                        String url = output.getUrl();
                        //上传音频资源到抖音
                        Long teacherId = audio.getTeacherId();
                        // TTeacher tTeacher = teacherService.selectTTeacherByTeacherId(teacherId);
                        //dyUploadResService.uploadDyResource(url,DyResourceType.audioContent,tTeacher.getAppNameType());
                        Long fileSize = output.getSize();
                        Float duration = output.getDuration();
                        if (StringUtils.isBlank(audio.getPathUrl()) && StringUtils.isNotBlank(url)) {
                            audio.setPathUrl(url);
                            audio.setTcvodMediaUrlAfterTrans(url);
                            audio.setTranscodeStatus(1);
                            audio.setDuration(duration);
                            change = true;
                        }
                        if ((audio.getFileAfterTransSize() == null || audio.getFileAfterTransSize() == 0) && fileSize != null) {
                            audio.setFileAfterTransSize(fileSize);
                            change = true;
                        }
                    }
                }
            }
        }
        if (change) {
            audioService.updateTAudio(audio);
        }
    }

    private TVideo findVideoByFileId(String fileId) {
        TVideo tVideo = new TVideo();
        tVideo.setTcvodFileId(fileId);
        List<TVideo> tVideos = videoService.selectTVideoList(tVideo);
        if (CollectionUtils.isNotEmpty(tVideos)) {
            return tVideos.get(0);
        }
        return null;
    }

    private TAudio findAudioByFileId(String fileId) {
        TAudio tAudio = new TAudio();
        tAudio.setTcvodFileId(fileId);
        List<TAudio> tAudios = audioService.selectTAudioList(tAudio);
        if (CollectionUtils.isNotEmpty(tAudios)) {
            return tAudios.get(0);
        }
        return null;
    }

    private <T, K> T postRequest(K dto, String uri, Class<T> clazz,String domain) {
        String url = domain + uri;
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();

        String body = JSON.toJSONString(dto);

        HttpPost httpPost = new HttpPost(url);
        try {
            httpPost.addHeader("Content-Type", "application/json");
            StringEntity stringEntity = new StringEntity(body, "utf-8");
            httpPost.setEntity(stringEntity);
            response = httpClient.execute(httpPost);
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String content = EntityUtils.toString(entity, "UTF-8");
                return JSON.parseObject(content, clazz);
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
        return null;
    }

}
