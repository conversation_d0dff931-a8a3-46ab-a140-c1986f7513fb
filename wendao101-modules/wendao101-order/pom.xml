<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>wendao101-modules</artifactId>
        <groupId>com.wendao101</groupId>
        <version>3.6.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>wendao101-modules-order</artifactId>

    <description>问到订单模块</description>

    <dependencies>

        <!-- SpringBoot Web -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Nacos Config -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>

        <!-- SpringCloud Alibaba Sentinel -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-sentinel</artifactId>
        </dependency>

        <!-- Mysql Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
        </dependency>

        <!-- wendao101 Common DataSource -->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-common-datasource</artifactId>
        </dependency>

        <!-- wendao101 Common DataScope -->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-common-datascope</artifactId>
        </dependency>

        <!-- wendao101 Common Log -->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-common-log</artifactId>
        </dependency>

        <!-- wendao101 wendao Log-->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-wendao-log</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-api-douyin</artifactId>
        </dependency>

        <!-- wendao101 Common Security-->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-common-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-wendao-log</artifactId>
            <version>3.6.3</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.39.60.ALL</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-dysmsapi20170525</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>wx-java-pay-spring-boot-starter</artifactId>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.github.binarywang</groupId>-->
<!--            <artifactId>wx-java-channel-spring-boot-starter</artifactId>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-channel</artifactId>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.github.jai-imageio/jai-imageio-core -->
        <dependency>
            <groupId>com.github.jai-imageio</groupId>
            <artifactId>jai-imageio-core</artifactId>
            <version>1.4.0</version>
        </dependency>


        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.tencentcloudapi</groupId>
            <artifactId>tencentcloud-sdk-java-vod</artifactId>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>vod_api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- webSocket 秒杀通知-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>com.aliyun.mns</groupId>
            <artifactId>aliyun-sdk-mns</artifactId>
            <version>1.1.9</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>

        <dependency>
            <groupId>com.xiaohongshu.fls</groupId>
            <artifactId>opensdk</artifactId>
        </dependency>

        <!-- 抖店接口 -->
        <dependency>
            <groupId>com.wendao101</groupId>
            <artifactId>wendao101-doudian-api</artifactId>
            <version>3.6.3</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>


</project>