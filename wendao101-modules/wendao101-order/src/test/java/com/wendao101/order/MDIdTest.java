package com.wendao101.order;

import com.wendao101.douyin.api.feign.WechatDyMDservice;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class MDIdTest {
    @Autowired
    private WechatDyMDservice wechatDyMDservice;


    @Test
    public void MDIdTest(){
        Long discountsId = wechatDyMDservice.getDiscountsId(10000141L);
        System.out.println("discountsId = " + discountsId);
    }

}
