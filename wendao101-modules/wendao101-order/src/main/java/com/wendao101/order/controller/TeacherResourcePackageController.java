package com.wendao101.order.controller;

import java.util.List;
import java.util.Objects;

import com.wendao101.common.security.utils.SecurityUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.order.domain.TeacherResourcePackage;
import com.wendao101.order.service.ITeacherResourcePackageService;
import com.wendao101.order.dto.ResourceSumDTO;
/**
 * 老师购买的资源包Controller
 */
@RestController
@RequestMapping("/teacher_resource_package")
public class TeacherResourcePackageController extends BaseController
{
    @Autowired
    private ITeacherResourcePackageService teacherResourcePackageService;

    /**
     * 查询老师购买的资源包列表
     * @param teacherResourcePackage 查询列表参数对象,可以传入pageNum,pageSize
     */
    @GetMapping("/own_give_course_pkg_list")
    public TableDataInfo ownGiveCoursePkgList(TeacherResourcePackage teacherResourcePackage)
    {
        //查询之前更新状态
        teacherResourcePackageService.updateExpiredPackageStatus();
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        startPage();
        teacherResourcePackage.setTeacherId(teacherId);
        teacherResourcePackage.setFeatureCode("gift_course");
        List<TeacherResourcePackage> list = teacherResourcePackageService.selectTeacherResourcePackageList(teacherResourcePackage);
        return getDataTable(list);
    }

    /**
     * 查询老师购买的资源包总额度和剩余额度
     */
    @GetMapping("/own_give_course_pkg_resource_count")
    public AjaxResult ownGiveCoursePkgResourceCount() {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        int remainingCount = teacherResourcePackageService.sumRemainingCountByTeacherIdAndFeatureCode(teacherId, "gift_course");
        int total = teacherResourcePackageService.sumResourceCountByTeacherIdAndFeatureCode(teacherId, "gift_course");
        ResourceSumDTO resourceSumDTO = new ResourceSumDTO();
        resourceSumDTO.setResourceCount(total);
        resourceSumDTO.setRemainingCount(remainingCount);
        return success(resourceSumDTO);
    }

    /**
     * 获取老师购买的某个资源包详细信息
     * @param pkgId 资源包主键id
     */
    @GetMapping(value = "/{pkgId}")
    public AjaxResult getInfo(@PathVariable("pkgId") Long pkgId) {
        TeacherResourcePackage result = null;
        Long teacherId = SecurityUtils.getUserId();
        TeacherResourcePackage teacherResourcePackage = teacherResourcePackageService.selectTeacherResourcePackageById(pkgId);
        if (teacherResourcePackage != null && Objects.equals(teacherId, teacherResourcePackage.getTeacherId())) {
            result = teacherResourcePackage;
        }
        return success(result);
    }

//    /**
//     * 新增老师购买的资源包
//     */
//    @PostMapping
//    public AjaxResult add(@RequestBody TeacherResourcePackage teacherResourcePackage)
//    {
//        return toAjax(teacherResourcePackageService.insertTeacherResourcePackage(teacherResourcePackage));
//    }
//
//    /**
//     * 修改老师购买的资源包
//     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody TeacherResourcePackage teacherResourcePackage)
//    {
//        return toAjax(teacherResourcePackageService.updateTeacherResourcePackage(teacherResourcePackage));
//    }
//
//    /**
//     * 删除老师购买的资源包
//     */
//    @DeleteMapping("/{pkgIds}")
//    public AjaxResult remove(@PathVariable Long[] pkgIds)
//    {
//        return toAjax(teacherResourcePackageService.deleteTeacherResourcePackageByIds(pkgIds));
//    }
//
//    /**
//     * 根据老师ID查询资源包列表
//     */
//    @GetMapping("/teacher/{teacherId}")
//    public AjaxResult getTeacherPackages(@PathVariable("teacherId") Long teacherId)
//    {
//        return success(teacherResourcePackageService.selectTeacherResourcePackageByTeacherId(teacherId));
//    }
//
//    /**
//     * 根据老师ID和功能代码查询资源包列表
//     */
//    @GetMapping("/teacher/{teacherId}/feature/{featureCode}")
//    public AjaxResult getTeacherPackagesByFeature(@PathVariable("teacherId") Long teacherId, @PathVariable("featureCode") String featureCode)
//    {
//        return success(teacherResourcePackageService.selectTeacherResourcePackageByTeacherIdAndFeatureCode(teacherId, featureCode));
//    }
} 