package com.wendao101.order.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 提现记录对象 withdraw_record
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class WithdrawRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 提现订单号 */
    @Excel(name = "提现订单号")
    private String withdrawOrderNumber;

    /** 提现人名称 */
    @Excel(name = "提现人名称")
    private String withdrawName;

    /** 提现人头像 */
    @Excel(name = "提现人头像")
    private String withdrawNameImg;

    /** 在途金额 */
    @Excel(name = "在途金额")
    private BigDecimal fundsTransitPrice;

    /** 可提现金额 */
    @Excel(name = "可提现金额")
    private BigDecimal mayWithdrawPrice;

    /** 提现金额 */
    @Excel(name = "提现金额")
    private BigDecimal withdrawPrice;

    /**
     * 提现账号
     */
    @Excel(name = "提现账号")
    private String accountPhone;

    /** 到账金额 */
    @Excel(name = "到账金额")
    private BigDecimal accountPrice;

    /** 服务费 */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;

    /** 服务费比例 */
    @Excel(name = "服务费比例")
    private Integer servicePriceRatio;

    /** 收入来源 0抖音 1微信 2快速 3视频号 */
    @Excel(name = "收入来源 0抖音 1微信 2快速 3视频号")
    private Integer incomePlatform;

    /** 提现账户 0支付宝提现 1公司账户提现 */
    @Excel(name = "提现账户 0支付宝提现 1公司账户提现 2余额划转")
    private Integer accountType;

    /** 申请提现时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请提现时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawApplyTime;

    /** 提现到账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "提现到账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date withdrawAccountTime;

    /** 财务处理时间（取通过，驳回时间） */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "财务处理时间（取通过，驳回时间）", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processingTime;

    /** 第三方处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "第三方处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date tripartiteProcessingTime;

    /**
     * 打款流水号
     */
    @Excel(name = "打款流水号")
    private String paymentSequenceNumber;
    /**
     * 支付宝转账订单号
     */
    @Excel(name = "支付宝转账订单号")
    private String zfbOrderId;

    /** 提现状态 0提现成功 1提现中 2提现失败  */
    @Excel(name = "提现状态 0提现成功 1提现中 2提现失败 ")
    private Integer withdrawStatus;

    /** 提现审核状态,0审核成功，1待审核，2审核驳回，3打款失败*/
    @Excel(name = "提现审核状态,0审核成功，1待审核，2审核驳回，3打款失败")
    private Integer withdrawAuditStatus;

    /** 实体类型 1: 个人 2:机构 3:公共资质*/
    @Excel(name = "实体类型 1: 个人 2:机构 3:公共资质")
    private Integer entityType;

    /** 备注信息 */
    @Excel(name = "备注信息")
    private String remarkMessage;

    /** 凭证 */
    @Excel(name = "凭证")
    private String evidence;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setWithdrawName(String withdrawName) 
    {
        this.withdrawName = withdrawName;
    }

    public String getWithdrawName() 
    {
        return withdrawName;
    }
    public void setWithdrawNameImg(String withdrawNameImg) 
    {
        this.withdrawNameImg = withdrawNameImg;
    }

    public String getWithdrawNameImg() 
    {
        return withdrawNameImg;
    }
    public void setFundsTransitPrice(BigDecimal fundsTransitPrice) 
    {
        this.fundsTransitPrice = fundsTransitPrice;
    }

    public BigDecimal getFundsTransitPrice() 
    {
        return fundsTransitPrice;
    }
    public void setMayWithdrawPrice(BigDecimal mayWithdrawPrice) 
    {
        this.mayWithdrawPrice = mayWithdrawPrice;
    }

    public BigDecimal getMayWithdrawPrice() 
    {
        return mayWithdrawPrice;
    }
    public void setWithdrawPrice(BigDecimal withdrawPrice) 
    {
        this.withdrawPrice = withdrawPrice;
    }

    public BigDecimal getWithdrawPrice() 
    {
        return withdrawPrice;
    }
    public void setAccountPrice(BigDecimal accountPrice) 
    {
        this.accountPrice = accountPrice;
    }

    public BigDecimal getAccountPrice() 
    {
        return accountPrice;
    }
    public void setServicePrice(BigDecimal servicePrice) 
    {
        this.servicePrice = servicePrice;
    }

    public BigDecimal getServicePrice() 
    {
        return servicePrice;
    }
    public void setAccountType(Integer accountType) 
    {
        this.accountType = accountType;
    }

    public Integer getAccountType() 
    {
        return accountType;
    }
    public void setWithdrawApplyTime(Date withdrawApplyTime) 
    {
        this.withdrawApplyTime = withdrawApplyTime;
    }

    public Date getWithdrawApplyTime() 
    {
        return withdrawApplyTime;
    }
    public void setWithdrawAccountTime(Date withdrawAccountTime) 
    {
        this.withdrawAccountTime = withdrawAccountTime;
    }

    public Date getWithdrawAccountTime() 
    {
        return withdrawAccountTime;
    }
    public void setWithdrawStatus(Integer withdrawStatus) 
    {
        this.withdrawStatus = withdrawStatus;
    }

    public Integer getWithdrawStatus() 
    {
        return withdrawStatus;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("withdrawName", getWithdrawName())
            .append("withdrawNameImg", getWithdrawNameImg())
            .append("fundsTransitPrice", getFundsTransitPrice())
            .append("mayWithdrawPrice", getMayWithdrawPrice())
            .append("withdrawPrice", getWithdrawPrice())
            .append("accountPrice", getAccountPrice())
            .append("servicePrice", getServicePrice())
            .append("accountType", getAccountType())
            .append("withdrawApplyTime", getWithdrawApplyTime())
            .append("withdrawAccountTime", getWithdrawAccountTime())
            .append("withdrawStatus", getWithdrawStatus())
            .append("isDelete", getIsDelete())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
