package com.wendao101.order.config;

import com.doudian.open.core.GlobalConfig;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OrderAppConfig {
    public static final String doudian_app_key = "7406208857338316322";
    public static final String doudian_app_secret = "7d1a5657-6a2f-431c-b90c-3d50d98dfa67";

//    static {
//        GlobalConfig.initAppKey(doudian_app_key);
//        GlobalConfig.initAppSecret(doudian_app_secret);
//    }

    //章鱼博士课堂
    //public static final String zyShopIdStr = "184833512";

    //成美知识小店
    //public static final String cmShopIdStr = "188161674";

    //public static final String wendaoShopIdStr = "129992130";

    //public static final String wendingShopIdStr = "146046627";

    //章鱼博士课堂
    public static final Long zyShopIdLong = 184833512L;

    //成美知识小店
    //public static final Long cmShopIdLong = 188161674L;

    public static final Long wendaoShopIdLong = 129992130L;

    public static final Long wendingShopIdLong = 146046627L;

    public static final Long wendaoNewShopIdLong = 193036387L;

    //public static final String wendaoNewShopIdStr = "193036387";


    /**
     * 零基础学裁剪
     */
    public static final Long ljcxcjIdLong = 3043376L;

    /**
     * 零基础学裁剪
     */
    public static final Long ygjydpIdLong = 179503183L;

    /**
     * 伍伯伯店铺id
     */
    public static final Long WBB_SHOP_ID = 41991642L;

    /**
     * 问达知识小店
     */
    public static final Long WenDa = 211370169L;

}
