package com.wendao101.order.mapper;

import com.wendao101.order.domain.SphCourseReceiveDeduction;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 视频号老师自营课程订单扣点费用Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-10-24
 */
public interface SphCourseReceiveDeductionMapper
{
    /**
     * 查询抖店老师自营课程订单扣点费用
     * 
     * @param id 抖店老师自营课程订单扣点费用主键
     * @return 抖店老师自营课程订单扣点费用
     */
    public SphCourseReceiveDeduction selectSphCourseReceiveDeductionById(Long id);

    /**
     * 查询视频号老师自营课程订单扣点费用列表
     * 
     * @param sphCourseReceiveDeduction 抖店老师自营课程订单扣点费用
     * @return 抖店老师自营课程订单扣点费用集合
     */
    public List<SphCourseReceiveDeduction> selectSphCourseReceiveDeductionList(SphCourseReceiveDeduction sphCourseReceiveDeduction);

    /**
     * 新增抖店老师自营课程订单扣点费用
     * 
     * @param sphCourseReceiveDeduction 抖店老师自营课程订单扣点费用
     * @return 结果
     */
    public int insertSphCourseReceiveDeduction(SphCourseReceiveDeduction sphCourseReceiveDeduction);

    /**
     * 修改抖店老师自营课程订单扣点费用
     * 
     * @param sphCourseReceiveDeduction 抖店老师自营课程订单扣点费用
     * @return 结果
     */
    public int updateSphCourseReceiveDeduction(SphCourseReceiveDeduction sphCourseReceiveDeduction);

    /**
     * 删除抖店老师自营课程订单扣点费用
     * 
     * @param id 抖店老师自营课程订单扣点费用主键
     * @return 结果
     */
    public int deleteSphCourseReceiveDeductionById(Long id);

    /**
     * 批量删除抖店老师自营课程订单扣点费用
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSphCourseReceiveDeductionByIds(Long[] ids);

    SphCourseReceiveDeduction selectSphCourseReceiveDeductionByOrderId(@Param("orderId") String orderId);

    int querySuccessOrderCount(SphCourseReceiveDeduction sphCourseReceiveDeduction);

    BigDecimal queryTotalMoney(SphCourseReceiveDeduction sphCourseReceiveDeduction);
}
