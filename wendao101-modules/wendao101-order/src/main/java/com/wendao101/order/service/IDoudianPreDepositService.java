package com.wendao101.order.service;

import java.util.List;
import com.wendao101.order.domain.DoudianPreDeposit;
import com.wendao101.order.dto.SaleVersionManageDTO;
import com.wendao101.order.dto.TeacherEnterInfoDTO;

/**
 * 抖店卖课程预充值Service接口
 * 
 * <AUTHOR>
 * @date 2024-10-24
 */
public interface IDoudianPreDepositService 
{
    /**
     * 查询抖店卖课程预充值
     * 
     * @param id 抖店卖课程预充值主键
     * @return 抖店卖课程预充值
     */
    public DoudianPreDeposit selectDoudianPreDepositById(Long id);

    /**
     * 查询抖店卖课程预充值列表
     * 
     * @param doudianPreDeposit 抖店卖课程预充值
     * @return 抖店卖课程预充值集合
     */
    public List<DoudianPreDeposit> selectDoudianPreDepositList(DoudianPreDeposit doudianPreDeposit);

    /**
     * 新增抖店卖课程预充值
     * 
     * @param doudianPreDeposit 抖店卖课程预充值
     * @return 结果
     */
    public int insertDoudianPreDeposit(DoudianPreDeposit doudianPreDeposit);

    /**
     * 修改抖店卖课程预充值
     * 
     * @param doudianPreDeposit 抖店卖课程预充值
     * @return 结果
     */
    public int updateDoudianPreDeposit(DoudianPreDeposit doudianPreDeposit);

    /**
     * 批量删除抖店卖课程预充值
     * 
     * @param ids 需要删除的抖店卖课程预充值主键集合
     * @return 结果
     */
    public int deleteDoudianPreDepositByIds(Long[] ids);

    /**
     * 删除抖店卖课程预充值信息
     * 
     * @param id 抖店卖课程预充值主键
     * @return 结果
     */
    public int deleteDoudianPreDepositById(Long id);

    DoudianPreDeposit selectDoudianPreDepositByDepositOrderId(String depositOrderId);

    SaleVersionManageDTO selectVersionManageByVersionId(Integer versionId);

    TeacherEnterInfoDTO selectTeacherEnterInfoByTeacherId(Long teacherId);

    int updateRyEnterInfo(TeacherEnterInfoDTO teacherEnterInfoDTO);

    int updateTeacherInfo(TeacherEnterInfoDTO teacherEnterInfoDTO);

    List<SaleVersionManageDTO> selectEnableVersionManage();

}
