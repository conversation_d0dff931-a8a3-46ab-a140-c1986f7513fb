package com.wendao101.order.service;



import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.vo.AlipayRefundVO;
import com.wendao101.order.vo.WithdrawWdVO;

import java.util.Map;

public interface AliPayService {
	public String pay(WithdrawWdVO withdrawWdVO);
	String aliPay(AlipayRefundVO alipayRefundVO);
	String h5Pay(CourseOrder courseOrder);
	boolean callbackVerify(Map<String, String> parameterMap);
	String refund(CourseRefund courseRefund);

}
