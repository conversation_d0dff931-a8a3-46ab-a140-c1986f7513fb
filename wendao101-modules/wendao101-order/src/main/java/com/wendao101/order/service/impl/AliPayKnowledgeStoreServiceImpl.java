package com.wendao101.order.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.CertAlipayRequest;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.domain.AlipayTradePagePayModel;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayTradeRefundRequest;
import com.alipay.api.request.AlipayTradeWapPayRequest;
import com.alipay.api.response.AlipayTradePagePayResponse;
import com.alipay.api.response.AlipayTradeRefundResponse;
import com.alipay.api.response.AlipayTradeWapPayResponse;
import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.domain.DoudianPreDeposit;
import com.wendao101.order.service.AliPayKnowledgeStoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import com.alipay.api.request.AlipayTradePagePayRequest;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class AliPayKnowledgeStoreServiceImpl implements AliPayKnowledgeStoreService {

    public static final Logger logger = LoggerFactory.getLogger(AliPayKnowledgeStoreServiceImpl.class);
    private static AlipayClient wendaoAlipayClient = null;
    private static ConcurrentHashMap<String, AlipayClient> ALIPAYCLIENT_MAP = new ConcurrentHashMap<String, AlipayClient>();
    //private static ConcurrentHashMap<String, String> ALIPAYPID_MAP = new ConcurrentHashMap<String, String>();
    private static ConcurrentHashMap<String, String> ALIPAYAPPID_MAP = new ConcurrentHashMap<String, String>();
    private static ConcurrentHashMap<String, String> ALIPAYPRIVATEKEY_MAP = new ConcurrentHashMap<String, String>();

    private static ConcurrentHashMap<String, String> ALIPAYCERTFILE_MAP = new ConcurrentHashMap<String, String>();
    private static ConcurrentHashMap<String, String> ALIPAYROOTCERTFILE_MAP = new ConcurrentHashMap<String, String>();

    static {
        // 问到证书地址
//        String wendaoAppPublicCertPath = "C:\\Users\\<USER>\\Desktop\\alipayCertificate\\zsdp\\appCertPublicKey_2021004143690534.crt";
        String wendaoAppPublicCertPath = "/root/alipaycert/zsdp/appCertPublicKey_2021004143690534.crt";
        //问到知识店铺appId
        ALIPAYAPPID_MAP.put("浙江问到科技有限公司", "2021004143690534");
        // 支付宝证书
//        ALIPAYCERTFILE_MAP.put("浙江问到科技有限公司", "C:\\Users\\<USER>\\Desktop\\alipayCertificate\\alipayCertPublicKey_RSA2.crt");
        ALIPAYCERTFILE_MAP.put("浙江问到科技有限公司", "/root/alipaycert/zsdp/alipayCertPublicKey_RSA2.crt");
        // 支付宝根证书
//        ALIPAYROOTCERTFILE_MAP.put("浙江问到科技有限公司", "C:\\Users\\<USER>\\Desktop\\alipayCertificate\\alipayRootCert.crt");
        ALIPAYROOTCERTFILE_MAP.put("浙江问到科技有限公司", "/root/alipaycert/zsdp/alipayRootCert.crt");
        //问到privateKey
        ALIPAYPRIVATEKEY_MAP.put("浙江问到科技有限公司", "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCu2S6P9mtUxzoqxO/vu5ZjTLTrG0YpoMxHw2JAjze9/w5MKvvFeyu42krQp/+rfzE77/rbg0RP708c/oCohe1Efbh4J2DQPOppQYpt0GGyZ1qjdbZZbLmUyTVolFUGoSXhAU4YZ2f7AMjY3hdXUa9uSIs9dBHGWnNE6Ja+Fu2yA4jOYVXtd2bDSrNpxWklU/2JCo1FDnHQnnFugVEnPI7hS4TsS7xUUff7TkNuPZNHJoZ+aQHnSElhQCR13zM4mbMp/ljI27ufxmjE/q8TZ7Ut1q/Y2hZHeWAuYRYbD0JJp7pBvINKyQgNUE6wBG6ctkOyEoEiMuZ3XzUyY7OufkDlAgMBAAECggEAdluHvGX3U8Uk58y2Cpb1/zPuh/PHc+hoP+/Ps5n/Brr/p6m1EoIFH7WMzV/bnvCTmFojerKJ7H43FI5m8yztZt6fn54wV+XUW/nExf84EvcJkPreu/vwR/pIEAnQ4JL3PC+Q+M4OwgdO7hawwCgIU+QBdVD7s0SJIM09NWH59vRxs9Mr5LR1Hxa2/sgT2sA+pd56nB8RViO2iceDj8C7nOR9GepCfqXoUslNaJcUP9eic0q7hpdVlscFPboIHj0a/oGfMjQLLUTcx/3TBTGueOKgyYDByVSnYrrXGg3TnQPqUjAjaUMcCfRqg+1Dt18d5LyfCwaMJ+26LRfoWZBhgQKBgQD2wh7hcaBb+8wOXQAZMH7YvkxL1y1BUwSWq80siLTmfKrtMTkVgJin/acrf9M/TO9DwFTSQkyrYIOVE9OC7+SAocJ1KWGfiU9+EDoChV/jJ0b0eYV8VnOtJxUgq9ZNuZpx4Kpupb6rMydT8s4zueTl9kwJOXUkvo7BRWOu1JbzIQKBgQC1ZZmwvx91QrKJ/SHPm7YVjRzbCdfDb+a/0hfv+Qcz4yh6bOoDTore/MiUOW4UcCTcyFrtuyj2GgIW8mCwqFSo/KtGRZ3AmuMvHfxEh08+p0bzFiaS1OZTi95b8wdRmkNDt0k/6P9eNrM9XTsr9DAvy3ff20Zrkf2ShbwzWoCZRQKBgC+p0XM0kogR/BluJ122O1o8b9paZvhuQ72i08/dXCwFOrL0RhV8MICKg2bomvJoNvM8TGYMvmiADQqXuMxa8CVUmY4t/xhk2oD8O2hPQPVMme0RKBKbVF7G5NLZvlxnFIkYn3Md+tVw3RDfHqtpJeixiYBio9kEIej9MUMM91dhAoGBAKuOoaJ1v6zJHc4/p2CQJc+QHEBUuMlP5e55JsaAwX0jU78ljtT9zX1DmfgFsMSHo3Vj0AuSvRS0WFGXfjbVnOwNu3bElIP6eO6w70topVveHM3+nxCzZfqSCATyfnOA8Cj1CcP1+r8CANcCe9oAIiqxxnfjDGDkO+v9q5bt+z35AoGBAPBAuaeggYPDWqjNUSZiyyYMZ171iXJgu3X1aheW3UMV+CIWpeOEw55aieyzLlYmQHhq9LGBs21Bn6bWBF7uZCYqJuppsqvxKPcgLPJlgxhFeV8n0zZcwJDmH/emVDLAC2SrrlyDHl70w9gAmbR4FtJJTFv2nH4YcI5UQOg8k0fm");

        try {
            String companyName = "浙江问到科技有限公司";
            wendaoAlipayClient = new DefaultAlipayClient(getClientParams(wendaoAppPublicCertPath,
                    ALIPAYAPPID_MAP.get(companyName), ALIPAYPRIVATEKEY_MAP.get(companyName),
                    ALIPAYCERTFILE_MAP.get(companyName), ALIPAYROOTCERTFILE_MAP.get(companyName)));
            ALIPAYCLIENT_MAP.put(companyName, wendaoAlipayClient);
        } catch (Exception e) {
            logger.error("支付宝初始化错误!本机或测试环境这个错误可忽略!", e);
        }
    }

    @Override
    public String h5Pay(CourseOrder courseOrder,String returnUrl) {
        AlipayClient alipayClient = ALIPAYCLIENT_MAP.get("浙江问到科技有限公司");
        if (alipayClient == null) {
            return "系统错误";
        }
        AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
//异步接收地址，仅支持http/https，公网可访问
        request.setNotifyUrl("https://teacher.wendao101.com/prod-api/order/wxkuaishou_pay_controller/alipayCallback");
//同步跳转地址，仅支持http/https
        request.setReturnUrl(returnUrl);
/******必传参数******/
        JSONObject bizContent = new JSONObject();
//商户订单号，商家自定义，保持唯一性
        bizContent.put("out_trade_no", courseOrder.getOrderId());
//支付金额，最小值0.01元
        bizContent.put("total_amount", courseOrder.getPayPrice());
//订单标题，不可使用特殊符号
        bizContent.put("subject", courseOrder.getCourseTitle());

/******可选参数******/
//手机网站支付默认传值QUICK_WAP_WAY
        bizContent.put("product_code", "QUICK_WAP_WAY");
//bizContent.put("time_expire", "2022-08-01 22:00:00");

//// 商品明细信息，按需传入
//JSONArray goodsDetail = new JSONArray();
//JSONObject goods1 = new JSONObject();
//goods1.put("goods_id", "goodsNo1");
//goods1.put("goods_name", "子商品1");
//goods1.put("quantity", 1);
//goods1.put("price", 0.01);
//goodsDetail.add(goods1);
//bizContent.put("goods_detail", goodsDetail);

//// 扩展信息，按需传入
//JSONObject extendParams = new JSONObject();
//extendParams.put("sys_service_provider_id", "2088511833207846");
//bizContent.put("extend_params", extendParams);

        request.setBizContent(bizContent.toString());
        AlipayTradeWapPayResponse response = null;
        try {
            response = alipayClient.pageExecute(request,"POST");
        } catch (AlipayApiException e) {
            System.out.println(e.getMessage());
            return "系统错误";
        }
        String pageRedirectionData = response.getBody();
        System.out.println(pageRedirectionData);

        if(response.isSuccess()){
            System.out.println("调用成功");
            return pageRedirectionData;
        } else {
            System.out.println("调用失败");
            return "系统错误";
        }
    }

    @Override
    public boolean callbackVerify(Map<String, String> parameterMap) {
        // 验签
        boolean signVerified;
        try {
            signVerified = AlipaySignature.rsaCertCheckV1(
                    parameterMap,
                    ALIPAYCERTFILE_MAP.get("浙江问到科技有限公司"),
                    "utf-8","RSA2");
        } catch (AlipayApiException e) {
            System.out.println(e.getMessage());
            return false;

        }
        return signVerified;
    }

    @Override
    public String refund(CourseRefund courseRefund) {
        AlipayClient alipayClient = ALIPAYCLIENT_MAP.get("浙江问到科技有限公司");
        if (alipayClient == null) {
            return "系统错误";
        }
        AlipayTradeRefundRequest request = new AlipayTradeRefundRequest();
        JSONObject bizContent = new JSONObject();
        bizContent.put("out_trade_no", courseRefund.getOrderId());
        bizContent.put("refund_amount", courseRefund.getRefundPrice());
        bizContent.put("out_request_no", courseRefund.getRefundId());
        bizContent.put("refund_reason", courseRefund.getRefundReason());

        request.setBizContent(bizContent.toString());
        AlipayTradeRefundResponse response = null;
        try {
            response = alipayClient.certificateExecute(request);
        } catch (AlipayApiException e) {
            //退款请求出错
            System.out.println("退款请求异常,错误:"+e.getMessage());
        }
        if (response != null && response.isSuccess()) {
            System.out.println("调用成功");
            return "success";
        } else {
            System.out.println("调用失败");
            return "系统错误";
        }
    }

    @Override
    public String pcPay(CourseOrder courseOrder, String returnUrl) {
        AlipayClient alipayClient = ALIPAYCLIENT_MAP.get("浙江问到科技有限公司");
        if (alipayClient == null) {
            return "系统错误";
        }
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();
        request.setNotifyUrl("https://teacher.wendao101.com/prod-api/order/wxkuaishou_pay_controller/alipayCallback");
        request.setReturnUrl(returnUrl);
        //model.setno
        // 设置订单标题
        model.setSubject(courseOrder.getCourseTitle());

        // 设置请求来源地址
        //model.setRequestFromUrl(returnUrl);

        // 设置产品码
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("1");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置请求后页面的集成方式
        model.setIntegrationType("PCWEB");

        // 设置商户订单号
        model.setOutTradeNo(courseOrder.getOrderId());

        // 设置订单总金额
        model.setTotalAmount(courseOrder.getPayPrice().toString());

        request.setBizModel(model);



        //AlipayTradeWapPayRequest request = new AlipayTradeWapPayRequest();
//异步接收地址，仅支持http/https，公网可访问
        //request.setNotifyUrl("https://teacher.wendao101.com/prod-api/order/wxkuaishou_pay_controller/alipayCallback");
//同步跳转地址，仅支持http/https
        //request.setReturnUrl(returnUrl);
/******必传参数******/
        //JSONObject bizContent = new JSONObject();
//商户订单号，商家自定义，保持唯一性
        //bizContent.put("out_trade_no", courseOrder.getOrderId());
//支付金额，最小值0.01元
        //bizContent.put("total_amount", courseOrder.getPayPrice());
//订单标题，不可使用特殊符号
        //bizContent.put("subject", courseOrder.getCourseTitle());

/******可选参数******/
//手机网站支付默认传值QUICK_WAP_WAY
        //bizContent.put("product_code", "FAST_INSTANT_TRADE_PAY");
//bizContent.put("time_expire", "2022-08-01 22:00:00");

//// 商品明细信息，按需传入
//JSONArray goodsDetail = new JSONArray();
//JSONObject goods1 = new JSONObject();
//goods1.put("goods_id", "goodsNo1");
//goods1.put("goods_name", "子商品1");
//goods1.put("quantity", 1);
//goods1.put("price", 0.01);
//goodsDetail.add(goods1);
//bizContent.put("goods_detail", goodsDetail);

//// 扩展信息，按需传入
//JSONObject extendParams = new JSONObject();
//extendParams.put("sys_service_provider_id", "2088511833207846");
//bizContent.put("extend_params", extendParams);

        //request.setBizContent(bizContent.toString());
        AlipayTradePagePayResponse response = null;
        try {
            response = alipayClient.pageExecute(request, "POST");
        } catch (AlipayApiException e) {
            System.out.println(e.getMessage());
            return "系统错误";
        }
        String pageRedirectionData = response.getBody();
        System.out.println(pageRedirectionData);

        if(response.isSuccess()){
            System.out.println("调用成功");
            return pageRedirectionData;
        } else {
            System.out.println("调用失败");
            return "系统错误";
        }
    }

    @Override
    public String pcPayForTeacher(DoudianPreDeposit doudianPreDeposit, String returnUrl) {
        AlipayClient alipayClient = ALIPAYCLIENT_MAP.get("浙江问到科技有限公司");
        if (alipayClient == null) {
            return "系统错误";
        }
        AlipayTradePagePayRequest request = new AlipayTradePagePayRequest();
        AlipayTradePagePayModel model = new AlipayTradePagePayModel();
        request.setNotifyUrl("https://teacher.wendao101.com/prod-api/order/teacher_backend_pay_controller/backendAlipayCallback");
        request.setReturnUrl(returnUrl);
        //model.setno
        // 设置订单标题
        model.setSubject(doudianPreDeposit.getProductName());

        // 设置请求来源地址
        //model.setRequestFromUrl(returnUrl);

        // 设置产品码
        model.setProductCode("FAST_INSTANT_TRADE_PAY");

        // 设置PC扫码支付的方式
        model.setQrPayMode("1");

        // 设置商户自定义二维码宽度
        model.setQrcodeWidth(100L);

        // 设置请求后页面的集成方式
        model.setIntegrationType("PCWEB");

        // 设置商户订单号
        model.setOutTradeNo(doudianPreDeposit.getDepositOrderId());

        // 设置订单总金额
        model.setTotalAmount(doudianPreDeposit.getOrderMoney().toString());

        request.setBizModel(model);
        AlipayTradePagePayResponse response = null;
        try {
            response = alipayClient.pageExecute(request, "POST");
        } catch (AlipayApiException e) {
            System.out.println(e.getMessage());
            return "系统错误";
        }
        String pageRedirectionData = response.getBody();
        System.out.println(pageRedirectionData);

        if(response.isSuccess()){
            System.out.println("调用成功");
            return pageRedirectionData;
        } else {
            System.out.println("调用失败");
            return "系统错误";
        }
    }


    private static CertAlipayRequest getClientParams(String appPublicCertPath, String appId, String privateKey, String alipayPublicCertPath, String alipayRootCertPath) {
        CertAlipayRequest certParams = new CertAlipayRequest();
        certParams.setServerUrl("https://openapi.alipay.com/gateway.do");
        //请更换为您的AppId
        certParams.setAppId(appId);//"2019082266374515");
        //请更换为您的PKCS8格式的应用私钥
        certParams.setPrivateKey(privateKey);
        //请更换为您使用的字符集编码，推荐采用utf-8
        certParams.setCharset("utf-8");
        certParams.setFormat("json");
        certParams.setSignType("RSA2");
        //请更换为您的应用公钥证书文件路径
        certParams.setCertPath(appPublicCertPath);
        //请更换您的支付宝公钥证书文件路径
        certParams.setAlipayPublicCertPath(alipayPublicCertPath);
        //更换为支付宝根证书文件路径
        certParams.setRootCertPath(alipayRootCertPath);
        //请更换为您的应用公钥证书文件路径
        return certParams;
    }
}
