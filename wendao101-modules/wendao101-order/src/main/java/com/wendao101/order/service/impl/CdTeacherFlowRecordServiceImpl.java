package com.wendao101.order.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.CdTeacherFlowRecordMapper;
import com.wendao101.order.domain.CdTeacherFlowRecord;
import com.wendao101.order.service.ICdTeacherFlowRecordService;

/**
 * 抽单老师流水记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class CdTeacherFlowRecordServiceImpl implements ICdTeacherFlowRecordService 
{
    @Autowired
    private CdTeacherFlowRecordMapper cdTeacherFlowRecordMapper;

    /**
     * 查询抽单老师流水记录
     * 
     * @param id 抽单老师流水记录主键
     * @return 抽单老师流水记录
     */
    @Override
    public CdTeacherFlowRecord selectCdTeacherFlowRecordById(Long id)
    {
        return cdTeacherFlowRecordMapper.selectCdTeacherFlowRecordById(id);
    }

    /**
     * 查询抽单老师流水记录列表
     * 
     * @param cdTeacherFlowRecord 抽单老师流水记录
     * @return 抽单老师流水记录
     */
    @Override
    public List<CdTeacherFlowRecord> selectCdTeacherFlowRecordList(CdTeacherFlowRecord cdTeacherFlowRecord)
    {
        return cdTeacherFlowRecordMapper.selectCdTeacherFlowRecordList(cdTeacherFlowRecord);
    }

    /**
     * 新增抽单老师流水记录
     * 
     * @param cdTeacherFlowRecord 抽单老师流水记录
     * @return 结果
     */
    @Override
    public int insertCdTeacherFlowRecord(CdTeacherFlowRecord cdTeacherFlowRecord)
    {
        cdTeacherFlowRecord.setCreateTime(DateUtils.getNowDate());
        return cdTeacherFlowRecordMapper.insertCdTeacherFlowRecord(cdTeacherFlowRecord);
    }

    /**
     * 修改抽单老师流水记录
     * 
     * @param cdTeacherFlowRecord 抽单老师流水记录
     * @return 结果
     */
    @Override
    public int updateCdTeacherFlowRecord(CdTeacherFlowRecord cdTeacherFlowRecord)
    {
        cdTeacherFlowRecord.setUpdateTime(DateUtils.getNowDate());
        return cdTeacherFlowRecordMapper.updateCdTeacherFlowRecord(cdTeacherFlowRecord);
    }

    /**
     * 批量删除抽单老师流水记录
     * 
     * @param ids 需要删除的抽单老师流水记录主键
     * @return 结果
     */
    @Override
    public int deleteCdTeacherFlowRecordByIds(Long[] ids)
    {
        return cdTeacherFlowRecordMapper.deleteCdTeacherFlowRecordByIds(ids);
    }

    /**
     * 删除抽单老师流水记录信息
     * 
     * @param id 抽单老师流水记录主键
     * @return 结果
     */
    @Override
    public int deleteCdTeacherFlowRecordById(Long id)
    {
        return cdTeacherFlowRecordMapper.deleteCdTeacherFlowRecordById(id);
    }
}
