package com.wendao101.order.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsRequest;
import com.aliyun.sdk.service.dysmsapi20170525.models.SendSmsResponse;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import com.wendao101.common.core.constant.WendaoRedisKey;
import com.wendao101.common.core.ktdto.CourseInfoDTO;
import com.wendao101.common.core.ktdto.GiveCourseUserChooseDTO;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.utils.bean.BeanUtils;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.order.domain.*;
import com.wendao101.order.dto.*;
import com.wendao101.order.mapper.*;
import com.wendao101.order.service.IBuyCourseGiveOrderService;
import com.wendao101.order.service.ICourseAuditService;
import com.wendao101.order.service.ICourseOrderService;
import com.wendao101.order.service.IGiftCourseRecordService;
import com.wendao101.order.vo.*;
import darabonba.core.client.ClientOverrideConfiguration;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 订单信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-2
 */
@Service
public class CourseOrderServiceImpl implements ICourseOrderService {

    @Autowired
    private IGiftCourseRecordService giftCourseRecordService;

    @Autowired
    private IBuyCourseGiveOrderService buyCourseGiveOrderService;

    @Autowired
    private ICourseAuditService courseAuditService;
    @Resource
    private CourseOrderMapper courseOrderMapper;

    @Resource
    private CourseRefundMapper courseRefundMapper;

    @Resource
    private WendaoComplaintMapper wendaoComplaintMapper;

    @Resource
    private WithdrawPriceMapper withdrawPriceMapper;

    @Resource
    private WithdrawRecordMapper withdrawRecordMapper;

    @Resource
    private FundIncomeMapper fundIncomeMapper;

    @Resource
    private RedisService redisService;

    private static final String give_order_id_cache_list = "give_order_id_cache_list:";

    private static final String give_order_user_choose_course_list = "give_order_user_choose_course_list:";

    private static final String course_give_away = "course_give_away:";

    /**
     * 查询订单信息
     *
     * @param id 订单信息主键
     * @return 订单信息
     */
    @Override
    public CourseOrder selectCourseOrderById(Long id) {
        return courseOrderMapper.selectCourseOrderById(id);
    }

    @Override
    public CourseOrder selectCourseOrderByOutOrderNumber(String outOrderNumber) {
        return courseOrderMapper.selectCourseOrderByOutOrderNumber(outOrderNumber);
    }

    /**
     * 查询订单信息列表
     *
     * @param courseOrder 订单信息
     * @return 订单信息
     */
    @Override
    public List<CourseOrder> selectCourseOrderList(CourseOrder courseOrder) {
        return courseOrderMapper.selectCourseOrderList(courseOrder);
    }

    /**
     * 新增订单信息
     *
     * @param courseOrder 订单信息
     * @return 结果
     */
    @Override
    public int insertCourseOrder(CourseOrder courseOrder) {
        courseOrder.setCreateTime(DateUtils.getNowDate());
        return courseOrderMapper.insertCourseOrder(courseOrder);
    }

    /**
     * 修改订单信息
     *
     * @param courseOrder 订单信息
     * @return 结果
     */
    @Override
    public int updateCourseOrder(CourseOrder courseOrder) {
        courseOrder.setUpdateTime(DateUtils.getNowDate());
        return courseOrderMapper.updateCourseOrder(courseOrder);
    }

    /**
     * 批量删除订单信息
     *
     * @param ids 需要删除的订单信息主键
     * @return 结果
     */
    @Override
    public int deleteCourseOrderByIds(Long[] ids) {
        return courseOrderMapper.deleteCourseOrderByIds(ids);
    }

    /**
     * 删除订单信息信息
     *
     * @param id 订单信息主键
     * @return 结果
     */
    @Override
    public int deleteCourseOrderById(Long id) {
        return courseOrderMapper.deleteCourseOrderById(id);
    }

    @Override
    public PageInfoActual <CourseOrderDTO> selectAllCourseOrderListForCd(CourseOrderVO courseOrderVO) {
        courseOrderVO.setOrderStatus(1);
        PageHelper.startPage(courseOrderVO.getPageNum(), courseOrderVO.getPageSize());
        List<CourseOrderDTO> courseOrderDTOS = courseOrderMapper.selectAllCourseOrderListForCd(courseOrderVO);

        for(CourseOrderDTO courseOrderDTO:courseOrderDTOS){
            //退款状态
            courseOrderDTO.setOrderStatus(11);
            String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrderDTO.getOrderId();
            String cacheResult = redisService.getCacheObject(orderKey);
            courseOrderDTO.setSecondKill(StringUtils.isNotEmpty(cacheResult));
            List<CourseRefund> list= courseRefundMapper.selectListByOrderIdAndStatus(courseOrderDTO.getOrderId());
            if(CollectionUtils.isNotEmpty(list)){
                courseOrderDTO.setRefundTime(list.get(0).getRefundTime());
            }
        }

        //已付款数
        Long amountPaidNum = courseOrderMapper.countAmountPaidNum(courseOrderVO);
        //待付款数
        Long unpaidAmountNum = courseOrderMapper.countUnpaidAmountNum(courseOrderVO);
        //已退款数
        Long refundedNum = courseOrderMapper.countRefundedNum(courseOrderVO);
        //拒绝退款
        Long refusalRefundedNum = courseOrderMapper.refusalRefundedNum(courseOrderVO);
        //已取消数
        Long cancelNum = courseOrderMapper.CanceldNum(courseOrderVO);

        //投诉中
        Long complaintNum = courseOrderMapper.ComplaintNum(courseOrderVO);

        //退款审核中 order_status = 9
        Long orderRefundReviewNum = courseOrderMapper.orderRefundReviewNum(courseOrderVO);
        //投诉处理退款
        Long orderRefundTouShuNum = courseOrderMapper.orderRefundTouShuNum(courseOrderVO);

        //查询总的收益
        BigDecimal totalActualAmount = BigDecimal.ZERO;
        BigDecimal payPrice = courseOrderMapper.gettotalActualAmountForCD(courseOrderVO);
        totalActualAmount = totalActualAmount.add(payPrice != null? payPrice : BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(courseOrderDTOS)) {
            courseOrderDTOS.forEach(courseOrderDTO -> {
                //已付款数
                courseOrderDTO.setAmountPaidNum(amountPaidNum);
                //待付款数
                courseOrderDTO.setUnpaidAmountNum(unpaidAmountNum);
                //已退款数
                courseOrderDTO.setRefundedNum(refundedNum);
                //拒绝退款
                courseOrderDTO.setRefusalRefundedNum(refusalRefundedNum);
                //已关闭数
                courseOrderDTO.setCancelNum(cancelNum);
                //投诉中个数
                courseOrderDTO.setComplaintNum(complaintNum);
                //退款审核中数量
                courseOrderDTO.setOrderRefundReviewNum(orderRefundReviewNum);
                //投诉处理退款
                courseOrderDTO.setOrderRefundTouShuNum(orderRefundTouShuNum);
            });
            PageInfoActual<CourseOrderDTO> pageInfoActual = new PageInfoActual<>(courseOrderDTOS);
            pageInfoActual.setTotalActualAmount(totalActualAmount);
            return pageInfoActual;
        } else {
            CourseOrderDTO courseOrderDTO = new CourseOrderDTO();
            //已付款数
            courseOrderDTO.setAmountPaidNum(amountPaidNum);
            //待付款数
            courseOrderDTO.setUnpaidAmountNum(unpaidAmountNum);
            //已退款数
            courseOrderDTO.setRefundedNum(refundedNum);
            //拒绝退款
            courseOrderDTO.setRefusalRefundedNum(refusalRefundedNum);
            //已关闭数
            courseOrderDTO.setCancelNum(cancelNum);
            courseOrderDTOS.add(courseOrderDTO);
            //投诉中个数
            courseOrderDTO.setComplaintNum(complaintNum);
            //退款审核中数量
            courseOrderDTO.setOrderRefundReviewNum(orderRefundReviewNum);
            //投诉处理退款
            courseOrderDTO.setOrderRefundTouShuNum(orderRefundTouShuNum);

        }
        PageInfoActual<CourseOrderDTO> pageInfoActual = new PageInfoActual<>(courseOrderDTOS);
        pageInfoActual.setTotalActualAmount(totalActualAmount);
        return pageInfoActual;
    }

    /**
     * 检索查询订单信息
     *
     * @param courseOrderVO
     * @return
     */
    @Override
    public PageInfoActual <CourseOrderDTO> selectAllCourseOrderList(CourseOrderVO courseOrderVO) {
        //courseOrderVO.getTeacherIds()如果这个有值就是代理商来的值
        if(courseOrderVO!=null&&courseOrderVO.getOrderStatus()!=null&&courseOrderVO.getOrderStatus()==11&&CollectionUtils.isEmpty(courseOrderVO.getTeacherIds())){
            return selectAllCourseOrderListForCd(courseOrderVO);
        }

        PageHelper.startPage(courseOrderVO.getPageNum(), courseOrderVO.getPageSize());
        List<CourseOrderDTO> courseOrderDTOS = courseOrderMapper.selectAllCourseOrderList(courseOrderVO);

        for(CourseOrderDTO courseOrderDTO:courseOrderDTOS){
            String orderKey = WendaoRedisKey.WENDAO_SECOND_KILL_FOR_ORDER_KEY + courseOrderDTO.getOrderId();
            String cacheResult = redisService.getCacheObject(orderKey);
            courseOrderDTO.setSecondKill(StringUtils.isNotEmpty(cacheResult));
            List<CourseRefund> list= courseRefundMapper.selectListByOrderIdAndStatus(courseOrderDTO.getOrderId());
            if(CollectionUtils.isNotEmpty(list)){
                courseOrderDTO.setRefundTime(list.get(0).getRefundTime());
            }
        }

        //已付款数
        Long amountPaidNum = courseOrderMapper.countAmountPaidNum(courseOrderVO);
        //待付款数
        Long unpaidAmountNum = courseOrderMapper.countUnpaidAmountNum(courseOrderVO);
        //已退款数
        Long refundedNum = courseOrderMapper.countRefundedNum(courseOrderVO);
        //拒绝退款
        Long refusalRefundedNum = courseOrderMapper.refusalRefundedNum(courseOrderVO);
        //已取消数
        Long cancelNum = courseOrderMapper.CanceldNum(courseOrderVO);

        //投诉中
        Long complaintNum = courseOrderMapper.ComplaintNum(courseOrderVO);

        //退款审核中 order_status = 9
        Long orderRefundReviewNum = courseOrderMapper.orderRefundReviewNum(courseOrderVO);

        Long orderRefundTouShuNum = courseOrderMapper.orderRefundTouShuNum(courseOrderVO);

        //查询总的收益
        BigDecimal totalActualAmount = BigDecimal.ZERO;
//        BigDecimal fundsAmount = courseOrderService.getFundsAmount(orderDataDTO);
//        orderDataVOPageInfoData.setFundsAmount(fundsAmount != null? fundsAmount : BigDecimal.ZERO);
        BigDecimal payPrice = courseOrderMapper.gettotalActualAmount(courseOrderVO);
        totalActualAmount = totalActualAmount.add(payPrice != null? payPrice : BigDecimal.ZERO);
        if (CollectionUtils.isNotEmpty(courseOrderDTOS)) {
            courseOrderDTOS.forEach(courseOrderDTO -> {
                //已付款数
                courseOrderDTO.setAmountPaidNum(amountPaidNum);
                //待付款数
                courseOrderDTO.setUnpaidAmountNum(unpaidAmountNum);
                //已退款数
                courseOrderDTO.setRefundedNum(refundedNum);
                //拒绝退款
                courseOrderDTO.setRefusalRefundedNum(refusalRefundedNum);
                //已关闭数
                courseOrderDTO.setCancelNum(cancelNum);
                //投诉中个数
                courseOrderDTO.setComplaintNum(complaintNum);
                //退款审核中数量
                courseOrderDTO.setOrderRefundReviewNum(orderRefundReviewNum);
                //投诉处理退款
                courseOrderDTO.setOrderRefundTouShuNum(orderRefundTouShuNum);
            });
            PageInfoActual<CourseOrderDTO> pageInfoActual = new PageInfoActual<>(courseOrderDTOS);
            pageInfoActual.setTotalActualAmount(totalActualAmount);
            return pageInfoActual;
        } else {
            CourseOrderDTO courseOrderDTO = new CourseOrderDTO();
            //已付款数
            courseOrderDTO.setAmountPaidNum(amountPaidNum);
            //待付款数
            courseOrderDTO.setUnpaidAmountNum(unpaidAmountNum);
            //已退款数
            courseOrderDTO.setRefundedNum(refundedNum);
            //拒绝退款
            courseOrderDTO.setRefusalRefundedNum(refusalRefundedNum);
            //已关闭数
            courseOrderDTO.setCancelNum(cancelNum);
            courseOrderDTOS.add(courseOrderDTO);

            //投诉中个数
            courseOrderDTO.setComplaintNum(complaintNum);
            //退款审核中数量
            courseOrderDTO.setOrderRefundReviewNum(orderRefundReviewNum);
            //投诉处理退款
            courseOrderDTO.setOrderRefundTouShuNum(orderRefundTouShuNum);

        }
        PageInfoActual<CourseOrderDTO> pageInfoActual = new PageInfoActual<>(courseOrderDTOS);
        pageInfoActual.setTotalActualAmount(totalActualAmount);
        return pageInfoActual;
    }

    @Override
    public PageInfo<CourseOrderRecordDTO> selectBuyRecord(CourseOrderRecordVO courseOrderRecordVO) {
        PageHelper.startPage(courseOrderRecordVO.getPageNum(), courseOrderRecordVO.getPageSize());
        //查询所有已入账资金
        List<CourseOrderRecordDTO> courseOrderRecordDTOS = courseOrderMapper.selectBuyRecord(courseOrderRecordVO);
        BigDecimal price = courseOrderMapper.getBuyRecordPrice(courseOrderRecordVO);
//        BigDecimal price = courseOrderRecordDTOS.stream().map(CourseOrderRecordDTO::getPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        if (CollectionUtils.isNotEmpty(courseOrderRecordDTOS)) {
            courseOrderRecordDTOS.stream().forEach(item -> {
                //获取合计总额
                item.setCountPayPrice(price);
            });

        }
        return new PageInfo<>(courseOrderRecordDTOS);
    }

    @Override
    public CourseOrder selectCourseOrderByOrderId(String orderId) {
        return courseOrderMapper.selectCourseOrderByOrderId(orderId);
    }

    @Override
    public CourseOrderDTO selectCourseOrderByOrderIdAndTeacherId(String orderId, Long teacherId) {
        return courseOrderMapper.selectCourseOrderByOrderIdAndTeacherId(orderId, teacherId);
    }

    @Override
    public List<ExportCourseOrderDTO> getExportCourseOrder(CourseOrderExportVO courseOrderExportVO) {
        return courseOrderMapper.getExportCourseOrder(courseOrderExportVO);
    }

    @Override
    public List<ExportPromoterOrderDTO> getExportPromoterOrder(CourseOrderExportVO courseOrderExportVO) {
        return courseOrderMapper.getExportPromoterOrder(courseOrderExportVO);
    }

    @Override
    public List<Long> getIsCourse(Long id) {
        return courseOrderMapper.getIsCourse(id);
    }

    @Override
    public PageInfo<CourseOrder> selectCourseOrderPromoter(CourseOrderPromoterVO courseOrderPromoterVO) {
        PageHelper.startPage(courseOrderPromoterVO.getPageNum(), courseOrderPromoterVO.getPageSize());
        List<CourseOrder> courseOrderList = courseOrderMapper.selectCourseOrderPromoter(courseOrderPromoterVO);
        return new PageInfo<>(courseOrderList);
    }

    /**
     * 线上退款
     *
     * @param courseOrderRefundDTO
     * @return
     */
    @Override
    public int onlineRefund(CourseOrderRefundDTO courseOrderRefundDTO) {
//修改订单状态
        CourseOrderDTO courseOrder = new CourseOrderDTO();
        courseOrder.setOrderStatus(2);
        courseOrder.setFundsType(0);
        courseOrder.setOrderId(courseOrderRefundDTO.getOrderId());


        //获取当前时间戳
        long l = System.currentTimeMillis();
        long epochMilli = l / 1000;

        //获取随机的两位数字
        Random random = new Random();
        int i = random.nextInt(90) + 10;


        CourseRefundVO courseRefundVO = courseOrderMapper.selectAll(courseOrderRefundDTO.getOrderId());


        //获取用户id的后两位
        Long buyerUserId = courseRefundVO.getBuyerUserId();
        String s = String.valueOf(buyerUserId);
        String a = null;
        if (s.length() != 0) {
            if (s.length() < 2) {
                a = "0" + s;
            } else if (s.length() == 2) {
                a = s;
            } else {
                char c1 = s.charAt(s.length() - 2);
                char c = s.charAt(s.length() - 1);
                a = String.valueOf(c1) + String.valueOf(c);
            }
        }
//生成退款编号
        String s1 = String.valueOf(epochMilli) + String.valueOf(i) + String.valueOf(a);

        courseRefundVO.setRefundId(s1);
        courseRefundVO.setRefundStatus(1);
        courseRefundVO.setRefundType(6);
        courseRefundVO.setRefundReason("平台介入退款");
        courseRefundVO.setRefundTime(DateUtils.getNowDate());
        courseRefundVO.setCreateTime(DateUtils.getNowDate());
        courseRefundVO.setUpdateTime(DateUtils.getNowDate());


        courseRefundMapper.insertOnlineCourseRefund(courseRefundVO);


        return courseOrderMapper.updateOrderStatus(courseOrder);

    }

    /**
     * 用户投诉
     *
     * @param userComplaintDTO
     * @return
     */
    @Override
    public int userComplaint(UserComplaintDTO userComplaintDTO) {


        Integer complaitType = userComplaintDTO.getComplaintType();

        String orderId = userComplaintDTO.getOrderId();
        CourseOrderDTO courseOrderDTO = new CourseOrderDTO();
        courseOrderDTO.setOrderId(orderId);
        courseOrderDTO.setOrderStatus(8);
        courseOrderMapper.updateOrderStatus(courseOrderDTO);

        //获取当前时间戳
        long l = System.currentTimeMillis();
        long epochMilli = l / 1000;

        //获取随机的两位数字
        Random random = new Random();
        int i = random.nextInt(90) + 10;

        WendaoComplaintVo wendaoComplaintVo = courseOrderMapper.userComplaint(userComplaintDTO);

        //获取用户id的后两位
        int buyerUserId = wendaoComplaintVo.getUserId();
        String s = String.valueOf(buyerUserId);
        String a = null;
        if (s.length() != 0) {
            if (s.length() < 2) {
                a = "0" + s;
            } else if (s.length() == 2) {
                a = s;
            } else {
                char c1 = s.charAt(s.length() - 2);
                char c = s.charAt(s.length() - 1);
                a = String.valueOf(c1) + String.valueOf(c);
            }
        }
        String s1 = String.valueOf(epochMilli) + String.valueOf(i) + String.valueOf(a);

        wendaoComplaintVo.setComplaintNumber(s1);
        wendaoComplaintVo.setComplaintStatus(0);
        wendaoComplaintVo.setComplaintType(userComplaintDTO.getComplaintType());
        wendaoComplaintVo.setComplaintTime(DateUtils.getNowDate());
        wendaoComplaintVo.setCreateTime(DateUtils.getNowDate());
        wendaoComplaintVo.setUpdateTime(DateUtils.getNowDate());
        wendaoComplaintVo.setBuyerPhoneNumber(userComplaintDTO.getBuyerPhoneNumber());
        wendaoComplaintVo.setComplaintDesc(userComplaintDTO.getComplaintDesc());

        if (userComplaintDTO.getComplaintImgs() != null) {
            wendaoComplaintVo.setComplaintImgs(userComplaintDTO.getComplaintImgs());
        } else {
            wendaoComplaintVo.setComplaintImgs(null);
        }

        return wendaoComplaintMapper.insertWendaoComplaint(wendaoComplaintVo);
    }

    @Override
    public CourseOrder selectCourseOrderByOrderIdAndBuyUserId(Long wendaoUserId, String orderId) {
        return courseOrderMapper.selectCourseOrderByOrderIdAndBuyUserId(wendaoUserId,orderId);
    }

    @Override
    public List<OrderDataVO> orderData(OrderDataDTO orderDataDTO) {
        List<OrderDataVO> orderDataVOS = courseOrderMapper.getOrderData(orderDataDTO);

        for (OrderDataVO orderDataVO : orderDataVOS) {
            //获取订单退款信息
            OrderRefundVO orderRefundVO = courseRefundMapper.orderData(orderDataVO.getTeacherId());
            if (orderRefundVO != null){
                BeanUtils.copyBeanProp(orderDataVO, orderRefundVO);
            }

            FundIncome fundIncome = new FundIncome();
            fundIncome.setTeacherId(orderDataVO.getTeacherId());
            fundIncome.setIsDelete(0);
            //查出所有平台金额
            List<FundIncome> fundIncomes = fundIncomeMapper.selectFundIncomeList(fundIncome);
            if (CollectionUtils.isNotEmpty(fundIncomes)){
                //获取抖音累计金额
                orderDataVO.setDyWithdrawnPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 0 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
                //获取快手累计金额
                orderDataVO.setKsWithdrawnPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 2 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
                //获取微信累计金额
                orderDataVO.setWxWithdrawnPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 1 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
                //获取视频号累计金额
                orderDataVO.setSphWithdrawnPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 3 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));

                //获取抖音在途金额
                orderDataVO.setDyFundsTransitPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 0 && item.getIncomeType() != 4 && item.getFundsType() == 0 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                //获取快手在途金额
                orderDataVO.setKsFundsTransitPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 2 && item.getIncomeType()!= 4 && item.getFundsType() == 0 && item.getIncomeType()!= 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                //获取微信在途金额
                orderDataVO.setWxFundsTransitPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 1 && item.getIncomeType()!= 4 && item.getFundsType() == 0 && item.getIncomeType()!= 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
                //获取视频号在途金额
                orderDataVO.setSphFundsTransitPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 3 && item.getIncomeType()!= 4 && item.getFundsType() == 0 && item.getIncomeType()!= 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));


                //获取抖音可提现金额
                BigDecimal dyMayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomePlatform() == 0 && item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                orderDataVO.setDyMayWithdrawPrice();
                //获取快手可提现金额
                BigDecimal ksMayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomePlatform() == 2 && item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                orderDataVO.setKsMayWithdrawPrice();
                //获取微信可提现金额
                BigDecimal wxMayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomePlatform() == 1 && item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                orderDataVO.setWxMayWithdrawPrice();
                //获取视频号可提现金额
                BigDecimal sphMayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomePlatform() == 3 && item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
//                orderDataVO.setSphMayWithdrawPrice();

                //获取可提现金额
                BigDecimal mayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
                //获取订单提现信息
            OrderWithdrawVO orderWithdrawVO= withdrawPriceMapper.orderData(orderDataVO.getTeacherId());
                if (ObjectUtils.isNotEmpty(orderWithdrawVO)){
                    BeanUtils.copyBeanProp(orderDataVO, orderWithdrawVO);
                    if (mayWithdrawPrice.compareTo(BigDecimal.ZERO) != 0){
                        //获取可提现金额
                        orderDataVO.setDyMayWithdrawPrice(dyMayWithdrawPrice.subtract(orderWithdrawVO.getDyWithdrawnPrice()));
                        orderDataVO.setKsMayWithdrawPrice(ksMayWithdrawPrice.subtract(orderWithdrawVO.getKsWithdrawnPrice()));
                        orderDataVO.setWxMayWithdrawPrice(wxMayWithdrawPrice.subtract(orderWithdrawVO.getWxWithdrawnPrice()));
                        orderDataVO.setSphMayWithdrawPrice(sphMayWithdrawPrice.subtract(orderWithdrawVO.getSphWithdrawnPrice()));
                        //获取累计提现金额
                        orderDataVO.setDyWithdrawnPrice(orderWithdrawVO.getDyWithdrawnPrice());
                        orderDataVO.setKsWithdrawnPrice(orderWithdrawVO.getKsWithdrawnPrice());
                        orderDataVO.setWxWithdrawnPrice(orderWithdrawVO.getWxWithdrawnPrice());
                        orderDataVO.setSphWithdrawnPrice(orderWithdrawVO.getSphWithdrawnPrice());
                    }else {
                        orderDataVO.setDyMayWithdrawPrice(BigDecimal.ZERO);
                        orderDataVO.setKsMayWithdrawPrice(BigDecimal.ZERO);
                        orderDataVO.setWxMayWithdrawPrice(BigDecimal.ZERO);
                        orderDataVO.setSphMayWithdrawPrice(BigDecimal.ZERO);
                        orderDataVO.setDyWithdrawnPrice(BigDecimal.ZERO);
                        orderDataVO.setKsWithdrawnPrice(BigDecimal.ZERO);
                        orderDataVO.setWxWithdrawnPrice(BigDecimal.ZERO);
                        orderDataVO.setSphWithdrawnPrice(BigDecimal.ZERO);
                    }
                }else {
                    //获取在途金额
//                    orderDataVO.setFundsTransitPrice(transitPrice);
                    orderDataVO.setDyMayWithdrawPrice(dyMayWithdrawPrice);
                    orderDataVO.setKsMayWithdrawPrice(ksMayWithdrawPrice);
                    orderDataVO.setWxMayWithdrawPrice(wxMayWithdrawPrice);
                    orderDataVO.setSphMayWithdrawPrice(sphMayWithdrawPrice);
                    orderDataVO.setDyWithdrawnPrice(BigDecimal.ZERO);
                    orderDataVO.setKsWithdrawnPrice(BigDecimal.ZERO);
                    orderDataVO.setWxWithdrawnPrice(BigDecimal.ZERO);
                    orderDataVO.setSphWithdrawnPrice(BigDecimal.ZERO);
                }
            }

            BigDecimal douyinAmount = orderDataVO.getDouyinAmount() != null ? orderDataVO.getDouyinAmount() : BigDecimal.ZERO;
            BigDecimal douyinRefundAmount = orderDataVO.getDouyinRefundAmount() != null ? orderDataVO.getDouyinRefundAmount() : BigDecimal.ZERO;
            orderDataVO.setDouyinNetAmount(douyinAmount.subtract(douyinRefundAmount));

            BigDecimal weixinAmount = orderDataVO.getWeixinAmount() != null ? orderDataVO.getWeixinAmount() : BigDecimal.ZERO;
            BigDecimal weixinRefundAmount = orderDataVO.getWeixinRefundAmount() != null ? orderDataVO.getWeixinRefundAmount() : BigDecimal.ZERO;
            orderDataVO.setWeixinNetAmount(weixinAmount.subtract(weixinRefundAmount));

            BigDecimal kuaisuAmount = orderDataVO.getKuaisuAmount() != null ? orderDataVO.getKuaisuAmount() : BigDecimal.ZERO;
            BigDecimal kuaisuRefundAmount = orderDataVO.getKuaisuRefundAmount() != null ? orderDataVO.getKuaisuRefundAmount() : BigDecimal.ZERO;
            orderDataVO.setKuaisuNetAmount(kuaisuAmount.subtract(kuaisuRefundAmount));

            BigDecimal shipinAmount = orderDataVO.getShipinAmount() != null ? orderDataVO.getShipinAmount() : BigDecimal.ZERO;
            BigDecimal shipinRefundAmount = orderDataVO.getShipinRefundAmount() != null ? orderDataVO.getShipinRefundAmount() : BigDecimal.ZERO;
            orderDataVO.setShipinNetAmount(shipinAmount.subtract(shipinRefundAmount));
        }
        return orderDataVOS;
    }

    @Override
    public Integer getTotalOrderNum(OrderDataDTO orderDataDTO) {
        return courseOrderMapper.getTotalOrderNum(orderDataDTO);
    }

    @Override
    public BigDecimal getGrossSales(OrderDataDTO orderDataDTO) {
        return courseOrderMapper.getGrossSales(orderDataDTO);
    }

    @Override
    public Integer getTotalRefundNum(OrderDataDTO orderDataDTO) {
        return courseRefundMapper.getTotalRefundNum(orderDataDTO);
    }

    @Override
    public BigDecimal getRefundAmount(OrderDataDTO orderDataDTO) {
        return courseRefundMapper.getRefundAmount(orderDataDTO);
    }

    @Override
    public BigDecimal getFundsAmount(OrderDataDTO orderDataDTO) {
        return withdrawPriceMapper.getFundsAmount(orderDataDTO);
    }

    @Override
    public BigDecimal getMayWithdrawnAmount(OrderDataDTO orderDataDTO) {
        return withdrawPriceMapper.getMayWithdrawnAmount(orderDataDTO);
    }

    @Override
    public BigDecimal getWithdrawnAmount(OrderDataDTO orderDataDTO) {
        return withdrawRecordMapper.getWithdrawnAmount(orderDataDTO);
    }

    @Override
    public BigDecimal getTotalCommission(OrderDataDTO orderDataDTO) {
        return withdrawRecordMapper.getTotalCommission(orderDataDTO);
    }

    @Override
    public GiveEntityVo isGiveEntity(Long courseId) {
        return courseOrderMapper.isGiveEntity(courseId);
    }

    @Override
    public ShippingAddressAddVO getShippingAddressAddVO(Long buyerUserId) {
        return courseOrderMapper.getShippingAddressAddVO(buyerUserId);
    }

    @Override
    public CourseOrder selectCourseOrderByOrderIdAndStatus(String orderId) {
        return courseOrderMapper.selectCourseOrderByOrderIdAndStatus(orderId);
    }

    @Override
    public TeacherDTO selectTeacherByTeacherId(Long teacherId) {
        return courseOrderMapper.selectTeacherByTeacherId(teacherId);
    }

    @Override
    public void updateOderStatusClosedByOrderId(String giveOrderId) {
        courseOrderMapper.updateOderStatusClosedByOrderId(giveOrderId);
    }

    @Override
    public void clearGiveInfo(CourseOrder courseOrder) {
//        if (courseOrder.getOrderPlatform() != 0 && courseOrder.getOrderPlatform() != 1 && courseOrder.getOrderPlatform() != 2) {
//            return;
//        }
        List<String> giveOrderIdList = redisService.getCacheList(give_order_id_cache_list + courseOrder.getOrderId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(giveOrderIdList)) {
            for (String giveOrderId : giveOrderIdList) {
                updateOderStatusClosedByOrderId(giveOrderId);
                buyCourseGiveOrderService.updateOrderStatus(giveOrderId, 3);
            }
            redisService.deleteObject(give_order_id_cache_list + courseOrder.getOrderId());
        }
        redisService.deleteObject(give_order_user_choose_course_list+ courseOrder.getOrderId());
    }

    @Override
    public void giveUserCourses(CourseOrder courseOrder) {
        //添加视频号小红书抖店h5
        if (courseOrder.getOrderPlatform() != 0
                && courseOrder.getOrderPlatform() != 1
                && courseOrder.getOrderPlatform() != 2
                && courseOrder.getOrderPlatform() != 3
                && courseOrder.getOrderPlatform() != 7
                && courseOrder.getOrderPlatform() != 11
                && courseOrder.getOrderPlatform() != 5) {
            return;
        }
        int platform = courseOrder.getOrderPlatform();
        //买课赠课逻辑
        //获取订单中的的课程id
        Long courseId = courseOrder.getCourseId();
        GiftCourseRecord giftCourseRecordResult = null;
        //查询赠课设置
        GiftCourseRecord giftCourseRecord = new GiftCourseRecord();
        giftCourseRecord.setPrimaryCourseId(courseId);
        giftCourseRecord.setIsDelete(0);
        giftCourseRecord.setGiveOpenStatus(1);
        List<GiftCourseRecord> giftCourseRecords = giftCourseRecordService.selectGiftCourseRecordList(giftCourseRecord);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(giftCourseRecords)) {
            //判断结束时间是否大于当前时间
            for (GiftCourseRecord item : giftCourseRecords) {
                //开始结束时间不为空且开始时间小于当前时间,结束时间大于当前时间
                if (item.getBeginTime() != null && item.getEndTime() != null
                        && item.getEndTime().getTime() > System.currentTimeMillis()
                        && item.getBeginTime().getTime() < System.currentTimeMillis()) {
                    giftCourseRecordResult = item;
                    break;
                }
            }
        }
        //赠课的orderId列表存起来
        List<String> giveOrderIdList = new ArrayList<>();
        List<com.wendao101.common.core.ktdto.CourseInfoDTO> courseInfoDTOList = new ArrayList<>();
        GiveCourseUserChooseDTO giveCourseUserChoose = new GiveCourseUserChooseDTO();
        if (giftCourseRecordResult != null && giftCourseRecordResult.getGiveType() != null) {
            if (giftCourseRecordResult.getGiveType() == 1) {
                giveCourseUserChoose.setGiveNumMax(giftCourseRecordResult.getGiveNumMax());
            }
            String giveCourseInfo = giftCourseRecordResult.getGiveCourseInfo();
            if (StringUtils.isNotBlank(giveCourseInfo)) {
                JSONArray jsonArray = JSON.parseArray(giveCourseInfo);
                if (jsonArray != null && !jsonArray.isEmpty()) {
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject jsonObject = jsonArray.getJSONObject(i);
                        //获取课程id
                        Long giveCourseId = jsonObject.getLong("id");
                        CourseAudit courseAudit = null;
                        // 变更,如果是h5和快手,微信的都属于跨平台!
                        if (platform == 5 || platform == 2 || platform == 1) {
                            //如果是h5,查询抖音快手微信端任意平台的有课就可以
                            courseAudit = courseAuditService.selectCourseAuditByIdAndPlatformWap(giveCourseId, 5, courseOrder.getTeacherId(), courseOrder.getAppNameType());
                        } else if (platform == 3 || platform == 7 || platform == 11) {
                            //视频号,小红书,抖店 查询微信平台的课程
                            courseAudit = courseAuditService.selectCourseAuditByIdAndPlatform(giveCourseId, 1, courseOrder.getTeacherId(), courseOrder.getAppNameType());
                        } else {
                            //否则按平台本身查询
                            courseAudit = courseAuditService.selectCourseAuditByIdAndPlatform(giveCourseId, platform, courseOrder.getTeacherId(), courseOrder.getAppNameType());
                        }
                        if (courseAudit == null) {
                            continue;
                        }
                        //不分平台查询是否有这个课程的有效订单
                        //int courseOrderCount = courseAuditService.countGiveAwayCourseOrder(giveCourseId, platform, courseOrder.getBuyerUserId(), courseOrder.getAppNameType());
                        int courseOrderCount = courseAuditService.countGiveAwayCourseOrderNoPlatform(giveCourseId, courseOrder.getBuyerUserId(), courseOrder.getAppNameType());
                        if (courseOrderCount <= 0 && giftCourseRecordResult.getGiveType() == 1) {
                            //自选课程
                            com.wendao101.common.core.ktdto.CourseInfoDTO cDto = new CourseInfoDTO();
                            cDto.setPrice(courseAudit.getPrice());
                            cDto.setTitle(courseAudit.getTitle());
                            cDto.setOriginalPrice(courseAudit.getOriginalPrice());
                            cDto.setPublishPlatform(courseAudit.getPublishPlatform());
                            cDto.setCoverPicUrl(courseAudit.getCoverPicUrl());
                            cDto.setId(courseAudit.getId());
                            courseInfoDTOList.add(cDto);
                        }
                        if (courseOrderCount <= 0 && giftCourseRecordResult.getGiveType() == 0) {
                            //获取价格
                            BigDecimal price = new BigDecimal(0);
                            //创建订单
                            CourseOrder courseOrderGive = new CourseOrder();
                            String yyyyMMdd = DateUtils.dateTime();
                            String giveOrderId = yyyyMMdd + courseOrder.getBuyerUserId() + String.format("%04d", redisService.incr(yyyyMMdd, courseOrder.getBuyerUserId()));
                            giveOrderIdList.add(giveOrderId);
                            courseOrderGive.setOrderId(giveOrderId);
                            Integer courseDuration = courseAudit.getCourseDuration();
                            courseOrderGive.setCourseDuration(courseDuration == null ? 0L : courseDuration.longValue());
                            courseOrderGive.setCourseId(giveCourseId);
                            courseOrderGive.setTeacherId(courseAudit.getTeacherId());
                            courseOrderGive.setCourseImgUrl(courseAudit.getCoverPicUrl());
                            courseOrderGive.setValidity(courseAudit.getExpirationDay());
                            courseOrderGive.setCourseTitle(courseAudit.getTitle());
                            courseOrderGive.setCoursePrice(courseAudit.getPrice());
                            courseOrderGive.setAppNameType(courseOrder.getAppNameType());
                            courseOrderGive.setPayPrice(price);
                            //支付时间
                            courseOrderGive.setPayTime(new Date());
                            //非推广订单
                            courseOrderGive.setIsPromoter(0);
                            //9赠送课程订单
                            courseOrderGive.setOrderType(9);
                            //是否赠送课程
                            courseOrderGive.setIsCourse(1);
                            courseOrderGive.setOriginalPrice(courseAudit.getOriginalPrice());
                            //直接设置为已支付
                            courseOrderGive.setOrderStatus(1);
                            courseOrderGive.setOrderTime(new Date());
                            courseOrderGive.setFundsType(1);
                            courseOrderGive.setOrderPlatform(platform);
                            courseOrderGive.setBuyerUserId(courseOrder.getBuyerUserId());
                            courseOrderGive.setBuyerUserImg(courseOrder.getBuyerUserImg());
                            courseOrderGive.setBuyerUserName(courseOrder.getBuyerUserName());
                            courseOrderGive.setBuyerUserMobile(courseOrder.getBuyerUserMobile());
                            courseOrderGive.setMyEarningsPrice(price);
                            int rowGive = insertCourseOrder(courseOrderGive);
                            if (rowGive > 0) {
                                BuyCourseGiveOrder buyCourseGiveOrder = new BuyCourseGiveOrder();
                                buyCourseGiveOrder.setTeacherId(courseOrderGive.getTeacherId());
                                buyCourseGiveOrder.setOrderId(courseOrderGive.getOrderId());
                                buyCourseGiveOrder.setOrderStatus(courseOrderGive.getOrderStatus());
                                buyCourseGiveOrderService.insertBuyCourseGiveOrder(buyCourseGiveOrder);
                                //获取订单手机号码
                                String telNumber = courseOrder.getBuyerUserMobile();
                                if(StringUtils.isNotBlank(telNumber)){
                                    //发送赠课短信
                                    sendSmsAndCache(courseOrderGive.getOrderId(),"一门",telNumber);
                                }
                                System.out.println("创建赠课订单成功!订单号:" + giveOrderId);
                            } else {
                                System.out.println("创建赠课订单失败!订单号:" + giveOrderId);
                            }
                        }
                    }
                }
            }
        }
        //自选课程
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(courseInfoDTOList) && giveCourseUserChoose.getGiveNumMax() > 0) {
            giveCourseUserChoose.setOrderId(courseOrder.getOrderId());
            giveCourseUserChoose.setCourseInfoList(courseInfoDTOList);
            redisService.setCacheObject(give_order_user_choose_course_list + courseOrder.getOrderId(), giveCourseUserChoose);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(giveOrderIdList)) {
            redisService.setCacheList(give_order_id_cache_list + courseOrder.getOrderId(), giveOrderIdList);
        }
    }

    private void sendSmsAndCache(String orderId, String courseName, String telNumber) {
        //产生8为短信数字的code
        String jumpCode = RandomStringUtils.randomNumeric(8);
        sendSms(telNumber, jumpCode);
        //缓存30天
        redisService.setCacheObject(course_give_away + jumpCode, orderId, 720L, TimeUnit.DAYS);
    }

    private void sendSms(String phoneNumber, String jumpCode) {
        try {
            // HttpClient Configuration
            /*HttpClient httpClient = new ApacheAsyncHttpClientBuilder()
                    .connectionTimeout(Duration.ofSeconds(10)) // Set the connection timeout time, the default is 10 seconds
                    .responseTimeout(Duration.ofSeconds(10)) // Set the response timeout time, the default is 20 seconds
                    .maxConnections(128) // Set the connection pool size
                    .maxIdleTimeOut(Duration.ofSeconds(50)) // Set the connection pool timeout, the default is 30 seconds
                    // Configure the proxy
                    .proxy(new ProxyOptions(ProxyOptions.Type.HTTP, new InetSocketAddress("<your-proxy-hostname>", 9001))
                            .setCredentials("<your-proxy-username>", "<your-proxy-password>"))
                    // If it is an https connection, you need to configure the certificate, or ignore the certificate(.ignoreSSL(true))
                    .x509TrustManagers(new X509TrustManager[]{})
                    .keyManagers(new KeyManager[]{})
                    .ignoreSSL(false)
                    .build();*/

            // Configure Credentials authentication information, including ak, secret, token
            StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                    // Please ensure that the environment variables ALIBABA_CLOUD_ACCESS_KEY_ID and ALIBABA_CLOUD_ACCESS_KEY_SECRET are set.
                    .accessKeyId("LTAI5tRfCNfcpvFCRaM1ydiy")
                    .accessKeySecret("******************************")
                    //.securityToken(System.getenv("ALIBABA_CLOUD_SECURITY_TOKEN")) // use STS token
                    .build());

            // Configure the Client
            AsyncClient client = AsyncClient.builder()
                    .region("cn-hangzhou") // Region ID
                    //.httpClient(httpClient) // Use the configured HttpClient, otherwise use the default HttpClient (Apache HttpClient)
                    .credentialsProvider(provider)
                    //.serviceConfiguration(Configuration.create()) // Service-level configuration
                    // Client-level configuration rewrite, can set Endpoint, Http request parameters, etc.
                    .overrideConfiguration(
                            ClientOverrideConfiguration.create()
                                    // Endpoint 请参考 https://api.aliyun.com/product/Dysmsapi
                                    .setEndpointOverride("dysmsapi.aliyuncs.com")
                            //.setConnectTimeout(Duration.ofSeconds(30))
                    )
                    .build();

            // Parameter settings for API request
            SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                    .signName("问到")
                    .templateCode("SMS_479050367")
                    .phoneNumbers(phoneNumber)
                    .templateParam("{\"wxcode\":\"" + jumpCode + "\"}")
                    // Request-level configuration rewrite, can set Http request parameters, etc.
                    // .requestConfiguration(RequestConfiguration.create().setHttpHeaders(new HttpHeaders()))
                    .build();

            // Asynchronously get the return value of the API request
            CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);
            // Synchronously get the return value of the API request
            SendSmsResponse resp = response.get();
            System.out.println(new Gson().toJson(resp));
            // Asynchronous processing of return values
            /*response.thenAccept(resp -> {
                System.out.println(new Gson().toJson(resp));
            }).exceptionally(throwable -> { // Handling exceptions
                System.out.println(throwable.getMessage());
                return null;
            });*/

            // Finally, close the client
            client.close();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int updateCourseOrderWhereOrderStatusIsNotPay(CourseOrder courseOrder) {
        return courseOrderMapper.updateCourseOrderWhereOrderStatusIsNotPay(courseOrder);
    }

    @Override
    public Long selectCourseIdNumberByCourseId(Long courseId) {
        return courseOrderMapper.selectCourseIdNumberByCourseId(courseId);
    }

    @Override
    public int updateDdSmsStatus(Integer smsStatus,String orderId) {
        return courseOrderMapper.updateDdSmsStatus(smsStatus,orderId);
    }

    @Override
    public int updateCourseOrderHeXiaoStatus(String orderId, Integer heXiaoStatus) {
        return courseOrderMapper.updateCourseOrderHeXiaoStatus(orderId,heXiaoStatus);
    }

    @Override
    public void updateCourseOrderNotTwo(String orderId, Integer newStatus) {
        courseOrderMapper.updateCourseOrderNotTwo(orderId,newStatus);
    }

}
