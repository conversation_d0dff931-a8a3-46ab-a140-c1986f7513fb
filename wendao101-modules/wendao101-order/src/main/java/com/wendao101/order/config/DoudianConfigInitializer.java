package com.wendao101.order.config;

import com.doudian.open.core.GlobalConfig;
import com.wendao101.order.domain.DoudianShopConfig;
import com.wendao101.order.service.IDoudianShopConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import javax.annotation.PostConstruct;
import java.util.List;

@Configuration
@EnableScheduling
public class DoudianConfigInitializer {

    @Autowired
    private IDoudianShopConfigService doudianShopConfigService;

    @PostConstruct
    public void init() {
        initializeDoudianConfig();
    }

    /**
     * 每一分钟执行一次
     */
    @Scheduled(fixedDelayString = "${doudian.config.update.interval:60000}")
    public void scheduledInitializeDoudianConfig() {
        initializeDoudianConfig();
    }

    private void initializeDoudianConfig() {
        List<DoudianShopConfig> configs = doudianShopConfigService.getAllUniqueDoudianShopConfigs();
        for (DoudianShopConfig config : configs) {
            String configName = config.getAppKey();
            GlobalConfig.getGlobalConfig(configName).setAppKey(config.getAppKey());
            GlobalConfig.getGlobalConfig(configName).setAppSecret(config.getAppSecret());
        }
        System.out.println("抖店配置已更新，共加载 " + configs.size() + " 个唯一配置");
    }
}