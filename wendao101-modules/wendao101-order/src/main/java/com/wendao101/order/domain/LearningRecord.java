package com.wendao101.order.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 学习时长记录对象 learning_record
 * 
 * <AUTHOR>
 * @date 2023-09-13
 */
public class LearningRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 目录id */
    @Excel(name = "目录id")
    private Long courseDirectoryId;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 当天日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "当天日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date timeDays;

    /** 学习位置 */
    @Excel(name = "学习位置")
    private Long learningLocation;

    /** 累计学习时长 */
    @Excel(name = "累计学习时长")
    private Long cumulativeLearningTime;

    /** 上报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "上报时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date reportingTime;

    /** 平台 0抖音 1微信 2快手 3视频号 */
    @Excel(name = "平台 0抖音 1微信 2快手 3视频号")
    private Integer platform;

    /** 课程类型 1普通课程 2 直播课 */
    @Excel(name = "课程类型 1普通课程 2 直播课")
    private Integer type;

    /** 直播观看时长 */
    @Excel(name = "直播观看时长")
    private Long liveViewingTime;

    /** 回放观看时长 */
    @Excel(name = "回放观看时长")
    private Long playbackViewingTime;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCourseDirectoryId(Long courseDirectoryId) 
    {
        this.courseDirectoryId = courseDirectoryId;
    }

    public Long getCourseDirectoryId() 
    {
        return courseDirectoryId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setTimeDays(Date timeDays) 
    {
        this.timeDays = timeDays;
    }

    public Date getTimeDays() 
    {
        return timeDays;
    }
    public void setLearningLocation(Long learningLocation) 
    {
        this.learningLocation = learningLocation;
    }

    public Long getLearningLocation() 
    {
        return learningLocation;
    }
    public void setCumulativeLearningTime(Long cumulativeLearningTime) 
    {
        this.cumulativeLearningTime = cumulativeLearningTime;
    }

    public Long getCumulativeLearningTime() 
    {
        return cumulativeLearningTime;
    }
    public void setReportingTime(Date reportingTime) 
    {
        this.reportingTime = reportingTime;
    }

    public Date getReportingTime() 
    {
        return reportingTime;
    }
    public void setPlatform(Integer platform) 
    {
        this.platform = platform;
    }

    public Integer getPlatform() 
    {
        return platform;
    }
    public void setType(Integer type) 
    {
        this.type = type;
    }

    public Integer getType() 
    {
        return type;
    }
    public void setLiveViewingTime(Long liveViewingTime) 
    {
        this.liveViewingTime = liveViewingTime;
    }

    public Long getLiveViewingTime() 
    {
        return liveViewingTime;
    }
    public void setPlaybackViewingTime(Long playbackViewingTime) 
    {
        this.playbackViewingTime = playbackViewingTime;
    }

    public Long getPlaybackViewingTime() 
    {
        return playbackViewingTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("courseDirectoryId", getCourseDirectoryId())
            .append("userId", getUserId())
            .append("orderId", getOrderId())
            .append("timeDays", getTimeDays())
            .append("learningLocation", getLearningLocation())
            .append("cumulativeLearningTime", getCumulativeLearningTime())
            .append("reportingTime", getReportingTime())
            .append("platform", getPlatform())
            .append("type", getType())
            .append("liveViewingTime", getLiveViewingTime())
            .append("playbackViewingTime", getPlaybackViewingTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
