package com.wendao101.order.vo;

import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/13
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AlipayRefundVO extends BaseEntity {

    private Long teacherId;
    /**
     * 投诉退款编号
     */
    private String complaintRefundNumber;
    /**
     * 退款人（投诉人）姓名
     */
    private String alipayName;
    /**
     * 提现(支付宝)账号
     */
    private String alipayAccount;
    /**
     * 退款订单的id
     */
    private String orderId;
    /**
     * 退款金额
     */
    private BigDecimal refundMoney;
    /**
     * 退款来源
     */
    private Integer source;

    /**
     * 凭证
     */
    private String evidence;


    /**
     * 退款备注
     */
    private String remark;

    /**
     * 落地公司名称
     */
    private String landingCompanyName;


    private Integer complaintId;





}
