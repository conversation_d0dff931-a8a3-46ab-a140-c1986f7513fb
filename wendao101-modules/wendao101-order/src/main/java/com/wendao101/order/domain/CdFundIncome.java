package com.wendao101.order.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 抽单资金收益对象 cd_fund_income
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
public class CdFundIncome extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 收入来源 0抖音 1微信 2快手  3视频号 */
    @Excel(name = "收入来源 0抖音 1微信 2快手  3视频号")
    private Integer incomePlatform;

    /** 资金类型 0 直推收入 1 分销收入 2推广收入 3平台提现 4退款 5问到精选收入 */
    @Excel(name = "资金类型 0 直推收入 1 分销收入 2推广收入 3平台提现 4退款 5问到精选收入")
    private Integer incomeType;

    /** 订单数量 */
    @Excel(name = "订单数量")
    private Integer orderNum;

    /** 资金状态 0 在途 1已入账 */
    @Excel(name = "资金状态 0 在途 1已入账")
    private Integer fundsType;

    /** 金额 */
    @Excel(name = "金额")
    private BigDecimal incomePrice;

    /** 真实收入（扣除服务费） */
    @Excel(name = "真实收入（扣除服务费）")
    private BigDecimal realIncomePrice;
    
    /** 出版物扣点（默认2%） */
    @Excel(name = "出版物扣点（默认2%）")
    private Integer publicationRate;
    
    /** 出版物扣费金额 */
    @Excel(name = "出版物扣费金额")
    private BigDecimal publicationFee;
    
    /** 物流费用扣减金额 */
    @Excel(name = "物流费用扣减金额")
    private BigDecimal logisticsFee;
    
    /** 直播带货佣金比例 */
    @Excel(name = "直播带货佣金比例")
    private Integer liveCommerceRate;
    
    /** 直播带货佣金金额 */
    @Excel(name = "直播带货佣金金额")
    private BigDecimal liveCommerceFee;

    /** 账号金额是否分账 0否 1是 */
    @Excel(name = "账号金额是否分账 0否 1是")
    private Integer accountPriceType;

    /** 店铺收益金额 */
    @Excel(name = "店铺收益金额")
    private BigDecimal storeIncomePrice;

    /** 店铺名称 */
    @Excel(name = "店铺名称")
    private String storeName;

    /** 服务费 */
    @Excel(name = "服务费")
    private BigDecimal servicePrice;

    /** 服务比率 */
    @Excel(name = "服务比率")
    private Integer servicePriceRatio;

    /** 订单用户实付金额 */
    @Excel(name = "订单用户实付金额")
    private BigDecimal payPrice;

    /** 订单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "订单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 入账时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "入账时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date incomePriceTime;

    /** 是否删除 0否 1是 */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;
    
    public void setRealIncomePrice(BigDecimal realIncomePrice) 
    {
        this.realIncomePrice = realIncomePrice;
    }

    public BigDecimal getRealIncomePrice() 
    {
        return realIncomePrice;
    }
    
    public void setPublicationRate(Integer publicationRate) 
    {
        this.publicationRate = publicationRate;
    }

    public Integer getPublicationRate() 
    {
        return publicationRate;
    }
    
    public void setPublicationFee(BigDecimal publicationFee) 
    {
        this.publicationFee = publicationFee;
    }

    public BigDecimal getPublicationFee() 
    {
        return publicationFee;
    }
    
    public void setLogisticsFee(BigDecimal logisticsFee) 
    {
        this.logisticsFee = logisticsFee;
    }

    public BigDecimal getLogisticsFee() 
    {
        return logisticsFee;
    }
    
    public void setLiveCommerceRate(Integer liveCommerceRate) 
    {
        this.liveCommerceRate = liveCommerceRate;
    }

    public Integer getLiveCommerceRate() 
    {
        return liveCommerceRate;
    }
    
    public void setLiveCommerceFee(BigDecimal liveCommerceFee) 
    {
        this.liveCommerceFee = liveCommerceFee;
    }

    public BigDecimal getLiveCommerceFee() 
    {
        return liveCommerceFee;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setPromoterId(Long promoterId) 
    {
        this.promoterId = promoterId;
    }

    public Long getPromoterId() 
    {
        return promoterId;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setIncomePlatform(Integer incomePlatform) 
    {
        this.incomePlatform = incomePlatform;
    }

    public Integer getIncomePlatform() 
    {
        return incomePlatform;
    }
    public void setIncomeType(Integer incomeType) 
    {
        this.incomeType = incomeType;
    }

    public Integer getIncomeType() 
    {
        return incomeType;
    }
    public void setOrderNum(Integer orderNum) 
    {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() 
    {
        return orderNum;
    }
    public void setFundsType(Integer fundsType) 
    {
        this.fundsType = fundsType;
    }

    public Integer getFundsType() 
    {
        return fundsType;
    }
    public void setIncomePrice(BigDecimal incomePrice) 
    {
        this.incomePrice = incomePrice;
    }

    public BigDecimal getIncomePrice() 
    {
        return incomePrice;
    }
    public void setAccountPriceType(Integer accountPriceType) 
    {
        this.accountPriceType = accountPriceType;
    }

    public Integer getAccountPriceType() 
    {
        return accountPriceType;
    }
    public void setStoreIncomePrice(BigDecimal storeIncomePrice) 
    {
        this.storeIncomePrice = storeIncomePrice;
    }

    public BigDecimal getStoreIncomePrice() 
    {
        return storeIncomePrice;
    }
    public void setStoreName(String storeName) 
    {
        this.storeName = storeName;
    }

    public String getStoreName() 
    {
        return storeName;
    }
    public void setServicePrice(BigDecimal servicePrice) 
    {
        this.servicePrice = servicePrice;
    }

    public BigDecimal getServicePrice() 
    {
        return servicePrice;
    }
    public void setServicePriceRatio(Integer servicePriceRatio)
    {
        this.servicePriceRatio = servicePriceRatio;
    }

    public Integer getServicePriceRatio()
    {
        return servicePriceRatio;
    }
    
    public void setPayPrice(BigDecimal payPrice)
    {
        this.payPrice = payPrice;
    }
    
    public BigDecimal getPayPrice()
    {
        return payPrice;
    }
    
    public void setOrderTime(Date orderTime) 
    {
        this.orderTime = orderTime;
    }

    public Date getOrderTime() 
    {
        return orderTime;
    }
    public void setIncomePriceTime(Date incomePriceTime) 
    {
        this.incomePriceTime = incomePriceTime;
    }

    public Date getIncomePriceTime() 
    {
        return incomePriceTime;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("promoterId", getPromoterId())
            .append("orderId", getOrderId())
            .append("incomePlatform", getIncomePlatform())
            .append("incomeType", getIncomeType())
            .append("orderNum", getOrderNum())
            .append("fundsType", getFundsType())
            .append("incomePrice", getIncomePrice())
            .append("realIncomePrice", getRealIncomePrice())
            .append("publicationRate", getPublicationRate())
            .append("publicationFee", getPublicationFee())
            .append("logisticsFee", getLogisticsFee())
            .append("liveCommerceRate", getLiveCommerceRate())
            .append("liveCommerceFee", getLiveCommerceFee())
            .append("accountPriceType", getAccountPriceType())
            .append("storeIncomePrice", getStoreIncomePrice())
            .append("storeName", getStoreName())
            .append("servicePrice", getServicePrice())
            .append("servicePriceRatio", getServicePriceRatio())
            .append("payPrice", getPayPrice())
            .append("remark", getRemark())
            .append("orderTime", getOrderTime())
            .append("incomePriceTime", getIncomePriceTime())
            .append("isDelete", getIsDelete())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
