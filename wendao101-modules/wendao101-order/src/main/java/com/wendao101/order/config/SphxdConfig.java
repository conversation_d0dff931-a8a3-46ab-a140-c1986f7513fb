package com.wendao101.order.config;

import me.chanjar.weixin.channel.api.WxChannelService;
import me.chanjar.weixin.channel.api.impl.WxChannelServiceImpl;
import me.chanjar.weixin.channel.config.WxChannelConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class SphxdConfig {
    @Bean("firstWxChannelService")
    public WxChannelService wxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wx72e0b2963317efe0");
        config.setSecret("c43347ebf4c42d9b3f317b0f21d95805");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    @Bean("secondWxChannelService")
    public WxChannelService secondWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wx96bf6060667a8612");
        config.setSecret("718cc8db79997fc988dddd571300e733");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    /**
     * 三色灯
     * @return
     */
    @Bean("thirdWxChannelService")
    public WxChannelService thirdWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wx36e9064e4c37a9fa");
        config.setSecret("f14238d849a64019d36487625e0c5b95");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    /**
     * 三色灯
     * @return
     */
    @Bean("forthWxChannelService")
    public WxChannelService forthWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wxe1409cfcac78593f");
        config.setSecret("dae9371e8192998762fd5fe61e131de2");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    /**
     * 问到课堂
     * @return
     */
    @Bean("fifthWxChannelService")
    public WxChannelService fifthWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wxc7956c35352983bb");
        config.setSecret("1e589cf2049af89251ceea41a0a93635");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    /**
     * 成美知识小店
     * @return
     */
    @Bean("sixthWxChannelService")
    public WxChannelService sixthWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wxe4184e53d3c580a3");
        config.setSecret("ebda972f53883c90e90ab43250d46db6");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }

    /**
     * 鹿鸣国乐笛箫社
     * @return
     */
    @Bean("seventhWxChannelService")
    public WxChannelService seventhWxChannelService() {
        SphxdConfigBean config = new SphxdConfigBean();
        config.setToken("asdfadfad");
        config.setAesKey("Wd51BsOTsAnbUhjv39rqCBkcsm9nTcrcFmKxCysZu92");
        config.setAppid("wx5a0ed8bacbf57c8a");
        config.setSecret("3d2ecec85f9d744f2f4964fbe94eac68");
        config.setMsgDataFormat("JSON");
        WxChannelService service = new WxChannelServiceImpl();
        service.setConfig(config);
        return service;
    }
}
