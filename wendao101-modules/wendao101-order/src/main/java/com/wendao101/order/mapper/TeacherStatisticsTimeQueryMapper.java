package com.wendao101.order.mapper;

import com.wendao101.order.domain.TeacherStatisticsTimeQuery;
import java.util.List;

/**
 * 教师统计时间查询Mapper接口
 * 
 * <AUTHOR>
 */
public interface TeacherStatisticsTimeQueryMapper {
    /**
     * 查询教师统计时间查询
     *
     * @param id 教师统计时间查询主键
     * @return 教师统计时间查询
     */
    public TeacherStatisticsTimeQuery selectTeacherStatisticsTimeQueryById(Long id);

    /**
     * 查询教师统计时间查询列表
     *
     * @param teacherStatisticsTimeQuery 教师统计时间查询
     * @return 教师统计时间查询集合
     */
    public List<TeacherStatisticsTimeQuery> selectTeacherStatisticsTimeQueryList(TeacherStatisticsTimeQuery teacherStatisticsTimeQuery);

    /**
     * 新增教师统计时间查询
     *
     * @param teacherStatisticsTimeQuery 教师统计时间查询
     * @return 结果
     */
    public int insertTeacherStatisticsTimeQuery(TeacherStatisticsTimeQuery teacherStatisticsTimeQuery);

    /**
     * 修改教师统计时间查询
     *
     * @param teacherStatisticsTimeQuery 教师统计时间查询
     * @return 结果
     */
    public int updateTeacherStatisticsTimeQuery(TeacherStatisticsTimeQuery teacherStatisticsTimeQuery);

    /**
     * 删除教师统计时间查询
     *
     * @param id 教师统计时间查询主键
     * @return 结果
     */
    public int deleteTeacherStatisticsTimeQueryById(Long id);

    /**
     * 批量删除教师统计时间查询
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTeacherStatisticsTimeQueryByIds(Long[] ids);

    TeacherStatisticsTimeQuery selectNewTeacherStatisticsTimeQuery();

    List<TeacherStatisticsTimeQuery> selectEmployeeStatisticsTimeQueryList(TeacherStatisticsTimeQuery teacherStatisticsTimeQuery);

    TeacherStatisticsTimeQuery selectNewEmployeeStatisticsTimeQuery();

    int insertEmployeeStatisticsTimeQuery(TeacherStatisticsTimeQuery teacherStatisticsTimeQuery);

    TeacherStatisticsTimeQuery selectEmployeeStatisticsTimeQueryById(Long timeQueryId);
}