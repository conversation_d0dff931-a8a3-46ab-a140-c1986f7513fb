package com.wendao101.order.domain;

import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
public class DdHexiaoWhitelist extends BaseEntity {
    private static final long serialVersionUID = 1L;
    // 主键id
    private Long id;
    // 老师id
    private Long teacherId;
    // 老师手机号码
    private String mobile;
    // 店铺名称
    private String shopName;
    // 开启状态,1开启,2关闭
    private Integer openStatus;
    private String teacherInfo;
    private String teacherName;
    private Integer appNameType;
}
