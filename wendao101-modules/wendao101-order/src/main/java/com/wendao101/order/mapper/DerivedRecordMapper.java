package com.wendao101.order.mapper;

import java.util.List;
import com.wendao101.order.domain.DerivedRecord;

/**
 * 导出记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-09-05
 */
public interface DerivedRecordMapper 
{
    /**
     * 查询导出记录
     * 
     * @param id 导出记录主键
     * @return 导出记录
     */
    public DerivedRecord selectDerivedRecordById(Long id);

    /**
     * 查询导出记录列表
     * 
     * @param derivedRecord 导出记录
     * @return 导出记录集合
     */
    public List<DerivedRecord> selectDerivedRecordList(DerivedRecord derivedRecord);

    /**
     * 新增导出记录
     * 
     * @param derivedRecord 导出记录
     * @return 结果
     */
    public int insertDerivedRecord(DerivedRecord derivedRecord);

    /**
     * 修改导出记录
     * 
     * @param derivedRecord 导出记录
     * @return 结果
     */
    public int updateDerivedRecord(DerivedRecord derivedRecord);

    /**
     * 删除导出记录
     * 
     * @param id 导出记录主键
     * @return 结果
     */
    public int deleteDerivedRecordById(Long id);

    /**
     * 批量删除导出记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDerivedRecordByIds(Long[] ids);

    void insertDerivedRecordBackend(DerivedRecord derivedRecord);
}
