package com.wendao101.order.service;

import java.util.List;
import com.wendao101.order.domain.CourseGiveALike;

/**
 * 课程评论点赞Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface ICourseGiveALikeService 
{
    /**
     * 查询课程评论点赞
     * 
     * @param id 课程评论点赞主键
     * @return 课程评论点赞
     */
    public CourseGiveALike selectCourseGiveALikeById(Long id);

    /**
     * 查询课程评论点赞列表
     * 
     * @param courseGiveALike 课程评论点赞
     * @return 课程评论点赞集合
     */
    public List<CourseGiveALike> selectCourseGiveALikeList(CourseGiveALike courseGiveALike);

    /**
     * 新增课程评论点赞
     * 
     * @param courseGiveALike 课程评论点赞
     * @return 结果
     */
    public int insertCourseGiveALike(CourseGiveALike courseGiveALike);

    /**
     * 修改课程评论点赞
     * 
     * @param courseGiveALike 课程评论点赞
     * @return 结果
     */
    public int updateCourseGiveALike(CourseGiveALike courseGiveALike);

    /**
     * 批量删除课程评论点赞
     * 
     * @param ids 需要删除的课程评论点赞主键集合
     * @return 结果
     */
    public int deleteCourseGiveALikeByIds(Long[] ids);

    /**
     * 删除课程评论点赞信息
     * 
     * @param id 课程评论点赞主键
     * @return 结果
     */
    public int deleteCourseGiveALikeById(Long id);
}
