package com.wendao101.order.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 支付宝转账 错误码对象 alipay_errors
 * 
 * <AUTHOR>
 * @date 2023-09-23
 */
public class AlipayErrors extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String errCode;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String errDesc;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private String handleMethod;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setErrCode(String errCode) 
    {
        this.errCode = errCode;
    }

    public String getErrCode() 
    {
        return errCode;
    }
    public void setErrDesc(String errDesc) 
    {
        this.errDesc = errDesc;
    }

    public String getErrDesc() 
    {
        return errDesc;
    }
    public void setHandleMethod(String handleMethod) 
    {
        this.handleMethod = handleMethod;
    }

    public String getHandleMethod() 
    {
        return handleMethod;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("errCode", getErrCode())
            .append("errDesc", getErrDesc())
            .append("handleMethod", getHandleMethod())
            .toString();
    }
}
