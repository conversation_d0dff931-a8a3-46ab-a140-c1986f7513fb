package com.wendao101.order.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wendao101.common.log.annotation.Log;
import com.wendao101.common.log.enums.BusinessType;
import com.wendao101.common.security.annotation.RequiresPermissions;
import com.wendao101.order.domain.CdCourseOrder;
import com.wendao101.order.service.ICdCourseOrderService;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.page.TableDataInfo;

/**
 * 抽单订单信息Controller
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@RestController
@RequestMapping("/0000order")
public class CdCourseOrderController extends BaseController
{
    @Autowired
    private ICdCourseOrderService cdCourseOrderService;

    /**
     * 查询抽单订单信息列表
     */
    @RequiresPermissions("order:order:list")
    @GetMapping("/list")
    public TableDataInfo list(CdCourseOrder cdCourseOrder)
    {
        startPage();
        List<CdCourseOrder> list = cdCourseOrderService.selectCdCourseOrderList(cdCourseOrder);
        return getDataTable(list);
    }

    /**
     * 导出抽单订单信息列表
     */
    @RequiresPermissions("order:order:export")
    @Log(title = "抽单订单信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CdCourseOrder cdCourseOrder)
    {
        List<CdCourseOrder> list = cdCourseOrderService.selectCdCourseOrderList(cdCourseOrder);
        ExcelUtil<CdCourseOrder> util = new ExcelUtil<CdCourseOrder>(CdCourseOrder.class);
        util.exportExcel(response, list, "抽单订单信息数据");
    }

    /**
     * 获取抽单订单信息详细信息
     */
    @RequiresPermissions("order:order:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(cdCourseOrderService.selectCdCourseOrderById(id));
    }

    /**
     * 新增抽单订单信息
     */
    @RequiresPermissions("order:order:add")
    @Log(title = "抽单订单信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CdCourseOrder cdCourseOrder)
    {
        return toAjax(cdCourseOrderService.insertCdCourseOrder(cdCourseOrder));
    }

    /**
     * 修改抽单订单信息
     */
    @RequiresPermissions("order:order:edit")
    @Log(title = "抽单订单信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CdCourseOrder cdCourseOrder)
    {
        return toAjax(cdCourseOrderService.updateCdCourseOrder(cdCourseOrder));
    }

    /**
     * 删除抽单订单信息
     */
    @RequiresPermissions("order:order:remove")
    @Log(title = "抽单订单信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(cdCourseOrderService.deleteCdCourseOrderByIds(ids));
    }
}
