package com.wendao101.order.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.core.web.page.TableDataInfoWithCountAndMoney;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.order.domain.DdCourseReceiveDeduction;
import com.wendao101.order.domain.DoudianPreDeposit;
import com.wendao101.order.domain.WithdrawRecord;
import com.wendao101.order.service.IDdCourseReceiveDeductionService;
import com.wendao101.order.service.IDoudianPreDepositService;
import com.wendao101.order.service.IWithdrawRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.List;

/**
 * 抖店老师自营课程订单扣点费用Controller
 * 
 * <AUTHOR>
 * @date 2024-10-24
 */
@RestController
@RequestMapping("/course_receive_deduction")
public class DdCourseReceiveDeductionController extends BaseController
{
    @Autowired
    private IDdCourseReceiveDeductionService ddCourseReceiveDeductionService;
    @Autowired
    private IDoudianPreDepositService doudianPreDepositService;
    @Autowired
    private IWithdrawRecordService withdrawRecordService;

    /**
     * 充值类型为在线支付列表
     * @return
     */
    @GetMapping("/onlinePayList")
    public TableDataInfo onlinePayList(){
        Long teacherId = SecurityUtils.getUserId();
        DoudianPreDeposit doudianPreDeposit = new DoudianPreDeposit();
        doudianPreDeposit.setTeacherId(teacherId);
        doudianPreDeposit.setPayStatus(1);
        doudianPreDeposit.setProductName("抖店课程领取预存账户充值");
        startPage();
        List<DoudianPreDeposit> doudianPreDeposits = doudianPreDepositService.selectDoudianPreDepositList(doudianPreDeposit);
        return getDataTable(doudianPreDeposits);
    }

    /**
     * 充值类型为账户划转列表
     * @return
     */
    @GetMapping("/depositFormAccountMoneyList")
    public TableDataInfo depositListFormAccountMoney(){
        Long teacherId = SecurityUtils.getUserId();
        WithdrawRecord withdrawRecord = new WithdrawRecord();
        withdrawRecord.setTeacherId(teacherId);
        withdrawRecord.setAccountType(2);
        withdrawRecord.setRemarkMessage("账户余额转为抖店领课预存款");
        withdrawRecord.setWithdrawStatus(0);
        withdrawRecord.setWithdrawAuditStatus(0);
        startPage();
        List<WithdrawRecord> withdrawRecordList = withdrawRecordService.selectWithdrawRecordList(withdrawRecord);
        return getDataTable(withdrawRecordList);
    }

    /**
     * 查询抖店老师自营课程订单扣点费用列表
     */
    @GetMapping("/list")
    public TableDataInfoWithCountAndMoney list(DdCourseReceiveDeduction ddCourseReceiveDeduction)
    {
        Long teacherId = SecurityUtils.getUserId();
        ddCourseReceiveDeduction.setTeacherId(teacherId);
        startPage();
        List<DdCourseReceiveDeduction> list = ddCourseReceiveDeductionService.selectDdCourseReceiveDeductionList(ddCourseReceiveDeduction);
        //汇总金额
        TableDataInfoWithCountAndMoney dataTableWithRecordCountAndMoney = getDataTableWithRecordCountAndMoney(list);
        //查询已扣款订单数
        int totalSuccessOrderCount = ddCourseReceiveDeductionService.querySuccessOrderCount(ddCourseReceiveDeduction);
        dataTableWithRecordCountAndMoney.setTotalRecordCount(totalSuccessOrderCount);
        BigDecimal totalMoney = ddCourseReceiveDeductionService.queryTotalMoney(ddCourseReceiveDeduction);
        dataTableWithRecordCountAndMoney.setTotalMoney(totalMoney);
        return dataTableWithRecordCountAndMoney;
    }
}
