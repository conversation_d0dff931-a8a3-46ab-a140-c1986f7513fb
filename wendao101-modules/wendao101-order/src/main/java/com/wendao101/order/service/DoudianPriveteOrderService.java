package com.wendao101.order.service;

import com.doudian.open.api.order_orderDetail.data.SkuOrderListItem;
import com.doudian.open.api.product_detail.data.SpecPricesItem;
import com.doudian.open.core.AccessToken;
import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.domain.DoudianCourse;
import com.wendao101.order.domain.DoudianShopConfig;

import java.util.List;
import java.util.concurrent.CompletableFuture;

public interface DoudianPriveteOrderService {

    void modifyFundsInfo(CourseRefund courseRefund, CourseOrder courseOrder);

    void createFundsRecord(CourseOrder courseOrder, DoudianShopConfig doudianShopConfig);

    void createSphFundsRecord(CourseOrder courseOrder,String appId);

    AccessToken getAccessToken(Long shopId);

    void upChannelProduct(DoudianCourse doudianCourse, List<SpecPricesItem> specPricesItems,DoudianShopConfig doudianShopConfig);

    int createChuBanWuOrder(CourseOrder courseOrder, SkuOrderListItem skuOrderListItem);

    void queryChuBanWuAndSetStatus(CourseOrder courseOrder);

    void sphModifyFundsInfo(CourseRefund courseRefund, CourseOrder courseOrder);
    CompletableFuture<Void> whiteListHeXiao(String orderId, Long shopId,String doudianAppKey);

    void onlineChannelProductAsync(DoudianCourse doudianCourse, DoudianShopConfig doudianShopConfig, AccessToken accessToken);

//    List<String> getFixOrders();
}
