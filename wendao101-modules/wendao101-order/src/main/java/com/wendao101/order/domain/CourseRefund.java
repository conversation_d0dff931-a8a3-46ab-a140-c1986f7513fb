package com.wendao101.order.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 退款信息对象 course_refund
 * 
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CourseRefund extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    private Integer complaintType;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;


    private String storeName;

    /** 退款订单编号 */
    @Excel(name = "退款订单编号")
    private String refundId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String courseTitle;

    /** 课程价格 */
    @Excel(name = "课程价格")
    private BigDecimal coursePrice;

    /** 课程划线价格 */
    @Excel(name = "课程划线价格")
    private BigDecimal originalPrice;

    /** 课程图片 */
    @Excel(name = "课程图片")
    private String courseImgUrl;

    /** 购买人id */
    @Excel(name = "购买人id")
    private Long buyerUserId;

    /** 购买人姓名 */
    @Excel(name = "购买人姓名")
    private String buyerUserName;

    /** 购买人手机号 */
    @Excel(name = "购买人手机号")
    private String buyerUserMobile;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 下单时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "下单时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date orderTime;

    /** 退款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 退款状态,0待处理,1已处理 */
    @Excel(name = "退款状态,0待处理,1已处理")
    private Integer refundStatus;

    /**
     * 退款金额
     */
    @Excel(name = "退款金额")
    private BigDecimal refundPrice;

    /** 退款类型,1已退款，2超时自动退款，3已拒绝退款 */
    @Excel(name = "退款状态,1已退款，2超时自动退款，3已拒绝退款")
    private Integer refundType;

    /** 退款平台,1抖音，2快手，3微信，4视频 */
    @Excel(name = "退款平台,1抖音，2快手，3微信，4视频")
    private Integer refundPlatform;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 补充描述 */
    @Excel(name = "补充描述")
    private String supplimentalDescription;

    /** 凭证图片 */
    @Excel(name = "凭证图片")
    private String receiptImg;

    /** 课程总时长 ， 单位是秒 */
    @Excel(name = "课程总时长 ， 单位是秒")
    private Long courseDuration;

    /** 学习时长 ， 单位是秒 */
    @Excel(name = "学习时长 ， 单位是秒")
    private Long studyDuration;

    /** 推广员id */
    @Excel(name = "推广员id")
    private Long promoterId;


    private String promotionRatio;

    /** 推广员名称 */
    @Excel(name = "推广员名称")
    private String promoterName;

    /** 推广员电话 */
    @Excel(name = "推广员电话")
    private String promoterMobile;

    /** 拒绝退款描述 */
    @Excel(name = "拒绝退款描述")
    private String refusalOfRefund;

    /** 拒绝退款图片 */
    @Excel(name = "拒绝退款图片")
    private String refusalOfRefundImg;

    /**
     * 删除状态 0否 1是
     */
    @Excel(name = "删除状态 0否 1是")
    private Integer isDelete;

    private String outRefundId;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date refundAuditDeadline;

    /**
     * 程序类型,来源的APP,1问到好课,2问到课堂
     */
    private Integer appNameType;

}
