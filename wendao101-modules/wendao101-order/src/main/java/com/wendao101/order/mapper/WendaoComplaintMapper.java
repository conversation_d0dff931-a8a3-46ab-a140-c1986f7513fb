package com.wendao101.order.mapper;


import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.domain.WendaoComplaint;
import com.wendao101.order.domain.WendaoComplaintRefund;
import com.wendao101.order.dto.WendaoComplaintDto;
import com.wendao101.order.vo.CourseRefundVO;
import com.wendao101.order.vo.WendaoComplaintRefundVo;
import com.wendao101.order.vo.WendaoComplaintVo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 问到投诉管理Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-07
 */
public interface WendaoComplaintMapper 
{


    int updateHandlingDescByOrderId(WendaoComplaintVo wendaoComplaint);
    /**
     * 查询问到投诉管理
     * 
     * @param  complaintId 立即处理显示该条投诉的所有信息
     * @return 问到投诉管理
     */
    WendaoComplaintDto getComplaintRefundById(@Param("complaintId") Long complaintId);

    String selectBuyerUserMobile(String orderId);
    WendaoComplaintVo getComplaintByComplaintId(@Param("complaintId") Long complaintId);

    WendaoComplaintRefund getRefundById(WendaoComplaintVo wendaoComplaintVo);

    CourseRefundVO getmessageById(@Param("complaintId") Long complaintId);
    /**
     * 查询问到投诉管理列表
     * 
     * @param wendaoComplaint 问到投诉管理
     * @return 问到投诉管理集合
     */
    List<WendaoComplaintDto> selectWendaoComplaintList(WendaoComplaintVo wendaoComplaint);



    /**
     * 新增问到投诉管理
     * 
     * @param wendaoComplaintVo 问到投诉管理
     * @return 结果
     */
    int insertWendaoComplaint(WendaoComplaintVo wendaoComplaintVo);

    /**
     * 问到好课总后台的投诉管理    添加到退款管理中
     * @param wendaoComplaintVo
     * @return
     */
    int insertCourseComplaintRefund(WendaoComplaintVo wendaoComplaintVo);


    CourseRefund selectCourseRefundByOrderId(String orderId);


    int deleteCourseRefundByOrderId(String orderId);

    /**
     * 修改问到投诉管理
     * 
     * @param wendaoComplaintVo 问到投诉管理
     * @return 结果
     */
    int updateWendaoComplaint(WendaoComplaintVo wendaoComplaintVo);


    int updateComplaintStatus(WendaoComplaintVo wendaoComplaintVo);




    int updateOffline(WendaoComplaintVo wendaoComplaintVo);





    /**
     * 线上退款
     * @param wendaoComplaintVo
     * @return
     */
    int onlineRefund(WendaoComplaintVo wendaoComplaintVo);




    int updateStatusById(WendaoComplaintVo wendaoComplaintVo);




    /**
     * 新增问到投诉管理
     *
     * @param wendaoComplaint 问到投诉管理
     * @return 结果
     */
    int insertComplaint(WendaoComplaint wendaoComplaint);

    /**
     * 主动驳回后修改投诉审核表的状态
     */
    int updateWendaoComplaintStatus(WendaoComplaintRefundVo wendaoComplaintRefundVo);

    Date selecctListByOrderId(@Param("orderId") String orderId,@Param("complaintNumber") String complaintNumber);

    BigDecimal selectOrderRealPayMoney(Long complaintId);

}
