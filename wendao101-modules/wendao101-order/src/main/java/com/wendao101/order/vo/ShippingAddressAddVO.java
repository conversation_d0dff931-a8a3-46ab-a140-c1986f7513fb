package com.wendao101.order.vo;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/15
 */
@Data
public class ShippingAddressAddVO {

    private Long id;

    private String uuid;

    /** 用户id */
    @Excel(name = "用户id")
    private Long userId;

    private String openId;

    /** 收货人 */
    @Excel(name = "收货人")
    private String consignee;

    /** 手机号 */
    @Excel(name = "手机号")
    private String telNumber;

    /** 地区 */
    @Excel(name = "地区")
    private String region;

    /** 详细地址 */
    @Excel(name = "详细地址")
    private String detailedAddress;

    /** 是否设置默认地址  0不默认，1默认*/
    @Excel(name = "是否设置默认地址 0不默认，1默认")
    private Integer isDefaultAddress;

    private Date createTime;

    private Date updateTime;

    /**
     * ---物流信息---
     */

    /**订单id*/
    private String orderId;

    /**老师id*/
    private Long teacherId;

    /**课程id*/
    private Long courseId;

    /**支付时间*/
    private Date payTime;

    /**教材信息*/
    private String teachingMaterialName;

    /**教材数量*/
    private Integer teachingMaterialNum;

}
