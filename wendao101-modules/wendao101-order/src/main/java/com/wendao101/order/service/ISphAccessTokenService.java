package com.wendao101.order.service;

import java.util.List;
import com.wendao101.order.domain.SphAccessToken;

/**
 * 问到微信视频号小店access_tokenService接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface ISphAccessTokenService 
{
    /**
     * 查询问到微信视频号小店access_token
     * 
     * @param id 问到微信视频号小店access_token主键
     * @return 问到微信视频号小店access_token
     */
    public SphAccessToken selectSphAccessTokenById(Long id);

    /**
     * 查询问到微信视频号小店access_token列表
     * 
     * @param sphAccessToken 问到微信视频号小店access_token
     * @return 问到微信视频号小店access_token集合
     */
    public List<SphAccessToken> selectSphAccessTokenList(SphAccessToken sphAccessToken);

    SphAccessToken selectSphAccessTokenByAppNameType(Integer appNameType);

    SphAccessToken selectSphAccessTokenByAppId(String appid);
}
