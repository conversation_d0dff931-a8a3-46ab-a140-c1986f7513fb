package com.wendao101.order.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.ResourcePackageMapper;
import com.wendao101.order.domain.ResourcePackage;
import com.wendao101.order.service.IResourcePackageService;

/**
 * 功能资源包Service业务层处理
 */
@Service
public class ResourcePackageServiceImpl implements IResourcePackageService
{
    @Autowired
    private ResourcePackageMapper resourcePackageMapper;

    /**
     * 查询功能资源包
     * 
     * @param resourcePkgId 功能资源包主键
     * @return 功能资源包
     */
    @Override
    public ResourcePackage selectResourcePackageById(Long resourcePkgId)
    {
        return resourcePackageMapper.selectResourcePackageById(resourcePkgId);
    }

    /**
     * 查询功能资源包列表
     * 
     * @param resourcePackage 功能资源包
     * @return 功能资源包
     */
    @Override
    public List<ResourcePackage> selectResourcePackageList(ResourcePackage resourcePackage)
    {
        return resourcePackageMapper.selectResourcePackageList(resourcePackage);
    }

    /**
     * 新增功能资源包
     * 
     * @param resourcePackage 功能资源包
     * @return 结果
     */
    @Override
    public int insertResourcePackage(ResourcePackage resourcePackage)
    {
        return resourcePackageMapper.insertResourcePackage(resourcePackage);
    }

    /**
     * 修改功能资源包
     * 
     * @param resourcePackage 功能资源包
     * @return 结果
     */
    @Override
    public int updateResourcePackage(ResourcePackage resourcePackage)
    {
        return resourcePackageMapper.updateResourcePackage(resourcePackage);
    }

    /**
     * 批量删除功能资源包
     * 
     * @param resourcePkgIds 需要删除的功能资源包主键
     * @return 结果
     */
    @Override
    public int deleteResourcePackageByIds(Long[] resourcePkgIds)
    {
        return resourcePackageMapper.deleteResourcePackageByIds(resourcePkgIds);
    }

    /**
     * 删除功能资源包信息
     * 
     * @param resourcePkgId 功能资源包主键
     * @return 结果
     */
    @Override
    public int deleteResourcePackageById(Long resourcePkgId)
    {
        return resourcePackageMapper.deleteResourcePackageById(resourcePkgId);
    }
} 