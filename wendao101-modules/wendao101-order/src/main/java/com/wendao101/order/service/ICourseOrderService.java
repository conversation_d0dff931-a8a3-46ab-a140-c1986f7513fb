package com.wendao101.order.service;

import java.math.BigDecimal;
import java.util.List;

import com.github.pagehelper.PageInfo;
import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.dto.*;
import com.wendao101.order.vo.*;

/**
 * 订单信息Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface ICourseOrderService 
{
    /**
     * 查询订单信息
     * 
     * @param id 订单信息主键
     * @return 订单信息
     */
    public CourseOrder selectCourseOrderById(Long id);
    public CourseOrder selectCourseOrderByOutOrderNumber(String outOrderNumber);

    /**
     * 查询订单信息列表
     * 
     * @param courseOrder 订单信息
     * @return 订单信息集合
     */
    public List<CourseOrder> selectCourseOrderList(CourseOrder courseOrder);

    /**
     * 新增订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int insertCourseOrder(CourseOrder courseOrder);

    /**
     * 修改订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int updateCourseOrder(CourseOrder courseOrder);

    /**
     * 批量删除订单信息
     * 
     * @param ids 需要删除的订单信息主键集合
     * @return 结果
     */
    public int deleteCourseOrderByIds(Long[] ids);

    /**
     * 删除订单信息信息
     * 
     * @param id 订单信息主键
     * @return 结果
     */
    public int deleteCourseOrderById(Long id);

    /**
     * 检索查询订单信息
     *
     * @param courseOrderVO
     * @return
     */
    PageInfoActual <CourseOrderDTO>selectAllCourseOrderList(CourseOrderVO courseOrderVO);
    PageInfoActual <CourseOrderDTO>selectAllCourseOrderListForCd(CourseOrderVO courseOrderVO);

    /**
     * 检索订单  购买记录
     *
     * @param courseOrderRecordVO
     * @return
     */
    PageInfo<CourseOrderRecordDTO> selectBuyRecord(CourseOrderRecordVO courseOrderRecordVO);

    CourseOrder selectCourseOrderByOrderId(String orderId);

    CourseOrderDTO selectCourseOrderByOrderIdAndTeacherId(String orderId, Long teacherId);

    List<ExportCourseOrderDTO> getExportCourseOrder(CourseOrderExportVO courseOrderExportVO);

    List<ExportPromoterOrderDTO> getExportPromoterOrder(CourseOrderExportVO courseOrderExportVO);

    List<Long> getIsCourse(Long id);

    /**
     * 推广记录
     *
     * @param courseOrderPromoterVO
     * @return
     */
    PageInfo<CourseOrder> selectCourseOrderPromoter(CourseOrderPromoterVO courseOrderPromoterVO);

    /**
     * 线上退款
     * @param courseOrderRefundDTO
     * @return
     */

    int onlineRefund(CourseOrderRefundDTO courseOrderRefundDTO);

    /**
     * 用户投诉退款
     * @param userComplaintDTO
     * @return
     */
    int userComplaint(UserComplaintDTO userComplaintDTO);

    CourseOrder selectCourseOrderByOrderIdAndBuyUserId(Long wendaoUserId, String orderId);

    /**
     * 总后台首页订单数据汇总
     * @param orderDataDTO
     * @return
     */
    List<OrderDataVO> orderData(OrderDataDTO orderDataDTO);

    Integer getTotalOrderNum(OrderDataDTO orderDataDTO);

    BigDecimal getGrossSales(OrderDataDTO orderDataDTO);

    Integer getTotalRefundNum(OrderDataDTO orderDataDTO);

    BigDecimal getRefundAmount(OrderDataDTO orderDataDTO);

    BigDecimal getFundsAmount(OrderDataDTO orderDataDTO);

    BigDecimal getMayWithdrawnAmount(OrderDataDTO orderDataDTO);

    BigDecimal getWithdrawnAmount(OrderDataDTO orderDataDTO);

    BigDecimal getTotalCommission(OrderDataDTO orderDataDTO);

    GiveEntityVo isGiveEntity(Long courseId);

    ShippingAddressAddVO getShippingAddressAddVO(Long buyerUserId);

    CourseOrder selectCourseOrderByOrderIdAndStatus(String orderId);

    TeacherDTO selectTeacherByTeacherId(Long teacherId);

    void updateOderStatusClosedByOrderId(String giveOrderId);

    public void clearGiveInfo(CourseOrder courseOrder);

    public void giveUserCourses(CourseOrder courseOrder);

    int updateCourseOrderWhereOrderStatusIsNotPay(CourseOrder courseOrder);

    Long selectCourseIdNumberByCourseId(Long courseId);

    int updateDdSmsStatus(Integer smsStatus,String orderId);

    int updateCourseOrderHeXiaoStatus(String orderId, Integer heXiaoStatus);

    void updateCourseOrderNotTwo(String orderId, Integer newStatus);
}
