package com.wendao101.order.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.wendao101.order.domain.CourseOrder;
import com.wendao101.order.domain.WendaoComplaint;
import com.wendao101.order.dto.*;
import com.wendao101.order.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 订单信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface CourseOrderMapper 
{
    /**
     * 查询订单信息
     * 
     * @param id 订单信息主键
     * @return 订单信息
     */
    public CourseOrder selectCourseOrderById(Long id);

    public CourseOrder selectCourseOrderByOutOrderNumber(String outOrderNumber);

    /**
     * 查询订单信息列表
     * 
     * @param courseOrder 订单信息
     * @return 订单信息集合
     */
    public List<CourseOrder> selectCourseOrderList(CourseOrder courseOrder);

    /**
     * 新增订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int insertCourseOrder(CourseOrder courseOrder);

    /**
     * 修改订单信息
     * 
     * @param courseOrder 订单信息
     * @return 结果
     */
    public int updateCourseOrder(CourseOrder courseOrder);

    /**
     * 删除订单信息
     * 
     * @param id 订单信息主键
     * @return 结果
     */
    public int deleteCourseOrderById(Long id);

    /**
     * 批量删除订单信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseOrderByIds(Long[] ids);

    /**
     * 检索查询订单信息
     *
     * @param courseOrderVO
     * @return
     */
    List<CourseOrderDTO> selectAllCourseOrderList(CourseOrderVO courseOrderVO);

    /**
     * 已支付数
     *
     * @param courseOrderVO
     * @return
     */
    Long countAmountPaidNum(CourseOrderVO courseOrderVO);

    /**
     * 待支付数
     *
     * @param courseOrderVO
     * @return
     */
    Long countUnpaidAmountNum(CourseOrderVO courseOrderVO);

    /**
     * 已退款数
     *
     * @param courseOrderVO
     * @return
     */
    Long countRefundedNum(CourseOrderVO courseOrderVO);

    /**
     * 拒绝退款
     * @param courseOrderVO
     * @return
     */
    Long refusalRefundedNum(CourseOrderVO courseOrderVO);

    /**
     * 取消订单数
     * @param courseOrderVO
     * @return
     */
    Long CanceldNum(CourseOrderVO courseOrderVO);

    Long ComplaintNum(CourseOrderVO courseOrderVO);


    /**
     * 检索订单  购买记录
     *
     * @param courseOrderRecordVO
     * @return
     */
    List<CourseOrderRecordDTO> selectBuyRecord(CourseOrderRecordVO courseOrderRecordVO);

    /**
     * 按order_id查询订单
     * @param orderId
     * @return
     */
    CourseOrder selectCourseOrderByOrderId(String orderId);

    CourseOrderDTO selectCourseOrderByOrderIdAndTeacherId(@Param("orderId") String orderId, @Param("teacherId")Long teacherId);

    List<ExportCourseOrderDTO> getExportCourseOrder(CourseOrderExportVO courseOrderExportVO);

    List<ExportPromoterOrderDTO> getExportPromoterOrder(CourseOrderExportVO courseOrderExportVO);

    BigDecimal getBuyRecordPrice(CourseOrderRecordVO courseOrderRecordVO);

    List<Long> getIsCourse(@Param("id")Long id);

    /**
     * 推广记录
     *
     * @param courseOrderPromoterVO
     * @return
     */
    List<CourseOrder> selectCourseOrderPromoter(CourseOrderPromoterVO courseOrderPromoterVO);

    /**
     * 线下沟通退款
     * @param courseOrderDTO
     * @return
     */
    CourseOrderDTO selectcourseOrderList(CourseOrderDTO courseOrderDTO);


    /**
     * 修改订单状态
     */

    int updateOrderStatus(CourseOrderDTO courseOrderDTO);


    CourseRefundVO selectAll(@Param("orderId")String orderId);


    /**
     * 用户投诉退款
     */


    WendaoComplaintVo userComplaint(UserComplaintDTO userComplaintDTO);

    /**
     * 根据订单号和购买人id查询订单
     * @param orderId
     * @param buyerUserId
     * @return
     */
    CourseOrder getByNumberAndOpenid(@Param("orderId") String orderId, @Param("buyerUserId") Long buyerUserId);

    /**
     * 根据订单号id查询订单
     * @param orderNumber
     * @return
     */
    CourseOrder getCourseOrderById(String orderNumber);

    CourseOrder selectCourseOrderByOrderIdAndBuyUserId(@Param("buyerUserId")Long wendaoUserId, @Param("orderId")String orderId);


    /**
     * 投诉退款中被主动驳回了，状态修改为已支付
     */
    int updateOrderStatusByOrderId(String orderId);


    List<OrderDataVO> getOrderData(OrderDataDTO orderDataDTO);


    Integer getTotalOrderNum(OrderDataDTO orderDataDTO);

    BigDecimal getGrossSales(OrderDataDTO orderDataDTO);

    BigDecimal gettotalActualAmount(CourseOrderVO courseOrderVO);
    BigDecimal gettotalActualAmountForCD(CourseOrderVO courseOrderVO);

    GiveEntityVo isGiveEntity(@Param("courseId") Long courseId);

    ShippingAddressAddVO getShippingAddressAddVO(@Param("buyerUserId")Long buyerUserId);

    CourseOrder selectCourseOrderByOrderIdAndStatus(@Param("orderId")String orderId);

    TeacherDTO selectTeacherByTeacherId(@Param("teacherId")Long teacherId);

    Long orderRefundReviewNum(CourseOrderVO courseOrderVO);

    List<CourseOrderDTO> selectAllCourseOrderListForCd(CourseOrderVO courseOrderVO);

    Long orderRefundTouShuNum(CourseOrderVO courseOrderVO);

    void updateOderStatusClosedByOrderId(@Param("orderId")String orderId);

    int updateCourseOrderWhereOrderStatusIsNotPay(CourseOrder courseOrder);

    Long selectCourseIdNumberByCourseId(@Param("courseId")Long courseId);

    int updateDdSmsStatus(@Param("smsStatus")Integer smsStatus,@Param("orderId")String orderId);

    int updateCourseOrderHeXiaoStatus(@Param("orderId")String orderId, @Param("heXiaoStatus")Integer heXiaoStatus);

    void updateCourseOrderNotTwo(@Param("orderId")String orderId, @Param("newStatus")Integer newStatus);
}
