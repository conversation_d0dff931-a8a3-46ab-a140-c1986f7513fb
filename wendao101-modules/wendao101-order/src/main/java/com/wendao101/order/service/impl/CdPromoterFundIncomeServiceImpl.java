package com.wendao101.order.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.CdPromoterFundIncomeMapper;
import com.wendao101.order.domain.CdPromoterFundIncome;
import com.wendao101.order.service.ICdPromoterFundIncomeService;

/**
 * 抽单推广员资金收益Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-24
 */
@Service
public class CdPromoterFundIncomeServiceImpl implements ICdPromoterFundIncomeService 
{
    @Autowired
    private CdPromoterFundIncomeMapper cdPromoterFundIncomeMapper;

    /**
     * 查询抽单推广员资金收益
     * 
     * @param id 抽单推广员资金收益主键
     * @return 抽单推广员资金收益
     */
    @Override
    public CdPromoterFundIncome selectCdPromoterFundIncomeById(Long id)
    {
        return cdPromoterFundIncomeMapper.selectCdPromoterFundIncomeById(id);
    }

    /**
     * 查询抽单推广员资金收益列表
     * 
     * @param cdPromoterFundIncome 抽单推广员资金收益
     * @return 抽单推广员资金收益
     */
    @Override
    public List<CdPromoterFundIncome> selectCdPromoterFundIncomeList(CdPromoterFundIncome cdPromoterFundIncome)
    {
        return cdPromoterFundIncomeMapper.selectCdPromoterFundIncomeList(cdPromoterFundIncome);
    }

    /**
     * 新增抽单推广员资金收益
     * 
     * @param cdPromoterFundIncome 抽单推广员资金收益
     * @return 结果
     */
    @Override
    public int insertCdPromoterFundIncome(CdPromoterFundIncome cdPromoterFundIncome)
    {
        //cdPromoterFundIncome.setCreateTime(DateUtils.getNowDate());
        return cdPromoterFundIncomeMapper.insertCdPromoterFundIncome(cdPromoterFundIncome);
    }

    /**
     * 修改抽单推广员资金收益
     * 
     * @param cdPromoterFundIncome 抽单推广员资金收益
     * @return 结果
     */
    @Override
    public int updateCdPromoterFundIncome(CdPromoterFundIncome cdPromoterFundIncome)
    {
        cdPromoterFundIncome.setUpdateTime(DateUtils.getNowDate());
        return cdPromoterFundIncomeMapper.updateCdPromoterFundIncome(cdPromoterFundIncome);
    }

    /**
     * 批量删除抽单推广员资金收益
     * 
     * @param ids 需要删除的抽单推广员资金收益主键
     * @return 结果
     */
    @Override
    public int deleteCdPromoterFundIncomeByIds(Long[] ids)
    {
        return cdPromoterFundIncomeMapper.deleteCdPromoterFundIncomeByIds(ids);
    }

    /**
     * 删除抽单推广员资金收益信息
     * 
     * @param id 抽单推广员资金收益主键
     * @return 结果
     */
    @Override
    public int deleteCdPromoterFundIncomeById(Long id)
    {
        return cdPromoterFundIncomeMapper.deleteCdPromoterFundIncomeById(id);
    }
}
