package com.wendao101.order.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.annotation.ExcelField;
import lombok.Data;

import java.util.Date;

/**
 * 物流信息对象 deliver_goods_order
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
public class ImportDeliverGoodsOrderDTO {

    /**
     * 序号
     */
    @ExcelField(columnName = "序号")
    private String serialNum;

    /**
     * 物流单号
     */
    @ExcelField(columnName = "物流单号")
    private String logisticsNumber;


    /**
     * 收货人手机
     */
    @ExcelField(columnName = "收货人手机")
    private String consigneeMobile;

    /**
     * 收货人名称
     */
    @ExcelField(columnName = "收货人名称")
    private String consigneeName;

    /**
     * 收货人地址
     */
    @ExcelField(columnName = "收货人地址")
    private String consigneeAddr;


    /**
     * 订单号
     */
    @ExcelField(columnName = "订单编号")
    private String orderId;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelField(columnName = "支付时间", columnWidth = 30, format = "yyyy-MM-dd")
    private String payTime;

    /**
     * 教材名称
     */
    @ExcelField(columnName = "教材名称")
    private String teachingMaterialName;

    /**
     * 教材数量
     */
    @ExcelField(columnName = "教材数量")
    private String teachingMaterialNum;

    /**
     * 收货信息状态 0未填写 1已填写
     */
    @ExcelField(columnName = "收货信息状态")
    private String receivingInformationStatus;

    /**
     * 发货状态 0 未发货 1已发货
     */
    @ExcelField(columnName = "发货状态")
    private String shipmentsStatus;

}
