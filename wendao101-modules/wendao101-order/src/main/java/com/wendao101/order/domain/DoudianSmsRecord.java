package com.wendao101.order.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 抖店短信发送记录对象 doudian_sms_record
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
public class DoudianSmsRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 抖店店铺id */
    @Excel(name = "抖店店铺id")
    private Long ddShopId;

    /** 发送时间-时间戳，单位秒 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间-时间戳，单位秒", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 短信内容 */
    @Excel(name = "短信内容")
    private String smsContent;

    /** 1、未回执，2、发送失败，3、发送成功 */
    @Excel(name = "1、未回执，2、发送失败，3、发送成功")
    private String status;

    /** 计费条数，如果短信过长，会分多次计费 */
    @Excel(name = "计费条数，如果短信过长，会分多次计费")
    private Long count;

    /** 错误码 */
    @Excel(name = "错误码")
    private String code;

    /** 错误说明 */
    @Excel(name = "错误说明")
    private String message;

    /** 消息ID */
    @Excel(name = "消息ID")
    private Long messageId;

    /** 透传字段，回执的时候原样返回给调用方。 */
    @Excel(name = "透传字段，回执的时候原样返回给调用方。")
    private String tag;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setDdShopId(Long ddShopId) 
    {
        this.ddShopId = ddShopId;
    }

    public Long getDdShopId() 
    {
        return ddShopId;
    }
    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }
    public void setSmsContent(String smsContent) 
    {
        this.smsContent = smsContent;
    }

    public String getSmsContent() 
    {
        return smsContent;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setCount(Long count) 
    {
        this.count = count;
    }

    public Long getCount() 
    {
        return count;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }
    public void setMessageId(Long messageId) 
    {
        this.messageId = messageId;
    }

    public Long getMessageId() 
    {
        return messageId;
    }
    public void setTag(String tag) 
    {
        this.tag = tag;
    }

    public String getTag() 
    {
        return tag;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("ddShopId", getDdShopId())
            .append("sendTime", getSendTime())
            .append("smsContent", getSmsContent())
            .append("status", getStatus())
            .append("count", getCount())
            .append("code", getCode())
            .append("message", getMessage())
            .append("messageId", getMessageId())
            .append("tag", getTag())
            .append("orderId", getOrderId())
            .append("teacherId", getTeacherId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
