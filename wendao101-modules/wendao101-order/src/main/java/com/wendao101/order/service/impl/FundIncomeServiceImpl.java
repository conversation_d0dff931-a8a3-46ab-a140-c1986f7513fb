package com.wendao101.order.service.impl;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.order.domain.TotalIncome;
import com.wendao101.order.domain.WithdrawPrice;
import com.wendao101.order.dto.ExportFundIncomeDTO;
import com.wendao101.order.dto.FundIncomeDTO;
import com.wendao101.order.dto.FundIncomePlatformDTO;
import com.wendao101.order.dto.TotalIncomeDTO;
import com.wendao101.order.mapper.TotalIncomeMapper;
import com.wendao101.order.mapper.WithdrawPriceMapper;
import com.wendao101.order.mapper.WithdrawRecordMapper;
import com.wendao101.order.vo.FundIncomeExportVO;
import com.wendao101.order.vo.FundIncomePlatformVO;
import com.wendao101.order.vo.FundIncomeVO;
import com.wendao101.order.vo.TotalIncomeVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.FundIncomeMapper;
import com.wendao101.order.domain.FundIncome;
import com.wendao101.order.service.IFundIncomeService;

import javax.annotation.Resource;

/**
 * 资金收益Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class FundIncomeServiceImpl implements IFundIncomeService {

    @Resource
    private FundIncomeMapper fundIncomeMapper;

    @Resource
    private TotalIncomeMapper totalIncomeMapper;

    @Resource
    private WithdrawPriceMapper withdrawPriceMapper;

    /**
     * 查询资金收益
     *
     * @param id 资金收益主键
     * @return 资金收益
     */
    @Override
    public FundIncome selectFundIncomeById(Long id) {
        return fundIncomeMapper.selectFundIncomeById(id);
    }

    /**
     * 查询资金收益列表
     *
     * @param fundIncome 资金收益
     * @return 资金收益
     */
    @Override
    public List<FundIncome> selectFundIncomeList(FundIncome fundIncome) {
        return fundIncomeMapper.selectFundIncomeList(fundIncome);
    }

    /**
     * 新增资金收益
     *
     * @param fundIncome 资金收益
     * @return 结果
     */
    @Override
    public int insertFundIncome(FundIncome fundIncome) {
        fundIncome.setCreateTime(DateUtils.getNowDate());
        return fundIncomeMapper.insertFundIncome(fundIncome);
    }

    /**
     * 修改资金收益
     *
     * @param fundIncome 资金收益
     * @return 结果
     */
    @Override
    public int updateFundIncome(FundIncome fundIncome) {
        fundIncome.setUpdateTime(DateUtils.getNowDate());
        return fundIncomeMapper.updateFundIncome(fundIncome);
    }

    /**
     * 根据订单id修改状态
     *
     * @return 结果
     */
    @Override
    public int updateFundIncomeAndAccountPriceType(String orderId) {
        return fundIncomeMapper.updateFundIncomeAndAccountPriceType(orderId);
    }

    /**
     * 批量删除资金收益
     *
     * @param ids 需要删除的资金收益主键
     * @return 结果
     */
    @Override
    public int deleteFundIncomeByIds(Long[] ids) {
        return fundIncomeMapper.deleteFundIncomeByIds(ids);
    }

    /**
     * 删除资金收益信息
     *
     * @param id 资金收益主键
     * @return 结果
     */
    @Override
    public int deleteFundIncomeById(Long id) {
        return fundIncomeMapper.deleteFundIncomeById(id);
    }

    /**
     * 查询资金列表
     *
     * @param fundIncomeVO
     * @return
     */
    @Override
    public PageInfo<FundIncomeDTO> selectAll(FundIncomeVO fundIncomeVO) {
        PageHelper.startPage(fundIncomeVO.getPageNum(), fundIncomeVO.getPageSize());

        List<FundIncomeDTO> fundIncomeDTOS = fundIncomeMapper.selectAll(fundIncomeVO);

        BigDecimal price = fundIncomeMapper.getIncomePrice(fundIncomeVO);
        if (CollectionUtils.isNotEmpty(fundIncomeDTOS)) {
            fundIncomeDTOS.stream().forEach(fundIncomeDTO -> {
                fundIncomeDTO.setSumIncomePrice(price);
                if (Objects.nonNull(fundIncomeDTO.getPromotionRatio())) {
//                    fundIncomeDTO.setIncomeType(1);
                    fundIncomeDTO.setPromotionRatio(100 - fundIncomeDTO.getPromotionRatio());
                }else {
                    fundIncomeDTO.setPromotionRatio(100);
                }

                if (Objects.nonNull(fundIncomeDTO.getOrderStatus()) && fundIncomeDTO.getOrderStatus() == 2 ){
                    fundIncomeDTO.setFundsType(2);
                }
            });
        }
        return new PageInfo<>(fundIncomeDTOS);
    }

    @Override
    public FundIncomePlatformDTO selectSumPrice(FundIncomePlatformVO fundIncomePlatformVO) {
        FundIncomePlatformDTO result = new FundIncomePlatformDTO();
        List<FundIncome> fundIncomeList = fundIncomeMapper.selectSumPrice(fundIncomePlatformVO);
        if (CollectionUtils.isNotEmpty(fundIncomeList)) {
            //直推收益金额
            result.setDirectPrice(fundIncomeList.stream().filter(fundIncome -> fundIncome.getIncomeType() == 0).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            //分销收益金额
            result.setDistributionPrice(fundIncomeList.stream().filter(fundIncome -> fundIncome.getIncomeType() == 1).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
            //问到精选收益
            result.setWenDaoPrice(fundIncomeList.stream().filter(fundIncome -> fundIncome.getIncomeType() == 5).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add));
        } else {
            //直推收益金额
            result.setDirectPrice(BigDecimal.ZERO);
            //分销收益金额
            result.setDistributionPrice(BigDecimal.ZERO);
            //问到精选收益
            result.setWenDaoPrice(BigDecimal.ZERO);
        }
        return result;
    }

    @Override
    public TotalIncomeDTO selectTotalIncome(TotalIncomeVO totalIncomeVO) {
        TotalIncomeDTO result = new TotalIncomeDTO();
        FundIncome fundIncome = new FundIncome();
        fundIncome.setTeacherId(totalIncomeVO.getTeacherId());
        fundIncome.setIsDelete(0);
        result.setTeacherId(totalIncomeVO.getTeacherId());
        //查出所有平台金额
        List<FundIncome> fundIncomes = fundIncomeMapper.selectFundIncomeList(fundIncome);
        if (CollectionUtils.isNotEmpty(fundIncomes)){
            //获取抖音累计金额
            result.setSumDyPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 0 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取快手累计金额
            result.setSumKsPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 2 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取微信累计金额
            result.setSumWxPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 1 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取视频号累计金额
            result.setSumSphPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 3 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取知识店铺累计金额
            result.setSumZsdpPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 8 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取小红书累计金额
            result.setSumXhsPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 7 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取pc端累计金额
            result.setSumPcPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 9 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取H5端累计金额
            result.setSumWapPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 5 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取抖店端累计金额
            result.setSumDdPrice(fundIncomes.stream().filter(item -> item.getIncomePlatform() == 11 && item.getIncomeType() != 4 && item.getIncomeType() != 3 && !StringUtils.equals(item.getRemark(),"抖店实物发货物流费用") && !StringUtils.equals(item.getRemark(),"出版物扣除2%费用")).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取累计金额
            result.setSumPrice(result.getSumDyPrice()
                    .add(result.getSumKsPrice())
                    .add(result.getSumWxPrice())
                    .add(result.getSumSphPrice())
                    .add(result.getSumZsdpPrice())
                    .add(result.getSumXhsPrice())
                    .add(result.getSumPcPrice())
                    .add(result.getSumWapPrice())
                    .add(result.getSumDdPrice())
            );

            //获取在途金额
            BigDecimal  transitPrice = fundIncomes.stream().filter(item -> item.getIncomeType() != 4 && item.getFundsType() == 0 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取可提现金额
            BigDecimal mayWithdrawPrice = fundIncomes.stream().filter(item -> item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);
            //获取可提现金额
            WithdrawPrice withdrawPrice = withdrawPriceMapper.selectByTeacherId(totalIncomeVO.getTeacherId());
            if (Objects.nonNull(withdrawPrice)){
                //获取在途金额
                result.setFundsTransitPrice(transitPrice);
                if (mayWithdrawPrice.compareTo(BigDecimal.ZERO) != 0){
                    //获取可提现金额
                    result.setMayWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getDyWithdrawnPrice())
                            .subtract(withdrawPrice.getKsWithdrawnPrice())
                            .subtract(withdrawPrice.getWxWithdrawnPrice())
                            .subtract(withdrawPrice.getSphWithdrawnPrice())
                            .subtract(withdrawPrice.getZsdpWithdrawnPrice())
                            .subtract(withdrawPrice.getXhsWithdrawnPrice())
                            .subtract(withdrawPrice.getPcWithdrawnPrice())
                            .subtract(withdrawPrice.getWapWithdrawnPrice())
                            .subtract(withdrawPrice.getDdWithdrawnPrice()));
                    //获取累计提现金额
                    result.setSumAccountPrice(withdrawPrice.getDyWithdrawnPrice()
                            .add(withdrawPrice.getKsWithdrawnPrice())
                            .add(withdrawPrice.getWxWithdrawnPrice())
                            .add(withdrawPrice.getSphWithdrawnPrice())
                            .add(withdrawPrice.getZsdpWithdrawnPrice())
                            .add(withdrawPrice.getXhsWithdrawnPrice())
                            .add(withdrawPrice.getPcWithdrawnPrice())
                            .add(withdrawPrice.getWapWithdrawnPrice())
                            .add(withdrawPrice.getDdWithdrawnPrice()));
                }else {
                    result.setMayWithdrawPrice(BigDecimal.ZERO);
                    result.setSumAccountPrice(BigDecimal.ZERO);
                }
            }else {
                //获取在途金额
                result.setFundsTransitPrice(transitPrice);
                result.setMayWithdrawPrice(mayWithdrawPrice);
                result.setSumAccountPrice(BigDecimal.ZERO);
            }
        }
        //当天时间 00:00:00
        totalIncomeVO.setBeginTime(LocalDateTime.of(LocalDateTime.now() .toLocalDate(), LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        //当天时间 23:59:59
        totalIncomeVO.setEndTime(LocalDateTime.of(LocalDateTime.now() .toLocalDate(), LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        //查询今天所有平台金额
//        List<TotalIncome> todayPrice = totalIncomeMapper.getTodayPrice(totalIncomeVO);
        List<FundIncome> todayPrice = fundIncomeMapper.getTodayPrice(totalIncomeVO);
        if (CollectionUtils.isNotEmpty(todayPrice)){
            //获取今天抖音金额
            result.setDyPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 0 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天快手金额
            result.setKsPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 2 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天微信金额
            result.setWxPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 1 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天视频号金额
            result.setSphPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 3 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天知识店铺金额
            result.setZsdpPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 8 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天小红书金额
            result.setXhsPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 7 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天Pc端金额
            result.setPcPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 9 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天H5端金额
            result.setWapPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 5 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天抖店金额
            result.setDdPrice(todayPrice.stream().filter(item -> item.getIncomePlatform() == 11 && item.getIncomeType() != 4 && item.getIncomeType() != 3).map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO,BigDecimal::add));
            //获取今天累计金额
            result.setTodaySumPrice(result.getDyPrice()
                    .add(result.getKsPrice())
                    .add(result.getWxPrice())
                    .add(result.getSphPrice())
                    .add(result.getZsdpPrice())
                    .add(result.getXhsPrice())
                    .add(result.getPcPrice())
                    .add(result.getWapPrice())
                    .add(result.getDdPrice())
            );
        }

        return result;
    }

    @Override
    public List<ExportFundIncomeDTO> getExportFundIncomeList(FundIncomeExportVO fundIncomeExportVO) {

        return fundIncomeMapper.getExportFundIncomeList(fundIncomeExportVO);
    }

    @Override
    public void updateByOrderId(String orderId,String remark,Integer incomeType) {
        fundIncomeMapper.updateByOrderId(orderId,remark,incomeType);
    }

    @Override
    public FundIncome getFundIncomeByTeacherId(Long teacherId, String orderId) {
        return fundIncomeMapper.getFundIncomeByTeacherId(teacherId,orderId);
    }

    @Override
    public FundIncome getFundIncomeByPromoterId(Long promoterId, String orderId) {
        return fundIncomeMapper.getFundIncomeByPromoterId(promoterId,orderId);
    }

    @Override
    public List<FundIncome> selectFundIncomeByOrderId(String orderId) {
        return fundIncomeMapper.selectFundIncomeByOrderId(orderId);
    }

    @Override
    public List<FundIncome> selectFundIncomeListNew(FundIncome fundIncome) {
        return fundIncomeMapper.selectFundIncomeListNew(fundIncome);
    }
}
