package com.wendao101.order.mapper;

import java.math.BigDecimal;
import java.util.List;

import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.domain.FundIncome;
import com.wendao101.order.dto.ExportFundIncomeDTO;
import com.wendao101.order.dto.FundIncomeDTO;
import com.wendao101.order.vo.FundIncomeExportVO;
import com.wendao101.order.vo.FundIncomePlatformVO;
import com.wendao101.order.vo.FundIncomeVO;
import com.wendao101.order.vo.TotalIncomeVO;
import org.apache.ibatis.annotations.Param;

/**
 * 资金收益Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
public interface FundIncomeMapper 
{
    /**
     * 查询资金收益
     * 
     * @param id 资金收益主键
     * @return 资金收益
     */
    public FundIncome selectFundIncomeById(Long id);

    String selectShopName(Long teacherId);

    /**
     * 查询资金收益列表
     * 
     * @param fundIncome 资金收益
     * @return 资金收益集合
     */
    public List<FundIncome> selectFundIncomeList(FundIncome fundIncome);

    /**
     * 新增资金收益
     * 
     * @param fundIncome 资金收益
     * @return 结果
     */
    public int insertFundIncome(FundIncome fundIncome);

    /**
     * 修改资金收益
     * 
     * @param fundIncome 资金收益
     * @return 结果
     */
    public int updateFundIncome(FundIncome fundIncome);

    /**
     * 根据订单id修改状态
     * @param orderId
     * @return
     */
    public int updateFundIncomeAndAccountPriceType(String orderId);

    /**
     * 删除资金收益
     * 
     * @param id 资金收益主键
     * @return 结果
     */
    public int deleteFundIncomeById(Long id);

    /**
     * 批量删除资金收益
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFundIncomeByIds(Long[] ids);

    /**
     * 查询资金列表
     *
     * @param fundIncomeVO
     * @return
     */
    List<FundIncomeDTO> selectAll(FundIncomeVO fundIncomeVO);

    /**
     * 查询资金累计收益
     *
     * @param fundIncomePlatformVO
     * @return
     */
    public List<FundIncome> selectSumPrice(FundIncomePlatformVO fundIncomePlatformVO);

    /**
     * 资金列表导出
     *
     * @param fundIncomeExportVO
     * @return
     */
    List<ExportFundIncomeDTO> getExportFundIncomeList(FundIncomeExportVO fundIncomeExportVO);

    /**
     * 如果是物流扣除的费用-5则不用退款
     * @param orderId
     * @param remark
     * @param incomeType
     */
    void updateByOrderId(@Param("orderId") String orderId,@Param("remark") String remark,@Param("incomeType")Integer incomeType);
    void updateManageByOrderId(@Param("orderId") String orderId,@Param("remark") String remark,@Param("incomeType")Integer incomeType,@Param("incomePrice") BigDecimal incomePrice);

    BigDecimal getIncomePrice(FundIncomeVO fundIncomeVO);

    List<FundIncome> getTodayPrice(TotalIncomeVO totalIncomeVO);

    FundIncome getFundIncomeByTeacherId(@Param("teacherId") Long teacherId,@Param("orderId") String orderId);

    FundIncome getFundIncomeByPromoterId(@Param("promoterId") Long promoterId, @Param("orderId")String orderId);

    List<FundIncome> selectFundIncomeByOrderId(@Param("orderId")String orderId);

    List<FundIncome> selectFundIncomeListForMoney(@Param("teacherId")Long teacherId);

    List<FundIncome> selectFundIncomeListNew(FundIncome fundIncome);
}
