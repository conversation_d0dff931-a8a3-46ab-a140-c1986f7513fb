package com.wendao101.order.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wendao101.common.log.annotation.Log;
import com.wendao101.common.log.enums.BusinessType;
import com.wendao101.common.security.annotation.RequiresPermissions;
import com.wendao101.order.domain.DoudianSmsRecord;
import com.wendao101.order.service.IDoudianSmsRecordService;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.page.TableDataInfo;

/**
 * 抖店短信发送记录Controller
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/doudian_sms_record")
public class DoudianSmsRecordController extends BaseController
{
    @Autowired
    private IDoudianSmsRecordService doudianSmsRecordService;

    /**
     * 查询抖店短信发送记录列表
     */
    @RequiresPermissions("order:record:list")
    @GetMapping("/list")
    public TableDataInfo list(DoudianSmsRecord doudianSmsRecord)
    {
        startPage();
        List<DoudianSmsRecord> list = doudianSmsRecordService.selectDoudianSmsRecordList(doudianSmsRecord);
        return getDataTable(list);
    }

    /**
     * 导出抖店短信发送记录列表
     */
    @RequiresPermissions("order:record:export")
    @Log(title = "抖店短信发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DoudianSmsRecord doudianSmsRecord)
    {
        List<DoudianSmsRecord> list = doudianSmsRecordService.selectDoudianSmsRecordList(doudianSmsRecord);
        ExcelUtil<DoudianSmsRecord> util = new ExcelUtil<DoudianSmsRecord>(DoudianSmsRecord.class);
        util.exportExcel(response, list, "抖店短信发送记录数据");
    }

    /**
     * 获取抖店短信发送记录详细信息
     */
    @RequiresPermissions("order:record:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(doudianSmsRecordService.selectDoudianSmsRecordById(id));
    }

    /**
     * 新增抖店短信发送记录
     */
    @RequiresPermissions("order:record:add")
    @Log(title = "抖店短信发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DoudianSmsRecord doudianSmsRecord)
    {
        return toAjax(doudianSmsRecordService.insertDoudianSmsRecord(doudianSmsRecord));
    }

    /**
     * 修改抖店短信发送记录
     */
    @RequiresPermissions("order:record:edit")
    @Log(title = "抖店短信发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DoudianSmsRecord doudianSmsRecord)
    {
        return toAjax(doudianSmsRecordService.updateDoudianSmsRecord(doudianSmsRecord));
    }

    /**
     * 删除抖店短信发送记录
     */
    @RequiresPermissions("order:record:remove")
    @Log(title = "抖店短信发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(doudianSmsRecordService.deleteDoudianSmsRecordByIds(ids));
    }
}
