package com.wendao101.order.mapper;

import com.wendao101.order.domain.DdCouponVerify;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 核销异常订单Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-12-14
 */
public interface DdCouponVerifyMapper 
{
    /**
     * 查询核销异常订单
     * 
     * @param id 核销异常订单主键
     * @return 核销异常订单
     */
    public DdCouponVerify selectDdCouponVerifyById(Long id);

    /**
     * 查询核销异常订单列表
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 核销异常订单集合
     */
    public List<DdCouponVerify> selectDdCouponVerifyList(DdCouponVerify ddCouponVerify);

    /**
     * 新增核销异常订单
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 结果
     */
    public int insertDdCouponVerify(DdCouponVerify ddCouponVerify);

    /**
     * 修改核销异常订单
     * 
     * @param ddCouponVerify 核销异常订单
     * @return 结果
     */
    public int updateDdCouponVerify(DdCouponVerify ddCouponVerify);

    /**
     * 删除核销异常订单
     * 
     * @param id 核销异常订单主键
     * @return 结果
     */
    public int deleteDdCouponVerifyById(Long id);

    /**
     * 批量删除核销异常订单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDdCouponVerifyByIds(Long[] ids);

    DdCouponVerify selectDdCouponVerifyByOrderId(@Param("orderId") String orderId);
}
