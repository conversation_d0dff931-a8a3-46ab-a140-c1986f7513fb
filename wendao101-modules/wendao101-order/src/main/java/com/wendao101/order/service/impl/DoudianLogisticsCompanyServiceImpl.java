package com.wendao101.order.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.DoudianLogisticsCompanyMapper;
import com.wendao101.order.domain.DoudianLogisticsCompany;
import com.wendao101.order.service.IDoudianLogisticsCompanyService;

/**
 * 物流公司和codeService业务层处理
 * 
 * <AUTHOR>
 * @date 2024-09-06
 */
@Service
public class DoudianLogisticsCompanyServiceImpl implements IDoudianLogisticsCompanyService 
{
    @Autowired
    private DoudianLogisticsCompanyMapper doudianLogisticsCompanyMapper;

    /**
     * 查询物流公司和code
     * 
     * @param id 物流公司和code主键
     * @return 物流公司和code
     */
    @Override
    public DoudianLogisticsCompany selectDoudianLogisticsCompanyById(Long id)
    {
        return doudianLogisticsCompanyMapper.selectDoudianLogisticsCompanyById(id);
    }

    /**
     * 查询物流公司和code列表
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 物流公司和code
     */
    @Override
    public List<DoudianLogisticsCompany> selectDoudianLogisticsCompanyList(DoudianLogisticsCompany doudianLogisticsCompany)
    {
        return doudianLogisticsCompanyMapper.selectDoudianLogisticsCompanyList(doudianLogisticsCompany);
    }

    /**
     * 新增物流公司和code
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 结果
     */
    @Override
    public int insertDoudianLogisticsCompany(DoudianLogisticsCompany doudianLogisticsCompany)
    {
        return doudianLogisticsCompanyMapper.insertDoudianLogisticsCompany(doudianLogisticsCompany);
    }

    /**
     * 修改物流公司和code
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 结果
     */
    @Override
    public int updateDoudianLogisticsCompany(DoudianLogisticsCompany doudianLogisticsCompany)
    {
        return doudianLogisticsCompanyMapper.updateDoudianLogisticsCompany(doudianLogisticsCompany);
    }

    /**
     * 批量删除物流公司和code
     * 
     * @param ids 需要删除的物流公司和code主键
     * @return 结果
     */
    @Override
    public int deleteDoudianLogisticsCompanyByIds(Long[] ids)
    {
        return doudianLogisticsCompanyMapper.deleteDoudianLogisticsCompanyByIds(ids);
    }

    /**
     * 删除物流公司和code信息
     * 
     * @param id 物流公司和code主键
     * @return 结果
     */
    @Override
    public int deleteDoudianLogisticsCompanyById(Long id)
    {
        return doudianLogisticsCompanyMapper.deleteDoudianLogisticsCompanyById(id);
    }
}
