package com.wendao101.order.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 支付宝 账户对象 account_withdraw
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
public class AccountWithdraw extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 提现账户 0支付宝账户 1公司账户
     */
    @Excel(name = "提现账户 0支付宝账户 1公司账户")
    private Integer accountType;

    /**
     * 账户名称
     */
    @Excel(name = "账户名称")
    private String accountName;

    /**
     * 账户号码
     */
    @Excel(name = "账户号码")
    private String accountPhone;

    /**
     * 开户银行
     */
    @Excel(name = "开户银行")
    private String accountBank;

    /**
     * 平台账户 0抖音 1快手 2微信 3视频号
     */
    @Excel(name = "平台账户 0抖音 1快手 2微信 3视频号")
    private Integer platformType;

    /**
     * 是否默认账户 0否 1是
     */
    @Excel(name = "是否默认账户 0否 1是")
    private Integer isTacitly;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setAccountType(Integer accountType) {
        this.accountType = accountType;
    }

    public Integer getAccountType() {
        return accountType;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountPhone(String accountPhone) {
        this.accountPhone = accountPhone;
    }

    public String getAccountPhone() {
        return accountPhone;
    }

    public void setAccountBank(String accountBank) {
        this.accountBank = accountBank;
    }

    public String getAccountBank() {
        return accountBank;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teacherId", getTeacherId())
                .append("accountType", getAccountType())
                .append("accountName", getAccountName())
                .append("accountPhone", getAccountPhone())
                .append("accountBank", getAccountBank())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
