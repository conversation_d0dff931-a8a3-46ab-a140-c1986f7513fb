package com.wendao101.order.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 自主发货设置对象 doudian_logistics_setting
 * 
 * <AUTHOR>
 * @date 2024-09-07
 */
public class DoudianLogisticsSetting extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 发货设置默认0,0问到发货,1自主发货 */
    @Excel(name = "发货设置默认0,0问到发货,1自主发货")
    private Integer logisticsSetting;

    /** 退货姓名 */
    @Excel(name = "退货姓名")
    private String returnName;

    /** 退货电话 */
    @Excel(name = "退货电话")
    private String returnTel;

    /** 退货地址省份Id */
    @Excel(name = "退货地址省份Id")
    private String provinceId;

    /** 退货地址省份名称 */
    @Excel(name = "退货地址省份名称")
    private String provinceName;

    /** 退货地址城市Id */
    @Excel(name = "退货地址城市Id")
    private String cityId;

    /** 退货地址城市名称 */
    @Excel(name = "退货地址城市名称")
    private String cityName;

    /** 退货地址区/镇Id */
    @Excel(name = "退货地址区/镇Id")
    private String townId;

    /** 退货地址区/镇名称 */
    @Excel(name = "退货地址区/镇名称")
    private String townName;

    /** 退货地址街道Id */
    @Excel(name = "退货地址街道Id")
    private String streetId;

    /** 退货地址街道名称 */
    @Excel(name = "退货地址街道名称")
    private String streetName;

    /** 退货地址详细地址 */
    @Excel(name = "退货地址详细地址")
    private String detail;

    /** 抖店设置的退货地址id */
    @Excel(name = "抖店设置的退货地址id")
    private Long afterSaleAddressId;

    /** 抖店id */
    @Excel(name = "抖店id")
    private String shopId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setLogisticsSetting(Integer logisticsSetting) 
    {
        this.logisticsSetting = logisticsSetting;
    }

    public Integer getLogisticsSetting() 
    {
        return logisticsSetting;
    }
    public void setReturnName(String returnName) 
    {
        this.returnName = returnName;
    }

    public String getReturnName() 
    {
        return returnName;
    }
    public void setReturnTel(String returnTel) 
    {
        this.returnTel = returnTel;
    }

    public String getReturnTel() 
    {
        return returnTel;
    }
    public void setProvinceId(String provinceId) 
    {
        this.provinceId = provinceId;
    }

    public String getProvinceId() 
    {
        return provinceId;
    }
    public void setProvinceName(String provinceName) 
    {
        this.provinceName = provinceName;
    }

    public String getProvinceName() 
    {
        return provinceName;
    }
    public void setCityId(String cityId) 
    {
        this.cityId = cityId;
    }

    public String getCityId() 
    {
        return cityId;
    }
    public void setCityName(String cityName) 
    {
        this.cityName = cityName;
    }

    public String getCityName() 
    {
        return cityName;
    }
    public void setTownId(String townId) 
    {
        this.townId = townId;
    }

    public String getTownId() 
    {
        return townId;
    }
    public void setTownName(String townName) 
    {
        this.townName = townName;
    }

    public String getTownName() 
    {
        return townName;
    }
    public void setStreetId(String streetId) 
    {
        this.streetId = streetId;
    }

    public String getStreetId() 
    {
        return streetId;
    }
    public void setStreetName(String streetName) 
    {
        this.streetName = streetName;
    }

    public String getStreetName() 
    {
        return streetName;
    }
    public void setDetail(String detail) 
    {
        this.detail = detail;
    }

    public String getDetail() 
    {
        return detail;
    }
    public void setAfterSaleAddressId(Long afterSaleAddressId) 
    {
        this.afterSaleAddressId = afterSaleAddressId;
    }

    public Long getAfterSaleAddressId() 
    {
        return afterSaleAddressId;
    }
    public void setShopId(String shopId) 
    {
        this.shopId = shopId;
    }

    public String getShopId() 
    {
        return shopId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("logisticsSetting", getLogisticsSetting())
            .append("returnName", getReturnName())
            .append("returnTel", getReturnTel())
            .append("provinceId", getProvinceId())
            .append("provinceName", getProvinceName())
            .append("cityId", getCityId())
            .append("cityName", getCityName())
            .append("townId", getTownId())
            .append("townName", getTownName())
            .append("streetId", getStreetId())
            .append("streetName", getStreetName())
            .append("detail", getDetail())
            .append("afterSaleAddressId", getAfterSaleAddressId())
            .append("shopId", getShopId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
