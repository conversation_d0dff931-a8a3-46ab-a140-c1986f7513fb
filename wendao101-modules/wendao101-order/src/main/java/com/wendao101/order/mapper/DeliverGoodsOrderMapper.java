package com.wendao101.order.mapper;

import java.util.List;
import com.wendao101.order.domain.DeliverGoodsOrder;
import com.wendao101.order.dto.DeliverGoodsOrderDTO;
import com.wendao101.order.dto.ExportDeliverGoodsOrderDTO;
import com.wendao101.order.dto.ImportDeliverGoodsOrderDTO;
import com.wendao101.order.vo.DeliverGoodsOrderExportVO;
import com.wendao101.order.vo.DeliverGoodsOrderVO;
import org.apache.ibatis.annotations.Param;

/**
 * 物流信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface DeliverGoodsOrderMapper 
{
    /**
     * 查询物流信息
     * 
     * @param id 物流信息主键
     * @return 物流信息
     */
    public DeliverGoodsOrder selectDeliverGoodsOrderById(Long id);

    /**
     * 查询物流信息列表
     * 
     * @param deliverGoodsOrder 物流信息
     * @return 物流信息集合
     */
    public List<DeliverGoodsOrder> selectDeliverGoodsOrderList(DeliverGoodsOrder deliverGoodsOrder);

    /**
     * 新增物流信息
     * 
     * @param deliverGoodsOrder 物流信息
     * @return 结果
     */
    public int insertDeliverGoodsOrder(DeliverGoodsOrder deliverGoodsOrder);

    /**
     * 修改物流信息
     * 
     * @param deliverGoodsOrder 物流信息
     * @return 结果
     */
    public int updateDeliverGoodsOrder(DeliverGoodsOrder deliverGoodsOrder);

    /**
     * 根据订单id修改发货信息
     *
     * @param deliverGoodsOrder 物流信息
     * @return 结果
     */
    public int updateDeliverGoodsOrderByOrderId(DeliverGoodsOrder deliverGoodsOrder);

    /**
     * 删除物流信息
     * 
     * @param id 物流信息主键
     * @return 结果
     */
    public int deleteDeliverGoodsOrderById(Long id);

    /**
     * 批量删除物流信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDeliverGoodsOrderByIds(Long[] ids);

    /**
     * 查询物流信息列表，加检索条件
     *
     * @param deliverGoodsOrderVO
     * @return
     */
    List<DeliverGoodsOrderDTO> selectAllDeliverGoodsOrderList(DeliverGoodsOrderVO deliverGoodsOrderVO);

    /**
     * 查询课程下的附赠实物
     *
     * @param courseId
     * @return
     */
    String getTeachingMaterialName(@Param("courseId") Long courseId);

    /**
     * 发货信息导出
     *
     * @param  deliverGoodsOrderExportVO
     * @return
     */
    List<ExportDeliverGoodsOrderDTO> getExportDeliverGoodsOrder(DeliverGoodsOrderExportVO deliverGoodsOrderExportVO);

    /**
     * 获取课程名称
     *
     * @param courseId
     * @return
     */
    String getTitleByCourseId(@Param("courseId")Long courseId);

    Integer countNotShipment(@Param("teacherId")Long teacherId);

    DeliverGoodsOrder selectDeliverGoodsOrderByOrderId(@Param("orderId")String orderId);
}
