package com.wendao101.order.mapper;

import java.math.BigDecimal;
import java.util.List;
import com.wendao101.order.domain.CourseRefund;
import com.wendao101.order.dto.CourseRefundDTO;
import com.wendao101.order.dto.CourseRefundDetailDTO;
import com.wendao101.order.dto.OrderDataDTO;
import com.wendao101.order.dto.WendaoComplaintDto;
import com.wendao101.order.vo.*;
import org.apache.ibatis.annotations.Param;

/**
 * 退款信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface CourseRefundMapper 
{
    /**
     * 查询退款信息
     * 
     * @param id 退款信息主键
     * @return 退款信息
     */
    public CourseRefund selectCourseRefundById(@Param("id") Long id);

    public CourseRefundDetailDTO getCourseRefundById(@Param("id") Long id);

    /**
     * 查询退款信息列表
     * 
     * @param courseRefundVO 退款信息
     * @return 退款信息集合
     */
    public List<CourseRefundDTO> selectCourseRefundList(CourseRefundVO courseRefundVO);

    public Long selectCourseRefundListCount(CourseRefundVO courseRefundVO);
    List<String> selectCourseRefundListByOrderId(String orderId);


    int deleteListByRefundId(String lists);
    /**
     * 新增退款信息
     * 
     * @param courseRefund 退款信息
     * @return 结果
     */
    int insertCourseRefund(CourseRefund courseRefund);

    int insertOnlineCourseRefund(CourseRefundVO courseRefundVO);



    /**
     * 修改退款信息
     * 
     * @param  courseRefundUpdateVO 退款信息
     * @return 结果
     */
    public int updateCourseRefundStatus(CourseRefundUpdateVO courseRefundUpdateVO);

    /**
     * 修改退款信息
     *
     * @param courseRefund 退款信息
     * @return 结果
     */
    public int updateCourseRefund(CourseRefund courseRefund);

    /**
     * 修改退款状态
     *
     * @param  courseRefundStateVO
     * @return 结果
     */
    public int updateStatus(CourseRefundStateVO courseRefundStateVO);

    /**
     * 删除退款信息
     * 
     * @param id 退款信息主键
     * @return 结果
     */
    public int deleteCourseRefundById(Long id);

    /**
     * 批量删除退款信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseRefundByIds(Long[] ids);

    CourseRefund selectCourseRefundByOutRefundId(@Param("outRefundId") String outRefundId);




    int insertDataFromComplaintRefund(CourseRefund courseRefund);


    CourseRefund selectCourseRefundByRefundId(@Param("refundId")String refundId);

    Integer getTotalRefundNum(OrderDataDTO orderDataDTO);

    BigDecimal getRefundAmount(OrderDataDTO orderDataDTO);

    BigDecimal getTotalRefundAmount(CourseRefundVO courseRefundVO);

    List<CourseRefund> selectListByOrderIdAndStatus(@Param("orderId") String orderId);

    OrderRefundVO orderData(@Param("teacherId")Long teacherId);

    void deleteCourseRefundByRefundId(@Param("refundId") String refundId);

    List<CourseRefund> selectCourseRefundListByRefundId(@Param("outRefundId")String outRefundId);

    CourseRefund selectCourseRefundNotHandledByOrderId(@Param("orderId")String orderId);

    List<CourseRefund> selectCourseRefundRefusedByOrderId(@Param("orderId")String orderId);

    void deleteCourseRefundByOrderId(@Param("orderId") String orderId);

    CourseRefund selectCourseRefundHandledByOrderId(@Param("orderId")String orderId);
}
