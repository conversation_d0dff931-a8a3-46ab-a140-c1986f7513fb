package com.wendao101.order.service.impl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.order.domain.FundIncome;
import com.wendao101.order.domain.WithdrawPrice;
import com.wendao101.order.dto.WithdrawPriceDTO;
import com.wendao101.order.mapper.FundIncomeMapper;
import com.wendao101.order.mapper.WithdrawPriceMapper;
import com.wendao101.order.vo.AccountWithdrawVO;
import com.wendao101.order.vo.FundIncomePlatformVO;
import com.wendao101.order.vo.WithdrawVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.AccountWithdrawMapper;
import com.wendao101.order.domain.AccountWithdraw;
import com.wendao101.order.service.IAccountWithdrawService;

import javax.annotation.Resource;

/**
 * 支付宝 账户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class AccountWithdrawServiceImpl implements IAccountWithdrawService 
{
    @Resource
    private AccountWithdrawMapper accountWithdrawMapper;

    @Resource
    private WithdrawPriceMapper withdrawPriceMapper;

    @Resource
    private FundIncomeMapper fundIncomeMapper;

    /**
     * 查询支付宝 账户
     * 
     * @param id 支付宝 账户主键
     * @return 支付宝 账户
     */
    @Override
    public AccountWithdraw selectAccountWithdrawById(Long id)
    {
        return accountWithdrawMapper.selectAccountWithdrawById(id);
    }

    /**
     * 查询支付宝 账户列表
     * 
     * @param accountWithdraw 支付宝 账户
     * @return 支付宝 账户
     */
    @Override
    public List<AccountWithdraw> selectAccountWithdrawList(AccountWithdraw accountWithdraw)
    {
        return accountWithdrawMapper.selectAccountWithdrawList(accountWithdraw);
    }

    /**
     * 新增支付宝 账户
     * 
     * @param accountWithdraw 支付宝 账户
     * @return 结果
     */
    @Override
    public int insertAccountWithdraw(AccountWithdraw accountWithdraw)
    {
        accountWithdraw.setCreateTime(DateUtils.getNowDate());
        return accountWithdrawMapper.insertAccountWithdraw(accountWithdraw);
    }

    /**
     * 修改支付宝 账户
     * 
     * @param accountWithdraw 支付宝 账户
     * @return 结果
     */
    @Override
    public int updateAccountWithdraw(AccountWithdraw accountWithdraw)
    {
        accountWithdraw.setUpdateTime(DateUtils.getNowDate());
        return accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
    }

    /**
     * 批量删除支付宝 账户
     * 
     * @param ids 需要删除的支付宝 账户主键
     * @return 结果
     */
    @Override
    public int deleteAccountWithdrawByIds(Long[] ids)
    {
        return accountWithdrawMapper.deleteAccountWithdrawByIds(ids);
    }

    /**
     * 删除支付宝 账户信息
     * 
     * @param id 支付宝 账户主键
     * @return 结果
     */
    @Override
    public int deleteAccountWithdrawById(Long id)
    {
        return accountWithdrawMapper.deleteAccountWithdrawById(id);
    }

    /**
     * 查询提现账户
     *
     * @param accountWithdrawVO
     * @return
     */
    @Override
    public List<AccountWithdraw> selectAll(AccountWithdrawVO accountWithdrawVO) {

        return accountWithdrawMapper.selectAll(accountWithdrawVO);
    }

    /**
     * 从收益相加减去已提现的金额
     * @param withdrawVO
     * @return
     */
    @Override
    public WithdrawPriceDTO selectById(WithdrawVO withdrawVO) {
        WithdrawPriceDTO result = new WithdrawPriceDTO();
        //获取提现账户信息
        AccountWithdraw accountWithdraw = accountWithdrawMapper.selectAccountWithdrawById(withdrawVO.getId());
        result.setAccountName(accountWithdraw.getAccountName());
        result.setAccountPhone(accountWithdraw.getAccountPhone());
        result.setId(withdrawVO.getId());
        result.setTeacherId(withdrawVO.getTeacherId());
        if (StringUtils.isNotEmpty(accountWithdraw.getAccountBank())){
            result.setAccountBank(accountWithdraw.getAccountBank());
        }
        FundIncome fundIncome = new FundIncome();
        fundIncome.setTeacherId(withdrawVO.getTeacherId());
        fundIncome.setIncomePlatform(withdrawVO.getIncomePlatform());
        fundIncome.setIsDelete(0);
        //查出平台金额
        List<FundIncome> fundIncomes = fundIncomeMapper.selectFundIncomeList(fundIncome);
        if (CollectionUtils.isNotEmpty(fundIncomes)) {
            //平台可提现金额
            BigDecimal mayWithdrawPrice = fundIncomes.stream().filter(item -> Objects.equals(item.getIncomePlatform(), withdrawVO.getIncomePlatform()) && item.getIncomeType() != 4 && item.getFundsType() == 1 && item.getIncomeType() != 3)
                    .map(FundIncome::getIncomePrice).reduce(BigDecimal.ZERO, BigDecimal::add);

            //获取各个平台可提现金额
            WithdrawPrice withdrawPrice = withdrawPriceMapper.selectByTeacherId(withdrawVO.getTeacherId());
            if (Objects.nonNull(withdrawPrice)) {
                if (withdrawVO.getIncomePlatform() == 0){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getDyWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 1){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getWxWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 2){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getKsWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 3){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getSphWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 7){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getXhsWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 8){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getZsdpWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 9){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getPcWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 5){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getWapWithdrawnPrice()));
                } else if (withdrawVO.getIncomePlatform() == 11){
                    result.setWithdrawPrice(mayWithdrawPrice.subtract(withdrawPrice.getDdWithdrawnPrice()));
                }
            }else {
                result.setWithdrawPrice(mayWithdrawPrice);
            }
        }

        return result;
    }

    /**
     * 添加提现账户
     *
     * @param accountWithdraw
     * @return
     */
    @Override
    public int addAccountWithdraw(AccountWithdraw accountWithdraw) {
        //TODO 需要校验银行卡
        accountWithdraw.setIsDelete(0);
        accountWithdraw.setCreateTime(DateUtils.getNowDate());
        accountWithdraw.setUpdateTime(DateUtils.getNowDate());

        if (accountWithdraw.getIsTacitly() == 1){
            //修改老师下所有提现账号不默认
            accountWithdrawMapper.updateByTeacherId(accountWithdraw.getTeacherId());
        }

        return accountWithdrawMapper.insertAccountWithdraw(accountWithdraw);
    }

    /**
     * 删除提现账户
     *
     * @param ids
     * @return
     */
    @Override
    public int removeById(List<Long> ids) {
        AccountWithdraw accountWithdraw = new AccountWithdraw();
        accountWithdraw.setIsDelete(1);
        for (Long id : ids) {
            accountWithdraw.setId(id);
            accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
        }
        return 1;
    }

//    @Override
//    public int updateById(Long id) {
//        AccountWithdraw accountWithdraw = accountWithdrawMapper.selectAccountWithdrawById(id);
//        if (Objects.nonNull(accountWithdraw)){
//            if (accountWithdraw.getIsTacitly() == 0){
//                accountWithdraw.setIsTacitly(1);
//                //修改老师下所有提现账号不默认
//                accountWithdrawMapper.updateByTeacherId(accountWithdraw.getTeacherId());
//                //修改这个这个账户为默认
//                return accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
//            }else if(accountWithdraw.getIsTacitly() == 1){
//                accountWithdraw.setIsTacitly(0);
//                //取消这个这个账户默认
//                return accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
//            }
//        }
//        return 0;
//    }

    @Override
    public int update(AccountWithdraw accountWithdraw) {
        accountWithdraw.setUpdateTime(DateUtils.getNowDate());
        if (accountWithdraw.getIsTacitly() == 1){
            //修改老师下所有提现账号不默认
            accountWithdrawMapper.updateByTeacherId(accountWithdraw.getTeacherId());
            //修改这个这个账户为默认
            return accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
        }

            return accountWithdrawMapper.updateAccountWithdraw(accountWithdraw);
    }
}
