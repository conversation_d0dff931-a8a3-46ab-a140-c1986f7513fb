package com.wendao101.order.service.impl;

import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.douyin.dto.DYWenDaoUserDTO;
import com.wendao101.order.domain.WendaoUser;
import com.wendao101.order.mapper.WendaoUserMapper;
import com.wendao101.order.service.IDYWenDaoUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
public class DYWenDaoUserServiceImpl implements IDYWenDaoUserService {

    @Resource
    private WendaoUserMapper wendaoUserMapper;


//    @Resource
//    private IPromoterService promoterService;

    @Resource
    private RedisService redisService;

    private String keyPrefix = "openIdKey:";


    /**
     * 根据openId 查询用户id
     *
     * @param openId
     * @return
     */
    @Override
    public Long getUserIdByOpenId(String openId) {
        DYWenDaoUserDTO wenDaoUserByOpenId = getWenDaoUserByOpenId(openId);
        return wenDaoUserByOpenId==null?-1L:wenDaoUserByOpenId.getId();
    }

//    @Override
//    public Long getPromoterIdByOpenId(String openId) {
//        Promoter promoter = promoterService.selectPromoterByOpenid(openId);
//        return promoter==null?-1L:promoter.getId();
//    }

    /**
     * 根据openId 查询用户信息
     *
     * @param openId
     * @return
     */
    @Override
    public DYWenDaoUserDTO getWenDaoUserByOpenId(String openId) {
        DYWenDaoUserDTO result = redisService.getCacheObject(keyPrefix + openId);

        if (Objects.isNull(result)) {
            WendaoUser wenDaoUserByOpenId = wendaoUserMapper.getWenDaoUserByOpenId(openId);
            result = new DYWenDaoUserDTO();
            BeanUtils.copyProperties(wenDaoUserByOpenId,result);
            redisService.setCacheObject(keyPrefix + openId, result, 600L, TimeUnit.SECONDS);
        }

        return result;
    }

    @Override
    public String getTelNumberById(Long buyerUserId) {
        return wendaoUserMapper.getTelNumberById(buyerUserId);
    }

    @Override
    public int updateWendaoUser(WendaoUser wendaoUser) {
        wendaoUser.setUpdateTime(DateUtils.getNowDate());
        return wendaoUserMapper.updateWendaoUser(wendaoUser);
    }

    @Override
    public  WendaoUser getWenDaoUserByUserId(Long userId) {
        return wendaoUserMapper.selectWendaoUserById(userId);
    }

    /**
     * 根据unionId 查询推广员
     *
     * @param unionId
     * @return
     */
//    @Override
//    public Long getPromoterIdByUnionId(String unionId) {
//
//        return wendaoUserMapper.getPromoterIdByUnionId(unionId);
//    }
}
