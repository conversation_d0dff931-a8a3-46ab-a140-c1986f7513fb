package com.wendao101.order.mapper;

import java.util.List;
import com.wendao101.order.domain.GiftCourseRecord;

/**
 * 买课赠课设置Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface GiftCourseRecordMapper 
{
    /**
     * 查询买课赠课设置
     * 
     * @param id 买课赠课设置主键
     * @return 买课赠课设置
     */
    public GiftCourseRecord selectGiftCourseRecordById(Long id);

    /**
     * 查询买课赠课设置列表
     * 
     * @param giftCourseRecord 买课赠课设置
     * @return 买课赠课设置集合
     */
    public List<GiftCourseRecord> selectGiftCourseRecordList(GiftCourseRecord giftCourseRecord);

    /**
     * 新增买课赠课设置
     * 
     * @param giftCourseRecord 买课赠课设置
     * @return 结果
     */
    public int insertGiftCourseRecord(GiftCourseRecord giftCourseRecord);

    /**
     * 修改买课赠课设置
     * 
     * @param giftCourseRecord 买课赠课设置
     * @return 结果
     */
    public int updateGiftCourseRecord(GiftCourseRecord giftCourseRecord);

    /**
     * 删除买课赠课设置
     * 
     * @param id 买课赠课设置主键
     * @return 结果
     */
    public int deleteGiftCourseRecordById(Long id);

    /**
     * 批量删除买课赠课设置
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteGiftCourseRecordByIds(Long[] ids);
}
