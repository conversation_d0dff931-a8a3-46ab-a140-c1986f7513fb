package com.wendao101.order.mapper;


import com.wendao101.order.domain.CourseAudit;
import com.wendao101.order.dto.WendaoLiveDTO;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程待审核Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-20
 */
public interface CourseAuditMapper 
{
    /**
     * 查询课程待审核
     * 
     * @param id 课程待审核主键
     * @return 课程待审核
     */
    public CourseAudit selectCourseAuditById(Long id);

    /**
     * 查询课程待审核列表
     * 
     * @param courseAudit 课程待审核
     * @return 课程待审核集合
     */
    public List<CourseAudit> selectCourseAuditList(CourseAudit courseAudit);

    /**
     * 新增课程待审核
     * 
     * @param courseAudit 课程待审核
     * @return 结果
     */
    public int insertCourseAudit(CourseAudit courseAudit);

    /**
     * 修改课程待审核
     * 
     * @param courseAudit 课程待审核
     * @return 结果
     */
    public int updateCourseAudit(CourseAudit courseAudit);

    /**
     * 删除课程待审核
     * 
     * @param id 课程待审核主键
     * @return 结果
     */
    public int deleteCourseAuditById(Long id);

    /**
     * 批量删除课程待审核
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseAuditByIds(Long[] ids);

    CourseAudit selectCourseAuditByProductId(String productId);

    void updateDyResourceUploadStatus(@Param("dyUri") String dyUri,@Param("produceSourceStatus") Integer produceSourceStatus,@Param("reason")String reason);

    void updateCourseByProductIdTakeDown(@Param("productId")String productId);

    void updateCourseDyByProductIdTakeDown(@Param("productId")String productId);

    void updateCourseAuditByProductIdTakeDown(@Param("productId")String productId);

    void insertAuditFailRecord(@Param("courseId") Long courseId, @Param("message")String message);

    Long selectCourseIdByProductId(@Param("productId")String productId);

    Integer selectAppNameTypeByCourseId(@Param("courseId")Long courseId);

    WendaoLiveDTO selectWendaoLiveById(@Param("liveId")Long liveId);

    CourseAudit selectCourseAuditByIdAndPlatform(@Param("giveCourseId")Long giveCourseId, @Param("platform")Integer platform,@Param("teacherId")Long teacherId,@Param("appNameType")Integer appNameType);

    int countGiveAwayCourseOrder(@Param("giveCourseId")Long giveCourseId, @Param("platform")Integer platform, @Param("userId")Long userId, @Param("appNameType")Integer appNameType);

    WendaoLiveDTO selectWendaoLiveWapById(@Param("liveId")Long liveId);

    CourseAudit selectCourseAuditByIdAndPlatformWap(@Param("giveCourseId")Long giveCourseId, @Param("platform")Integer platform,@Param("teacherId")Long teacherId,@Param("appNameType")Integer appNameType);

    int countGiveAwayCourseOrderNoPlatform(@Param("giveCourseId")Long giveCourseId, @Param("userId")Long userId, @Param("appNameType")Integer appNameType);

    BigDecimal selectCoursePriceByCourseId(@Param("courseId")Long courseId);
}
