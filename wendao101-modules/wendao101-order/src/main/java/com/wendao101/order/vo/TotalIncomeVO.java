package com.wendao101.order.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class TotalIncomeVO {

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 开始时间  当天的 00:00:00
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String beginTime;

    /**
     *结束时间 当天的 23:59:59
     */
//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
}
