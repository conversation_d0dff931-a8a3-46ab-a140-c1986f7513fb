package com.wendao101.order.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CreatePreDepositOrderDTO {

    //创建支付订单参数
    private BigDecimal orderMoney;

    //订单商品名称
    private String productName;

    //抖店店铺ID
    private Long shopId;
    //抖店店铺名称
    private String shopName;

    //创建支付宝订单参数
    private String depositOrderId;
    //充值完返回地址
    private String returnUrl;
}
