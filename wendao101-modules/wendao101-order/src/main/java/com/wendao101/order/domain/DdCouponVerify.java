package com.wendao101.order.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 核销异常订单对象 dd_coupon_verify
 * 
 * <AUTHOR>
 * @date 2024-12-14
 */
public class DdCouponVerify extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 订单id */
    @Excel(name = "订单id")
    private String orderId;

    /** 核销码 */
    @Excel(name = "核销码")
    private String couponNumber;

    /** 抖店店铺id */
    @Excel(name = "抖店店铺id")
    private Long ddShopId;

    /** 0未处理,1已处理 */
    @Excel(name = "0未处理,1已处理")
    private Integer verify;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setCouponNumber(String couponNumber) 
    {
        this.couponNumber = couponNumber;
    }

    public String getCouponNumber() 
    {
        return couponNumber;
    }
    public void setDdShopId(Long ddShopId) 
    {
        this.ddShopId = ddShopId;
    }

    public Long getDdShopId() 
    {
        return ddShopId;
    }
    public void setVerify(Integer verify) 
    {
        this.verify = verify;
    }

    public Integer getVerify() 
    {
        return verify;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("orderId", getOrderId())
            .append("couponNumber", getCouponNumber())
            .append("ddShopId", getDdShopId())
            .append("verify", getVerify())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
