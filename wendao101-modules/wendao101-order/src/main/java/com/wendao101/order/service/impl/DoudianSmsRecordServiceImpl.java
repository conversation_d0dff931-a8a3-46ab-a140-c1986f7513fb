package com.wendao101.order.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.order.mapper.DoudianSmsRecordMapper;
import com.wendao101.order.domain.DoudianSmsRecord;
import com.wendao101.order.service.IDoudianSmsRecordService;

/**
 * 抖店短信发送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@Service
public class DoudianSmsRecordServiceImpl implements IDoudianSmsRecordService 
{
    @Autowired
    private DoudianSmsRecordMapper doudianSmsRecordMapper;

    /**
     * 查询抖店短信发送记录
     * 
     * @param id 抖店短信发送记录主键
     * @return 抖店短信发送记录
     */
    @Override
    public DoudianSmsRecord selectDoudianSmsRecordById(Long id)
    {
        return doudianSmsRecordMapper.selectDoudianSmsRecordById(id);
    }

    /**
     * 查询抖店短信发送记录列表
     * 
     * @param doudianSmsRecord 抖店短信发送记录
     * @return 抖店短信发送记录
     */
    @Override
    public List<DoudianSmsRecord> selectDoudianSmsRecordList(DoudianSmsRecord doudianSmsRecord)
    {
        return doudianSmsRecordMapper.selectDoudianSmsRecordList(doudianSmsRecord);
    }

    /**
     * 新增抖店短信发送记录
     * 
     * @param doudianSmsRecord 抖店短信发送记录
     * @return 结果
     */
    @Override
    public int insertDoudianSmsRecord(DoudianSmsRecord doudianSmsRecord)
    {
        doudianSmsRecord.setCreateTime(DateUtils.getNowDate());
        return doudianSmsRecordMapper.insertDoudianSmsRecord(doudianSmsRecord);
    }

    /**
     * 修改抖店短信发送记录
     * 
     * @param doudianSmsRecord 抖店短信发送记录
     * @return 结果
     */
    @Override
    public int updateDoudianSmsRecord(DoudianSmsRecord doudianSmsRecord)
    {
        doudianSmsRecord.setUpdateTime(DateUtils.getNowDate());
        return doudianSmsRecordMapper.updateDoudianSmsRecord(doudianSmsRecord);
    }

    /**
     * 批量删除抖店短信发送记录
     * 
     * @param ids 需要删除的抖店短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteDoudianSmsRecordByIds(Long[] ids)
    {
        return doudianSmsRecordMapper.deleteDoudianSmsRecordByIds(ids);
    }

    /**
     * 删除抖店短信发送记录信息
     * 
     * @param id 抖店短信发送记录主键
     * @return 结果
     */
    @Override
    public int deleteDoudianSmsRecordById(Long id)
    {
        return doudianSmsRecordMapper.deleteDoudianSmsRecordById(id);
    }
}
