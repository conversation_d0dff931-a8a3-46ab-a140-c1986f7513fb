<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.WxAccessTokenMapper">
    
    <resultMap type="WxAccessToken" id="WxAccessTokenResult">
        <result property="id"    column="id"    />
        <result property="appid"    column="appid"    />
        <result property="secret"    column="secret"    />
        <result property="accessToken"    column="access_token"    />
        <result property="expiresIn"    column="expires_in"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="appName"    column="app_name"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectWxAccessTokenVo">
        select id, appid, secret, access_token, expires_in, app_name_type, app_name, create_time, update_time from `wendao101-teacher`.wx_access_token
    </sql>

    <select id="selectWxAccessTokenList" parameterType="WxAccessToken" resultMap="WxAccessTokenResult">
        <include refid="selectWxAccessTokenVo"/>
        <where>  
            <if test="appid != null  and appid != ''"> and appid = #{appid}</if>
            <if test="secret != null  and secret != ''"> and secret = #{secret}</if>
            <if test="accessToken != null  and accessToken != ''"> and access_token = #{accessToken}</if>
            <if test="expiresIn != null "> and expires_in = #{expiresIn}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="appName != null  and appName != ''"> and app_name like concat('%', #{appName}, '%')</if>
        </where>
    </select>
    
    <select id="selectWxAccessTokenById" parameterType="Long" resultMap="WxAccessTokenResult">
        <include refid="selectWxAccessTokenVo"/>
        where id = #{id}
    </select>
    <select id="selectWxAccessTokenByAppNameType" resultMap="WxAccessTokenResult">
        <include refid="selectWxAccessTokenVo"/>
        where app_name_type = #{appNameType} limit 1
    </select>
</mapper>