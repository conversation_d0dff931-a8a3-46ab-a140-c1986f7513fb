<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.TeacherResourcePackageMapper">
    
    <resultMap type="TeacherResourcePackage" id="TeacherResourcePackageResult">
        <result property="pkgId"            column="pkg_id"            />
        <result property="resourcePkgId"    column="resource_pkg_id"   />
        <result property="teacherId"        column="teacher_id"        />
        <result property="resourcePkgName"  column="resource_pkg_name" />
        <result property="pkgPrice"         column="pkg_price"         />
        <result property="validityPeriod"   column="validity_period"   />
        <result property="resourceCount"    column="resource_count"    />
        <result property="remainingCount"   column="remaining_count"   />
        <result property="isEnable"         column="is_enable"         />
        <result property="featureCode"      column="feature_code"      />
        <result property="startTime"        column="start_time"        />
        <result property="endTime"          column="end_time"          />
        <result property="createTime"       column="create_time"       />
        <result property="updateTime"       column="update_time"       />
    </resultMap>

    <sql id="selectTeacherResourcePackageVo">
        select pkg_id, resource_pkg_id, teacher_id, resource_pkg_name, pkg_price, validity_period, resource_count, 
        remaining_count, is_enable, feature_code, start_time, end_time, create_time, update_time
        from teacher_resource_package
    </sql>

    <select id="selectTeacherResourcePackageList" parameterType="TeacherResourcePackage" resultMap="TeacherResourcePackageResult">
        <include refid="selectTeacherResourcePackageVo"/>
        <where>
            <if test="resourcePkgId != null "> and resource_pkg_id = #{resourcePkgId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="resourcePkgName != null  and resourcePkgName != ''"> and resource_pkg_name like concat('%', #{resourcePkgName}, '%')</if>
            <if test="pkgPrice != null "> and pkg_price = #{pkgPrice}</if>
            <if test="validityPeriod != null "> and validity_period = #{validityPeriod}</if>
            <if test="resourceCount != null "> and resource_count = #{resourceCount}</if>
            <if test="remainingCount != null "> and remaining_count = #{remainingCount}</if>
            <if test="isEnable != null "> and is_enable = #{isEnable}</if>
            <if test="featureCode != null  and featureCode != ''"> and feature_code = #{featureCode}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectTeacherResourcePackageById" parameterType="Long" resultMap="TeacherResourcePackageResult">
        <include refid="selectTeacherResourcePackageVo"/>
        where pkg_id = #{pkgId}
    </select>

    <select id="selectTeacherResourcePackageByTeacherId" parameterType="Long" resultMap="TeacherResourcePackageResult">
        <include refid="selectTeacherResourcePackageVo"/>
        where teacher_id = #{teacherId}
        order by create_time desc
    </select>

    <select id="selectTeacherResourcePackageByTeacherIdAndFeatureCode" resultMap="TeacherResourcePackageResult">
        <include refid="selectTeacherResourcePackageVo"/>
        where teacher_id = #{teacherId} and feature_code = #{featureCode}
        order by create_time desc
    </select>
        
    <insert id="insertTeacherResourcePackage" parameterType="TeacherResourcePackage" useGeneratedKeys="true" keyProperty="pkgId">
        insert into teacher_resource_package
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="resourcePkgId != null">resource_pkg_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="resourcePkgName != null">resource_pkg_name,</if>
            <if test="pkgPrice != null">pkg_price,</if>
            <if test="validityPeriod != null">validity_period,</if>
            <if test="resourceCount != null">resource_count,</if>
            <if test="remainingCount != null">remaining_count,</if>
            <if test="isEnable != null">is_enable,</if>
            <if test="featureCode != null">feature_code,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="resourcePkgId != null">#{resourcePkgId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="resourcePkgName != null">#{resourcePkgName},</if>
            <if test="pkgPrice != null">#{pkgPrice},</if>
            <if test="validityPeriod != null">#{validityPeriod},</if>
            <if test="resourceCount != null">#{resourceCount},</if>
            <if test="remainingCount != null">#{remainingCount},</if>
            <if test="isEnable != null">#{isEnable},</if>
            <if test="featureCode != null">#{featureCode},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTeacherResourcePackage" parameterType="TeacherResourcePackage">
        update teacher_resource_package
        <trim prefix="SET" suffixOverrides=",">
            <if test="resourcePkgId != null">resource_pkg_id = #{resourcePkgId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="resourcePkgName != null">resource_pkg_name = #{resourcePkgName},</if>
            <if test="pkgPrice != null">pkg_price = #{pkgPrice},</if>
            <if test="validityPeriod != null">validity_period = #{validityPeriod},</if>
            <if test="resourceCount != null">resource_count = #{resourceCount},</if>
            <if test="remainingCount != null">remaining_count = #{remainingCount},</if>
            <if test="isEnable != null">is_enable = #{isEnable},</if>
            <if test="featureCode != null">feature_code = #{featureCode},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where pkg_id = #{pkgId}
    </update>

    <delete id="deleteTeacherResourcePackageById" parameterType="Long">
        delete from teacher_resource_package where pkg_id = #{pkgId}
    </delete>

    <delete id="deleteTeacherResourcePackageByIds" parameterType="String">
        delete from teacher_resource_package where pkg_id in 
        <foreach item="pkgId" collection="array" open="(" separator="," close=")">
            #{pkgId}
        </foreach>
    </delete>

    <select id="sumRemainingCountByTeacherIdAndFeatureCode" resultType="java.lang.Integer">
        <![CDATA[ select IFNULL(sum(remaining_count), 0)
        from teacher_resource_package
        where teacher_id = #{teacherId} 
        and feature_code = #{featureCode}
        and end_time > NOW()
        and is_enable = 1
        ]]>
    </select>
    <select id="sumResourceCountByTeacherIdAndFeatureCode" resultType="java.lang.Integer">
        <![CDATA[ select IFNULL(sum(resource_count), 0)
         from teacher_resource_package
         where teacher_id = #{teacherId}
         and feature_code = #{featureCode}
        ]]>
    </select>

    <update id="deductResourcePackageCount">
        <![CDATA[
        update teacher_resource_package
        set remaining_count = remaining_count - 1,
            is_enable = CASE WHEN remaining_count - 1 <= 0 THEN 0 ELSE is_enable END,
            update_time = NOW()
        where pkg_id = (
            select pkg_id
            from (
                select pkg_id
                from teacher_resource_package
                where teacher_id = #{teacherId}
                and feature_code = #{featureCode}
                and is_enable = 1
                and remaining_count > 0
                and end_time > NOW()
                order by pkg_id asc
                limit 1
            ) t
        )
        ]]>
    </update>

    <update id="updateExpiredPackageStatus">
        <![CDATA[
        update teacher_resource_package
        set is_enable = 0,
            update_time = NOW()
        where end_time < NOW()
        and is_enable = 1
        ]]>
    </update>
</mapper> 