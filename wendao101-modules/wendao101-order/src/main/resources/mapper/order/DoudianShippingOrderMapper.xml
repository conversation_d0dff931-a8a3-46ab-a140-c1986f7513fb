<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.DoudianShippingOrderMapper">
    
    <resultMap type="DoudianShippingOrder" id="DoudianShippingOrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="buyerUserName"    column="buyer_user_name"    />
        <result property="buyerUserMobile"    column="buyer_user_mobile"    />
        <result property="payTime"    column="pay_time"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="orderPlatform"    column="order_platform"    />
        <result property="productType"    column="product_type"    />
        <result property="provinceId"    column="province_id"    />
        <result property="provinceName"    column="province_name"    />
        <result property="cityId"    column="city_id"    />
        <result property="cityName"    column="city_name"    />
        <result property="townId"    column="town_id"    />
        <result property="townName"    column="town_name"    />
        <result property="streetId"    column="street_id"    />
        <result property="streetName"    column="street_name"    />
        <result property="detail"    column="detail"    />
        <result property="doudianOrderJson"    column="doudian_order_json"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="shippingStatus"    column="shipping_status"    />
        <result property="logisticsCode"    column="logistics_code"    />
        <result property="companyCode"    column="company_code"    />
        <result property="costPrice"    column="cost_price"    />
    </resultMap>

    <sql id="selectDoudianShippingOrderVo">
        select id, order_id, teacher_id, buyer_user_name, buyer_user_mobile, pay_time, app_name_type, order_platform, product_type, province_id, province_name, city_id, city_name, town_id, town_name, street_id, street_name, detail, doudian_order_json, create_time, update_time, shipping_status, logistics_code, company_code, cost_price from doudian_shipping_order
    </sql>

    <select id="selectDoudianShippingOrderList" parameterType="DoudianShippingOrder" resultMap="DoudianShippingOrderResult">
        <include refid="selectDoudianShippingOrderVo"/>
        <where>  
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="buyerUserName != null  and buyerUserName != ''"> and buyer_user_name like concat('%', #{buyerUserName}, '%')</if>
            <if test="buyerUserMobile != null  and buyerUserMobile != ''"> and buyer_user_mobile = #{buyerUserMobile}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="orderPlatform != null "> and order_platform = #{orderPlatform}</if>
            <if test="productType != null "> and product_type = #{productType}</if>
            <if test="provinceId != null  and provinceId != ''"> and province_id = #{provinceId}</if>
            <if test="provinceName != null  and provinceName != ''"> and province_name like concat('%', #{provinceName}, '%')</if>
            <if test="cityId != null  and cityId != ''"> and city_id = #{cityId}</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="townId != null  and townId != ''"> and town_id = #{townId}</if>
            <if test="townName != null  and townName != ''"> and town_name like concat('%', #{townName}, '%')</if>
            <if test="streetId != null  and streetId != ''"> and street_id = #{streetId}</if>
            <if test="streetName != null  and streetName != ''"> and street_name like concat('%', #{streetName}, '%')</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="doudianOrderJson != null  and doudianOrderJson != ''"> and doudian_order_json = #{doudianOrderJson}</if>
            <if test="shippingStatus != null "> and shipping_status = #{shippingStatus}</if>
            <if test="logisticsCode != null  and logisticsCode != ''"> and logistics_code = #{logisticsCode}</if>
            <if test="companyCode != null  and companyCode != ''"> and company_code = #{companyCode}</if>
            <if test="costPrice != null "> and cost_price = #{costPrice}</if>
        </where>
    </select>
    
    <select id="selectDoudianShippingOrderById" parameterType="Long" resultMap="DoudianShippingOrderResult">
        <include refid="selectDoudianShippingOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDoudianShippingOrder" parameterType="DoudianShippingOrder" useGeneratedKeys="true" keyProperty="id">
        insert into doudian_shipping_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orderId != null">order_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="buyerUserName != null">buyer_user_name,</if>
            <if test="buyerUserMobile != null">buyer_user_mobile,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="orderPlatform != null">order_platform,</if>
            <if test="productType != null">product_type,</if>
            <if test="provinceId != null">province_id,</if>
            <if test="provinceName != null">province_name,</if>
            <if test="cityId != null">city_id,</if>
            <if test="cityName != null">city_name,</if>
            <if test="townId != null">town_id,</if>
            <if test="townName != null">town_name,</if>
            <if test="streetId != null">street_id,</if>
            <if test="streetName != null">street_name,</if>
            <if test="detail != null">detail,</if>
            <if test="doudianOrderJson != null">doudian_order_json,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="shippingStatus != null">shipping_status,</if>
            <if test="logisticsCode != null">logistics_code,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="costPrice != null">cost_price,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orderId != null">#{orderId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="buyerUserName != null">#{buyerUserName},</if>
            <if test="buyerUserMobile != null">#{buyerUserMobile},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="orderPlatform != null">#{orderPlatform},</if>
            <if test="productType != null">#{productType},</if>
            <if test="provinceId != null">#{provinceId},</if>
            <if test="provinceName != null">#{provinceName},</if>
            <if test="cityId != null">#{cityId},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="townId != null">#{townId},</if>
            <if test="townName != null">#{townName},</if>
            <if test="streetId != null">#{streetId},</if>
            <if test="streetName != null">#{streetName},</if>
            <if test="detail != null">#{detail},</if>
            <if test="doudianOrderJson != null">#{doudianOrderJson},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="shippingStatus != null">#{shippingStatus},</if>
            <if test="logisticsCode != null">#{logisticsCode},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="costPrice != null">#{costPrice},</if>
         </trim>
    </insert>

    <update id="updateDoudianShippingOrder" parameterType="DoudianShippingOrder">
        update doudian_shipping_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="buyerUserName != null">buyer_user_name = #{buyerUserName},</if>
            <if test="buyerUserMobile != null">buyer_user_mobile = #{buyerUserMobile},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="orderPlatform != null">order_platform = #{orderPlatform},</if>
            <if test="productType != null">product_type = #{productType},</if>
            <if test="provinceId != null">province_id = #{provinceId},</if>
            <if test="provinceName != null">province_name = #{provinceName},</if>
            <if test="cityId != null">city_id = #{cityId},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="townId != null">town_id = #{townId},</if>
            <if test="townName != null">town_name = #{townName},</if>
            <if test="streetId != null">street_id = #{streetId},</if>
            <if test="streetName != null">street_name = #{streetName},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="doudianOrderJson != null">doudian_order_json = #{doudianOrderJson},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="shippingStatus != null">shipping_status = #{shippingStatus},</if>
            <if test="logisticsCode != null">logistics_code = #{logisticsCode},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="costPrice != null">cost_price = #{costPrice},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoudianShippingOrderById" parameterType="Long">
        delete from doudian_shipping_order where id = #{id}
    </delete>

    <delete id="deleteDoudianShippingOrderByIds" parameterType="String">
        delete from doudian_shipping_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>