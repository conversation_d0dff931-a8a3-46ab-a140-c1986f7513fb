<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.DoudianSmsRecordMapper">
    
    <resultMap type="DoudianSmsRecord" id="DoudianSmsRecordResult">
        <result property="id"    column="id"    />
        <result property="ddShopId"    column="dd_shop_id"    />
        <result property="sendTime"    column="send_time"    />
        <result property="smsContent"    column="sms_content"    />
        <result property="status"    column="status"    />
        <result property="count"    column="count"    />
        <result property="code"    column="code"    />
        <result property="message"    column="message"    />
        <result property="messageId"    column="message_id"    />
        <result property="tag"    column="tag"    />
        <result property="orderId"    column="order_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectDoudianSmsRecordVo">
        select id, dd_shop_id, send_time, sms_content, status, count, code, message, message_id, tag, order_id, teacher_id, create_time, update_time from doudian_sms_record
    </sql>

    <select id="selectDoudianSmsRecordList" parameterType="DoudianSmsRecord" resultMap="DoudianSmsRecordResult">
        <include refid="selectDoudianSmsRecordVo"/>
        <where>  
            <if test="ddShopId != null "> and dd_shop_id = #{ddShopId}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
            <if test="smsContent != null  and smsContent != ''"> and sms_content = #{smsContent}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="count != null "> and count = #{count}</if>
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="message != null  and message != ''"> and message = #{message}</if>
            <if test="messageId != null "> and message_id = #{messageId}</if>
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
        </where>
    </select>
    
    <select id="selectDoudianSmsRecordById" parameterType="Long" resultMap="DoudianSmsRecordResult">
        <include refid="selectDoudianSmsRecordVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertDoudianSmsRecord" parameterType="DoudianSmsRecord" useGeneratedKeys="true" keyProperty="id">
        insert into doudian_sms_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ddShopId != null">dd_shop_id,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="smsContent != null">sms_content,</if>
            <if test="status != null">status,</if>
            <if test="count != null">count,</if>
            <if test="code != null">code,</if>
            <if test="message != null">message,</if>
            <if test="messageId != null">message_id,</if>
            <if test="tag != null">tag,</if>
            <if test="orderId != null">order_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ddShopId != null">#{ddShopId},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="smsContent != null">#{smsContent},</if>
            <if test="status != null">#{status},</if>
            <if test="count != null">#{count},</if>
            <if test="code != null">#{code},</if>
            <if test="message != null">#{message},</if>
            <if test="messageId != null">#{messageId},</if>
            <if test="tag != null">#{tag},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateDoudianSmsRecord" parameterType="DoudianSmsRecord">
        update doudian_sms_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="ddShopId != null">dd_shop_id = #{ddShopId},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="smsContent != null">sms_content = #{smsContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="count != null">count = #{count},</if>
            <if test="code != null">code = #{code},</if>
            <if test="message != null">message = #{message},</if>
            <if test="messageId != null">message_id = #{messageId},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDoudianSmsRecordById" parameterType="Long">
        delete from doudian_sms_record where id = #{id}
    </delete>

    <delete id="deleteDoudianSmsRecordByIds" parameterType="String">
        delete from doudian_sms_record where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>