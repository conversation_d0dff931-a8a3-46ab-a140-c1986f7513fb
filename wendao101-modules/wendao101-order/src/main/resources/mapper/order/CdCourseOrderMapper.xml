<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.CdCourseOrderMapper">
    
    <resultMap type="CdCourseOrder" id="CdCourseOrderResult">
        <result property="id"    column="id"    />
        <result property="orderId"    column="order_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="buyerUserId"    column="buyer_user_id"    />
        <result property="buyerUserImg"    column="buyer_user_img"    />
        <result property="buyerUserName"    column="buyer_user_name"    />
        <result property="buyerUserMobile"    column="buyer_user_mobile"    />
        <result property="courseDuration"    column="course_duration"    />
        <result property="courseImgUrl"    column="course_img_url"    />
        <result property="studyDuration"    column="study_duration"    />
        <result property="validity"    column="validity"    />
        <result property="promotionRatio"    column="promotion_ratio"    />
        <result property="promoterId"    column="promoter_id"    />
        <result property="isPromoter"    column="is_promoter"    />
        <result property="promoterName"    column="promoter_name"    />
        <result property="promoterMobile"    column="promoter_mobile"    />
        <result property="myEarningsPrice"    column="my_earnings_price"    />
        <result property="promoterEarningsPrice"    column="promoter_earnings_price"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseTitle"    column="course_title"    />
        <result property="coursePrice"    column="course_price"    />
        <result property="isCourse"    column="is_course"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="orderStatus"    column="order_status"    />
        <result property="orderType"    column="order_type"    />
        <result property="orderPlatform"    column="order_platform"    />
        <result property="orderTime"    column="order_time"    />
        <result property="fundsType"    column="funds_type"    />
        <result property="payWay"    column="pay_way"    />
        <result property="payTime"    column="pay_time"    />
        <result property="outOrderNumber"    column="out_order_number"    />
        <result property="tradingOrderNumber"    column="trading_order_number"    />
        <result property="deliverGoodsOrderId"    column="deliver_goods_order_id"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="learningReportingTime"    column="learning_reporting_time"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="openId"    column="open_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="settleStatus"    column="settle_status"    />
    </resultMap>

    <sql id="selectCdCourseOrderVo">
        select id, order_id, teacher_id, buyer_user_id, buyer_user_img, buyer_user_name, buyer_user_mobile, course_duration, course_img_url, study_duration, validity, promotion_ratio, promoter_id, is_promoter, promoter_name, promoter_mobile, my_earnings_price, promoter_earnings_price, course_id, course_title, course_price, is_course, pay_price, original_price, order_status, order_type, order_platform, order_time, funds_type, pay_way, pay_time, out_order_number, trading_order_number, deliver_goods_order_id, is_delete, learning_reporting_time, create_time, update_time, app_name_type, open_id, union_id, settle_status from cd_course_order
    </sql>

    <select id="selectCdCourseOrderList" parameterType="CdCourseOrder" resultMap="CdCourseOrderResult">
        <include refid="selectCdCourseOrderVo"/>
        <where>  
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="buyerUserId != null "> and buyer_user_id = #{buyerUserId}</if>
            <if test="buyerUserImg != null  and buyerUserImg != ''"> and buyer_user_img = #{buyerUserImg}</if>
            <if test="buyerUserName != null  and buyerUserName != ''"> and buyer_user_name like concat('%', #{buyerUserName}, '%')</if>
            <if test="buyerUserMobile != null  and buyerUserMobile != ''"> and buyer_user_mobile = #{buyerUserMobile}</if>
            <if test="courseDuration != null "> and course_duration = #{courseDuration}</if>
            <if test="courseImgUrl != null  and courseImgUrl != ''"> and course_img_url = #{courseImgUrl}</if>
            <if test="studyDuration != null "> and study_duration = #{studyDuration}</if>
            <if test="validity != null "> and validity = #{validity}</if>
            <if test="promotionRatio != null  and promotionRatio != ''"> and promotion_ratio = #{promotionRatio}</if>
            <if test="promoterId != null "> and promoter_id = #{promoterId}</if>
            <if test="isPromoter != null "> and is_promoter = #{isPromoter}</if>
            <if test="promoterName != null  and promoterName != ''"> and promoter_name like concat('%', #{promoterName}, '%')</if>
            <if test="promoterMobile != null  and promoterMobile != ''"> and promoter_mobile = #{promoterMobile}</if>
            <if test="myEarningsPrice != null "> and my_earnings_price = #{myEarningsPrice}</if>
            <if test="promoterEarningsPrice != null "> and promoter_earnings_price = #{promoterEarningsPrice}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseTitle != null  and courseTitle != ''"> and course_title = #{courseTitle}</if>
            <if test="coursePrice != null "> and course_price = #{coursePrice}</if>
            <if test="isCourse != null "> and is_course = #{isCourse}</if>
            <if test="payPrice != null "> and pay_price = #{payPrice}</if>
            <if test="originalPrice != null "> and original_price = #{originalPrice}</if>
            <if test="orderStatus != null "> and order_status = #{orderStatus}</if>
            <if test="orderType != null "> and order_type = #{orderType}</if>
            <if test="orderPlatform != null "> and order_platform = #{orderPlatform}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="fundsType != null "> and funds_type = #{fundsType}</if>
            <if test="payWay != null  and payWay != ''"> and pay_way = #{payWay}</if>
            <if test="payTime != null "> and pay_time = #{payTime}</if>
            <if test="outOrderNumber != null  and outOrderNumber != ''"> and out_order_number = #{outOrderNumber}</if>
            <if test="tradingOrderNumber != null  and tradingOrderNumber != ''"> and trading_order_number = #{tradingOrderNumber}</if>
            <if test="deliverGoodsOrderId != null  and deliverGoodsOrderId != ''"> and deliver_goods_order_id = #{deliverGoodsOrderId}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="learningReportingTime != null "> and learning_reporting_time = #{learningReportingTime}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="settleStatus != null "> and settle_status = #{settleStatus}</if>
        </where>
    </select>
    
    <select id="selectCdCourseOrderById" parameterType="Long" resultMap="CdCourseOrderResult">
        <include refid="selectCdCourseOrderVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCdCourseOrder" parameterType="CdCourseOrder">
        insert into cd_course_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="buyerUserId != null">buyer_user_id,</if>
            <if test="buyerUserImg != null">buyer_user_img,</if>
            <if test="buyerUserName != null">buyer_user_name,</if>
            <if test="buyerUserMobile != null">buyer_user_mobile,</if>
            <if test="courseDuration != null">course_duration,</if>
            <if test="courseImgUrl != null">course_img_url,</if>
            <if test="studyDuration != null">study_duration,</if>
            <if test="validity != null">validity,</if>
            <if test="promotionRatio != null">promotion_ratio,</if>
            <if test="promoterId != null">promoter_id,</if>
            <if test="isPromoter != null">is_promoter,</if>
            <if test="promoterName != null">promoter_name,</if>
            <if test="promoterMobile != null">promoter_mobile,</if>
            <if test="myEarningsPrice != null">my_earnings_price,</if>
            <if test="promoterEarningsPrice != null">promoter_earnings_price,</if>
            <if test="courseId != null">course_id,</if>
            <if test="courseTitle != null">course_title,</if>
            <if test="coursePrice != null">course_price,</if>
            <if test="isCourse != null">is_course,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="orderStatus != null">order_status,</if>
            <if test="orderType != null">order_type,</if>
            <if test="orderPlatform != null">order_platform,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="fundsType != null">funds_type,</if>
            <if test="payWay != null">pay_way,</if>
            <if test="payTime != null">pay_time,</if>
            <if test="outOrderNumber != null">out_order_number,</if>
            <if test="tradingOrderNumber != null">trading_order_number,</if>
            <if test="deliverGoodsOrderId != null">deliver_goods_order_id,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="learningReportingTime != null">learning_reporting_time,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="appNameType != null">app_name_type,</if>
            <if test="openId != null">open_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="settleStatus != null">settle_status,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="buyerUserId != null">#{buyerUserId},</if>
            <if test="buyerUserImg != null">#{buyerUserImg},</if>
            <if test="buyerUserName != null">#{buyerUserName},</if>
            <if test="buyerUserMobile != null">#{buyerUserMobile},</if>
            <if test="courseDuration != null">#{courseDuration},</if>
            <if test="courseImgUrl != null">#{courseImgUrl},</if>
            <if test="studyDuration != null">#{studyDuration},</if>
            <if test="validity != null">#{validity},</if>
            <if test="promotionRatio != null">#{promotionRatio},</if>
            <if test="promoterId != null">#{promoterId},</if>
            <if test="isPromoter != null">#{isPromoter},</if>
            <if test="promoterName != null">#{promoterName},</if>
            <if test="promoterMobile != null">#{promoterMobile},</if>
            <if test="myEarningsPrice != null">#{myEarningsPrice},</if>
            <if test="promoterEarningsPrice != null">#{promoterEarningsPrice},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="courseTitle != null">#{courseTitle},</if>
            <if test="coursePrice != null">#{coursePrice},</if>
            <if test="isCourse != null">#{isCourse},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="orderStatus != null">#{orderStatus},</if>
            <if test="orderType != null">#{orderType},</if>
            <if test="orderPlatform != null">#{orderPlatform},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="fundsType != null">#{fundsType},</if>
            <if test="payWay != null">#{payWay},</if>
            <if test="payTime != null">#{payTime},</if>
            <if test="outOrderNumber != null">#{outOrderNumber},</if>
            <if test="tradingOrderNumber != null">#{tradingOrderNumber},</if>
            <if test="deliverGoodsOrderId != null">#{deliverGoodsOrderId},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="learningReportingTime != null">#{learningReportingTime},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="appNameType != null">#{appNameType},</if>
            <if test="openId != null">#{openId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="settleStatus != null">#{settleStatus},</if>
         </trim>
    </insert>

    <update id="updateCdCourseOrder" parameterType="CdCourseOrder">
        update cd_course_order
        <trim prefix="SET" suffixOverrides=",">
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="buyerUserId != null">buyer_user_id = #{buyerUserId},</if>
            <if test="buyerUserImg != null">buyer_user_img = #{buyerUserImg},</if>
            <if test="buyerUserName != null">buyer_user_name = #{buyerUserName},</if>
            <if test="buyerUserMobile != null">buyer_user_mobile = #{buyerUserMobile},</if>
            <if test="courseDuration != null">course_duration = #{courseDuration},</if>
            <if test="courseImgUrl != null">course_img_url = #{courseImgUrl},</if>
            <if test="studyDuration != null">study_duration = #{studyDuration},</if>
            <if test="validity != null">validity = #{validity},</if>
            <if test="promotionRatio != null">promotion_ratio = #{promotionRatio},</if>
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="isPromoter != null">is_promoter = #{isPromoter},</if>
            <if test="promoterName != null">promoter_name = #{promoterName},</if>
            <if test="promoterMobile != null">promoter_mobile = #{promoterMobile},</if>
            <if test="myEarningsPrice != null">my_earnings_price = #{myEarningsPrice},</if>
            <if test="promoterEarningsPrice != null">promoter_earnings_price = #{promoterEarningsPrice},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseTitle != null">course_title = #{courseTitle},</if>
            <if test="coursePrice != null">course_price = #{coursePrice},</if>
            <if test="isCourse != null">is_course = #{isCourse},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="orderType != null">order_type = #{orderType},</if>
            <if test="orderPlatform != null">order_platform = #{orderPlatform},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="fundsType != null">funds_type = #{fundsType},</if>
            <if test="payWay != null">pay_way = #{payWay},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="outOrderNumber != null">out_order_number = #{outOrderNumber},</if>
            <if test="tradingOrderNumber != null">trading_order_number = #{tradingOrderNumber},</if>
            <if test="deliverGoodsOrderId != null">deliver_goods_order_id = #{deliverGoodsOrderId},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="learningReportingTime != null">learning_reporting_time = #{learningReportingTime},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="settleStatus != null">settle_status = #{settleStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCdCourseOrderById" parameterType="Long">
        delete from cd_course_order where id = #{id}
    </delete>

    <delete id="deleteCdCourseOrderByIds" parameterType="String">
        delete from cd_course_order where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>