<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.CdFundIncomeMapper">
    
    <resultMap type="CdFundIncome" id="CdFundIncomeResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="promoterId"    column="promoter_id"    />
        <result property="orderId"    column="order_id"    />
        <result property="incomePlatform"    column="income_platform"    />
        <result property="incomeType"    column="income_type"    />
        <result property="orderNum"    column="order_num"    />
        <result property="fundsType"    column="funds_type"    />
        <result property="incomePrice"    column="income_price"    />
        <result property="accountPriceType"    column="account_price_type"    />
        <result property="storeIncomePrice"    column="store_income_price"    />
        <result property="storeName"    column="store_name"    />
        <result property="servicePrice"    column="service_price"    />
        <result property="servicePriceRatio"    column="service_price_ratio"    />
        <result property="realIncomePrice"    column="real_income_price"    />
        <result property="publicationRate"    column="publication_rate"    />
        <result property="publicationFee"    column="publication_fee"    />
        <result property="logisticsFee"    column="logistics_fee"    />
        <result property="liveCommerceRate"    column="live_commerce_rate"    />
        <result property="liveCommerceFee"    column="live_commerce_fee"    />
        <result property="payPrice"    column="pay_price"    />
        <result property="remark"    column="remark"    />
        <result property="orderTime"    column="order_time"    />
        <result property="incomePriceTime"    column="income_price_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCdFundIncomeVo">
        select id, teacher_id, promoter_id, order_id, income_platform, income_type, order_num, funds_type, income_price, account_price_type, store_income_price, store_name, service_price, service_price_ratio, real_income_price, publication_rate, publication_fee, logistics_fee, live_commerce_rate, live_commerce_fee, pay_price, remark, order_time, income_price_time, is_delete, create_time, update_time from cd_fund_income
    </sql>

    <select id="selectCdFundIncomeList" parameterType="CdFundIncome" resultMap="CdFundIncomeResult">
        <include refid="selectCdFundIncomeVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="promoterId != null "> and promoter_id = #{promoterId}</if>
            <if test="orderId != null  and orderId != ''"> and order_id = #{orderId}</if>
            <if test="incomePlatform != null "> and income_platform = #{incomePlatform}</if>
            <if test="incomeType != null "> and income_type = #{incomeType}</if>
            <if test="orderNum != null "> and order_num = #{orderNum}</if>
            <if test="fundsType != null "> and funds_type = #{fundsType}</if>
            <if test="incomePrice != null "> and income_price = #{incomePrice}</if>
            <if test="accountPriceType != null "> and account_price_type = #{accountPriceType}</if>
            <if test="storeIncomePrice != null "> and store_income_price = #{storeIncomePrice}</if>
            <if test="storeName != null  and storeName != ''"> and store_name like concat('%', #{storeName}, '%')</if>
            <if test="servicePrice != null "> and service_price = #{servicePrice}</if>
            <if test="servicePriceRatio != null "> and service_price_ratio = #{servicePriceRatio}</if>
            <if test="realIncomePrice != null "> and real_income_price = #{realIncomePrice}</if>
            <if test="publicationRate != null "> and publication_rate = #{publicationRate}</if>
            <if test="publicationFee != null "> and publication_fee = #{publicationFee}</if>
            <if test="logisticsFee != null "> and logistics_fee = #{logisticsFee}</if>
            <if test="liveCommerceRate != null "> and live_commerce_rate = #{liveCommerceRate}</if>
            <if test="liveCommerceFee != null "> and live_commerce_fee = #{liveCommerceFee}</if>
            <if test="payPrice != null "> and pay_price = #{payPrice}</if>
            <if test="orderTime != null "> and order_time = #{orderTime}</if>
            <if test="incomePriceTime != null "> and income_price_time = #{incomePriceTime}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectCdFundIncomeById" parameterType="Long" resultMap="CdFundIncomeResult">
        <include refid="selectCdFundIncomeVo"/>
        where id = #{id}
    </select>
    <select id="selectCdFundIncomeByOrderId" resultMap="CdFundIncomeResult">
        <include refid="selectCdFundIncomeVo"/>
        where order_id = #{orderId}
    </select>

    <insert id="insertCdFundIncome" parameterType="CdFundIncome">
        insert into cd_fund_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="promoterId != null">promoter_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="incomePlatform != null">income_platform,</if>
            <if test="incomeType != null">income_type,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="fundsType != null">funds_type,</if>
            <if test="incomePrice != null">income_price,</if>
            <if test="accountPriceType != null">account_price_type,</if>
            <if test="storeIncomePrice != null">store_income_price,</if>
            <if test="storeName != null">store_name,</if>
            <if test="servicePrice != null">service_price,</if>
            <if test="servicePriceRatio != null">service_price_ratio,</if>
            <if test="realIncomePrice != null">real_income_price,</if>
            <if test="publicationRate != null">publication_rate,</if>
            <if test="publicationFee != null">publication_fee,</if>
            <if test="logisticsFee != null">logistics_fee,</if>
            <if test="liveCommerceRate != null">live_commerce_rate,</if>
            <if test="liveCommerceFee != null">live_commerce_fee,</if>
            <if test="payPrice != null">pay_price,</if>
            <if test="remark != null">remark,</if>
            <if test="orderTime != null">order_time,</if>
            <if test="incomePriceTime != null">income_price_time,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="promoterId != null">#{promoterId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="incomePlatform != null">#{incomePlatform},</if>
            <if test="incomeType != null">#{incomeType},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="fundsType != null">#{fundsType},</if>
            <if test="incomePrice != null">#{incomePrice},</if>
            <if test="accountPriceType != null">#{accountPriceType},</if>
            <if test="storeIncomePrice != null">#{storeIncomePrice},</if>
            <if test="storeName != null">#{storeName},</if>
            <if test="servicePrice != null">#{servicePrice},</if>
            <if test="servicePriceRatio != null">#{servicePriceRatio},</if>
            <if test="realIncomePrice != null">#{realIncomePrice},</if>
            <if test="publicationRate != null">#{publicationRate},</if>
            <if test="publicationFee != null">#{publicationFee},</if>
            <if test="logisticsFee != null">#{logisticsFee},</if>
            <if test="liveCommerceRate != null">#{liveCommerceRate},</if>
            <if test="liveCommerceFee != null">#{liveCommerceFee},</if>
            <if test="payPrice != null">#{payPrice},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orderTime != null">#{orderTime},</if>
            <if test="incomePriceTime != null">#{incomePriceTime},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCdFundIncome" parameterType="CdFundIncome">
        update cd_fund_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="promoterId != null">promoter_id = #{promoterId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="incomePlatform != null">income_platform = #{incomePlatform},</if>
            <if test="incomeType != null">income_type = #{incomeType},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="fundsType != null">funds_type = #{fundsType},</if>
            <if test="incomePrice != null">income_price = #{incomePrice},</if>
            <if test="accountPriceType != null">account_price_type = #{accountPriceType},</if>
            <if test="storeIncomePrice != null">store_income_price = #{storeIncomePrice},</if>
            <if test="storeName != null">store_name = #{storeName},</if>
            <if test="servicePrice != null">service_price = #{servicePrice},</if>
            <if test="servicePriceRatio != null">service_price_ratio = #{servicePriceRatio},</if>
            <if test="realIncomePrice != null">real_income_price = #{realIncomePrice},</if>
            <if test="publicationRate != null">publication_rate = #{publicationRate},</if>
            <if test="publicationFee != null">publication_fee = #{publicationFee},</if>
            <if test="logisticsFee != null">logistics_fee = #{logisticsFee},</if>
            <if test="liveCommerceRate != null">live_commerce_rate = #{liveCommerceRate},</if>
            <if test="liveCommerceFee != null">live_commerce_fee = #{liveCommerceFee},</if>
            <if test="payPrice != null">pay_price = #{payPrice},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="orderTime != null">order_time = #{orderTime},</if>
            <if test="incomePriceTime != null">income_price_time = #{incomePriceTime},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCdFundIncomeById" parameterType="Long">
        delete from cd_fund_income where id = #{id}
    </delete>

    <delete id="deleteCdFundIncomeByIds" parameterType="String">
        delete from cd_fund_income where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>