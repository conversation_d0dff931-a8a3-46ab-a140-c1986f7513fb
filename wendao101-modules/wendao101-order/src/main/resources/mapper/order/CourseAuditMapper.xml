<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.CourseAuditMapper">

    <resultMap type="CourseAudit" id="CourseAuditResult">
        <result property="id"    column="id"    />
        <result property="courseIdNumber"    column="course_id_number"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseUploadTitle"    column="course_upload_title"    />
        <result property="courseUploadUrl"    column="course_upload_url"    />
        <result property="coverPicUrl"    column="cover_pic_url"    />
        <result property="carouselPicUrls"    column="carousel_pic_urls"    />
        <result property="serialNumber"    column="serial_number"    />
        <result property="title"    column="title"    />
        <result property="subTitle"    column="sub_title"    />
        <result property="price"    column="price"    />
        <result property="originalPrice"    column="original_price"    />
        <result property="courseType"    column="course_type"    />
        <result property="detailType"    column="detail_type"    />
        <result property="detailPicUrls"    column="detail_pic_urls"    />
        <result property="detail"    column="detail"    />
        <result property="marqueeIsOpen"    column="marquee_is_open"    />
        <result property="spreadIsOpen"    column="spread_is_open"    />
        <result property="spreadRate"    column="spread_rate"    />
        <result property="expirationDay"    column="expiration_day"    />
        <result property="publishPlatform"    column="publish_platform"    />
        <result property="watchPlatform"    column="watch_platform"    />
        <result property="visualBuyCarousel"    column="visual_buy_carousel"    />
        <result property="visualLearnNumType"    column="visual_learn_num_type"    />
        <result property="visualLearnNum"    column="visual_learn_num"    />
        <result property="anchorInfo"    column="anchor_info"    />
        <result property="qualification"    column="qualification"    />
        <result property="dyAuditStatus"    column="dy_audit_status"    />
        <result property="auditStatus"    column="audit_status"    />
        <result property="viewsNum"    column="views_num"    />
        <result property="commentsNum"    column="comments_num"    />
        <result property="participateInActivities"    column="participate_in_activities"    />
        <result property="dyQrCodeUrl"    column="dy_qr_code_url"    />
        <result property="ksQrCodeUrl"    column="ks_qr_code_url"    />
        <result property="wxQrCodeUrl"    column="wx_qr_code_url"    />
        <result property="sendEmail"    column="send_email"    />
        <result property="studyUnlock"    column="study_unlock"    />
        <result property="increaseTeacherWecom"    column="increase_teacher_wecom"    />
        <result property="increaseTeacherWxphone"    column="increase_teacher_wxphone"    />
        <result property="classId"    column="class_id"    />
        <result property="courseClassName"    column="course_class_name"    />
        <result property="salesVolume"    column="sales_volume"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="courseOnShelfStatus"    column="course_on_shelf_status"    />
        <result property="auditRejectReason"    column="audit_reject_reason"    />
        <result property="totalCourseCount"    column="total_course_count"    />
        <result property="commentCount"    column="comment_count"    />
        <result property="courseDraftType"    column="course_draft_type"    />
        <result property="viewCount"    column="view_count"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="ksAuditStatus"    column="ks_audit_status"    />
        <result property="wxAuditStatus"    column="wx_audit_status"    />
        <result property="productId"    column="product_id"    />
        <result property="courseDuration"    column="course_duration"    />
        <result property="teacherWecomQrcodeUrl"    column="teacher_wecom_qrcode_url"    />
        <result property="teacherWxphoneContent"    column="teacher_wxphone_content"    />
        <result property="firstClassId"    column="first_class_id"    />
        <result property="firstClassPid"    column="first_class_pid"    />
        <result property="firstClassTitle"    column="first_class_title"    />
        <result property="firstClassDouyinClassId"    column="first_class_douyin_class_id"    />
        <result property="secondClassId"    column="second_class_id"    />
        <result property="secondClassPid"    column="second_class_pid"    />
        <result property="secondClassTitle"    column="second_class_title"    />
        <result property="secondClassDouyinClassId"    column="second_class_douyin_class_id"    />
        <result property="certificationId"    column="certification_id"    />
        <result property="appNameType"    column="app_name_type"    />
        <result property="ksCourseId"    column="ks_course_id"    />

    </resultMap>

    <sql id="selectCourseAuditVo">
        select id, course_id_number, teacher_id, course_upload_title, course_upload_url, cover_pic_url, carousel_pic_urls, serial_number, title, sub_title, price, original_price, course_type, detail_type, detail_pic_urls, detail, marquee_is_open, spread_is_open, spread_rate, expiration_day, publish_platform, watch_platform, visual_buy_carousel, visual_learn_num_type, visual_learn_num, anchor_info, qualification, dy_audit_status, audit_status, views_num, comments_num, participate_in_activities, dy_qr_code_url, ks_qr_code_url, wx_qr_code_url, send_email, study_unlock, increase_teacher_wecom, increase_teacher_wxphone, class_id, course_class_name, sales_volume, is_delete, course_on_shelf_status, audit_reject_reason, total_course_count, comment_count, course_draft_type, view_count, create_time, update_time, ks_audit_status, wx_audit_status, product_id, course_duration, teacher_wecom_qrcode_url, teacher_wxphone_content, first_class_id, first_class_pid, first_class_title, first_class_douyin_class_id, second_class_id, second_class_pid, second_class_title, second_class_douyin_class_id, certification_id, app_name_type, ks_course_id
         from `wendao101-teacher`.course_audit
    </sql>

    <select id="selectCourseAuditList" parameterType="CourseAudit" resultMap="CourseAuditResult">
        <include refid="selectCourseAuditVo"/>
        <where>  
            <if test="courseIdNumber != null "> and course_id_number = #{courseIdNumber}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="courseUploadTitle != null  and courseUploadTitle != ''"> and course_upload_title = #{courseUploadTitle}</if>
            <if test="courseUploadUrl != null  and courseUploadUrl != ''"> and course_upload_url = #{courseUploadUrl}</if>
            <if test="coverPicUrl != null  and coverPicUrl != ''"> and cover_pic_url = #{coverPicUrl}</if>
            <if test="carouselPicUrls != null  and carouselPicUrls != ''"> and carousel_pic_urls = #{carouselPicUrls}</if>
            <if test="serialNumber != null "> and serial_number = #{serialNumber}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="subTitle != null  and subTitle != ''"> and sub_title = #{subTitle}</if>
            <if test="price != null "> and price = #{price}</if>
            <if test="originalPrice != null "> and original_price = #{originalPrice}</if>
            <if test="courseType != null "> and course_type = #{courseType}</if>
            <if test="detailType != null "> and detail_type = #{detailType}</if>
            <if test="detailPicUrls != null  and detailPicUrls != ''"> and detail_pic_urls = #{detailPicUrls}</if>
            <if test="detail != null  and detail != ''"> and detail = #{detail}</if>
            <if test="marqueeIsOpen != null "> and marquee_is_open = #{marqueeIsOpen}</if>
            <if test="spreadIsOpen != null "> and spread_is_open = #{spreadIsOpen}</if>
            <if test="spreadRate != null "> and spread_rate = #{spreadRate}</if>
            <if test="expirationDay != null "> and expiration_day = #{expirationDay}</if>
            <if test="publishPlatform != null  and publishPlatform != ''"> and publish_platform = #{publishPlatform}</if>
            <if test="watchPlatform != null  and watchPlatform != ''"> and watch_platform = #{watchPlatform}</if>
            <if test="visualBuyCarousel != null "> and visual_buy_carousel = #{visualBuyCarousel}</if>
            <if test="visualLearnNumType != null "> and visual_learn_num_type = #{visualLearnNumType}</if>
            <if test="visualLearnNum != null "> and visual_learn_num = #{visualLearnNum}</if>
            <if test="anchorInfo != null  and anchorInfo != ''"> and anchor_info = #{anchorInfo}</if>
            <if test="qualification != null  and qualification != ''"> and qualification = #{qualification}</if>
            <if test="dyAuditStatus != null "> and dy_audit_status = #{dyAuditStatus}</if>
            <if test="auditStatus != null "> and audit_status = #{auditStatus}</if>
            <if test="viewsNum != null "> and views_num = #{viewsNum}</if>
            <if test="commentsNum != null "> and comments_num = #{commentsNum}</if>
            <if test="participateInActivities != null  and participateInActivities != ''"> and participate_in_activities = #{participateInActivities}</if>
            <if test="dyQrCodeUrl != null  and dyQrCodeUrl != ''"> and dy_qr_code_url = #{dyQrCodeUrl}</if>
            <if test="ksQrCodeUrl != null  and ksQrCodeUrl != ''"> and ks_qr_code_url = #{ksQrCodeUrl}</if>
            <if test="wxQrCodeUrl != null  and wxQrCodeUrl != ''"> and wx_qr_code_url = #{wxQrCodeUrl}</if>
            <if test="sendEmail != null "> and send_email = #{sendEmail}</if>
            <if test="studyUnlock != null "> and study_unlock = #{studyUnlock}</if>
            <if test="increaseTeacherWecom != null "> and increase_teacher_wecom = #{increaseTeacherWecom}</if>
            <if test="increaseTeacherWxphone != null "> and increase_teacher_wxphone = #{increaseTeacherWxphone}</if>
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="courseClassName != null  and courseClassName != ''"> and course_class_name like concat('%', #{courseClassName}, '%')</if>
            <if test="salesVolume != null "> and sales_volume = #{salesVolume}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="courseOnShelfStatus != null "> and course_on_shelf_status = #{courseOnShelfStatus}</if>
            <if test="auditRejectReason != null  and auditRejectReason != ''"> and audit_reject_reason = #{auditRejectReason}</if>
            <if test="totalCourseCount != null "> and total_course_count = #{totalCourseCount}</if>
            <if test="commentCount != null "> and comment_count = #{commentCount}</if>
            <if test="courseDraftType != null "> and course_draft_type = #{courseDraftType}</if>
            <if test="viewCount != null "> and view_count = #{viewCount}</if>
            <if test="ksAuditStatus != null "> and ks_audit_status = #{ksAuditStatus}</if>
            <if test="wxAuditStatus != null "> and wx_audit_status = #{wxAuditStatus}</if>
            <if test="productId != null  and productId != ''"> and product_id = #{productId}</if>
            <if test="appNameType != null"> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectCourseAuditById" parameterType="Long" resultMap="CourseAuditResult">
        <include refid="selectCourseAuditVo"/>
        where id = #{id}
    </select>
    <select id="selectCourseAuditByProductId" parameterType="String" resultMap="CourseAuditResult">
        <include refid="selectCourseAuditVo"/>
        where product_id = #{productId} limit 1
    </select>
    <select id="selectCourseIdByProductId" resultType="java.lang.Long">
        select id from `wendao101-teacher`.course where product_id = #{productId} limit 1
    </select>
    <select id="selectAppNameTypeByCourseId" resultType="java.lang.Integer">
        select app_name_type from `wendao101-teacher`.course where id = #{courseId}
    </select>
    <select id="selectWendaoLiveById" resultType="com.wendao101.order.dto.WendaoLiveDTO">
        select id as id,
               teacher_id as teacherId,
               live_type as liveType,
               horizontal_or_vertical as horizontalOrVertical,
               live_name as liveName,
               live_desc as liveDesc,
               start_time as startTime,
               end_time as endTime,
               cover_url as coverUrl,
               warm_up_type as warmUpType,
               warm_picture_video_url as warmPictureVideoUrl,
               warm_video_cover_url as warmVideoCoverUrl,
               live_promotion_cover_url as livePromotionCoverUrl,
               live_detail as liveDetail,
               playback_time_limit as playbackTimeLimit,
               fast_forward_double_speed as fastForwardDoubleSpeed,
               sale_type as saleType,
               price as price,
               original_price as originalPrice,
                password as password,
                live_up_time as liveUpTime,
                record_live_video_url as recordLiveVideoUrl,
                record_live_video_cover_url as recordLiveVideoCoverUrl,
                record_live_convert_video_720_url as recordLiveConvertVideo720Url,
                record_live_convert_video_480_url as recordLiveConvertVideo480Url,
                record_live_video_quality as recordLiveVideoQuality,
                is_delete as isDelete,
                create_time as createTime,
                update_time as updateTime,
                live_record_url as liveRecordUrl,
                push_url as pushUrl,
                play_url as playUrl from `wendao101-teacher`.wendao_live where id = #{liveId} and is_delete=0
    </select>
    <select id="selectCourseAuditByIdAndPlatform" resultMap="CourseAuditResult">
        <include refid="selectCourseAuditVo"/>
        WHERE app_name_type = #{appNameType} and teacher_id = #{teacherId}
        and is_delete = 0
        and course_draft_type = 0
        and id = #{giveCourseId}
        <if test="platform != null and platform == 0"> and dy_audit_status = 2 </if>
        <if test="platform != null and platform == 1"> and wx_audit_status = 2 </if>
        <if test="platform != null and platform == 2"> and ks_audit_status = 2 </if>

        <if test="platform != null and platform == 0"> AND JSON_EXTRACT(publish_platform, '$.dy') = 1 </if>
        <if test="platform != null and platform == 1"> AND JSON_EXTRACT(publish_platform, '$.wx') = 1 </if>
        <if test="platform != null and platform == 2"> AND JSON_EXTRACT(publish_platform, '$.ks') = 1 </if>
        <if test="platform != null and platform == 3"> AND JSON_EXTRACT(publish_platform, '$.gzh') = 1 </if>
        <if test="platform != null and platform == 4"> AND JSON_EXTRACT(publish_platform, '$.dn') = 1 </if>
        <if test="platform != null and platform == 8"> AND JSON_EXTRACT(publish_platform, '$.zsdp') = 1 </if>
        <if test="platform != null and platform == 7"> AND JSON_EXTRACT(publish_platform, '$.xhs') = 1 </if>
        and course_on_shelf_status = 1
    </select>
    <select id="countGiveAwayCourseOrder" resultType="java.lang.Integer">
        select count(1) from `wendao101-order`.course_order
        where course_id = #{giveCourseId} and buyer_user_id = #{userId} and order_platform = #{platform} and app_name_type = #{appNameType} and order_status in (1,6)
    </select>
    <select id="selectWendaoLiveWapById" resultType="com.wendao101.order.dto.WendaoLiveDTO">
        select id as id,
               teacher_id as teacherId,
               live_type as liveType,
               horizontal_or_vertical as horizontalOrVertical,
               live_name as liveName,
               live_desc as liveDesc,
               start_time as startTime,
               end_time as endTime,
               cover_url as coverUrl,
               warm_up_type as warmUpType,
               warm_picture_video_url as warmPictureVideoUrl,
               warm_video_cover_url as warmVideoCoverUrl,
               live_promotion_cover_url as livePromotionCoverUrl,
               live_detail as liveDetail,
               playback_time_limit as playbackTimeLimit,
               fast_forward_double_speed as fastForwardDoubleSpeed,
               sale_type as saleType,
               price as price,
               original_price as originalPrice,
               password as password,
               live_up_time as liveUpTime,
               record_live_video_url as recordLiveVideoUrl,
               record_live_video_cover_url as recordLiveVideoCoverUrl,
               record_live_convert_video_720_url as recordLiveConvertVideo720Url,
               record_live_convert_video_480_url as recordLiveConvertVideo480Url,
               record_live_video_quality as recordLiveVideoQuality,
               is_delete as isDelete,
               create_time as createTime,
               update_time as updateTime,
               live_record_url as liveRecordUrl,
               push_url as pushUrl,
               play_url as playUrl from `wendao101-teacher`.wendao_live_wap where id = #{liveId} and is_delete=0

    </select>
    <select id="selectCourseAuditByIdAndPlatformWap" resultMap="CourseAuditResult">
        <include refid="selectCourseAuditVo"/>
        WHERE app_name_type = #{appNameType} and teacher_id = #{teacherId}
        and is_delete = 0
        and course_draft_type = 0
        and id = #{giveCourseId}
        <if test="platform != null and platform == 5">
         and
         (
           (dy_audit_status = 2 AND JSON_EXTRACT(publish_platform, '$.dy') = 1)
         ||(wx_audit_status = 2 AND JSON_EXTRACT(publish_platform, '$.wx') = 1)
         ||(ks_audit_status = 2 AND JSON_EXTRACT(publish_platform, '$.ks') = 1)
         )
        </if>
        and course_on_shelf_status = 1
    </select>
    <select id="countGiveAwayCourseOrderNoPlatform" resultType="java.lang.Integer">
        select count(1) from `wendao101-order`.course_order
        where course_id = #{giveCourseId} and buyer_user_id = #{userId} and app_name_type = #{appNameType} and order_status in (1,6)
    </select>
    <select id="selectCoursePriceByCourseId" resultType="java.math.BigDecimal">
        select price from `wendao101-teacher`.course where id =#{courseId}
    </select>

    <insert id="insertCourseAudit" parameterType="CourseAudit">
        insert into `wendao101-teacher`.course_audit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="courseIdNumber != null">course_id_number,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseUploadTitle != null">course_upload_title,</if>
            <if test="courseUploadUrl != null">course_upload_url,</if>
            <if test="coverPicUrl != null">cover_pic_url,</if>
            <if test="carouselPicUrls != null">carousel_pic_urls,</if>
            <if test="serialNumber != null">serial_number,</if>
            <if test="title != null">title,</if>
            <if test="subTitle != null">sub_title,</if>
            <if test="price != null">price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="courseType != null">course_type,</if>
            <if test="detailType != null">detail_type,</if>
            <if test="detailPicUrls != null">detail_pic_urls,</if>
            <if test="detail != null">detail,</if>
            <if test="marqueeIsOpen != null">marquee_is_open,</if>
            <if test="spreadIsOpen != null">spread_is_open,</if>
            <if test="spreadRate != null">spread_rate,</if>
            <if test="expirationDay != null">expiration_day,</if>
            <if test="publishPlatform != null">publish_platform,</if>
            <if test="watchPlatform != null">watch_platform,</if>
            <if test="visualBuyCarousel != null">visual_buy_carousel,</if>
            <if test="visualLearnNumType != null">visual_learn_num_type,</if>
            <if test="visualLearnNum != null">visual_learn_num,</if>
            <if test="anchorInfo != null">anchor_info,</if>
            <if test="qualification != null">qualification,</if>
            <if test="dyAuditStatus != null">dy_audit_status,</if>
            <if test="auditStatus != null">audit_status,</if>
            <if test="viewsNum != null">views_num,</if>
            <if test="commentsNum != null">comments_num,</if>
            <if test="participateInActivities != null">participate_in_activities,</if>
            <if test="dyQrCodeUrl != null">dy_qr_code_url,</if>
            <if test="ksQrCodeUrl != null">ks_qr_code_url,</if>
            <if test="wxQrCodeUrl != null">wx_qr_code_url,</if>
            <if test="sendEmail != null">send_email,</if>
            <if test="studyUnlock != null">study_unlock,</if>
            <if test="increaseTeacherWecom != null">increase_teacher_wecom,</if>
            <if test="increaseTeacherWxphone != null">increase_teacher_wxphone,</if>
            <if test="classId != null">class_id,</if>
            <if test="courseClassName != null">course_class_name,</if>
            <if test="salesVolume != null">sales_volume,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="courseOnShelfStatus != null">course_on_shelf_status,</if>
            <if test="auditRejectReason != null">audit_reject_reason,</if>
            <if test="totalCourseCount != null">total_course_count,</if>
            <if test="commentCount != null">comment_count,</if>
            <if test="courseDraftType != null">course_draft_type,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ksAuditStatus != null">ks_audit_status,</if>
            <if test="wxAuditStatus != null">wx_audit_status,</if>
            <if test="productId != null">product_id,</if>
            <if test="courseDuration != null">course_duration,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="courseIdNumber != null">#{courseIdNumber},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseUploadTitle != null">#{courseUploadTitle},</if>
            <if test="courseUploadUrl != null">#{courseUploadUrl},</if>
            <if test="coverPicUrl != null">#{coverPicUrl},</if>
            <if test="carouselPicUrls != null">#{carouselPicUrls},</if>
            <if test="serialNumber != null">#{serialNumber},</if>
            <if test="title != null">#{title},</if>
            <if test="subTitle != null">#{subTitle},</if>
            <if test="price != null">#{price},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="detailType != null">#{detailType},</if>
            <if test="detailPicUrls != null">#{detailPicUrls},</if>
            <if test="detail != null">#{detail},</if>
            <if test="marqueeIsOpen != null">#{marqueeIsOpen},</if>
            <if test="spreadIsOpen != null">#{spreadIsOpen},</if>
            <if test="spreadRate != null">#{spreadRate},</if>
            <if test="expirationDay != null">#{expirationDay},</if>
            <if test="publishPlatform != null">#{publishPlatform},</if>
            <if test="watchPlatform != null">#{watchPlatform},</if>
            <if test="visualBuyCarousel != null">#{visualBuyCarousel},</if>
            <if test="visualLearnNumType != null">#{visualLearnNumType},</if>
            <if test="visualLearnNum != null">#{visualLearnNum},</if>
            <if test="anchorInfo != null">#{anchorInfo},</if>
            <if test="qualification != null">#{qualification},</if>
            <if test="dyAuditStatus != null">#{dyAuditStatus},</if>
            <if test="auditStatus != null">#{auditStatus},</if>
            <if test="viewsNum != null">#{viewsNum},</if>
            <if test="commentsNum != null">#{commentsNum},</if>
            <if test="participateInActivities != null">#{participateInActivities},</if>
            <if test="dyQrCodeUrl != null">#{dyQrCodeUrl},</if>
            <if test="ksQrCodeUrl != null">#{ksQrCodeUrl},</if>
            <if test="wxQrCodeUrl != null">#{wxQrCodeUrl},</if>
            <if test="sendEmail != null">#{sendEmail},</if>
            <if test="studyUnlock != null">#{studyUnlock},</if>
            <if test="increaseTeacherWecom != null">#{increaseTeacherWecom},</if>
            <if test="increaseTeacherWxphone != null">#{increaseTeacherWxphone},</if>
            <if test="classId != null">#{classId},</if>
            <if test="courseClassName != null">#{courseClassName},</if>
            <if test="salesVolume != null">#{salesVolume},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="courseOnShelfStatus != null">#{courseOnShelfStatus},</if>
            <if test="auditRejectReason != null">#{auditRejectReason},</if>
            <if test="totalCourseCount != null">#{totalCourseCount},</if>
            <if test="commentCount != null">#{commentCount},</if>
            <if test="courseDraftType != null">#{courseDraftType},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ksAuditStatus != null">#{ksAuditStatus},</if>
            <if test="wxAuditStatus != null">#{wxAuditStatus},</if>
            <if test="productId != null">#{productId},</if>
            <if test="courseDuration != null">#{courseDuration},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>
    <insert id="insertAuditFailRecord">
        insert into `wendao101-teacher`.course_dy_audit (course_id,audit_status,message,create_time,update_time)
        values ( #{courseId},3,#{message},now(),now() )
    </insert>

    <update id="updateCourseAudit" parameterType="CourseAudit">
        update `wendao101-teacher`.course_audit
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseIdNumber != null">course_id_number = #{courseIdNumber},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseUploadTitle != null">course_upload_title = #{courseUploadTitle},</if>
            <if test="courseUploadUrl != null">course_upload_url = #{courseUploadUrl},</if>
            <if test="coverPicUrl != null">cover_pic_url = #{coverPicUrl},</if>
            <if test="carouselPicUrls != null">carousel_pic_urls = #{carouselPicUrls},</if>
            <if test="serialNumber != null">serial_number = #{serialNumber},</if>
            <if test="title != null">title = #{title},</if>
            <if test="subTitle != null">sub_title = #{subTitle},</if>
            <if test="price != null">price = #{price},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="detailType != null">detail_type = #{detailType},</if>
            <if test="detailPicUrls != null">detail_pic_urls = #{detailPicUrls},</if>
            <if test="detail != null">detail = #{detail},</if>
            <if test="marqueeIsOpen != null">marquee_is_open = #{marqueeIsOpen},</if>
            <if test="spreadIsOpen != null">spread_is_open = #{spreadIsOpen},</if>
            <if test="spreadRate != null">spread_rate = #{spreadRate},</if>
            <if test="expirationDay != null">expiration_day = #{expirationDay},</if>
            <if test="publishPlatform != null">publish_platform = #{publishPlatform},</if>
            <if test="watchPlatform != null">watch_platform = #{watchPlatform},</if>
            <if test="visualBuyCarousel != null">visual_buy_carousel = #{visualBuyCarousel},</if>
            <if test="visualLearnNumType != null">visual_learn_num_type = #{visualLearnNumType},</if>
            <if test="visualLearnNum != null">visual_learn_num = #{visualLearnNum},</if>
            <if test="anchorInfo != null">anchor_info = #{anchorInfo},</if>
            <if test="qualification != null">qualification = #{qualification},</if>
            <if test="dyAuditStatus != null">dy_audit_status = #{dyAuditStatus},</if>
            <if test="auditStatus != null">audit_status = #{auditStatus},</if>
            <if test="viewsNum != null">views_num = #{viewsNum},</if>
            <if test="commentsNum != null">comments_num = #{commentsNum},</if>
            <if test="participateInActivities != null">participate_in_activities = #{participateInActivities},</if>
            <if test="dyQrCodeUrl != null">dy_qr_code_url = #{dyQrCodeUrl},</if>
            <if test="ksQrCodeUrl != null">ks_qr_code_url = #{ksQrCodeUrl},</if>
            <if test="wxQrCodeUrl != null">wx_qr_code_url = #{wxQrCodeUrl},</if>
            <if test="sendEmail != null">send_email = #{sendEmail},</if>
            <if test="studyUnlock != null">study_unlock = #{studyUnlock},</if>
            <if test="increaseTeacherWecom != null">increase_teacher_wecom = #{increaseTeacherWecom},</if>
            <if test="increaseTeacherWxphone != null">increase_teacher_wxphone = #{increaseTeacherWxphone},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="courseClassName != null">course_class_name = #{courseClassName},</if>
            <if test="salesVolume != null">sales_volume = #{salesVolume},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="courseOnShelfStatus != null">course_on_shelf_status = #{courseOnShelfStatus},</if>
            <if test="auditRejectReason != null">audit_reject_reason = #{auditRejectReason},</if>
            <if test="totalCourseCount != null">total_course_count = #{totalCourseCount},</if>
            <if test="commentCount != null">comment_count = #{commentCount},</if>
            <if test="courseDraftType != null">course_draft_type = #{courseDraftType},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ksAuditStatus != null">ks_audit_status = #{ksAuditStatus},</if>
            <if test="wxAuditStatus != null">wx_audit_status = #{wxAuditStatus},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="courseDuration != null">course_duration = #{courseDuration},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateDyResourceUploadStatus">
        update `wendao101-teacher`.dy_resource_upload
        <trim prefix="SET" suffixOverrides=",">
            <if test="produceSourceStatus != null">dy_status = #{produceSourceStatus},</if>
            <if test="reason != null">err_msg = #{reason},</if>
            <if test="produceSourceStatus != null">update_time = now(),</if>
        </trim>
        where dy_resource_uri = #{dyUri}
    </update>
    <update id="updateCourseByProductIdTakeDown">
        update `wendao101-teacher`.course set dy_audit_status=3 where product_id = #{productId}
    </update>
    <update id="updateCourseDyByProductIdTakeDown">
        update `wendao101-teacher`.course_dy set dy_audit_status=3 where product_id = #{productId}
    </update>
    <update id="updateCourseAuditByProductIdTakeDown">
        update `wendao101-teacher`.course_audit set dy_audit_status=3 where product_id = #{productId}
    </update>

    <delete id="deleteCourseAuditById" parameterType="Long">
        delete from `wendao101-teacher`.course_audit where id = #{id}
    </delete>

    <delete id="deleteCourseAuditByIds" parameterType="String">
        delete from `wendao101-teacher`.course_audit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>