<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.CourseGiveALikeMapper">
    
    <resultMap type="CourseGiveALike" id="CourseGiveALikeResult">
        <result property="id"    column="id"    />
        <result property="userId"    column="user_id"    />
        <result property="reviewCourseId"    column="review_course_id"    />
        <result property="isGiveALike"    column="is_give_a_like"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectCourseGiveALikeVo">
        select id, user_id, review_course_id, is_give_a_like, create_time, update_time from course_give_a_like
    </sql>

    <select id="selectCourseGiveALikeList" parameterType="CourseGiveALike" resultMap="CourseGiveALikeResult">
        <include refid="selectCourseGiveALikeVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="reviewCourseId != null "> and review_course_id = #{reviewCourseId}</if>
            <if test="isGiveALike != null "> and is_give_a_like = #{isGiveALike}</if>
        </where>
    </select>
    
    <select id="selectCourseGiveALikeById" parameterType="Long" resultMap="CourseGiveALikeResult">
        <include refid="selectCourseGiveALikeVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertCourseGiveALike" parameterType="CourseGiveALike" useGeneratedKeys="true" keyProperty="id">
        insert into course_give_a_like
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="reviewCourseId != null">review_course_id,</if>
            <if test="isGiveALike != null">is_give_a_like,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="reviewCourseId != null">#{reviewCourseId},</if>
            <if test="isGiveALike != null">#{isGiveALike},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateCourseGiveALike" parameterType="CourseGiveALike">
        update course_give_a_like
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="reviewCourseId != null">review_course_id = #{reviewCourseId},</if>
            <if test="isGiveALike != null">is_give_a_like = #{isGiveALike},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCourseGiveALikeById" parameterType="Long">
        delete from course_give_a_like where id = #{id}
    </delete>

    <delete id="deleteCourseGiveALikeByIds" parameterType="String">
        delete from course_give_a_like where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getReviewGiveALikeNum" parameterType="Long" resultType="Integer">
        select count(1)
        from `wendao101-order`.course_give_a_like
        where review_course_id =#{id}
          and is_give_a_like = 1
    </select>
</mapper>