<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.DoudianPreDepositMapper">
    
    <resultMap type="DoudianPreDeposit" id="DoudianPreDepositResult">
        <result property="id"    column="id"    />
        <result property="shopId"    column="shop_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="payWay"    column="pay_way"    />
        <result property="payStatus"    column="pay_status"    />
        <result property="depositOrderId"    column="deposit_order_id"    />
        <result property="outOrderNumber"    column="out_order_number"    />
        <result property="productName"    column="product_name"    />
        <result property="productDesc"    column="product_desc"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="orderMoney"    column="order_money"    />
        <result property="payTime"    column="pay_time"    />
    </resultMap>

    <sql id="selectDoudianPreDepositVo">
        select id, shop_id, shop_name, pay_way, pay_status, deposit_order_id, out_order_number, product_name, 
        product_desc, create_time, update_time, teacher_id, order_money, pay_time from doudian_pre_deposit
    </sql>

    <select id="selectDoudianPreDepositList" parameterType="DoudianPreDeposit" resultMap="DoudianPreDepositResult">
        <include refid="selectDoudianPreDepositVo"/>
        <where>  
            <if test="shopId != null "> and shop_id = #{shopId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="payWay != null  and payWay != ''"> and pay_way = #{payWay}</if>
            <if test="payStatus != null "> and pay_status = #{payStatus}</if>
            <if test="depositOrderId != null  and depositOrderId != ''"> and deposit_order_id = #{depositOrderId}</if>
            <if test="outOrderNumber != null  and outOrderNumber != ''"> and out_order_number = #{outOrderNumber}</if>
            <if test="productName != null  and productName != ''"> and product_name like concat('%', #{productName}, '%')</if>
            <if test="productDesc != null  and productDesc != ''"> and product_desc = #{productDesc}</if>
            <if test="teacherId != null"> and teacher_id = #{teacherId}</if>
        </where>
    </select>
    
    <select id="selectDoudianPreDepositById" parameterType="Long" resultMap="DoudianPreDepositResult">
        <include refid="selectDoudianPreDepositVo"/>
        where id = #{id}
    </select>
    <select id="selectDoudianPreDepositByDepositOrderId" resultMap="DoudianPreDepositResult">
        <include refid="selectDoudianPreDepositVo"/>
        where deposit_order_id = #{depositOrderId} limit 1
    </select>
    <select id="selectVersionManageByVersionId" resultType="com.wendao101.order.dto.SaleVersionManageDTO">
        select id,
               version_name as versionName,
               version_image as versionImage,
               version_id as versionId,
               version_desc as versionDesc,
               is_enabled as isEnabled,
               version_price as versionPrice,
               margin_margin as marginMargin,
               gmv_guarantee as gmvGuarantee,
               version_purchase_cycle as versionPurchaseCycle,
               rate from `ry`.sale_version_manage where version_id = #{versionId}
    </select>
    <select id="selectTeacherEnterInfoByTeacherId" resultType="com.wendao101.order.dto.TeacherEnterInfoDTO">
        select `shop_id` as teacherId,
               `version`,
               `service_begin_time` as serviceBeginTime,
               `service_end_time` as serviceEndTime
        from `ry`.enter_information where shop_id = #{teacherId} limit 1
    </select>
    <select id="selectEnableVersionManage" resultType="com.wendao101.order.dto.SaleVersionManageDTO">
        select id,
               version_name as versionName,
               version_image as versionImage,
               version_id as versionId,
               version_desc as versionDesc,
               is_enabled as isEnabled,
               version_price as versionPrice,
               margin_margin as marginMargin,
               gmv_guarantee as gmvGuarantee,
               version_purchase_cycle as versionPurchaseCycle,
               rate from `ry`.sale_version_manage where is_enabled = 1 order by version_id asc
    </select>

    <insert id="insertDoudianPreDeposit" parameterType="DoudianPreDeposit" useGeneratedKeys="true" keyProperty="id">
        insert into doudian_pre_deposit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="shopId != null">shop_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="payWay != null">pay_way,</if>
            <if test="payStatus != null">pay_status,</if>
            <if test="depositOrderId != null">deposit_order_id,</if>
            <if test="outOrderNumber != null">out_order_number,</if>
            <if test="productName != null">product_name,</if>
            <if test="productDesc != null">product_desc,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="orderMoney != null">order_money,</if>
            <if test="payTime != null">pay_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="shopId != null">#{shopId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="payWay != null">#{payWay},</if>
            <if test="payStatus != null">#{payStatus},</if>
            <if test="depositOrderId != null">#{depositOrderId},</if>
            <if test="outOrderNumber != null">#{outOrderNumber},</if>
            <if test="productName != null">#{productName},</if>
            <if test="productDesc != null">#{productDesc},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="orderMoney != null">#{orderMoney},</if>
            <if test="payTime != null">#{payTime},</if>
         </trim>
    </insert>

    <update id="updateDoudianPreDeposit" parameterType="DoudianPreDeposit">
        update doudian_pre_deposit
        <trim prefix="SET" suffixOverrides=",">
            <if test="shopId != null">shop_id = #{shopId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="payWay != null">pay_way = #{payWay},</if>
            <if test="payStatus != null">pay_status = #{payStatus},</if>
            <if test="depositOrderId != null">deposit_order_id = #{depositOrderId},</if>
            <if test="outOrderNumber != null">out_order_number = #{outOrderNumber},</if>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productDesc != null">product_desc = #{productDesc},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="orderMoney != null">order_money = #{orderMoney},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="updateRyEnterInfo">
        update `ry`.enter_information set `version` = #{version},
        `service_begin_time` = #{serviceBeginTime},
        `service_end_time` = #{serviceEndTime}
        where `shop_id` = #{teacherId}
    </update>
    <update id="updateTeacherInfo">
        update `wendao101-teacher`.`t_teacher` set `version` = #{version},
        `end_date` = #{serviceEndTime}
        where `teacher_id` = #{teacherId}
    </update>

    <delete id="deleteDoudianPreDepositById" parameterType="Long">
        delete from doudian_pre_deposit where id = #{id}
    </delete>

    <delete id="deleteDoudianPreDepositByIds" parameterType="String">
        delete from doudian_pre_deposit where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
