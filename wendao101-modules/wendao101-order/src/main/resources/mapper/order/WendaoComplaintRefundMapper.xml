<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.order.mapper.WendaoComplaintRefundMapper">

    <resultMap type="WendaoComplaintRefund" id="WendaoComplaintRefundResult">
        <result property="complaintId" column="complaint_id"/>
        <result property="teacherId" column="teacher_id"/>
        <result property="orderId" column="order_id"/>
        <result property="courseId" column="course_id"/>
        <result property="title" column="title"/>
        <result property="complaintRefundNumber" column="complaint_refund_number"/>
        <result property="price" column="price"/>
        <result property="originalPrice" column="original_price"/>
        <result property="buyerPhoneNumber" column="buyer_phone_number"/>
        <result property="question" column="question"/>
        <result property="buyerNickName" column="buyer_nick_name"/>
        <result property="source" column="source"/>
        <result property="complaintTime" column="complaint_time"/>
        <result property="complaintStatus" column="complaint_status"/>
        <result property="complaintImgs" column="complaint_imgs"/>
        <result property="complaintDesc" column="complaint_desc"/>
        <result property="complaintNumber" column="complaint_number"/>
        <result property="handlingMethod" column="handling_method"/>
        <result property="handlingDesc" column="handling_desc"/>
        <result property="handlingImgs" column="handling_imgs"/>
        <result property="courseCoverUrl" column="course_cover_url"/>
        <result property="refundMoney" column="refund_money"/>
        <result property="orderRealPayMoney" column="order_real_pay_money"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userId" column="user_id"/>
        <result property="openid" column="openid"/>
        <result property="appId" column="appId"/>
        <result property="complaintType" column="complaint_type"/>
        <result property="complaintTypeContent" column="complaint_type_content"/>
        <result property="alipayName" column="alipay_name"/>
        <result property="alipayAccount" column="alipay_account"/>
        <result property="operator" column="operator"/>
    </resultMap>

    <sql id="selectWendaoComplaintRefundVo">
        select complaint_id,
               teacher_id,
               order_id,
               course_id,
               title,
               complaint_refund_number,
               price,
               original_price,
               buyer_phone_number,
               question,
               buyer_nick_name,
               source,
               complaint_time,
               complaint_status,
               complaint_imgs,
               complaint_desc,
               complaint_number,
               handling_method,
               handling_desc,
               handling_imgs,
               course_cover_url,
               refund_money,
               order_real_pay_money,
               create_time,
               update_time,
               user_id,
               openid,
               appId,
               complaint_type,
               complaint_type_content,
               alipay_name,
               alipay_account,
               operator
        from wendao_complaint_refund
    </sql>

    <select id="selectWendaoComplaintRefundList" resultType="com.wendao101.order.vo.WendaoComplaintRefundVo">
        select
        w.complaint_id complaintId,
        w.teacher_id teacherId,
        w.order_id orderId,
        w.course_id courseId,
        w.title title,
        w.complaint_refund_number complaintRefundnumber,
        w.price,
        w.original_price originalPrice,
        w.buyer_phone_number buyerPhoneNumber,
        w.question,
        w.buyer_nick_name buyerNickName,
        w.source,
        w.handling_time handlingTime,
        w.complaint_time complaintTime,
        w.complaint_status complaintStatus,
        w.complaint_imgs complaintImgs,
        w.complaint_desc complaintDesc,
        w.complaint_number complaintNumber,
        w.handling_method handlingMethod,
        w.handling_desc handlingDesc,
        w.handling_imgs handlingImgs,
        w.course_cover_url courseCoverUrl,
        w.refund_money refundMoney,
        w.order_real_pay_money orderRealPayMoney,
        w.create_time createTime,
        w.update_time updateTime,
        w.user_id userId,
        w.openid,
        w.appId,
        w.complaint_type complaintType,
        w.complaint_type_content complaintTypeContent,
        w.alipay_name alipayName,
        w.alipay_account alipayAccount,
        w.operator,
        c.order_time orderTime,
        c.course_duration courseDuration,
        c.study_duration studuDuration,
        c.trading_order_number tradingOrderNumber,
        t.mobile,
        t.shop_name shopName
        from wendao_complaint_refund w
        left join `wendao101-teacher`.t_teacher t on w.teacher_id = t.teacher_id
        left join course_order c on w.order_id = c.order_id
        <where>
            <if test="complaintId != null ">and w.complaint_id = #{complaintId}</if>
            <if test="teacherId != null ">and w.teacher_id = #{teacherId}</if>
            <if test="orderId != null  and orderId != ''">and w.order_id = #{orderId}</if>
            <if test="courseId != null ">and w.course_id = #{courseId}</if>
            <if test="title != null  and title != ''">and w.title like concat('%', #{title}, '%')</if>
            <if test="complaintRefundNumber != null and complaintRefundNumber !=''">and w.complaint_refund_number =
                #{complaintRefundNumber}
            </if>
            <if test="price != null ">and w.price = #{price}</if>
            <if test="originalPrice != null ">and w.original_price = #{originalPrice}</if>
            <if test="buyerPhoneNumber != null  and buyerPhoneNumber != ''">and w.buyer_phone_number =
                #{buyerPhoneNumber}
            </if>
            <if test="question != null  and question != ''">and w.question = #{question}</if>
            <if test="buyerNickName != null  and buyerNickName != ''">and w.buyer_nick_name like concat('%',
                #{buyerNickName}, '%')
            </if>
            <if test="source != null ">and w.source = #{source}</if>
            <if test="complaintTime != null ">and w.complaint_time = #{complaintTime}</if>
            <if test="complaintStatus != null">and w.complaint_status = #{complaintStatus}</if>
            <if test="complaintImgs != null  and complaintImgs != ''">and w.complaint_imgs = #{complaintImgs}</if>
            <if test="complaintDesc != null  and complaintDesc != ''">and w.complaint_desc = #{complaintDesc}</if>
            <if test="complaintNumber != null  and complaintNumber != ''">and w.complaint_number = #{complaintNumber}
            </if>
            <if test="handlingMethod != null  and handlingMethod != ''">and w.handling_method = #{handlingMethod}</if>
            <if test="handlingDesc != null  and handlingDesc != ''">and w.handling_desc = #{handlingDesc}</if>
            <if test="handlingImgs != null  and handlingImgs != ''">and w.handling_imgs = #{handlingImgs}</if>
            <if test="courseCoverUrl != null  and courseCoverUrl != ''">and w.course_cover_url = #{courseCoverUrl}</if>
            <if test="refundMoney != null ">and w.refund_money = #{refundMoney}</if>
            <if test="orderRealPayMoney != null ">and w.order_real_pay_money = #{orderRealPayMoney}</if>
            <if test="createTime != null ">and w.create_time = #{createTime}</if>
            <if test="updateTime != null ">and w.update_time = #{updateTime}</if>
            <if test="userId != null ">and w.user_id = #{userId}</if>
            <if test="openid != null  and openid != ''">and w.openid = #{openid}</if>
            <if test="appId != null  and appId != ''">and w.appId = #{appId}</if>
            <if test="complaintType != null ">and w.complaint_type = #{complaintType}</if>
            <if test="beginTime != null">and w.complaint_time &lt;= #{endTime}</if>
            <if test="beginTime != null">and w.complaint_time >= #{beginTime}</if>
            <if test="complaintTypeContent != null  and complaintTypeContent != ''">and w.complaint_type_content =
                #{complaintTypeContent}
            </if>
            <if test="alipayName != null  and alipayName != ''">and w.alipay_name like concat('%', #{alipayName}, '%')
            </if>
            <if test="alipayAccount != null  and alipayAccount != ''">and w.alipay_account = #{alipayAccount}</if>
            <if test="operator != null  and operator != ''">and w.operator = #{operator}</if>
        </where>
        <if test="complaintStatus !=  0 and complaintStatus !=  3 and complaintStatus !=  2">
            ORDER BY c.order_time DESC
        </if>
    </select>

    <select id="selectWendaoComplaintRefundByComplaintId" parameterType="Integer"
            resultType="com.wendao101.order.vo.WendaoComplaintRefundVo">
        select w.complaint_id           complaintId,
               w.teacher_id             teacherId,
               w.order_id               orderId,
               w.course_id              courseId,
               w.title,
               w.handling_time          handlingTime,
               w.complaint_refund_number complaintRefundNumber,
               w.price,
               w.original_price         originalPrice,
               w.buyer_phone_number     buyerPhoneNumber,
               w.question,
               w.buyer_nick_name        buyerNickName,
               w.source,
               w.complaint_time         complaintTime,
               w.complaint_status       complaintStatus,
               w.complaint_imgs         complaintImgs,
               w.complaint_desc         complaintDesc,
               w.complaint_number       complaintNumber,
               w.handling_method        handlingMethod,
               w.handling_desc          handlingDesc,
               w.handling_imgs          handlingImgs,
               w.course_cover_url       courseCoverUrl,
               w.refund_money           refundMoney,
               w.order_real_pay_money   orderRealPayMoney,
               w.create_time            createTime,
               w.update_time            updateTime,
               w.user_id                userId,
               w.openid,
               w.appId,
               w.complaint_type         complaintType,
               w.complaint_type_content complaintTypeContent,
               w.alipay_name            alipayName,
               w.alipay_account         alipayAccount,
               w.operator,
               t.shop_name              shopName,
               t.mobile,
               t.dy_uid                 dyUid,

               c.trading_order_number   tradingOrderNumber,
               c.study_duration         studyDuration,
               c.course_duration        courseDuration,
               c.order_time             orderTime,
               c.promoter_id            promoterId,
               c.promoter_name          promoterName,
               c.promoter_mobile        promoterMobile
        from wendao_complaint_refund w
                 left join `wendao101-teacher`.t_teacher t on w.teacher_id = t.teacher_id
                 left join course_order c on w.order_id = c.order_id
        where w.complaint_id = #{complaintId}
    </select>

    <select id="selectAll" resultType="com.wendao101.order.domain.WendaoComplaintRefund">
        <include refid="selectWendaoComplaintRefundVo"></include>
        <where>
            <if test="teacherId != null">and teacher_id = #{teacherId}</if>
            <if test="orderId != null and orderId != ''">and order_id = #{orderId}</if>
            <if test="complaintId != null">and complaint_id = #{complaintId}</if>
            <if test="userId != null">and user_id = #{userId}</if>
        </where>

    </select>
    <select id="selectWendaoComplaint" resultType="com.wendao101.order.domain.CourseRefund">
        select w.complaint_id complaintId,
        w.teacher_id teacherId,
        w.order_id orderId,
        w.course_id courseId,
        w.title courseTitle,
        w.complaint_refund_number refundId,
        w.price coursePrice,
        w.original_price originalPrice,
        w.buyer_phone_number buyerUserMobile,
        w.buyer_nick_name buyerUserName,
        w.source refundPlatform,
        w.complaint_status complaintStatus,
        w.complaint_imgs receiptImg,
        w.complaint_desc supplimentalDescription,
        w.course_cover_url courseImgUrl,
        w.refund_money refundPrice,
        w.user_id buyerUserId,
        w.complaint_type complaintType,
        w.alipay_name,
        w.alipay_account,
        w.operator,
        c.order_time orderTime,
        c.promoter_id promoterId,
        c.promoter_name promoterName,
        c.promoter_mobile promoterMobile,
        c.course_duration courseDuration,
        c.study_duration studyDuration
        from wendao_complaint_refund w
        left join course_order c on w.order_id = c.order_id
        <where>
            <if test="complaintId != null">and w.complaint_id = #{complaintId}</if>
            <if test="orderId != null and orderId != ''">and w.order_id = #{orderId}</if>
        </where>
    </select>
    <select id="getRefundListByRefundNumber" resultMap="WendaoComplaintRefundResult">
        <include refid="selectWendaoComplaintRefundVo"/>
         where complaint_id = #{complaintId}
    </select>
    <select id="getAlipayRefundByOrderId" resultType="com.wendao101.order.domain.AlipayRefund">
        select teacher_id teacherId,
               order_id orderId,
               complaint_refund_number complaintRefundNumber,
               alipay_name alipayName,
               alipay_account alipayAccout,
               refund_money refundMoney,
               source
        from wendao_complaint_refund
        where order_id = #{orderId}
    </select>
    <select id="getmessageById" resultType="com.wendao101.order.vo.CourseRefundVO">
        select
            w.teacher_id             teacherId,
            w.order_id               orderId,
            w.course_id              courseId,
            w.title courseTitle,
            w.price coursePrice,
            w.original_price         originalPrice,
            w.buyer_phone_number     buyerUserMobile,

            w.buyer_nick_name        buyerUserName,
            w.source refundPlatform,
            w.complaint_time         complaintTime,
            w.complaint_status       complaintStatus,
            w.complaint_imgs         complaintImgs,
            w.complaint_desc         complaintDesc,
            w.complaint_number       complaintNumber,
            w.handling_method        handlingMethod,
            w.handling_desc          handlingDesc,
            w.handling_imgs          handlingImgs,
            w.course_cover_url       courseImgUrl,
            w.refund_money           refundPrice,
            w.order_real_pay_money   orderRealPayMoney,
            w.create_time            createTime,
            w.update_time            updateTime,
            w.user_id buyerUserId,
            w.openid,
            w.appId,
            w.complaint_type         complaintType,
            w.complaint_type_content complaintTypeContent,
            w.alipay_name            alipayName,
            w.alipay_account         alipayAccount,
            w.operator,
            t.shop_name              shopName,
            t.mobile,
            t.dy_uid                 dyUid,

            c.trading_order_number   tradingOrderNumber,
            c.study_duration         studyDuration,
            c.course_duration        courseDuration,
            c.order_time             orderTime,
            c.promoter_id promoterId,
            c.promoter_name promoterName,
            c.promoter_mobile promoterMobile,
            c.pay_way payWay
        from wendao_complaint_refund w
                 left join `wendao101-teacher`.t_teacher t on w.teacher_id = t.teacher_id
                 left join course_order c on w.order_id = c.order_id
        where w.order_id = #{orderId}
    </select>
    <select id="selectOrderIdByComplaintId" resultType="java.lang.String">
        select order_id from wendao_complaint_refund where complaint_id = #{complaintId}
    </select>


    <insert id="insertWendaoComplaintRefund" parameterType="com.wendao101.order.domain.WendaoComplaintRefund"
            useGeneratedKeys="true" keyProperty="complaintId">
        insert into wendao_complaint_refund
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="orderId != null">order_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="title != null">title,</if>
            <if test="complaintRefundNumber != null">complaint_refund_number,</if>
            <if test="price != null">price,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="buyerPhoneNumber != null">buyer_phone_number,</if>
            <if test="question != null">question,</if>
            <if test="buyerNickName != null">buyer_nick_name,</if>
            <if test="source != null">source,</if>
            <if test="complaintTime != null">complaint_time,</if>
            <if test="complaintStatus != null">complaint_status,</if>
            <if test="complaintImgs != null">complaint_imgs,</if>
            <if test="complaintDesc != null">complaint_desc,</if>
            <if test="complaintNumber != null">complaint_number,</if>
            <if test="handlingMethod != null">handling_method,</if>
            <if test="handlingDesc != null">handling_desc,</if>
            <if test="handlingImgs != null">handling_imgs,</if>
            <if test="courseCoverUrl != null">course_cover_url,</if>
            <if test="refundMoney != null">refund_money,</if>
            <if test="orderRealPayMoney != null">order_real_pay_money,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="openid != null">openid,</if>
            <if test="appId != null">appId,</if>
            <if test="complaintType != null">complaint_type,</if>
            <if test="complaintTypeContent != null">complaint_type_content,</if>
            <if test="alipayName != null">alipay_name,</if>
            <if test="alipayAccount != null">alipay_account,</if>
            <if test="operator != null">operator,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="orderId != null">#{orderId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="title != null">#{title},</if>
            <if test="complaintRefundNumber != null">#{complaintRefundNumber},</if>
            <if test="price != null">#{price},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="buyerPhoneNumber != null">#{buyerPhoneNumber},</if>
            <if test="question != null">#{question},</if>
            <if test="buyerNickName != null">#{buyerNickName},</if>
            <if test="source != null">#{source},</if>
            <if test="complaintTime != null">#{complaintTime},</if>
            <if test="complaintStatus != null">#{complaintStatus},</if>
            <if test="complaintImgs != null">#{complaintImgs},</if>
            <if test="complaintDesc != null">#{complaintDesc},</if>
            <if test="complaintNumber != null">#{complaintNumber},</if>
            <if test="handlingMethod != null">#{handlingMethod},</if>
            <if test="handlingDesc != null">#{handlingDesc},</if>
            <if test="handlingImgs != null">#{handlingImgs},</if>
            <if test="courseCoverUrl != null">#{courseCoverUrl},</if>
            <if test="refundMoney != null">#{refundMoney},</if>
            <if test="orderRealPayMoney != null">#{orderRealPayMoney},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="openid != null">#{openid},</if>
            <if test="appId != null">#{appId},</if>
            <if test="complaintType != null">#{complaintType},</if>
            <if test="complaintTypeContent != null">#{complaintTypeContent},</if>
            <if test="alipayName != null">#{alipayName},</if>
            <if test="alipayAccount != null">#{alipayAccount},</if>
            <if test="operator != null">#{operator},</if>
        </trim>
    </insert>

    <update id="updateWendaoComplaintRefund" parameterType="WendaoComplaintRefund">
        update wendao_complaint_refund
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="orderId != null">order_id = #{orderId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="title != null">title = #{title},</if>
            <if test="complaintRefundNumber != null">complaint_refund_number = #{complaintRefundNumber},</if>
            <if test="price != null">price = #{price},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="buyerPhoneNumber != null">buyer_phone_number = #{buyerPhoneNumber},</if>
            <if test="question != null">question = #{question},</if>
            <if test="buyerNickName != null">buyer_nick_name = #{buyerNickName},</if>
            <if test="source != null">source = #{source},</if>
            <if test="complaintTime != null">complaint_time = #{complaintTime},</if>
            <if test="complaintStatus != null">complaint_status = #{complaintStatus},</if>
            <if test="complaintImgs != null">complaint_imgs = #{complaintImgs},</if>
            <if test="complaintDesc != null">complaint_desc = #{complaintDesc},</if>
            <if test="complaintNumber != null">complaint_number = #{complaintNumber},</if>
            <if test="handlingMethod != null">handling_method = #{handlingMethod},</if>
            <if test="handlingDesc != null">handling_desc = #{handlingDesc},</if>
            <if test="handlingImgs != null">handling_imgs = #{handlingImgs},</if>
            <if test="courseCoverUrl != null">course_cover_url = #{courseCoverUrl},</if>
            <if test="refundMoney != null">refund_money = #{refundMoney},</if>
            <if test="orderRealPayMoney != null">order_real_pay_money = #{orderRealPayMoney},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="appId != null">appId = #{appId},</if>
            <if test="complaintType != null">complaint_type = #{complaintType},</if>
            <if test="complaintTypeContent != null">complaint_type_content = #{complaintTypeContent},</if>
            <if test="alipayName != null">alipay_name = #{alipayName},</if>
            <if test="alipayAccount != null">alipay_account = #{alipayAccount},</if>
            <if test="operator != null">operator = #{operator},</if>
        </trim>
        where complaint_id = #{complaintId}
    </update>

    <delete id="deleteWendaoComplaintRefundByComplaintId" parameterType="Integer">
        delete
        from wendao_complaint_refund
        where complaint_id = #{complaintId}
    </delete>

    <delete id="deleteWendaoComplaintRefundByComplaintIds" parameterType="String">
        delete from wendao_complaint_refund where complaint_id in
        <foreach item="complaintId" collection="array" open="(" separator="," close=")">
            #{complaintId}
        </foreach>
    </delete>

    <delete id="deleteWendaoComplaintRefundByOrderId">
        delete
        from wendao_complaint_refund
        where order_id = #{orderId}
    </delete>


    <update id="updateCourseOrderStatus">
        update course_order set order_status = 2,update_time = #{updateTime},funds_type = #{fundsType}
           where order_id = #{orderId}
    </update>


    <update id="updateWendaoComplaint">
        update wendao_complaint set complaint_status = 3,<if test="updateTime != null">update_time = #{updateTime}</if>
        <where>
             and order_id = #{orderId}
            <if test="complaintId != null">and complaint_id = #{complaintId}</if>
        </where>
    </update>


    <update id="updateComplaintStatus">
        update wendao_complaint_refund set complaint_status = 1,update_time = #{updateTime},handling_time = #{handlingTime} where order_id = #{orderId}
    </update>

    <update id="updateStatusByOrderId">
        update wendao_complaint_refund set complaint_status = 3,update_time = #{updateTime},handling_time = #{handlingTime}
        <where>
            <if test="complaintId != null">and complaint_id = #{complaintId}</if>
            and order_id = #{orderId}
            <if test="complaintOrderNumber != null and complaintOrderNumber !=''">and complaint_order_number = #{complaintOrderNumber}</if>
        </where>
    </update>

    <update id="updateStatusById">
        update wendao_complaint_refund set complaint_status = 2,<if test="updateTime != null">update_time = #{updateTime},</if>
        <if test="handlingTime != null">handling_time = #{handlingTime}</if>
        <where>
            <if test="complaintId != null">and complaint_id = #{complaintId}</if>
        and order_id = #{orderId}
        </where>
    </update>


    <update id="updateComplaintStatusByOrderId">
        update wendao_complaint_refund set complaint_status = 1 and update_time = NOW()
        where order_id = #{orderId}
    </update>
    <update id="updateWendaoComplaintRefundStatus">
        update wendao_complaint_refund set complaint_status = 5,update_time = #{updateTime},handling_desc = #{handlingDesc},handling_time = #{handlingTime}
        where order_id = #{orderId} and complaint_id = #{complaintId}
    </update>


</mapper>