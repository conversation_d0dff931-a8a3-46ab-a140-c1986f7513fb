package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.MLiveCourseMap;

/**
 * 直播页和课程关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
public interface MLiveCourseMapMapper 
{
    /**
     * 查询直播页和课程关联
     * 
     * @param id 直播页和课程关联主键
     * @return 直播页和课程关联
     */
    public MLiveCourseMap selectMLiveCourseMapById(Long id);

    /**
     * 查询直播页和课程关联列表
     * 
     * @param mLiveCourseMap 直播页和课程关联
     * @return 直播页和课程关联集合
     */
    public List<MLiveCourseMap> selectMLiveCourseMapList(MLiveCourseMap mLiveCourseMap);

    /**
     * 新增直播页和课程关联
     * 
     * @param mLiveCourseMap 直播页和课程关联
     * @return 结果
     */
    public int insertMLiveCourseMap(MLiveCourseMap mLiveCourseMap);

    /**
     * 修改直播页和课程关联
     * 
     * @param mLiveCourseMap 直播页和课程关联
     * @return 结果
     */
    public int updateMLiveCourseMap(MLiveCourseMap mLiveCourseMap);

    /**
     * 删除直播页和课程关联
     * 
     * @param id 直播页和课程关联主键
     * @return 结果
     */
    public int deleteMLiveCourseMapById(Long id);

    /**
     * 批量删除直播页和课程关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMLiveCourseMapByIds(Long[] ids);
}
