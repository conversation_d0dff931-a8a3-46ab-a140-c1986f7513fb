package com.wendao101.teacher.controller;

import com.wendao101.common.core.utils.PageUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.dto.MSpreadAdminAndReceiveDTO;
import com.wendao101.teacher.dto.MSpreadAdminDTO;
import com.wendao101.teacher.service.IMSpreadAdminService;
import com.wendao101.teacher.vo.MSpreadAdminAndReceiveVO;
import com.wendao101.teacher.vo.MSpreadAdminAndStatusVO;
import com.wendao101.teacher.vo.MSpreadAdminVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 推广员管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-05
 */
@RestController
@RequestMapping("/spread_admin")
public class MSpreadAdminController extends BaseController {
    @Autowired
    private IMSpreadAdminService mSpreadAdminService;

    /**
     * 查询推广员基本数据列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody MSpreadAdminVO mSpreadAdminVO, HttpServletRequest request) {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        mSpreadAdminVO.setTeacherId(teacherId);
        startPage();
        PageUtils.startPage(mSpreadAdminVO.getPageNum(), mSpreadAdminVO.getPageSize());
        List<MSpreadAdminDTO> list = mSpreadAdminService.selectMSpreadAdminList(mSpreadAdminVO);
        return getDataTable(list);
    }

    /**
     * 修改推广员状态
     * @param mSpreadAdminAndStatusVO
     * @return
     */
    @PostMapping("/updateStatus")
    public AjaxResult remove(@RequestBody MSpreadAdminAndStatusVO mSpreadAdminAndStatusVO) {
        return toAjax(mSpreadAdminService.updateStatus(mSpreadAdminAndStatusVO));
    }

    /**
     * 查询推广员领取记录列表
     */
    @PostMapping("/receive/list")
    public TableDataInfo receiveList(@RequestBody MSpreadAdminAndReceiveVO mSpreadAdminAndReceiveVO, HttpServletRequest request) {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        mSpreadAdminAndReceiveVO.setTeacherId(teacherId);
        startPage();
        List<MSpreadAdminAndReceiveDTO> list = mSpreadAdminService.selectReceiveList(mSpreadAdminAndReceiveVO);
        return getDataTable(list);
    }
}
