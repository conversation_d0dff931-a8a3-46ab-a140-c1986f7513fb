package com.wendao101.teacher.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 推广员对象 promoter
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@Data
public class Promoter extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * unionId
     */
    @Excel(name = "unionId”")
    private String unionId;

    /**
     * openId”
     */
    @Excel(name = "openId”")
    private String openId;


    /**
     * 推广员姓名
     */
    @Excel(name = "推广员姓名")
    private String promoterName;

    /**
     * 推广员手机号码
     */
    @Excel(name = "推广员手机号码")
    private String promoterPhone;

    /**
     * 推广员头像
     */
    @Excel(name = "推广员头像")
    private String promoterImg;

    /**
     * 平台（多个，以逗号分隔）0快手，1抖音，2微信，3视频号
     */
    @Excel(name = "平台", readConverterExp = "多=个，以逗号分隔")
    private String platform;

    /**
     * 账号状态，0禁用，1启用
     */
    @Excel(name = "账号状态，0禁用，1启用")
    private Integer accountState;

    /**
     * 是否删除推广员（0不删除1删除）
     */
    @Excel(name = "是否删除推广员", readConverterExp = "0=不删除1删除")
    private Integer isDelete;

    /**
     * 授权账号
     */
    @Excel(name = "授权账号")
    private String authorizedAccountList;

    private Integer appNameType;

}
