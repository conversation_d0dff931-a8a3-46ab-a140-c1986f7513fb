package com.wendao101.teacher.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.dto.ChapterWithCourseDTO;
import com.wendao101.teacher.vo.AddCourseDirectoryVO;
import com.wendao101.teacher.vo.CourseDirectoryVO;
import com.wendao101.teacher.vo.UpdateCourseDirectoryVO;
import com.wendao101.teacher.vo.UpdateDirectoryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.CourseDirectoryMapper;
import com.wendao101.teacher.domain.CourseDirectory;
import com.wendao101.teacher.service.ICourseDirectoryService;

import javax.annotation.Resource;

/**
 * 目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@Service
public class CourseDirectoryServiceImpl implements ICourseDirectoryService {
    @Resource
    private CourseDirectoryMapper courseDirectoryMapper;

    /**
     * 查询目录
     *
     * @param id 目录主键
     * @return 目录
     */
    @Override
    public CourseDirectory selectCourseDirectoryById(Long id) {
        return courseDirectoryMapper.selectCourseDirectoryById(id);
    }

    /**
     * 查询目录列表
     *
     * @param courseDirectory 目录
     * @return 目录
     */
    @Override
    public List<CourseDirectory> selectCourseDirectoryList(CourseDirectory courseDirectory) {
        return courseDirectoryMapper.selectCourseDirectoryList(courseDirectory);
    }

    /**
     * 新增目录
     *
     * @param couseDirectory 目录
     * @return 结果
     */
    @Override
    public int insertCourseDirectory(CourseDirectory couseDirectory) {
        couseDirectory.setCreateTime(DateUtils.getNowDate());
        return courseDirectoryMapper.insertCourseDirectory(couseDirectory);
    }

    /**
     * 修改目录
     *
     * @param courseDirectory 目录
     * @return 结果
     */
    @Override
    public int updateCourseDirectory(CourseDirectory courseDirectory) {
        courseDirectory.setUpdateTime(DateUtils.getNowDate());
        return courseDirectoryMapper.updateCourseDirectory(courseDirectory);
    }

    /**
     * 批量删除目录
     *
     * @param ids 需要删除的目录主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryByIds(Long[] ids) {
        return courseDirectoryMapper.deleteCourseDirectoryByIds(ids);
    }

    /**
     * 删除目录信息
     *
     * @param id 目录主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryById(Long id) {
        return courseDirectoryMapper.deleteCourseDirectoryById(id);
    }

    public ArrayList<ChapterWithCourseDTO> selectChapterWithCourse(ChapterWithCourseDTO chapterWithCourse) {
        return courseDirectoryMapper.selectChapterWithCourse(chapterWithCourse);
    }

    /**
     * 根据条件查询目录列表
     *
     * @param courseDirectoryVO
     * @return
     */
    public List<CourseDirectory> selectCourseDirectory(CourseDirectoryVO courseDirectoryVO) {

        return courseDirectoryMapper.selectCourseDirectory(courseDirectoryVO);
    }

    @Override
    public List<Long> addCourseDirectory(AddCourseDirectoryVO addCourseDirectoryVO) {
        CourseDirectory courseDirectory = new CourseDirectory();
        List<Long> ids = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(addCourseDirectoryVO.getCourseDirectoryList())) {
            addCourseDirectoryVO.getCourseDirectoryList().forEach(item -> {
                courseDirectory.setId(null);
                courseDirectory.setChapterId(addCourseDirectoryVO.getChapterId());
                courseDirectory.setTeacherId(addCourseDirectoryVO.getTeacherId());
                courseDirectory.setCourseId(addCourseDirectoryVO.getCourseId());
                courseDirectory.setSerialNumber(item.getSerialNumber());
                courseDirectory.setSerialAllNumber(item.getSerialAllNumber());
                courseDirectory.setDirectoryName(item.getDirectoryName());
                courseDirectory.setCourseDirectoryUrl(item.getCourseDirectoryUrl());
                courseDirectory.setDuration(item.getDuration());
                courseDirectory.setIsTrySee(item.getIsTrySee());
                courseDirectory.setTrySeeDuration(item.getTrySeeDuration());
                courseDirectory.setRemoveChapter(0);
                courseDirectory.setIsDelete(0);
                courseDirectory.setCreateTime(DateUtils.getNowDate());
                courseDirectory.setUpdateTime(DateUtils.getNowDate());
                courseDirectoryMapper.insertCourseDirectory(courseDirectory);

                ids.add(courseDirectory.getId());
            });
        }

        return ids;
    }

    @Override
    public int updateDirectory(UpdateCourseDirectoryVO courseDirectoryVO) {
        courseDirectoryVO.setUpdateTime(DateUtils.getNowDate());
        if (courseDirectoryVO.getSerialNumber() != null && !courseDirectoryVO.getSerialNumber().equals("") && Objects.nonNull(courseDirectoryVO.getNumber())) {
            if (courseDirectoryVO.getSerialNumber() > courseDirectoryVO.getNumber()) {
                //修改前数小于修改后数
                courseDirectoryMapper.updateSerialNumber(courseDirectoryVO);
            } else if (courseDirectoryVO.getSerialNumber() < courseDirectoryVO.getNumber()) {
                //修改前数大于修改后数
                courseDirectoryMapper.updateNumber(courseDirectoryVO);
            }
        } else if (courseDirectoryVO.getSerialAllNumber() != null && !courseDirectoryVO.getSerialAllNumber().equals("") && Objects.nonNull(courseDirectoryVO.getNumber())) {
            if (courseDirectoryVO.getSerialAllNumber() > courseDirectoryVO.getNumber()) {
                //修改前数小于修改后数
                courseDirectoryMapper.updateSerialAllNumber(courseDirectoryVO);
            } else if (courseDirectoryVO.getSerialAllNumber() < courseDirectoryVO.getNumber()) {
                //修改前数大于修改后数
                courseDirectoryMapper.updateAllNumber(courseDirectoryVO);
            }
        }
        CourseDirectory courseDirectory = new CourseDirectory();
        courseDirectory.setId(courseDirectoryVO.getId());
        courseDirectory.setChapterId(courseDirectoryVO.getChapterId());
//        courseDirectory.setTeacherId(courseDirectoryVO.getTeacherId());
//        courseDirectory.setCourseId(courseDirectoryVO.getCourseId());
        courseDirectory.setSerialNumber(courseDirectoryVO.getSerialNumber());
        courseDirectory.setCourseDirectoryUrl(courseDirectoryVO.getCourseDirectoryUrl());
        courseDirectory.setSerialAllNumber(courseDirectoryVO.getSerialAllNumber());
        courseDirectory.setDirectoryName(courseDirectoryVO.getDirectoryName());
        courseDirectory.setIsTrySee(courseDirectoryVO.getIsTrySee());
        courseDirectory.setTrySeeDuration(courseDirectoryVO.getTrySeeDuration()!=null?courseDirectoryVO.getTrySeeDuration().longValue():0L);
        courseDirectory.setIsDelete(courseDirectoryVO.getIsDelete());
        courseDirectory.setUpdateTime(DateUtils.getNowDate());

        return courseDirectoryMapper.updateCourseDirectory(courseDirectory);
    }

    @Override
    public int update(UpdateDirectoryVO updateDirectoryVO) {

        List<CourseDirectory> courseDirectories = courseDirectoryMapper.selectByChapterId(updateDirectoryVO.getChapterId());
        if (CollectionUtils.isNotEmpty(updateDirectoryVO.getCourseDirectoryList())) {
            updateDirectoryVO.getCourseDirectoryList().stream().forEach(info -> {
                for (int i = 0; i < updateDirectoryVO.getCourseDirectoryList().size(); i++) {
                    CourseDirectory courseDirectory = new CourseDirectory();
                    courseDirectory.setChapterId(updateDirectoryVO.getChapterId());
                    courseDirectory.setId(info.getId());
                    courseDirectory.setSerialNumber(courseDirectories.size() + 1 + i);
                    courseDirectory.setRemoveChapter(info.getRemoveChapter());
                    courseDirectoryMapper.update(courseDirectory);
                }
            });
        } else {
            CourseDirectory courseDirectory = selectCourseDirectoryById(updateDirectoryVO.getDirectoryId());
            courseDirectory.setRemoveChapter(1);
            //移除章节
            courseDirectoryMapper.updateCourseDirectory(courseDirectory);
            if (Objects.nonNull(courseDirectory.getSerialNumber()) && courseDirectory.getSerialNumber() == 1){
                return 1;
            }
            //修改对于章节序号数  大于这个序号数都-1
            courseDirectoryMapper.removeDirectory(courseDirectory);
        }

        return 1;
    }

    @Override
    public int delDirectory(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0;
        }
        for (Long id : ids) {
            CourseDirectory courseDirectory = courseDirectoryMapper.selectCourseDirectoryById(id);
            //删除目录
            courseDirectory.setIsDelete(1);
            courseDirectoryMapper.updateCourseDirectory(courseDirectory);
            //修改全部序号数
            return courseDirectoryMapper.updateNum(courseDirectory);
        }

        return 1;
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryListOrderByAllSeq(CourseDirectory courseDirectoryQuery) {
        return courseDirectoryMapper.selectCourseDirectoryListOrderByAllSeq(courseDirectoryQuery);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryWhereChapterIdIsNull(CourseDirectory courseDirectoryQuery) {
        return courseDirectoryMapper.selectCourseDirectoryWhereChapterIdIsNull(courseDirectoryQuery);
    }

    @Override
    public int updateChapterIdNull(Long chapterId) {
        return courseDirectoryMapper.updateChapterIdNull(chapterId);
    }

    @Override
    public int deleteCourseDirectoryByIdsAndTeacherId(Long[] dcdArr, Long teacherId) {
        return courseDirectoryMapper.deleteCourseDirectoryByIdsAndTeacherId(dcdArr,teacherId);
    }

    @Override
    public int updateCourseDirectorySetChapterIdNull(CourseDirectory courseDirectory) {
        return courseDirectoryMapper.updateCourseDirectorySetChapterIdNull(courseDirectory);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryListByChapterId(Long chapterId) {
        return courseDirectoryMapper.selectCourseDirectoryListByChapterId(chapterId);
    }

    @Override
    public void deleteCourseDirectoryChapterIdByCourseId(Long courseId, Long teacherId) {
         courseDirectoryMapper.deleteCourseDirectoryChapterIdByCourseId(courseId,teacherId);
    }

    @Override
    public void batchInsert(List<CourseDirectory> allD) {
        courseDirectoryMapper.batchInsert(allD);
    }

    @Override
    public void batchUpdate(List<CourseDirectory> updateList) {
        courseDirectoryMapper.batchUpdate(updateList);
    }

    @Override
    public void deleteCourseDirectoryWhereChapterIdIsNull(Long courseId) {
        courseDirectoryMapper.deleteCourseDirectoryWhereChapterIdIsNull(courseId);
    }

    @Override
    public void deleteCourseDirectoryByChapterId(Long chapterId) {
        courseDirectoryMapper.deleteCourseDirectoryByChapterId(chapterId);
    }

    @Override
    public void deleteCourseDirectoryByChapterIds(Long[] chapterIds) {
        courseDirectoryMapper.deleteCourseDirectoryByChapterIds(chapterIds);
    }

    @Override
    public List<CourseDirectory> selectCourseDirectoryListByTeacherIdAndPathUrl(Set<String> keySet, Long teacherId) {
        return courseDirectoryMapper.selectCourseDirectoryListByTeacherIdAndPathUrl(keySet,teacherId);
    }
}
