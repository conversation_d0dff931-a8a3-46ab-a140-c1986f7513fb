package com.wendao101.teacher.service;

import java.util.List;

import com.wendao101.teacher.domain.ClockReply;
import com.wendao101.teacher.vo.ClockReplyVO;

/**
 * 打卡回复Service接口
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
public interface IClockReplyService {
    /**
     * 查询打卡回复
     *
     * @param id 打卡回复主键
     * @return 打卡回复
     */
    public ClockReply selectClockReplyById(Long id);

    /**
     * 查询打卡回复列表
     *
     * @param clockReply 打卡回复
     * @return 打卡回复集合
     */
    public List<ClockReply> selectClockReplyList(ClockReply clockReply);

    /**
     * 新增打卡回复
     *
     * @param clockReply 打卡回复
     * @return 结果
     */
    public int insertClockReply(ClockReply clockReply);

    /**
     * 修改打卡回复
     *
     * @param clockReply 打卡回复
     * @return 结果
     */
    public int updateClockReply(ClockReply clockReply);

    /**
     * 批量删除打卡回复
     *
     * @param ids 需要删除的打卡回复主键集合
     * @return 结果
     */
    public int deleteClockReplyByIds(Long[] ids);

    /**
     * 删除打卡回复信息
     *
     * @param id 打卡回复主键
     * @return 结果
     */
    public int deleteClockReplyById(Long id);

    /**
     * 后台打卡回复
     *
     * @param clockReplyVO
     * @return
     */
    List<ClockReply> selectByClockRecordId(ClockReplyVO clockReplyVO);

    /**
     * 回复评论
     *
     * @param clockReply
     * @return
     */
    int addTeacherReply(ClockReply clockReply);

    /**
     * 隐藏回复
     *
     * @param id
     * @return
     */
    int hideById(Long id);
}
