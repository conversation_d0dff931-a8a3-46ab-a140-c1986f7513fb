package com.wendao101.teacher.mapper;

import com.wendao101.teacher.domain.SphAccessToken;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 问到微信视频号小店access_tokenMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-31
 */
public interface SphAccessTokenMapper 
{
    /**
     * 查询问到微信视频号小店access_token
     * 
     * @param id 问到微信视频号小店access_token主键
     * @return 问到微信视频号小店access_token
     */
    public SphAccessToken selectSphAccessTokenById(Long id);

    /**
     * 查询问到微信视频号小店access_token列表
     * 
     * @param sphAccessToken 问到微信视频号小店access_token
     * @return 问到微信视频号小店access_token集合
     */
    public List<SphAccessToken> selectSphAccessTokenList(SphAccessToken sphAccessToken);

    SphAccessToken selectSphAccessTokenByAppNameType(@Param("appNameType") Integer appNameType);

    SphAccessToken selectSphAccessTokenByWxxdAppId(@Param("appId")String appId);

    SphAccessToken selectSphAccessTokenByTeacherId(@Param("teacherId")Long teacherId);
}
