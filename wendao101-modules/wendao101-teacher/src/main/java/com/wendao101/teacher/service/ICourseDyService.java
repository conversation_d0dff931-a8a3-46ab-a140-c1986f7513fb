package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.CourseDy;

/**
 * 课程Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface ICourseDyService 
{
    /**
     * 查询课程
     * 
     * @param id 课程主键
     * @return 课程
     */
    public CourseDy selectCourseDyById(Long id);

    /**
     * 查询课程列表
     * 
     * @param courseDy 课程
     * @return 课程集合
     */
    public List<CourseDy> selectCourseDyList(CourseDy courseDy);

    /**
     * 新增课程
     * 
     * @param courseDy 课程
     * @return 结果
     */
    public int insertCourseDy(CourseDy courseDy);

    /**
     * 修改课程
     * 
     * @param courseDy 课程
     * @return 结果
     */
    public int updateCourseDy(CourseDy courseDy);

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的课程主键集合
     * @return 结果
     */
    public int deleteCourseDyByIds(Long[] ids);

    /**
     * 删除课程信息
     * 
     * @param id 课程主键
     * @return 结果
     */
    public int deleteCourseDyById(Long id);

    CourseDy selectCourseByCourseIdNumber(Long courseIdNumber);

    int updateMinusOne(Long oldSeq, Long newSeq, Long teacherId);

    int updatePlusOne(Long oldSeq, Long newSeq, Long teacherId);

    int updateOldSeq2NewSeq(Long oldSeq, Long newSeq, Long teacherId, Long id);
}
