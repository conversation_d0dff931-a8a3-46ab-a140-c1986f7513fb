package com.wendao101.teacher.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.teacher.domain.Course;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MDiscountsDTO {
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 优惠券名称 */
    @Excel(name = "优惠券名称")
    private String discountsName;

    /** 优惠券类型, 0普通优惠券，1分享优惠券 */
    @Excel(name = "优惠券类型, 0普通优惠券，1分享优惠券")
    private Integer discountsType;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 优惠券码id */
    @Excel(name = "优惠券码id")
    private Long discountsCodeId;

    /** 优惠券价格 */
    @Excel(name = "优惠券价格")
    private BigDecimal discountsMoney;

    /** 最多领取数 */
    @Excel(name = "最多领取数")
    private Integer receiveMax;

    /** 优惠券使用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsStartTime;

    /** 优惠券使用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券使用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date discountsEndTime;

    /** 优惠券总数 */
    @Excel(name = "优惠券总数")
    private Integer discountsSum;

    /** 领取优惠券总数 */
    @Excel(name = "领取优惠券总数")
    private Integer receiveCouponSum;

    /** 优惠券是否无限，0 不是 ， 1 是 */
    @Excel(name = "优惠券是否无限，0 不是 ， 1 是")
    private Integer discountsNumberType;

    /** 优惠券状态，0 未开始，1进行中，2已结束 */
    @Excel(name = "优惠券状态，0 未开始，1进行中，2已结束")
    private Integer discountsStatus;

    /** 限制时间状态；0限制时间范围；1限制有效时间*/
    @Excel(name = "限制时间状态；0限制时间范围；1限制有效时间")
    private Integer timeStatus;

    /** 优惠券开始领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券开始领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponStartTime;

    /** 优惠券结束领取时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "优惠券结束领取时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date receiveCouponEndTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    @Excel(name = "限制有效时间")
    private Integer validTime;

    /** 是否允许继续使用，0不允许，1允许 */
    @Excel(name = "是否允许继续使用，0不允许，1允许")
    private Integer isKeepUsing;

    /** 是否停止活动，0不停止，1停止 */
    @Excel(name = "是否停止活动，0不停止，1停止")
    private Integer isStopAction;

    private String title;

    private Integer remain;

    private Course course;

    public Course getCourse() {
        if(course!=null){
            course.setDetail(null);
        }
        return course;
    }

    public void setCourse(Course course) {
        this.course = course;
    }
}
