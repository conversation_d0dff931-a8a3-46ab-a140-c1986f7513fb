package com.wendao101.teacher.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class UpdateTeacherUrlVO {

    private Long teacherId;

    /**
     * 老师头像
     */
    private String avatarUrl;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 老师简介
     */
    private String teacherDesc;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
