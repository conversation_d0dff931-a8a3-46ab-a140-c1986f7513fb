package com.wendao101.teacher.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 *  入驻信息对象 enter_information_0202
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public class EnterInformation0202 extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 入驻类型.1个人,2机构 */
    @Excel(name = "入驻类型.1个人,2机构")
    private Integer entityType;

    /** 个人-身份证正面链接,人像面 */
    @Excel(name = "个人-身份证正面链接,人像面")
    private String frontPath;

    /** 个人-身份反面链接,国徽面 */
    @Excel(name = "个人-身份反面链接,国徽面")
    private String backPath;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 个人-身份证号码 */
    @Excel(name = "个人-身份证号码")
    private String idNumber;

    /** 个人-讲师姓名 */
    @Excel(name = "个人-讲师姓名")
    private String teacherName;

    /** 公司名称 */
    @Excel(name = "公司名称")
    private String businessLicenseCompanyName;

    /** 统一社会信用代码 */
    @Excel(name = "统一社会信用代码")
    private String businessLicenseNo;

    /** 机构-营业执照文件访问路径 */
    @Excel(name = "机构-营业执照文件访问路径")
    private String businessLicensePath;

    /** 联系方式 */
    @Excel(name = "联系方式")
    private String telNum;

    /** 课程形式,1 线上课程2线下课程 */
    @Excel(name = "课程形式,1 线上课程2线下课程")
    private Integer courseForm;

    /** 类目选择一级id */
    @Excel(name = "类目选择一级id")
    private Long firstClassId;

    /** 类目选择一级pid */
    @Excel(name = "类目选择一级pid")
    private Long firstClassPid;

    /** 类目选择一级名称 */
    @Excel(name = "类目选择一级名称")
    private String firstClassTitle;

    /** 类目选择一级抖音类目id */
    @Excel(name = "类目选择一级抖音类目id")
    private Long firstClassDouyinClassId;

    /** 类目选择二级id */
    @Excel(name = "类目选择二级id")
    private Long secondClassId;

    /** 类目选择二级pid */
    @Excel(name = "类目选择二级pid")
    private Long secondClassPid;

    /** 类目选择二级名称 */
    @Excel(name = "类目选择二级名称")
    private String secondClassTitle;

    /** 类目选择二级抖音类目id */
    @Excel(name = "类目选择二级抖音类目id")
    private Long secondClassDouyinClassId;

    /** 店铺昵称 */
    @Excel(name = "店铺昵称")
    private String shopNickname;

    /** 店铺头像 */
    @Excel(name = "店铺头像")
    private String shopAvatarUrl;

    /** 店铺介绍 */
    @Excel(name = "店铺介绍")
    private String shopDesc;

    /** 入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开 */
    @Excel(name = "入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开")
    private String platform;

    /**  抖音账号 */
    @Excel(name = " 抖音账号")
    private String dyAccount;

    /** 抖音uid */
    @Excel(name = "抖音uid")
    private String dyUid;

    /** 快手账号 */
    @Excel(name = "快手账号")
    private String ksAccount;

    /** 微信账号 */
    @Excel(name = "微信账号")
    private String wxAccount;

    /** 视频号账号 */
    @Excel(name = "视频号账号")
    private String sphAccount;

    /** 服务开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "服务开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date serviceBeginTime;

    /** 服务结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "服务结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date serviceEndTime;

    /** 1.免费版本,2.标准版本,3.企业版本 */
    @Excel(name = "1.免费版本,2.标准版本,3.企业版本")
    private Integer version;

    /** 抽佣类型1阶梯,2统提 */
    @Excel(name = "抽佣类型1阶梯,2统提")
    private Integer rateType;

    /** 抽佣比例 */
    @Excel(name = "抽佣比例")
    private Long rate;

    /** 开户专员 */
    @Excel(name = "开户专员")
    private String accountSpecialist;

    /** 客服专员 */
    @Excel(name = "客服专员")
    private String customerServiceSpecialist;

    /** 店铺账号 */
    @Excel(name = "店铺账号")
    private String shopAccount;

    /** 店铺id(老师id) */
    @Excel(name = "店铺id(老师id)")
    private Long shopId;

    /** 审核类型 0审核通过 1审核中 2驳回 */
    @Excel(name = "审核类型 0审核通过 1审核中 2驳回")
    private Integer auditType;

    /** 驳回原因 */
    @Excel(name = "驳回原因")
    private String rejectReason;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 抖音粉丝数 */
    @Excel(name = "抖音粉丝数")
    private Long dyFansNum;

    /** 快手粉丝数 */
    @Excel(name = "快手粉丝数")
    private Long ksFansNum;

    /** 视频号粉丝数 */
    @Excel(name = "视频号粉丝数")
    private Long sphFansNum;

    /** 抖音主图片地址 */
    @Excel(name = "抖音主图片地址")
    private String dyMasterImg;

    /** 快手主图片地址 */
    @Excel(name = "快手主图片地址")
    private String ksMasterImg;

    /** 微信主图片地址 */
    @Excel(name = "微信主图片地址")
    private String wxMasterImg;

    /** 视频号主图片地址 */
    @Excel(name = "视频号主图片地址")
    private String sphMasterImg;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    @Excel(name = "店铺所属APP，1问到好课，2问到课堂")
    private Integer appNameType;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setEntityType(Integer entityType) 
    {
        this.entityType = entityType;
    }

    public Integer getEntityType() 
    {
        return entityType;
    }
    public void setFrontPath(String frontPath) 
    {
        this.frontPath = frontPath;
    }

    public String getFrontPath() 
    {
        return frontPath;
    }
    public void setBackPath(String backPath) 
    {
        this.backPath = backPath;
    }

    public String getBackPath() 
    {
        return backPath;
    }
    public void setRealName(String realName) 
    {
        this.realName = realName;
    }

    public String getRealName() 
    {
        return realName;
    }
    public void setIdNumber(String idNumber) 
    {
        this.idNumber = idNumber;
    }

    public String getIdNumber() 
    {
        return idNumber;
    }
    public void setTeacherName(String teacherName) 
    {
        this.teacherName = teacherName;
    }

    public String getTeacherName() 
    {
        return teacherName;
    }
    public void setBusinessLicenseCompanyName(String businessLicenseCompanyName) 
    {
        this.businessLicenseCompanyName = businessLicenseCompanyName;
    }

    public String getBusinessLicenseCompanyName() 
    {
        return businessLicenseCompanyName;
    }
    public void setBusinessLicenseNo(String businessLicenseNo) 
    {
        this.businessLicenseNo = businessLicenseNo;
    }

    public String getBusinessLicenseNo() 
    {
        return businessLicenseNo;
    }
    public void setBusinessLicensePath(String businessLicensePath) 
    {
        this.businessLicensePath = businessLicensePath;
    }

    public String getBusinessLicensePath() 
    {
        return businessLicensePath;
    }
    public void setTelNum(String telNum) 
    {
        this.telNum = telNum;
    }

    public String getTelNum() 
    {
        return telNum;
    }
    public void setCourseForm(Integer courseForm) 
    {
        this.courseForm = courseForm;
    }

    public Integer getCourseForm() 
    {
        return courseForm;
    }
    public void setFirstClassId(Long firstClassId) 
    {
        this.firstClassId = firstClassId;
    }

    public Long getFirstClassId() 
    {
        return firstClassId;
    }
    public void setFirstClassPid(Long firstClassPid) 
    {
        this.firstClassPid = firstClassPid;
    }

    public Long getFirstClassPid() 
    {
        return firstClassPid;
    }
    public void setFirstClassTitle(String firstClassTitle) 
    {
        this.firstClassTitle = firstClassTitle;
    }

    public String getFirstClassTitle() 
    {
        return firstClassTitle;
    }
    public void setFirstClassDouyinClassId(Long firstClassDouyinClassId) 
    {
        this.firstClassDouyinClassId = firstClassDouyinClassId;
    }

    public Long getFirstClassDouyinClassId() 
    {
        return firstClassDouyinClassId;
    }
    public void setSecondClassId(Long secondClassId) 
    {
        this.secondClassId = secondClassId;
    }

    public Long getSecondClassId() 
    {
        return secondClassId;
    }
    public void setSecondClassPid(Long secondClassPid) 
    {
        this.secondClassPid = secondClassPid;
    }

    public Long getSecondClassPid() 
    {
        return secondClassPid;
    }
    public void setSecondClassTitle(String secondClassTitle) 
    {
        this.secondClassTitle = secondClassTitle;
    }

    public String getSecondClassTitle() 
    {
        return secondClassTitle;
    }
    public void setSecondClassDouyinClassId(Long secondClassDouyinClassId) 
    {
        this.secondClassDouyinClassId = secondClassDouyinClassId;
    }

    public Long getSecondClassDouyinClassId() 
    {
        return secondClassDouyinClassId;
    }
    public void setShopNickname(String shopNickname) 
    {
        this.shopNickname = shopNickname;
    }

    public String getShopNickname() 
    {
        return shopNickname;
    }
    public void setShopAvatarUrl(String shopAvatarUrl) 
    {
        this.shopAvatarUrl = shopAvatarUrl;
    }

    public String getShopAvatarUrl() 
    {
        return shopAvatarUrl;
    }
    public void setShopDesc(String shopDesc) 
    {
        this.shopDesc = shopDesc;
    }

    public String getShopDesc() 
    {
        return shopDesc;
    }
    public void setPlatform(String platform) 
    {
        this.platform = platform;
    }

    public String getPlatform() 
    {
        return platform;
    }
    public void setDyAccount(String dyAccount) 
    {
        this.dyAccount = dyAccount;
    }

    public String getDyAccount() 
    {
        return dyAccount;
    }
    public void setDyUid(String dyUid) 
    {
        this.dyUid = dyUid;
    }

    public String getDyUid() 
    {
        return dyUid;
    }
    public void setKsAccount(String ksAccount) 
    {
        this.ksAccount = ksAccount;
    }

    public String getKsAccount() 
    {
        return ksAccount;
    }
    public void setWxAccount(String wxAccount) 
    {
        this.wxAccount = wxAccount;
    }

    public String getWxAccount() 
    {
        return wxAccount;
    }
    public void setSphAccount(String sphAccount) 
    {
        this.sphAccount = sphAccount;
    }

    public String getSphAccount() 
    {
        return sphAccount;
    }
    public void setServiceBeginTime(Date serviceBeginTime) 
    {
        this.serviceBeginTime = serviceBeginTime;
    }

    public Date getServiceBeginTime() 
    {
        return serviceBeginTime;
    }
    public void setServiceEndTime(Date serviceEndTime) 
    {
        this.serviceEndTime = serviceEndTime;
    }

    public Date getServiceEndTime() 
    {
        return serviceEndTime;
    }
    public void setVersion(Integer version) 
    {
        this.version = version;
    }

    public Integer getVersion() 
    {
        return version;
    }
    public void setRateType(Integer rateType) 
    {
        this.rateType = rateType;
    }

    public Integer getRateType() 
    {
        return rateType;
    }
    public void setRate(Long rate) 
    {
        this.rate = rate;
    }

    public Long getRate() 
    {
        return rate;
    }
    public void setAccountSpecialist(String accountSpecialist) 
    {
        this.accountSpecialist = accountSpecialist;
    }

    public String getAccountSpecialist() 
    {
        return accountSpecialist;
    }
    public void setCustomerServiceSpecialist(String customerServiceSpecialist) 
    {
        this.customerServiceSpecialist = customerServiceSpecialist;
    }

    public String getCustomerServiceSpecialist() 
    {
        return customerServiceSpecialist;
    }
    public void setShopAccount(String shopAccount) 
    {
        this.shopAccount = shopAccount;
    }

    public String getShopAccount() 
    {
        return shopAccount;
    }
    public void setShopId(Long shopId) 
    {
        this.shopId = shopId;
    }

    public Long getShopId() 
    {
        return shopId;
    }
    public void setAuditType(Integer auditType) 
    {
        this.auditType = auditType;
    }

    public Integer getAuditType() 
    {
        return auditType;
    }
    public void setRejectReason(String rejectReason) 
    {
        this.rejectReason = rejectReason;
    }

    public String getRejectReason() 
    {
        return rejectReason;
    }
    public void setAuditTime(Date auditTime) 
    {
        this.auditTime = auditTime;
    }

    public Date getAuditTime() 
    {
        return auditTime;
    }
    public void setDyFansNum(Long dyFansNum) 
    {
        this.dyFansNum = dyFansNum;
    }

    public Long getDyFansNum() 
    {
        return dyFansNum;
    }
    public void setKsFansNum(Long ksFansNum) 
    {
        this.ksFansNum = ksFansNum;
    }

    public Long getKsFansNum() 
    {
        return ksFansNum;
    }
    public void setSphFansNum(Long sphFansNum) 
    {
        this.sphFansNum = sphFansNum;
    }

    public Long getSphFansNum() 
    {
        return sphFansNum;
    }
    public void setDyMasterImg(String dyMasterImg) 
    {
        this.dyMasterImg = dyMasterImg;
    }

    public String getDyMasterImg() 
    {
        return dyMasterImg;
    }
    public void setKsMasterImg(String ksMasterImg) 
    {
        this.ksMasterImg = ksMasterImg;
    }

    public String getKsMasterImg() 
    {
        return ksMasterImg;
    }
    public void setWxMasterImg(String wxMasterImg) 
    {
        this.wxMasterImg = wxMasterImg;
    }

    public String getWxMasterImg() 
    {
        return wxMasterImg;
    }
    public void setSphMasterImg(String sphMasterImg) 
    {
        this.sphMasterImg = sphMasterImg;
    }

    public String getSphMasterImg() 
    {
        return sphMasterImg;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("entityType", getEntityType())
            .append("frontPath", getFrontPath())
            .append("backPath", getBackPath())
            .append("realName", getRealName())
            .append("idNumber", getIdNumber())
            .append("teacherName", getTeacherName())
            .append("businessLicenseCompanyName", getBusinessLicenseCompanyName())
            .append("businessLicenseNo", getBusinessLicenseNo())
            .append("businessLicensePath", getBusinessLicensePath())
            .append("telNum", getTelNum())
            .append("courseForm", getCourseForm())
            .append("firstClassId", getFirstClassId())
            .append("firstClassPid", getFirstClassPid())
            .append("firstClassTitle", getFirstClassTitle())
            .append("firstClassDouyinClassId", getFirstClassDouyinClassId())
            .append("secondClassId", getSecondClassId())
            .append("secondClassPid", getSecondClassPid())
            .append("secondClassTitle", getSecondClassTitle())
            .append("secondClassDouyinClassId", getSecondClassDouyinClassId())
            .append("shopNickname", getShopNickname())
            .append("shopAvatarUrl", getShopAvatarUrl())
            .append("shopDesc", getShopDesc())
            .append("platform", getPlatform())
            .append("dyAccount", getDyAccount())
            .append("dyUid", getDyUid())
            .append("ksAccount", getKsAccount())
            .append("wxAccount", getWxAccount())
            .append("sphAccount", getSphAccount())
            .append("serviceBeginTime", getServiceBeginTime())
            .append("serviceEndTime", getServiceEndTime())
            .append("version", getVersion())
            .append("rateType", getRateType())
            .append("rate", getRate())
            .append("accountSpecialist", getAccountSpecialist())
            .append("customerServiceSpecialist", getCustomerServiceSpecialist())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("shopAccount", getShopAccount())
            .append("shopId", getShopId())
            .append("auditType", getAuditType())
            .append("rejectReason", getRejectReason())
            .append("auditTime", getAuditTime())
            .append("dyFansNum", getDyFansNum())
            .append("ksFansNum", getKsFansNum())
            .append("sphFansNum", getSphFansNum())
            .append("dyMasterImg", getDyMasterImg())
            .append("ksMasterImg", getKsMasterImg())
            .append("wxMasterImg", getWxMasterImg())
            .append("sphMasterImg", getSphMasterImg())
            .append("appNameType", getAppNameType())
            .toString();
    }
}
