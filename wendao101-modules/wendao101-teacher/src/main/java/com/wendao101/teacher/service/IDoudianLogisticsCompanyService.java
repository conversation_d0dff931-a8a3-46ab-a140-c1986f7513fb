package com.wendao101.teacher.service;


import com.wendao101.teacher.domain.DoudianLogisticsCompany;

import java.util.List;

/**
 * 物流公司和codeService接口
 * 
 * <AUTHOR>
 * @date 2024-09-06
 */
public interface IDoudianLogisticsCompanyService 
{
    /**
     * 查询物流公司和code
     * 
     * @param id 物流公司和code主键
     * @return 物流公司和code
     */
    public DoudianLogisticsCompany selectDoudianLogisticsCompanyById(Long id);

    /**
     * 查询物流公司和code列表
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 物流公司和code集合
     */
    public List<DoudianLogisticsCompany> selectDoudianLogisticsCompanyList(DoudianLogisticsCompany doudianLogisticsCompany);

    /**
     * 新增物流公司和code
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 结果
     */
    public int insertDoudianLogisticsCompany(DoudianLogisticsCompany doudianLogisticsCompany);

    /**
     * 修改物流公司和code
     * 
     * @param doudianLogisticsCompany 物流公司和code
     * @return 结果
     */
    public int updateDoudianLogisticsCompany(DoudianLogisticsCompany doudianLogisticsCompany);

    /**
     * 批量删除物流公司和code
     * 
     * @param ids 需要删除的物流公司和code主键集合
     * @return 结果
     */
    public int deleteDoudianLogisticsCompanyByIds(Long[] ids);

    /**
     * 删除物流公司和code信息
     * 
     * @param id 物流公司和code主键
     * @return 结果
     */
    public int deleteDoudianLogisticsCompanyById(Long id);
}
