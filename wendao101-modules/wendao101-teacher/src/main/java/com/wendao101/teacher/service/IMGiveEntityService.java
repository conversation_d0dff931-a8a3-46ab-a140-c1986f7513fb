package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.MGiveEntity;
import com.wendao101.teacher.domain.PageBean;
import com.wendao101.teacher.domain.PageRows;
import com.wendao101.teacher.dto.MGiveEntityAndDetailDTO;
import com.wendao101.teacher.dto.MGiveEntityCourseDTO;
import com.wendao101.teacher.dto.MGiveEntityDTO;
import com.wendao101.teacher.dto.ShopGiveEntityDTO;
import com.wendao101.teacher.vo.MGiveEntityAndAddVO;
import com.wendao101.teacher.vo.MGiveEntityVO;
import com.wendao101.teacher.vo.ShopGiveEntityVO;

/**
 * 附赠实物Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
public interface IMGiveEntityService 
{
    /**
     * 查询附赠实物
     * 
     * @param id 附赠实物主键
     * @return 附赠实物
     */
    public MGiveEntityAndDetailDTO selectMGiveEntityById(Long id, String strTeacherId);

    /**
     * 查询附赠实物列表
     * 
     * @param mGiveEntityVO 附赠实物
     * @return 附赠实物集合
     */
    public List<MGiveEntityDTO> selectMGiveEntityList(MGiveEntityVO mGiveEntityVO);

    /**
     * 查询课程列表
     *
     * @param strTeacherId 附赠实物
     * @return 附赠实物集合
     */
    public List<MGiveEntityCourseDTO> selectCourse(String strTeacherId);

    /**
     * 新增附赠实物
     * 
     * @param mGiveEntityAndAddVO 附赠实物
     * @return 结果
     */
    public int insertMGiveEntity(MGiveEntityAndAddVO mGiveEntityAndAddVO);

    /**
     * 修改附赠实物
     * 
     * @param mGiveEntityAndAddVO 附赠实物
     * @return 结果
     */
    public int updateMGiveEntity(MGiveEntityAndAddVO mGiveEntityAndAddVO);

//    /**
//     * 批量删除附赠实物
//     *
//     * @param id 需要删除的附赠实物主键
//     * @return 结果
//     */
//    public int deleteMGiveEntityByIds(Long id);

    /**
     * 删除附赠实物信息
     * 
     * @param id 附赠实物主键
     * @return 结果
     */
    public int deleteMGiveEntityById(Long id);


    /**
     * 查询店铺所拥有的课程和附赠实物数量
     * @param shopGiveEntityVO
     * @return
     */
    List<ShopGiveEntityDTO> selectShopInfomation(ShopGiveEntityVO shopGiveEntityVO);

}
