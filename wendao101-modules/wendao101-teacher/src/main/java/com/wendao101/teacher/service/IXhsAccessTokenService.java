package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.XhsAccessToken;

/**
 * 小红书access_tokenService接口
 * 
 * <AUTHOR>
 * @date 2024-05-31
 */
public interface IXhsAccessTokenService 
{
    /**
     * 查询小红书access_token
     * 
     * @param id 小红书access_token主键
     * @return 小红书access_token
     */
    public XhsAccessToken selectXhsAccessTokenById(Long id);

    /**
     * 查询小红书access_token列表
     * 
     * @param xhsAccessToken 小红书access_token
     * @return 小红书access_token集合
     */
    public List<XhsAccessToken> selectXhsAccessTokenList(XhsAccessToken xhsAccessToken);

    /**
     * 新增小红书access_token
     * 
     * @param xhsAccessToken 小红书access_token
     * @return 结果
     */
    public int insertXhsAccessToken(XhsAccessToken xhsAccessToken);

    /**
     * 修改小红书access_token
     * 
     * @param xhsAccessToken 小红书access_token
     * @return 结果
     */
    public int updateXhsAccessToken(XhsAccessToken xhsAccessToken);

    /**
     * 批量删除小红书access_token
     * 
     * @param ids 需要删除的小红书access_token主键集合
     * @return 结果
     */
    public int deleteXhsAccessTokenByIds(Long[] ids);

    /**
     * 删除小红书access_token信息
     * 
     * @param id 小红书access_token主键
     * @return 结果
     */
    public int deleteXhsAccessTokenById(Long id);

    public String  getValidAccessToken();
}
