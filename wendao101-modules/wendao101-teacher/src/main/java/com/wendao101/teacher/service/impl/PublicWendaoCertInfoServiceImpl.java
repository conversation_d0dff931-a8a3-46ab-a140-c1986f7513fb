package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.PublicWendaoCertInfoMapper;
import com.wendao101.teacher.domain.PublicWendaoCertInfo;
import com.wendao101.teacher.service.IPublicWendaoCertInfoService;

/**
 * 公共资质Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-29
 */
@Service
public class PublicWendaoCertInfoServiceImpl implements IPublicWendaoCertInfoService 
{
    @Autowired
    private PublicWendaoCertInfoMapper publicWendaoCertInfoMapper;

    /**
     * 查询公共资质
     * 
     * @param id 公共资质主键
     * @return 公共资质
     */
    @Override
    public PublicWendaoCertInfo selectPublicWendaoCertInfoById(Long id)
    {
        return publicWendaoCertInfoMapper.selectPublicWendaoCertInfoById(id);
    }

    /**
     * 查询公共资质列表
     * 
     * @param publicWendaoCertInfo 公共资质
     * @return 公共资质
     */
    @Override
    public List<PublicWendaoCertInfo> selectPublicWendaoCertInfoList(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        return publicWendaoCertInfoMapper.selectPublicWendaoCertInfoList(publicWendaoCertInfo);
    }

    /**
     * 新增公共资质
     * 
     * @param publicWendaoCertInfo 公共资质
     * @return 结果
     */
    @Override
    public int insertPublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        publicWendaoCertInfo.setCreateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoMapper.insertPublicWendaoCertInfo(publicWendaoCertInfo);
    }

    /**
     * 修改公共资质
     * 
     * @param publicWendaoCertInfo 公共资质
     * @return 结果
     */
    @Override
    public int updatePublicWendaoCertInfo(PublicWendaoCertInfo publicWendaoCertInfo)
    {
        publicWendaoCertInfo.setUpdateTime(DateUtils.getNowDate());
        return publicWendaoCertInfoMapper.updatePublicWendaoCertInfo(publicWendaoCertInfo);
    }

    /**
     * 批量删除公共资质
     * 
     * @param ids 需要删除的公共资质主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoByIds(Long[] ids)
    {
        return publicWendaoCertInfoMapper.deletePublicWendaoCertInfoByIds(ids);
    }

    /**
     * 删除公共资质信息
     * 
     * @param id 公共资质主键
     * @return 结果
     */
    @Override
    public int deletePublicWendaoCertInfoById(Long id)
    {
        return publicWendaoCertInfoMapper.deletePublicWendaoCertInfoById(id);
    }

    @Override
    public PublicWendaoCertInfo selectDistinctNameNickName(String businessLicenseCompanyName) {
        return publicWendaoCertInfoMapper.selectDistinctNameNickName(businessLicenseCompanyName);
    }
}
