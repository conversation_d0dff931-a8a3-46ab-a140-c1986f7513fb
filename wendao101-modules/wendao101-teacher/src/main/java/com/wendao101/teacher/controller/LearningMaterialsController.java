package com.wendao101.teacher.controller;


import com.wendao101.common.core.utils.PageUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.CourseClass;
import com.wendao101.teacher.domain.StudyData;
import com.wendao101.teacher.dto.StudyDataDTO;
import com.wendao101.teacher.service.ILearningMaterialsService;


import com.wendao101.teacher.vo.MaterialsVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;


/**
 * <p>
 * 学习资料 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-04
 */
//@Api("学习资料相关接口")
@RestController
@RequestMapping("/learning_materials")
@Slf4j
@RequiredArgsConstructor
public class LearningMaterialsController extends BaseController {

    private final ILearningMaterialsService learningMaterialsService;


    /**
     * 根据老师id查询学习资料
     *
     * @param studyData
     * @return
     */
    @PostMapping("/listByTeacher")
    public TableDataInfo listByTeacher(@RequestBody StudyData studyData) {
        //获取教师id
        if (studyData.getTeacherId()==null){
            Long teacherId = SecurityUtils.getUserId();
            studyData.setTeacherId(teacherId);
        }
        startPage();
        PageUtils.startPage(studyData.getPageNum(), studyData.getPageSize());
        List<StudyData> studyDataList = learningMaterialsService.listByTeacher(studyData);
        return getDataTable(studyDataList);
    }


    /**
     * 根据学习资料查询学习资料详情
     *
     * @param
     * @return
     */
    @PostMapping("/seleteById")
    public TableDataInfo selectLearningMaterialsById(@RequestBody StudyData studyData) {
        Long id = studyData.getId();
        startPage();
        PageUtils.startPage(studyData.getPageNum(), studyData.getPageSize());
        List<StudyData> list = learningMaterialsService.getByLearningMaterialsById(id);
        return getDataTable(list);
    }

    /**
     * 查询没有和这个学习资料绑定的课程
     * @return
     */
    @GetMapping("/list_not_binding/{teacherId}/{id}")
    public TableDataInfo selectByTeacherIdToCourse(@PathVariable("teacherId") Long teacherId,
                                                   @PathVariable("id") Long id) {
        if (teacherId==null){
            teacherId = SecurityUtils.getUserId();
        }
        List<CourseClass> list = learningMaterialsService.selectByTeacherIdToCourse(teacherId,id);
        return getDataTable(list);
    }

    /**
     * 添加学习资料与课程的绑定
     * @return
     */
    @PostMapping("/binding_course")
    public AjaxResult bindingCourseById(@RequestBody StudyDataDTO studyDataDTO) {
        if (studyDataDTO.getTeacherId() == null){
            studyDataDTO.setTeacherId(SecurityUtils.getUserId());
        }
        return toAjax(learningMaterialsService.bindingCourseById(studyDataDTO));
    }


    /**
     * 根据学习资料id删除qi绑定的所有课程
     * @param id  学习资料id
     * @return
     */
    @PostMapping("/delete/{id}")
    public AjaxResult deleteLearningMaterialsById(@PathVariable("id") Long id) {

        return toAjax(learningMaterialsService.deleteLearningMaterialsById(id));
    }


    /**
     * 根据学习资料id移除绑定课程
     * @param id 学习资料id courseId 课程id
     * @return
     */
    @PostMapping("/remove/{id}/{courseId}")
    public AjaxResult removCourseById(@PathVariable("id") Long id,
                                      @PathVariable("courseId") Long courseId) {

        return toAjax(learningMaterialsService.removCourseById(id,courseId));
    }


    /**
     * 总后台查询所有学习资料
     * @param studyData
     * @return
     */
    @PostMapping("/selectAll")
    public TableDataInfo selectAll(@RequestBody StudyData studyData) {
        startPage();
        PageUtils.startPage(studyData.getPageNum(), studyData.getPageSize());
        List<MaterialsVO> list = learningMaterialsService.selectAll(studyData);
        return getDataTable(list);
    }

    /**
     * 上传学习资料
     * @param studyData
     * @return
     */
    @PostMapping("/upload")
    public AjaxResult upload(@RequestBody StudyData studyData) {

        return toAjax(learningMaterialsService.upload(studyData));
    }
}