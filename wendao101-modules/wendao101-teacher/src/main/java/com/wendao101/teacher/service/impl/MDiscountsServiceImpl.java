package com.wendao101.teacher.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.Course;
import com.wendao101.teacher.domain.MDiscounts;
import com.wendao101.teacher.domain.MDiscountsCodeMap;
import com.wendao101.teacher.dto.MDiscountsAndCodeDataDTO;
import com.wendao101.teacher.dto.MDiscountsAndSelectCodeDTO;
import com.wendao101.teacher.dto.MDiscountsDTO;
import com.wendao101.teacher.dto.MdTotalSearchDTO;
import com.wendao101.teacher.mapper.CourseMapper;
import com.wendao101.teacher.mapper.MDiscountsCodeMapMapper;
import com.wendao101.teacher.mapper.MDiscountsMapper;
import com.wendao101.teacher.mapper.MReceiveCouponMapper;
import com.wendao101.teacher.service.IMDiscountsService;
import com.wendao101.teacher.vo.*;
import org.apache.commons.lang3.RandomStringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Pattern;

/**
 * 优惠券Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-05
 */
@Service
public class MDiscountsServiceImpl implements IMDiscountsService {
    @Resource
    private MDiscountsMapper mDiscountsMapper;

    @Resource
    private MReceiveCouponMapper mReceiveCouponMapper;

    @Resource
    private CourseMapper courseMapper;

    @Resource
    private MDiscountsCodeMapMapper mDiscountsCodeMapMapper;


    /**
     * 获取优惠券详细信息
     *
     * @param id 优惠券主键
     * @return 优惠券
     */
    @Override
    public MDiscountsDTO selectMDiscountsById(Long id, String teacherId) {

        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(id, Long.valueOf(teacherId));
        //根据id查询课程并返回
        Course course = courseMapper.selectCourseById(mDiscountsDTO.getCourseId());
        mDiscountsDTO.setCourse(course);

        return mDiscountsDTO;
    }

    /**
     * 查询优惠券列表并跟新优惠券的状态
     *
     * @param mDiscountsAndStatusVO 优惠券查询条件vo
     * @return 优惠券
     */
    @Override
    public List<MDiscountsDTO> selectMDiscountsList(MDiscountsAndStatusVO mDiscountsAndStatusVO) {
        //修改优惠券状态
        mDiscountsMapper.updateStatus();
        //查询优惠券列表并跟新优惠券的状态
        List<MDiscountsDTO> list = mDiscountsMapper.selectMDiscountsList(mDiscountsAndStatusVO);
        if(CollectionUtils.isNotEmpty(list)){
            list.forEach(e -> {
                updateDiscountsStatus(e);
                if (e.getDiscountsType() == 0) {
                    Integer count = mReceiveCouponMapper.selectAll(e.getId());
                    e.setReceiveCouponSum(count);
                } else if (e.getDiscountsType() == 1) {
                    Integer count = mDiscountsCodeMapMapper.selectAll(e.getId());
                    e.setReceiveCouponSum(count);
                }
            });
            return list;
        }
        return new ArrayList<>();
    }

    /**
     * 新增优惠券
     *
     * @param mDiscountsVO 优惠券
     * @return 结果
     */
    @Override
    @Transactional
    public void insertMDiscounts(MDiscountsVO mDiscountsVO) {
        //获取所有的课程id和价格对象
        List<MpriceAndCourseIdVO> priceAndCourseIdList = mDiscountsVO.getPriceAndCourseIdList();

        MDiscounts mDiscounts = new MDiscounts();
        mDiscounts.setId(null);
        /** 是否允许继续使用，0不允许，1允许 */
        mDiscounts.setIsKeepUsing(1);
        //设置是否停止活动  新增时默认为  0不停止活动
        mDiscounts.setIsStopAction(0);
        mDiscounts.setReceiveMax(mDiscountsVO.getReceiveMax());
        mDiscounts.setTimeStatus(mDiscountsVO.getTimeStatus());
        mDiscounts.setValidTime(mDiscountsVO.getValidTime());
        mDiscounts.setDiscountsName(mDiscountsVO.getDiscountsName());
        mDiscounts.setDiscountsType(mDiscountsVO.getDiscountsType());
        mDiscounts.setTeacherId(mDiscountsVO.getTeacherId());
        mDiscounts.setDiscountsCodeId(null);
        mDiscounts.setDiscountsMoney(mDiscountsVO.getDiscountsMoney());
        mDiscounts.setDiscountsStartTime(mDiscountsVO.getDiscountsStartTime());
        mDiscounts.setDiscountsEndTime(mDiscountsVO.getDiscountsEndTime());
        mDiscounts.setDiscountsSum(mDiscountsVO.getDiscountsSum());
        mDiscounts.setReceiveCouponSum(mDiscountsVO.getReceiveCouponSum());
        mDiscounts.setDiscountsNumberType(mDiscountsVO.getDiscountsNumberType());
        mDiscounts.setReceiveCouponStartTime(mDiscountsVO.getReceiveCouponStartTime());
        mDiscounts.setReceiveCouponEndTime(mDiscountsVO.getReceiveCouponEndTime());
        mDiscounts.setCreateTime(DateUtils.getNowDate());
        mDiscounts.setUpdateTime(DateUtils.getNowDate());
        mDiscounts.setIsDelete(0);

        //设置优惠券状态
        Date receiveCouponStartTime = mDiscounts.getReceiveCouponStartTime();
        Date receiveCouponEndTime = mDiscounts.getReceiveCouponEndTime();
        Date date = new Date();
        if (!date.after(receiveCouponStartTime)) {
            //当前时间早于开始时间  未开始
            mDiscounts.setDiscountsStatus(0);
        } else if (date.after(receiveCouponStartTime) && !date.after(receiveCouponEndTime)) {
            //当前时间晚于开始时间并且当前时间早于结束时间  进行中
            mDiscounts.setDiscountsStatus(1);
        } else {
            //否则就是已结束
            mDiscounts.setDiscountsStatus(2);
        }


        //有多少个课程创建多少个优惠券对应一个课程
        try {
            for (int i = 0; i < priceAndCourseIdList.size(); i++) {

                //根据课程id和优惠券类型判断当前是否有存在同类型绑定相同的课程的优惠券
                int count = mDiscountsMapper.selectMDiscountsByCourseId(priceAndCourseIdList.get(i).getCourseId(), mDiscounts.getDiscountsType());
                if (count > 0) {
                    throw new IllegalArgumentException("关联该课程的已有对应优惠，请选择其他课程！");
                }

                //获取课程价格和优惠价格相减
                //BigDecimal subtract = priceAndCourseIdList.get(i).getPrice().subtract(mDiscounts.getDiscountsMoney());
                //获取当前优惠券类型
                //Integer discountsType = mDiscountsVO.getDiscountsType();
//                if (discountsType == 0) {
//                    //相减后的价格和10比较
//                    //小于0 说明优惠后的价格课程低于10元 则抛出异常
//                    if (subtract.compareTo(BigDecimal.valueOf(10)) < 0) {
//                        throw new IllegalArgumentException("普通课程优惠后的价格不能低于10元");
//                    }
//                } else {
//                    if (subtract.compareTo(BigDecimal.valueOf(0)) <= 0) {
//                        throw new IllegalArgumentException("分享课程优惠后的价格不能低于0.01元");
//                    }
//                }
                //获取优惠券总数
                Integer discountsSum = mDiscounts.getDiscountsSum();
                //优惠后的价格>=10 则添加优惠券
                //设置课程id
                mDiscounts.setCourseId(priceAndCourseIdList.get(i).getCourseId());
                //添加优惠券
                mDiscountsMapper.insertMDiscounts(mDiscounts);

                //获取添加后的优惠券id
                Long discountsId = mDiscounts.getId();
                //判断当前优惠券是否是分享优惠卷
                if (mDiscounts.getDiscountsType() == 1 && discountsSum > 0) {
                    for (int j = 0; j < discountsSum; j++) {
                        //添加优惠码
                        //创建优惠码对象
                        MDiscountsCodeMap mDiscountsCodeMap = new MDiscountsCodeMap();
                        mDiscountsCodeMap.setId(null);
                        mDiscountsCodeMap.setDiscountsId(discountsId);
                        mDiscountsCodeMap.setDiscountsCode("YHM" + generateUuid());
                        mDiscountsCodeMap.setUseType(3);
                        mDiscountsCodeMap.setUserId(null);
                        mDiscountsCodeMap.setNickName(null);
                        mDiscountsCodeMap.setGetTime(null);
                        mDiscountsCodeMap.setUseTime(null);
                        mDiscountsCodeMap.setCreateTime(DateUtils.getNowDate());
                        mDiscountsCodeMap.setUpdateTime(DateUtils.getNowDate());
                        mDiscountsCodeMapMapper.insertMDiscountsCodeMap(mDiscountsCodeMap);
                    }
                }

            }
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage());
        }
    }

    /**
     * 修改优惠券
     *
     * @param mDiscountsVO 优惠券
     * @return 结果
     */
    @Override
    public int update(MDiscountsVO mDiscountsVO) {
        //获取所有的课程id和价格对象
        List<MpriceAndCourseIdVO> priceAndCourseIdList = mDiscountsVO.getPriceAndCourseIdList();

        MDiscounts mDiscounts = new MDiscounts();
        mDiscounts.setId(mDiscountsVO.getId());
        /** 是否允许继续使用，0不允许，1允许 */
        mDiscounts.setIsKeepUsing(1);
        //设置是否停止活动  新增时默认为  0不停止活动
        mDiscounts.setIsStopAction(0);
        mDiscounts.setReceiveMax(mDiscountsVO.getReceiveMax());
        mDiscounts.setTimeStatus(mDiscountsVO.getTimeStatus());
        mDiscounts.setValidTime(mDiscountsVO.getValidTime());
        mDiscounts.setDiscountsName(mDiscountsVO.getDiscountsName());
        mDiscounts.setDiscountsType(mDiscountsVO.getDiscountsType());
        mDiscounts.setTeacherId(mDiscountsVO.getTeacherId());
        mDiscounts.setDiscountsCodeId(null);
        mDiscounts.setDiscountsMoney(mDiscountsVO.getDiscountsMoney());
        mDiscounts.setDiscountsStartTime(mDiscountsVO.getDiscountsStartTime());
        mDiscounts.setDiscountsEndTime(mDiscountsVO.getDiscountsEndTime());
        mDiscounts.setReceiveCouponSum(mDiscountsVO.getReceiveCouponSum());
        mDiscounts.setDiscountsNumberType(mDiscountsVO.getDiscountsNumberType());
        mDiscounts.setReceiveCouponStartTime(mDiscountsVO.getReceiveCouponStartTime());
        mDiscounts.setReceiveCouponEndTime(mDiscountsVO.getReceiveCouponEndTime());
        mDiscounts.setCreateTime(DateUtils.getNowDate());
        mDiscounts.setUpdateTime(DateUtils.getNowDate());
        mDiscounts.setIsDelete(0);

        //获取编辑前的数据
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(mDiscountsVO.getId(), mDiscountsVO.getTeacherId());
        //设置优惠券状态
        Date receiveCouponStartTime = mDiscounts.getReceiveCouponStartTime();
        Date receiveCouponEndTime = mDiscounts.getReceiveCouponEndTime();
        Date date = new Date();
        if (!date.after(receiveCouponStartTime)) {
            //当前时间早于开始时间  未开始
            mDiscounts.setDiscountsStatus(0);
        } else if (date.after(receiveCouponStartTime) && !date.after(receiveCouponEndTime)) {
            //当前时间晚于开始时间并且当前时间早于结束时间  进行中
            mDiscounts.setDiscountsStatus(1);
            //普通优惠券判断当前添加的数量是否是大于之前的数量
            if(mDiscountsDTO.getDiscountsNumberType() == 0 && mDiscountsVO.getDiscountsNumberType() == 0){
                Integer discountsSum = mDiscountsDTO.getDiscountsSum();
                if (discountsSum > mDiscountsVO.getDiscountsSum()) {
                    throw new IllegalArgumentException("设置的优惠券数量不能小于之前的数量！");
                }
            }
        } else {
            //否则就是已结束
            mDiscounts.setDiscountsStatus(2);
        }
        //设置优惠券的数量
        mDiscounts.setDiscountsSum(mDiscountsVO.getDiscountsSum());

        //修改前
        Long beforeCourseId = mDiscountsDTO.getCourseId();
        //修改后
        Long afterCourseId = mDiscountsVO.getPriceAndCourseIdList().get(0).getCourseId();
        if (!beforeCourseId.equals(afterCourseId)) {
            //根据课程id和优惠券类型判断当前是否有存在同类型绑定相同的课程的优惠券
            int count = mDiscountsMapper.selectMDiscountsByCourseId(priceAndCourseIdList.get(0).getCourseId(), mDiscounts.getDiscountsType());
            if (count > 0) {
                throw new IllegalArgumentException("关联该课程的已有对应优惠，请选择其他课程！");
            }
        }

        //获取课程价格和优惠价格相减
//        BigDecimal subtract = priceAndCourseIdList.get(0).getPrice().subtract(mDiscounts.getDiscountsMoney());
//
//        //获取当前优惠券类型
//        Integer discountsType = mDiscountsVO.getDiscountsType();
//        if (discountsType == 0) {
//            //相减后的价格和10比较
//            //小于0 说明优惠后的价格课程低于10元 则抛出异常
//            if (subtract.compareTo(BigDecimal.valueOf(10)) < 0) {
//                throw new IllegalArgumentException("普通课程优惠后的价格不能低于10元");
//            }
//        } else {
//            if (subtract.compareTo(BigDecimal.valueOf(0)) <= 0) {
//                throw new IllegalArgumentException("分享课程优惠后的价格不能低于0.01元");
//            }
//        }
        //设置课程id
        mDiscounts.setCourseId(priceAndCourseIdList.get(0).getCourseId());
        return mDiscountsMapper.update(mDiscounts);


    }

    /**
     * 优惠码页面列表查询
     *
     * @param
     * @return 结果
     */
    @Override
    public List<MDiscountsAndSelectCodeDTO> SelectDiscountsCode(MDiscountsAndCodeVO mDiscountsAndCodeVO) {
        List<MDiscountsAndSelectCodeDTO> list = mDiscountsCodeMapMapper.SelectDiscountsCode(mDiscountsAndCodeVO);
        list.removeIf(Objects::isNull);
        return list;
    }

    /**
     * 优惠码页面数据
     *
     * @param
     * @return 结果
     */
    @Override
    public MDiscountsAndCodeDataDTO SelectDiscountsCodeData(Long discountsId, String strTeacherId) {
        //修改优惠券状态
        mDiscountsMapper.updateStatus();
        //获取未使用人数
        Integer unusedNum = mDiscountsCodeMapMapper.unusedNum(discountsId);
        //获取已使用人数
        Integer haveBeenUsedNum = mDiscountsCodeMapMapper.haveBeenUsedNum(discountsId);
        //获取当前剩余
        Integer residueNum = mDiscountsCodeMapMapper.residueNum(discountsId);
        //查询优惠券信息
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(discountsId, Long.valueOf(strTeacherId));
        MDiscountsAndCodeDataDTO mDiscountsAndCodeDataDTO = new MDiscountsAndCodeDataDTO();
        mDiscountsAndCodeDataDTO.setDiscountsName(mDiscountsDTO.getDiscountsName());
        mDiscountsAndCodeDataDTO.setTitle(mDiscountsDTO.getTitle());
        mDiscountsAndCodeDataDTO.setReceiveCouponStartTime(mDiscountsDTO.getReceiveCouponStartTime());
        mDiscountsAndCodeDataDTO.setReceiveCouponEndTime(mDiscountsDTO.getReceiveCouponEndTime());
        mDiscountsAndCodeDataDTO.setDiscountsStatus(mDiscountsDTO.getDiscountsStatus());
        mDiscountsAndCodeDataDTO.setUnusedNum(unusedNum);
        mDiscountsAndCodeDataDTO.setHaveBeenUsedNum(haveBeenUsedNum);
        mDiscountsAndCodeDataDTO.setResidueNum(residueNum);
        mDiscountsAndCodeDataDTO.setDiscountsStatus(mDiscountsDTO.getDiscountsStatus());

        return mDiscountsAndCodeDataDTO;
    }

    /**
     * 停止活动
     *
     * @param
     * @return 结果
     */
    @Override
    public int stopAction(MDiscountsStopActionVO mDiscountsStopActionVO) {
        //获取当前是停止活动继续使用还是不继续使用
        Integer isKeepUsing = mDiscountsStopActionVO.getIsKeepUsing();
        //查询当前优惠券的类型，根据类型选择修改优惠券的状态
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(mDiscountsStopActionVO.getDiscountsId(), mDiscountsStopActionVO.getTeacherId());
        Integer discountsType = mDiscountsDTO.getDiscountsType();
        //不继续使用就将对应的优惠券都改为过期
        if (isKeepUsing == 0) {
            if (discountsType == 0) {
                //修改普通优惠券状态并修改过期时间
                mReceiveCouponMapper.updateMReceiveCouponStatus(mDiscountsStopActionVO.getDiscountsId());
            } else if (discountsType == 1) {
                //修改分享优惠券状态并修改过期时间
                mDiscountsCodeMapMapper.updateMdiscountsStatus(mDiscountsStopActionVO.getDiscountsId());
            }
        } else if (isKeepUsing == 1) {
            //允许继续使用则将未领取的优惠都作废
            if (discountsType == 0) {
                //修改普通优惠券未领取状态并修改过期时间 //普通优惠券没有未领取
//                mReceiveCouponMapper.updateMReceiveCouponContinueStatus(mDiscountsStopActionVO.getDiscountsId());
            } else if (discountsType == 1) {
                //修改分享优惠券未领取状态并修改过期时间
                mDiscountsCodeMapMapper.updateMdiscountsContinueStatus(mDiscountsStopActionVO.getDiscountsId());
            }
        }
        //修改优惠券的领取结束时间为当前停止时间
        MDiscounts mDiscounts = new MDiscounts();
        mDiscounts.setId(mDiscountsStopActionVO.getDiscountsId());
        mDiscounts.setReceiveCouponEndTime(DateUtils.getNowDate());
        mDiscountsMapper.update(mDiscounts);

        mDiscountsStopActionVO.setDiscountsStatus(3);
        mDiscountsStopActionVO.setUpdateTime(DateUtils.getNowDate());
        return mDiscountsMapper.stopAction(mDiscountsStopActionVO);
    }

    /**
     * 优惠码作废
     *
     * @param
     * @return 结果
     */
    @Override
    public int isCancellation(Long discountsCodeId) {

        return mDiscountsCodeMapMapper.isCancellation(discountsCodeId);
    }

    /**
     * 添加优惠码
     *
     * @param
     * @return 结果
     */
    @Override
    @Transactional
    public void AddDiscountsCode(MDiscountsAndAddCodeVO mDiscountsAndAddCodeVO, String strTeacherId) {


        for (int i = 0; i < mDiscountsAndAddCodeVO.getCount(); i++) {
            MDiscountsCodeMap mDiscountsCodeMap = new MDiscountsCodeMap();
            mDiscountsCodeMap.setId(null);
            mDiscountsCodeMap.setDiscountsId(mDiscountsAndAddCodeVO.getDiscountsId());
            mDiscountsCodeMap.setDiscountsCode("YHM" + generateUuid());
            mDiscountsCodeMap.setUseType(3);
            mDiscountsCodeMap.setUserId(null);
            mDiscountsCodeMap.setNickName(null);
            mDiscountsCodeMap.setGetTime(null);
            mDiscountsCodeMap.setUseTime(null);
            mDiscountsCodeMap.setCreateTime(DateUtils.getNowDate());
            mDiscountsCodeMap.setUpdateTime(DateUtils.getNowDate());
            //添加优惠码
            mDiscountsCodeMapMapper.insertMDiscountsCodeMap(mDiscountsCodeMap);
        }
        //添加完优惠码，修改优惠券的总数
        Long discountsId = mDiscountsAndAddCodeVO.getDiscountsId();
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(discountsId, Long.valueOf(strTeacherId));
        Integer discountsSum = mDiscountsDTO.getDiscountsSum();
        discountsSum = discountsSum + mDiscountsAndAddCodeVO.getCount();

        mDiscountsMapper.updateDiscountsSum(Long.valueOf(strTeacherId), discountsId, discountsSum);
    }

    /**
     * 定向发放优惠券
     *
     * @param
     * @return 结果
     */
    @Override
    @Transactional
    public void provideDiscounts(MDiscountsCodeProvideVO mDiscountsCodeProvideVO, String strTeacherId) {
        //优惠卷id
        Long discountsId = mDiscountsCodeProvideVO.getDiscountsId();
        //当前剩余总数
        Integer sum = mDiscountsCodeMapMapper.getResidueNum(discountsId);

        //app类型
        Integer appNameType = mDiscountsCodeProvideVO.getAppNameType();
        if(appNameType==null){
            throw new IllegalArgumentException("appNameType必传!");
        }

        //判断是单个还是多个
        if (mDiscountsCodeProvideVO.getContent().contains("#")) {
            //将多个进行切割
            String[] split = mDiscountsCodeProvideVO.getContent().split("#");
            List<String> list = Arrays.asList(split);
            //根据数量判断是否超出发放优惠券数量
            if (list.size() > sum) {
                throw new RuntimeException("可发放的优惠券码数量不足,请增加优惠码!");
            } else {
                if (mDiscountsCodeProvideVO.getProvideType() == 0) {
                    //手机号
                    //循环判断是否有手机号格式不正确
                    list.forEach(e -> {
                        //判断手机号是否正确
                        if (!validatePhoneNumber(e)) {
                            throw new IllegalArgumentException("手机号格式不正确");
                        }
                    });
                    //将用户数据存入到优惠码中
                    updatePhoneList(list, discountsId, strTeacherId,appNameType);
                } else {
                    //HID
                    updateUserIdList(list, discountsId, strTeacherId,appNameType);
                }
            }
        } else {
            String content = mDiscountsCodeProvideVO.getContent();
            List<String> strings = new ArrayList<>();
            strings.add(content);
            //根据数量判断是否超出发放优惠券数量
            if (sum < 1) {
                throw new RuntimeException("可发放的优惠券码数量不足,请增加优惠码!");
            } else {
                //单个
                if (mDiscountsCodeProvideVO.getProvideType() == 0) {
                    //手机号
                    //判断手机号是否正确
                    if (!validatePhoneNumber(strings.get(0))) {
                        throw new IllegalArgumentException("手机号格式不正确");
                    }
                    //将用户数据存入到优惠码中
                    updatePhoneList(strings, discountsId, strTeacherId,appNameType);
                } else {
                    //HID
                    updateUserIdList(strings, discountsId, strTeacherId,appNameType);
                }
            }
        }


    }

    /**
     * 导出优惠码列表
     *
     * @param
     * @return 结果
     */
    @Override
    public List<MDiscountsAndSelectCodeDTO> exportExcel(MDiscountsAndCodeVO mDiscountsAndCodeVO) {
        List<MDiscountsAndSelectCodeDTO> list = mDiscountsCodeMapMapper.SelectDiscountsCode(mDiscountsAndCodeVO);
        return list;
    }

//    @Override
//    public List<MDiscountsDTO> selectReceiveList(MDiscountsAndReceiveVO mDiscountsAndReceiveVO) {
//        //查询优惠券列表并跟新优惠券的状态
//        List<MDiscountsDTO> list = mDiscountsMapper.selectReceiveList(mDiscountsAndReceiveVO);
//
//        return list;
//    }


    /**
     * 批量删除优惠券
     *
     * @param ids 需要删除的优惠券主键
     * @return 结果
     */
    @Override
    public int deleteMDiscountsByIds(Long[] ids) {
        return mDiscountsMapper.deleteMDiscountsByIds(ids);
    }

    /**
     * 删除优惠券信息
     *
     * @param id 优惠券主键
     * @return 结果
     */
    @Override
    public int deleteMDiscountsById(Long id, String strTeacherId) {
        //查询是否有领取数据
//        Integer receiveNum = mReceiveCouponMapper.selectAll(id);
        //查询是否是允许状态
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(id, Long.valueOf(strTeacherId));
//        Integer isKeepUsing = mDiscountsDTO.getIsKeepUsing();
        //已领取和允许继续使用不能删除
        Integer discountsStatus = mDiscountsDTO.getDiscountsStatus();
        //优惠券状态等于0 未开始,可直接删除
//        if (discountsStatus != 0) {
//            if (receiveNum > 0 || isKeepUsing == 1) {
//                throw new IllegalArgumentException("优惠券已领取不能删除！");
//            }
//        }
        if (discountsStatus == 1) {
            throw new IllegalArgumentException("优惠券进行中不能删除！");
        }

        //删除优惠券则将所有未使用和未领取的记录都修改为已作废状态
        if(mDiscountsDTO.getDiscountsType() == 0){
            mReceiveCouponMapper.updateMReceiveCouponStatus(mDiscountsDTO.getId());
        }
        if(mDiscountsDTO.getDiscountsType() == 1){
            mDiscountsCodeMapMapper.updateMdiscountsStatus(mDiscountsDTO.getId());
        }
        //修改优惠券状态
        return mDiscountsMapper.deleteMDiscountsById(id);
    }

    /**
     * 总后台查询所有店铺优惠券
     */
    @Override
    public List<MdTotalSearchVO> selectAll(MdTotalSearchDTO mdTotalSearchDTO) {
        return mDiscountsMapper.selectAll(mdTotalSearchDTO);
    }

    /**
     * 根据优惠券id获取优惠券金额
     * @param discountsId
     * @return
     */
    @Override
    public BigDecimal getDiscountsMoney(Long discountsId) {
        return mDiscountsMapper.getDiscountsMoney(discountsId);
    }

    /**
     * 修改手机号对应的优惠码信息
     *
     * @param phoneList   手机号数组
     * @param discountsId 优惠券id
     */
    public void updatePhoneList(List<String> phoneList, Long discountsId, String strTeacherId,Integer appNameType) {
        //根据手机号查询用户信息（用户id和用户名称）
        List<MDiscountsCodeUserVO> userList = mDiscountsMapper.selectUser(phoneList,appNameType);
//        if (CollectionUtils.isEmpty(userList) || userList.size() != phoneList.size()) {
//            throw new IllegalArgumentException("发放中的手机号有未注册用户");
//        }
        //根据用户id查询出当前优惠券最大领取数
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(discountsId, Long.valueOf(strTeacherId));
        Integer receiveMax = mDiscountsDTO.getReceiveMax();

        //根据手机号个数修改对应未领取的优惠券对应
        //遍历 将用户信息存储
        userList.forEach(item -> {
            //查询领取表信息领取数
            int i = mDiscountsMapper.selectCounts(discountsId, item.getUserId());
            //判断领取表的数量是否超过最大数
            if (receiveMax <= i) {
                throw new IllegalArgumentException("添加的用户中有超过该活动优惠券数量限制");
            }
            //根据优惠券id获取一张没有领取的优惠券并填充数据
            MDiscountsCodeMap mDiscountsCodeMap = mDiscountsCodeMapMapper.residueAndCode(discountsId, 1);
            mDiscountsCodeMap.setUserId(item.getUserId());
            mDiscountsCodeMap.setNickName(item.getNickName());
            mDiscountsCodeMap.setUseType(0);
            mDiscountsCodeMap.setGetTime(DateUtils.getNowDate());
            mDiscountsCodeMap.setUseTime(null);
            mDiscountsCodeMap.setUpdateTime(DateUtils.getNowDate());
            //将优惠码数据进行修改
            mDiscountsCodeMapMapper.updateMDiscountsCodeMap(mDiscountsCodeMap);

        });
    }

    /**
     * 修改用户信息对应的优惠码信息
     *
     * @param userIdList  用户id数组
     * @param discountsId 优惠券id
     */
    public void updateUserIdList(List<String> userIdList, Long discountsId, String strTeacherId,Integer appNameType) {
        //根据用户id查询用户信息（用户id和用户名称）
        List<MDiscountsCodeUserVO> userList = mDiscountsMapper.selectUserId(userIdList,appNameType);
        if(userList.isEmpty()){
            if(appNameType==1){
                throw new IllegalArgumentException("未找到对应的用户,请确认是问到好课的用户!");
            }else{
                throw new IllegalArgumentException("未找到对应的用户,请确认是问到课堂的用户!");
            }
        }
        if (CollectionUtils.isEmpty(userList) || userList.size() != userIdList.size()) {
            throw new IllegalArgumentException("发放中的id有未注册用户");
        }

        //根据用户id查询出当前优惠券最大领取数
        MDiscountsDTO mDiscountsDTO = mDiscountsMapper.selectMDiscountsById(discountsId, Long.valueOf(strTeacherId));
        Integer receiveMax = mDiscountsDTO.getReceiveMax();

        //遍历 将用户信息存储
        userList.forEach(item -> {
            //查询领取表信息领取数
            int i = mDiscountsMapper.selectCounts(discountsId, item.getUserId());
            //判断领取表的数量是否超过最大数
            if (receiveMax <= i) {
                throw new IllegalArgumentException("添加的用户中不能获得活动优惠券限制数量");
            }
            //根据优惠券id获取一张没有领取的优惠券并填充数据
            MDiscountsCodeMap mDiscountsCodeMap = mDiscountsCodeMapMapper.residueAndCode(discountsId, 1);
            mDiscountsCodeMap.setUserId(item.getUserId());
            mDiscountsCodeMap.setNickName(item.getNickName());
            mDiscountsCodeMap.setUseType(0);
            mDiscountsCodeMap.setGetTime(DateUtils.getNowDate());
            mDiscountsCodeMap.setUseTime(null);
            mDiscountsCodeMap.setUpdateTime(DateUtils.getNowDate());
            //将优惠码数据进行修改
            mDiscountsCodeMapMapper.updateMDiscountsCodeMap(mDiscountsCodeMap);

        });
    }


    //用于跟新优惠券状态
    public static void updateDiscountsStatus(MDiscountsDTO mDiscountsDTO) {

        Date receiveCouponStartTime = mDiscountsDTO.getReceiveCouponStartTime();
        Date receiveCouponEndTime = mDiscountsDTO.getReceiveCouponEndTime();
        Date date = new Date();
        //判断是否停止活动
        if (mDiscountsDTO.getIsStopAction() == 1) {
            mDiscountsDTO.setDiscountsStatus(2);
        } else {
            if (!date.after(receiveCouponStartTime)) {
                //当前时间早于开始时间  未开始
                mDiscountsDTO.setDiscountsStatus(0);
            } else if (date.after(receiveCouponStartTime) && !date.after(receiveCouponEndTime)) {
                //当前时间晚于开始时间并且当前时间早于结束时间  进行中
                mDiscountsDTO.setDiscountsStatus(1);
            } else {
                //否则就是已结束
                mDiscountsDTO.setDiscountsStatus(2);
            }
        }


    }

    //手机号校验
    public static boolean validatePhoneNumber(String phoneNumber) {
        // 定义手机号的正则表达式模式
        String pattern = "^1[3456789]\\d{9}$";

        // 使用 Pattern 类的 compile 方法将正则表达式编译为 Pattern 对象
        Pattern regex = Pattern.compile(pattern);

        // 使用 Matcher 类的 matches 方法进行匹配
        return regex.matcher(phoneNumber).matches();
    }

    //生成16位数字+小写英文 uuid
    public static String generateUuid() {
        return RandomStringUtils.randomAlphanumeric(13);
    }
}
