package com.wendao101.teacher.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 提现金额对象 withdraw_price
 *
 * <AUTHOR>
 * @date 2023-09-02
 */
@Data
public class HomePageWithdrawPriceDTO{

    /**
     * 主键id
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 推广员id
     */
    @Excel(name = "推广员id")
    private Long promoterId;

    /**
     * 在途金额
     */
    @Excel(name = "在途金额")
    private BigDecimal fundsTransitPrice;

    /**
     * 抖音可提现金额
     */
    @Excel(name = "抖音可提现金额")
    private BigDecimal dyMayWithdrawPrice;

    /**
     * 抖音已提现金额
     */
    @Excel(name = "抖音已提现金额")
    private BigDecimal dyWithdrawnPrice;

    /**
     * 快手可提现金额
     */
    @Excel(name = "快手可提现金额")
    private BigDecimal ksMayWithdrawPrice;

    /**
     * 快手已提现金额
     */
    @Excel(name = "快手已提现金额")
    private BigDecimal ksWithdrawnPrice;

    /**
     * 微信可提现金额
     */
    @Excel(name = "微信可提现金额")
    private BigDecimal wxMayWithdrawPrice;

    /**
     * 微信已提现金额
     */
    @Excel(name = "微信已提现金额")
    private BigDecimal wxWithdrawnPrice;

    /**
     * 视频号可提现金额
     */
    @Excel(name = "视频号可提现金额")
    private BigDecimal sphMayWithdrawPrice;

    /**
     * 视频号已提现金额
     */
    @Excel(name = "视频号已提现金额")
    private BigDecimal sphWithdrawnPrice;

    /**
     * 可提现金额
     */
    @Excel(name = "可提现金额")
    private BigDecimal mayWithdrawPrice;

    /**
     * 累计提现总金额
     */
    @Excel(name = "累计提现总金额")
    private BigDecimal totalWithdrawPrice;

    /**
     * 是否删除 0否 1是
     */
    @Excel(name = "是否删除 0否 1是")
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新时间 */
    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;



    private BigDecimal wapWithdrawnPrice;
    private BigDecimal xhsWithdrawnPrice;
    private BigDecimal pcWithdrawnPrice;
    private BigDecimal zsdpWithdrawnPrice;
    private BigDecimal ddWithdrawnPrice;
}
