package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.TAudioGroupRelation;
import com.wendao101.teacher.dto.AddAudioGroupRelationMapDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 音频和分组关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-26
 */
public interface TAudioGroupRelationMapper 
{
    /**
     * 查询音频和分组关系
     * 
     * @param id 音频和分组关系主键
     * @return 音频和分组关系
     */
    public TAudioGroupRelation selectTAudioGroupRelationById(Long id);

    /**
     * 查询音频和分组关系列表
     * 
     * @param tAudioGroupRelation 音频和分组关系
     * @return 音频和分组关系集合
     */
    public List<TAudioGroupRelation> selectTAudioGroupRelationList(TAudioGroupRelation tAudioGroupRelation);

    /**
     * 新增音频和分组关系
     * 
     * @param list 音频和分组关系
     * @return 结果
     */
    public int insertTAudioGroupRelation(List<AddAudioGroupRelationMapDTO> list);

    /**
     * 修改音频和分组关系
     * 
     * @param tAudioGroupRelation 音频和分组关系
     * @return 结果
     */
    public int updateTAudioGroupRelation(TAudioGroupRelation tAudioGroupRelation);

    /**
     * 删除音频和分组关系
     * 
     * @param id 音频和分组关系主键
     * @return 结果
     */
    public int deleteTAudioGroupRelationById(Long id);

    /**
     * 批量删除音频和分组关系
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTAudioGroupRelationByIds(Long[] ids);

    int removeFromGroup(@Param("teacherId") Long teacherId, @Param("groupId") Long groupId, @Param("audioIdArr") String[] audioIdArr);
}
