package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.QJkbdMapper;
import com.wendao101.teacher.domain.QJkbd;
import com.wendao101.teacher.service.IQJkbdService;

/**
 * 驾考宝典题目Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class QJkbdServiceImpl implements IQJkbdService 
{
    @Autowired
    private QJkbdMapper qJkbdMapper;

    /**
     * 查询驾考宝典题目
     * 
     * @param id 驾考宝典题目主键
     * @return 驾考宝典题目
     */
    @Override
    public QJkbd selectQJkbdById(Long id)
    {
        return qJkbdMapper.selectQJkbdById(id);
    }

    /**
     * 查询驾考宝典题目列表
     * 
     * @param qJkbd 驾考宝典题目
     * @return 驾考宝典题目
     */
    @Override
    public List<QJkbd> selectQJkbdList(QJkbd qJkbd)
    {
        return qJkbdMapper.selectQJkbdList(qJkbd);
    }

    /**
     * 新增驾考宝典题目
     * 
     * @param qJkbd 驾考宝典题目
     * @return 结果
     */
    @Override
    public int insertQJkbd(QJkbd qJkbd)
    {
        qJkbd.setCreateTime(DateUtils.getNowDate());
        return qJkbdMapper.insertQJkbd(qJkbd);
    }

    /**
     * 修改驾考宝典题目
     * 
     * @param qJkbd 驾考宝典题目
     * @return 结果
     */
    @Override
    public int updateQJkbd(QJkbd qJkbd)
    {
        qJkbd.setUpdateTime(DateUtils.getNowDate());
        return qJkbdMapper.updateQJkbd(qJkbd);
    }

    /**
     * 批量删除驾考宝典题目
     * 
     * @param ids 需要删除的驾考宝典题目主键
     * @return 结果
     */
    @Override
    public int deleteQJkbdByIds(Long[] ids)
    {
        return qJkbdMapper.deleteQJkbdByIds(ids);
    }

    /**
     * 删除驾考宝典题目信息
     * 
     * @param id 驾考宝典题目主键
     * @return 结果
     */
    @Override
    public int deleteQJkbdById(Long id)
    {
        return qJkbdMapper.deleteQJkbdById(id);
    }
}
