package com.wendao101.teacher.controller;

import com.wendao101.common.core.douyin.clock.request.QueryClockRecordRequest;
import com.wendao101.common.core.utils.PageUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.dto.ClockInQuestSearchDTO;
import com.wendao101.teacher.dto.TeacherInfomationDTO;
import com.wendao101.teacher.service.IClockInQuestService;
import com.wendao101.teacher.vo.ClockInQuestAllVO;
import com.wendao101.teacher.vo.ClockInQuestVO;
import com.wendao101.wendao.log.annotation.WenDaoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 打卡任务Controller
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@RestController
@RequestMapping("/clock_in_quest")
public class ClockInQuestController extends BaseController {
    @Autowired
    private IClockInQuestService clockInQuestService;
    /**
     * 新增打卡任务
     */
    @WenDaoLog(title = "营销中心", subTitle = "打卡管理", businessType = "新增打卡任务")
    @PostMapping("/addClockInQuest")
    public AjaxResult addClockInQuest(@RequestBody ClockInQuestVO clockInQuestVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockInQuestVO.setTeacherId(teacherId);
        clockInQuestService.addClockInQuest(clockInQuestVO);
        return success();
    }


    /**
     * 获取打卡任务详细信息
     */
    @GetMapping(value = "/getById/{id}")
    public AjaxResult getById(@PathVariable("id") Long id) {
        return success(clockInQuestService.getById(id));
    }

    /**
     * 修改打卡任务
     */
    @WenDaoLog(title = "营销中心", subTitle = "打卡管理", businessType = "修改打卡任务")
    @PostMapping("/updateQuest")
    public AjaxResult updateQuest(@RequestBody ClockInQuestVO clockInQuestVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockInQuestVO.setTeacherId(teacherId);
        clockInQuestService.updateQuest(clockInQuestVO);
        return success();
    }

    /**
     * 打卡任务列表
     *
     * @param clockInQuestAllVO
     * @param request
     * @return
     */
    @PostMapping("/selectAll")
    public AjaxResult selectAll(@RequestBody ClockInQuestAllVO clockInQuestAllVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockInQuestAllVO.setTeacherId(teacherId);
        return success(clockInQuestService.selectAll(clockInQuestAllVO));
    }

    /**
     * 查询该老师下的课程是否已经有打卡任务
     */
    @GetMapping(value = "/getByCourseId/{id}")
    public AjaxResult getByCourseId(@PathVariable("id") Long id, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        QueryClockRecordRequest recordRequest = new QueryClockRecordRequest();
        recordRequest.setCourseId(id);
        recordRequest.setTeacherId(teacherId);
        return success(clockInQuestService.getByCourseId(recordRequest));
    }

    /**
     * 总后台查询打卡任务列表
     */
    @PostMapping("/listTotalAll")
    public TableDataInfo listTotalAll(@RequestBody ClockInQuestSearchDTO clockInQuestSearchDTO, HttpServletRequest request) {
        startPage();
        PageUtils.startPage(clockInQuestSearchDTO.getPageNum(), clockInQuestSearchDTO.getPageSize());
        return getDataTable(clockInQuestService.listTotalAll(clockInQuestSearchDTO));
    }
    /**
     * 根据老师id查询店铺（老师）信息
     * @param clockInQuestVO
     * @return
     */
    @PostMapping("/teacherInfomation")
    public AjaxResult teacherInfomation(@RequestBody ClockInQuestVO clockInQuestVO){
        TeacherInfomationDTO teacherInfomationDTO = clockInQuestService.selectListByTeacherId(clockInQuestVO.getTeacherId());
        return success(teacherInfomationDTO);
    }

}
