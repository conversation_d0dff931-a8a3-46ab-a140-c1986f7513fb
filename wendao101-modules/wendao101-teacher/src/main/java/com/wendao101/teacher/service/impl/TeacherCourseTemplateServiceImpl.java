package com.wendao101.teacher.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Arrays;
import java.util.ArrayList;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.TeacherCourseTemplateMapper;
import com.wendao101.teacher.domain.TeacherCourseTemplate;
import com.wendao101.teacher.service.TeacherCourseTemplateService;

/**
 * 教师课程模板Service业务层处理
 * 
 */
@Service
public class TeacherCourseTemplateServiceImpl implements TeacherCourseTemplateService 
{
    @Autowired
    private TeacherCourseTemplateMapper teacherCourseTemplateMapper;

    /**
     * 查询教师课程模板
     * 
     * @param id 教师课程模板主键
     * @return 教师课程模板
     */
    @Override
    public TeacherCourseTemplate selectTeacherCourseTemplateById(Long id)
    {
        return teacherCourseTemplateMapper.selectTeacherCourseTemplateById(id);
    }

    /**
     * 根据ID和教师ID查询教师课程模板
     * 
     * @param id 教师课程模板主键
     * @param teacherId 教师ID
     * @return 教师课程模板
     */
    @Override
    public TeacherCourseTemplate selectTeacherCourseTemplateByIdAndTeacherId(Long id, Long teacherId)
    {
        TeacherCourseTemplate template = teacherCourseTemplateMapper.selectTeacherCourseTemplateById(id);
        if (template != null && template.getTeacherId().equals(teacherId)) {
            return template;
        }
        return null;
    }

    /**
     * 查询教师课程模板列表
     * 
     * @param teacherCourseTemplate 教师课程模板
     * @return 教师课程模板
     */
    @Override
    public List<TeacherCourseTemplate> selectTeacherCourseTemplateList(TeacherCourseTemplate teacherCourseTemplate)
    {
        return teacherCourseTemplateMapper.selectTeacherCourseTemplateList(teacherCourseTemplate);
    }

    /**
     * 新增教师课程模板
     * 
     * @param teacherCourseTemplate 教师课程模板
     * @return 结果
     */
    @Override
    public int insertTeacherCourseTemplate(TeacherCourseTemplate teacherCourseTemplate)
    {
        teacherCourseTemplate.setCreateTime(new Date());
        teacherCourseTemplate.setUpdateTime(new Date());
        return teacherCourseTemplateMapper.insertTeacherCourseTemplate(teacherCourseTemplate);
    }

    /**
     * 修改教师课程模板
     * 
     * @param teacherCourseTemplate 教师课程模板
     * @return 结果
     */
    @Override
    public int updateTeacherCourseTemplate(TeacherCourseTemplate teacherCourseTemplate)
    {
        teacherCourseTemplate.setUpdateTime(new Date());
        return teacherCourseTemplateMapper.updateTeacherCourseTemplate(teacherCourseTemplate);
    }

    /**
     * 根据ID和教师ID修改教师课程模板
     * 
     * @param teacherCourseTemplate 教师课程模板
     * @return 结果
     */
    @Override
    public int updateTeacherCourseTemplateByIdAndTeacherId(TeacherCourseTemplate teacherCourseTemplate)
    {
        // 先查询是否存在且属于当前教师
        TeacherCourseTemplate existTemplate = selectTeacherCourseTemplateByIdAndTeacherId(
            teacherCourseTemplate.getId(), teacherCourseTemplate.getTeacherId());
        
        if (existTemplate != null) {
            teacherCourseTemplate.setUpdateTime(new Date());
            return teacherCourseTemplateMapper.updateTeacherCourseTemplate(teacherCourseTemplate);
        }
        
        return 0;
    }

    /**
     * 批量删除教师课程模板
     * 
     * @param ids 需要删除的教师课程模板主键
     * @return 结果
     */
    @Override
    public int deleteTeacherCourseTemplateByIds(Long[] ids)
    {
        return teacherCourseTemplateMapper.deleteTeacherCourseTemplateByIds(ids);
    }

    /**
     * 根据ID和教师ID批量删除教师课程模板
     * 
     * @param ids 需要删除的教师课程模板主键集合
     * @param teacherId 教师ID
     * @return 结果
     */
    @Override
    public int deleteTeacherCourseTemplateByIdsAndTeacherId(Long[] ids, Long teacherId)
    {
        List<Long> validIds = new ArrayList<>();
        
        // 验证每个ID是否属于当前教师
        for (Long id : ids) {
            TeacherCourseTemplate template = selectTeacherCourseTemplateByIdAndTeacherId(id, teacherId);
            if (template != null) {
                validIds.add(id);
            }
        }
        
        if (!validIds.isEmpty()) {
            return teacherCourseTemplateMapper.deleteTeacherCourseTemplateByIds(validIds.toArray(new Long[0]));
        }
        
        return 0;
    }

    /**
     * 删除教师课程模板信息
     * 
     * @param id 教师课程模板主键
     * @return 结果
     */
    @Override
    public int deleteTeacherCourseTemplateById(Long id)
    {
        return teacherCourseTemplateMapper.deleteTeacherCourseTemplateById(id);
    }
} 