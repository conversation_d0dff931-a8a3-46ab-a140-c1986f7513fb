package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.CourseTemplate;

/**
 * 模版图片和htmlMapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface CourseTemplateMapper 
{
    /**
     * 查询模版图片和html
     * 
     * @param id 模版图片和html主键
     * @return 模版图片和html
     */
    public CourseTemplate selectCourseTemplateById(Long id);

    /**
     * 查询模版图片和html列表
     * 
     * @param courseTemplate 模版图片和html
     * @return 模版图片和html集合
     */
    public List<CourseTemplate> selectCourseTemplateList(CourseTemplate courseTemplate);

    /**
     * 新增模版图片和html
     * 
     * @param courseTemplate 模版图片和html
     * @return 结果
     */
    public int insertCourseTemplate(CourseTemplate courseTemplate);

    /**
     * 修改模版图片和html
     * 
     * @param courseTemplate 模版图片和html
     * @return 结果
     */
    public int updateCourseTemplate(CourseTemplate courseTemplate);

    /**
     * 删除模版图片和html
     * 
     * @param id 模版图片和html主键
     * @return 结果
     */
    public int deleteCourseTemplateById(Long id);

    /**
     * 批量删除模版图片和html
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseTemplateByIds(Long[] ids);
}
