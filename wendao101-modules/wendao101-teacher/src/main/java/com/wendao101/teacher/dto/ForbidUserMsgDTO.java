package com.wendao101.teacher.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * {
 *   "GroupId": "@TGS#2C5SZEAEF",
 *   "Members_Account": [ // 最多支持500个
 *       "peter",
 *       "leckie"
 *   ],
 *   "MuteTime": 60 // 禁言时间，单位为秒
 * }
 */
@Data
public class ForbidUserMsgDTO {
    /**
     * 用户名，长度不超过32字节
     */
    @JsonProperty("GroupId")
    @JSONField(name = "GroupId")
    String groupId;
    /**
     * 用户昵称
     */
    @JsonProperty("Members_Account")
    @JSONField(name = "Members_Account")
    String[] membersAccount;
    /**
     * 用户头像 URL
     */
    @JsonProperty("MuteTime")
    @JSONField(name = "MuteTime")
    Integer muteTime;
}
