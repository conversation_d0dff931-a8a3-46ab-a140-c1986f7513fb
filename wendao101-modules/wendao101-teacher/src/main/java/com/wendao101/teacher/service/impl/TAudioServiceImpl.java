package com.wendao101.teacher.service.impl;

import java.util.Date;
import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.TVideo;
import com.wendao101.teacher.dto.QueryAudioDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.TAudioMapper;
import com.wendao101.teacher.domain.TAudio;
import com.wendao101.teacher.service.ITAudioService;

/**
 * 素材音频Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-26
 */
@Service
public class TAudioServiceImpl implements ITAudioService 
{
    @Autowired
    private TAudioMapper tAudioMapper;

    /**
     * 查询素材音频
     * 
     * @param id 素材音频主键
     * @return 素材音频
     */
    @Override
    public TAudio selectTAudioById(Long id)
    {
        return tAudioMapper.selectTAudioById(id);
    }

    /**
     * 查询素材音频列表
     * 
     * @param tAudio 素材音频
     * @return 素材音频
     */
    @Override
    public List<TAudio> selectTAudioList(TAudio tAudio)
    {
        return tAudioMapper.selectTAudioList(tAudio);
    }

    /**
     * 新增素材音频
     * 
     * @param tAudio 素材音频
     * @return 结果
     */
    @Override
    public int insertTAudio(TAudio tAudio)
    {
        tAudio.setCreateTime(DateUtils.getNowDate());
        return tAudioMapper.insertTAudio(tAudio);
    }

    /**
     * 修改素材音频
     * 
     * @param tAudio 素材音频
     * @return 结果
     */
    @Override
    public int updateTAudio(TAudio tAudio)
    {
        tAudio.setUpdateTime(DateUtils.getNowDate());
        return tAudioMapper.updateTAudio(tAudio);
    }

    /**
     * 批量删除素材音频
     * 
     * @param ids 需要删除的素材音频主键
     * @return 结果
     */
    @Override
    public int deleteTAudioByIds(Long[] ids,Long teacherId)
    {
        return tAudioMapper.deleteTAudioByIds(ids,teacherId);
    }

    /**
     * 删除素材音频信息
     * 
     * @param id 素材音频主键
     * @return 结果
     */
    @Override
    public int deleteTAudioById(Long id)
    {
        return tAudioMapper.deleteTAudioById(id);
    }

    @Override
    public List<TAudio> selectTAudioListByGroup(QueryAudioDTO queryAudioDTO) {
        return tAudioMapper.selectTAudioListByGroup(queryAudioDTO);
    }

    @Override
    public int updateTAudioNameByIdAndTeacherId(Long teacherId, Long id, String fileName) {
        Date time = new Date();
        return tAudioMapper.updateTAudioNameByIdAndTeacherId( teacherId,  id,  fileName,time);
    }

    @Override
    public TAudio selectAudioByUrlOrOriginalUrl(String courseDirectoryUrl) {
        return tAudioMapper.selectAudioByUrlOrOriginalUrl(courseDirectoryUrl);
    }

    @Override
    public List<TAudio> selectTAudioListByIds(Long[] ids, Long teacherId) {
        return tAudioMapper.selectTAudioListByIds(ids,teacherId);
    }
}
