package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.CourseMachineAuditMapper;
import com.wendao101.teacher.domain.CourseMachineAudit;
import com.wendao101.teacher.service.ICourseMachineAuditService;

/**
 * 机器审核Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-01-02
 */
@Service
public class CourseMachineAuditServiceImpl implements ICourseMachineAuditService 
{
    @Autowired
    private CourseMachineAuditMapper courseMachineAuditMapper;

    /**
     * 查询机器审核
     * 
     * @param id 机器审核主键
     * @return 机器审核
     */
    @Override
    public CourseMachineAudit selectCourseMachineAuditById(Long id)
    {
        return courseMachineAuditMapper.selectCourseMachineAuditById(id);
    }

    /**
     * 查询机器审核列表
     * 
     * @param courseMachineAudit 机器审核
     * @return 机器审核
     */
    @Override
    public List<CourseMachineAudit> selectCourseMachineAuditList(CourseMachineAudit courseMachineAudit)
    {
        return courseMachineAuditMapper.selectCourseMachineAuditList(courseMachineAudit);
    }

    /**
     * 新增机器审核
     * 
     * @param courseMachineAudit 机器审核
     * @return 结果
     */
    @Override
    public int insertCourseMachineAudit(CourseMachineAudit courseMachineAudit)
    {
        courseMachineAudit.setCreateTime(DateUtils.getNowDate());
        return courseMachineAuditMapper.insertCourseMachineAudit(courseMachineAudit);
    }

    /**
     * 修改机器审核
     * 
     * @param courseMachineAudit 机器审核
     * @return 结果
     */
    @Override
    public int updateCourseMachineAudit(CourseMachineAudit courseMachineAudit)
    {
        courseMachineAudit.setUpdateTime(DateUtils.getNowDate());
        return courseMachineAuditMapper.updateCourseMachineAudit(courseMachineAudit);
    }

    /**
     * 批量删除机器审核
     * 
     * @param ids 需要删除的机器审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseMachineAuditByIds(Long[] ids)
    {
        return courseMachineAuditMapper.deleteCourseMachineAuditByIds(ids);
    }

    /**
     * 删除机器审核信息
     * 
     * @param id 机器审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseMachineAuditById(Long id)
    {
        return courseMachineAuditMapper.deleteCourseMachineAuditById(id);
    }
}
