package com.wendao101.teacher.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.wendao101.common.core.utils.DateUtils;
import com.wendao101.teacher.domain.CoursePromoter;
import com.wendao101.teacher.domain.TTeacher;
import com.wendao101.teacher.dto.CoursePromoterDTO;
import com.wendao101.teacher.mapper.CoursePromoterMapper;
import com.wendao101.teacher.mapper.PromoterCourseMapper;
import com.wendao101.teacher.mapper.PromoterMapper;
import com.wendao101.teacher.mapper.TTeacherMapper;
import com.wendao101.teacher.service.ICoursePromoterService;
import com.wendao101.teacher.vo.PromoterTeacherVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 推广员和课程关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-29
 */
@Service
public class CoursePromoterServiceImpl implements ICoursePromoterService {
    @Autowired
    private CoursePromoterMapper coursePromoterMapper;

    @Autowired
    private PromoterCourseMapper promoterCourseMapper;

    @Autowired
    private TTeacherMapper tTeacherMapper;

    @Autowired
    private PromoterMapper promoterMapper;

    /**
     * 查询推广员和课程关系
     *
     * @param id 推广员和课程关系主键
     * @return 推广员和课程关系
     */
    @Override
    public CoursePromoter selectCoursePromoterById(Long id) {
        return coursePromoterMapper.selectCoursePromoterById(id);
    }

    /**
     * 查询推广员和课程关系列表
     *
     * @param coursePromoter 推广员和课程关系
     * @return 推广员和课程关系
     */
    @Override
    public List<CoursePromoter> selectCoursePromoterList(CoursePromoter coursePromoter) {
        return coursePromoterMapper.selectCoursePromoterList(coursePromoter);
    }

    /**
     * 新增推广员和课程关系
     *
     * @param coursePromoter 推广员和课程关系
     * @return 结果
     */
    @Override
    public int insertCoursePromoter(CoursePromoter coursePromoter) {
        coursePromoter.setCreateTime(DateUtils.getNowDate());
        return coursePromoterMapper.insertCoursePromoter(coursePromoter);
    }

    /**
     * 修改推广员和课程关系
     *
     * @param coursePromoter 推广员和课程关系
     * @return 结果
     */
    @Override
    public int updateCoursePromoter(CoursePromoter coursePromoter) {
        coursePromoter.setUpdateTime(DateUtils.getNowDate());
        return coursePromoterMapper.updateCoursePromoter(coursePromoter);
    }

    /**
     * 批量删除推广员和课程关系
     *
     * @param ids 需要删除的推广员和课程关系主键
     * @return 结果
     */
    @Override
    public int deleteCoursePromoterByIds(Long[] ids) {
        return coursePromoterMapper.deleteCoursePromoterByIds(ids);
    }

    /**
     * 删除推广员和课程关系信息
     *
     * @param id 推广员和课程关系主键
     * @return 结果
     */
    @Override
    public int deleteCoursePromoterById(Long id) {
        return coursePromoterMapper.deleteCoursePromoterById(id);
    }


    /**
     * 总后台查询所有店铺与于光远关系
     */
    @Override
    public PageInfo<PromoterTeacherVO> coursePromoterListAll(CoursePromoterDTO coursePromoterDTO) {
        PageHelper.startPage(coursePromoterDTO.getPageNum(), coursePromoterDTO.getPageSize());
        List<PromoterTeacherVO> promoterResultDTOList = null;
        if (coursePromoterDTO.getIsPromoterNum() != null) {
            // 数量排序
            promoterResultDTOList = promoterMapper.selectTeacherIdList(coursePromoterDTO);

            List<Long> teacherIdList = promoterResultDTOList.stream().map(PromoterTeacherVO::getTeacherId).collect(Collectors.toList());
            coursePromoterDTO.setTeacherIdList(teacherIdList);
            PageHelper.clearPage();
            List<PromoterTeacherVO> promoterResultDTOList1 = promoterMapper.selectTeacherIdList1(coursePromoterDTO);
            Map<Long, PromoterTeacherVO> teacherVOMap = promoterResultDTOList1.stream().collect(Collectors.toMap(PromoterTeacherVO::getTeacherId, promoterTeacherVO -> promoterTeacherVO));
            for (PromoterTeacherVO dto : promoterResultDTOList) {
                if (teacherVOMap.get(dto.getTeacherId()) != null) {
                    dto.setPromoterincome(teacherVOMap.get(dto.getTeacherId()).getPromoterincome());
                }
            }
        } else if (coursePromoterDTO.getIsPromoternIcome() != null) {
            //按金额排序
            promoterResultDTOList = promoterMapper.selectTeacherIdList1(coursePromoterDTO);
            List<Long> teacherIdList = promoterResultDTOList.stream().map(PromoterTeacherVO::getTeacherId).collect(Collectors.toList());
            coursePromoterDTO.setTeacherIdList(teacherIdList);
            PageHelper.clearPage();
            List<PromoterTeacherVO> promoterResultDTOList1 = promoterMapper.selectTeacherIdList(coursePromoterDTO);
            Map<Long, PromoterTeacherVO> teacherVOMap = promoterResultDTOList1.stream().collect(Collectors.toMap(PromoterTeacherVO::getTeacherId, promoterTeacherVO -> promoterTeacherVO));
            for (PromoterTeacherVO dto : promoterResultDTOList) {
                if (teacherVOMap.get(dto.getTeacherId()) != null) {
                    dto.setPromoterNum(teacherVOMap.get(dto.getTeacherId()).getPromoterNum());
                }
            }
        } else {
            // 数量排序
            promoterResultDTOList = promoterMapper.selectTeacherIdList(coursePromoterDTO);
            List<Long> teacherIdList = promoterResultDTOList.stream().map(PromoterTeacherVO::getTeacherId).collect(Collectors.toList());
            coursePromoterDTO.setTeacherIdList(teacherIdList);
            PageHelper.clearPage();
            List<PromoterTeacherVO> promoterResultDTOList1 = promoterMapper.selectTeacherIdList1(coursePromoterDTO);
            Map<Long, PromoterTeacherVO> teacherVOMap = promoterResultDTOList1.stream().collect(Collectors.toMap(PromoterTeacherVO::getTeacherId, promoterTeacherVO -> promoterTeacherVO));
            for (PromoterTeacherVO dto : promoterResultDTOList) {
                if (teacherVOMap.get(dto.getTeacherId()) != null) {
                    dto.setPromoterincome(teacherVOMap.get(dto.getTeacherId()).getPromoterincome());
                }
            }
        }
        for (PromoterTeacherVO dto : promoterResultDTOList) {
            TTeacher tTeacher = tTeacherMapper.selectTTeacherByTeacherId(dto.getTeacherId());
            if (tTeacher != null) {
                dto.setAppNameType(tTeacher.getAppNameType());
                dto.setShopName(tTeacher.getShopName());
                dto.setMobile(tTeacher.getMobile());
                dto.setPlatform(tTeacher.getPlatform());
                dto.setPromoterNum(dto.getPromoterNum() == null ? 0 : dto.getPromoterNum());
                dto.setPromoterincome(dto.getPromoterincome() == null ? BigDecimal.ZERO : dto.getPromoterincome());
            }
        }
        return new PageInfo<>(promoterResultDTOList);
    }

    /**
     * 根据推广员数量排序
     *
     * @param voList
     * @param isPromoterNum
     */
    private void sortByPromoterNum(List<PromoterTeacherVO> voList, Integer isPromoterNum) {
        if (isPromoterNum == 1) {
            //升序排序
            // 使用 Collections.sort 方法和自定义的 Comparator 对象进行升序排序
            Collections.sort(voList, Comparator.comparing(PromoterTeacherVO::getPromoterNum));
        } else {
            //降序排序
            Collections.sort(voList, (vo1, vo2) -> Integer.compare(vo2.getPromoterNum(), vo1.getPromoterNum()));
        }

    }

    /**
     * 根据推广员数量排序
     *
     * @param voList
     * @param isPromoterincome
     */
    private void sortByPromoterIcom(List<PromoterTeacherVO> voList, Integer isPromoterincome) {
        if (isPromoterincome == 1) {

            //升序排序
            Collections.sort(voList, Comparator.comparing(PromoterTeacherVO::getPromoterincome));
        } else {
            //降序排序
            Collections.sort(voList, (vo1, vo2) -> vo2.getPromoterincome().compareTo(vo1.getPromoterincome()));
        }

    }
}
