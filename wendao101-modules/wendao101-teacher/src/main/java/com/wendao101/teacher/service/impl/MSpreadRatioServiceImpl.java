package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.MSpreadRatioMapper;
import com.wendao101.teacher.domain.MSpreadRatio;
import com.wendao101.teacher.service.IMSpreadRatioService;

/**
 * 推广人和购买信息关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Service
public class MSpreadRatioServiceImpl implements IMSpreadRatioService 
{
    @Autowired
    private MSpreadRatioMapper mSpreadRatioMapper;

    /**
     * 查询推广人和购买信息关联
     * 
     * @param id 推广人和购买信息关联主键
     * @return 推广人和购买信息关联
     */
    @Override
    public MSpreadRatio selectMSpreadRatioById(Long id)
    {
        return mSpreadRatioMapper.selectMSpreadRatioById(id);
    }

    /**
     * 查询推广人和购买信息关联列表
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 推广人和购买信息关联
     */
    @Override
    public List<MSpreadRatio> selectMSpreadRatioList(MSpreadRatio mSpreadRatio)
    {
        return mSpreadRatioMapper.selectMSpreadRatioList(mSpreadRatio);
    }

    /**
     * 新增推广人和购买信息关联
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 结果
     */
    @Override
    public int insertMSpreadRatio(MSpreadRatio mSpreadRatio)
    {
        mSpreadRatio.setCreateTime(DateUtils.getNowDate());
        return mSpreadRatioMapper.insertMSpreadRatio(mSpreadRatio);
    }

    /**
     * 修改推广人和购买信息关联
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 结果
     */
    @Override
    public int updateMSpreadRatio(MSpreadRatio mSpreadRatio)
    {
        mSpreadRatio.setUpdateTime(DateUtils.getNowDate());
        return mSpreadRatioMapper.updateMSpreadRatio(mSpreadRatio);
    }

    /**
     * 批量删除推广人和购买信息关联
     * 
     * @param ids 需要删除的推广人和购买信息关联主键
     * @return 结果
     */
    @Override
    public int deleteMSpreadRatioByIds(Long[] ids)
    {
        return mSpreadRatioMapper.deleteMSpreadRatioByIds(ids);
    }

    /**
     * 删除推广人和购买信息关联信息
     * 
     * @param id 推广人和购买信息关联主键
     * @return 结果
     */
    @Override
    public int deleteMSpreadRatioById(Long id)
    {
        return mSpreadRatioMapper.deleteMSpreadRatioById(id);
    }
}
