package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.TStatisticsReportMapper;
import com.wendao101.teacher.domain.TStatisticsReport;
import com.wendao101.teacher.service.ITStatisticsReportService;

/**
 * 首页数据报统计数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-07-24
 */
@Service
public class TStatisticsReportServiceImpl implements ITStatisticsReportService 
{
    @Autowired
    private TStatisticsReportMapper tStatisticsReportMapper;

    /**
     * 查询首页数据报统计数据
     * 
     * @param id 首页数据报统计数据主键
     * @return 首页数据报统计数据
     */
    @Override
    public TStatisticsReport selectTStatisticsReportById(Long id)
    {
        return tStatisticsReportMapper.selectTStatisticsReportById(id);
    }

    /**
     * 查询首页数据报统计数据列表
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 首页数据报统计数据
     */
    @Override
    public List<TStatisticsReport> selectTStatisticsReportList(TStatisticsReport tStatisticsReport)
    {
        return tStatisticsReportMapper.selectTStatisticsReportList(tStatisticsReport);
    }

    /**
     * 新增首页数据报统计数据
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 结果
     */
    @Override
    public int insertTStatisticsReport(TStatisticsReport tStatisticsReport)
    {
        tStatisticsReport.setCreateTime(DateUtils.getNowDate());
        return tStatisticsReportMapper.insertTStatisticsReport(tStatisticsReport);
    }

    /**
     * 修改首页数据报统计数据
     * 
     * @param tStatisticsReport 首页数据报统计数据
     * @return 结果
     */
    @Override
    public int updateTStatisticsReport(TStatisticsReport tStatisticsReport)
    {
        tStatisticsReport.setUpdateTime(DateUtils.getNowDate());
        return tStatisticsReportMapper.updateTStatisticsReport(tStatisticsReport);
    }

    /**
     * 批量删除首页数据报统计数据
     * 
     * @param ids 需要删除的首页数据报统计数据主键
     * @return 结果
     */
    @Override
    public int deleteTStatisticsReportByIds(Long[] ids)
    {
        return tStatisticsReportMapper.deleteTStatisticsReportByIds(ids);
    }

    /**
     * 删除首页数据报统计数据信息
     * 
     * @param id 首页数据报统计数据主键
     * @return 结果
     */
    @Override
    public int deleteTStatisticsReportById(Long id)
    {
        return tStatisticsReportMapper.deleteTStatisticsReportById(id);
    }
}
