package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.SphxdCourse;
import org.apache.ibatis.annotations.Param;

/**
 * 视频号小店课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface SphxdCourseMapper 
{
    /**
     * 查询视频号小店课程
     * 
     * @param id 视频号小店课程主键
     * @return 视频号小店课程
     */
    public SphxdCourse selectSphxdCourseById(Long id);

    /**
     * 查询视频号小店课程列表
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 视频号小店课程集合
     */
    public List<SphxdCourse> selectSphxdCourseList(SphxdCourse sphxdCourse);

    /**
     * 新增视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    public int insertSphxdCourse(SphxdCourse sphxdCourse);

    /**
     * 修改视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    public int updateSphxdCourse(SphxdCourse sphxdCourse);

    /**
     * 删除视频号小店课程
     * 
     * @param id 视频号小店课程主键
     * @return 结果
     */
    public int deleteSphxdCourseById(Long id);

    /**
     * 批量删除视频号小店课程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSphxdCourseByIds(Long[] ids);

    SphxdCourse selectSphxdCourseByProductId(@Param("productId") String productId);

    int deleteSphxdCourseByProductId(@Param("productId")String productId);

    List<SphxdCourse> selectByWendaoCourseId(@Param("wendaoCourseId")Long wendaoCourseId);

    List<SphxdCourse> selectByWendaoCourseIdAndWxxdAppId(@Param("wendaoCourseId")Long wendaoCourseId, @Param("wxxdAppId")String wxxdAppId);
}
