package com.wendao101.teacher.service.impl;

import java.util.List;

import com.wendao101.system.api.domain.WenDaoSysOperLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.SysOperLogMapper;
import com.wendao101.teacher.service.ISysOperLogService;

/**
 * 操作日志记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-09-06
 */
@Service
public class SysOperLogServiceImpl implements ISysOperLogService 
{
    @Autowired
    private SysOperLogMapper sysOperLogMapper;

    /**
     * 查询操作日志记录
     * 
     * @param operId 操作日志记录主键
     * @return 操作日志记录
     */
    @Override
    public WenDaoSysOperLog selectSysOperLogByOperId(Long operId)
    {
        return sysOperLogMapper.selectSysOperLogByOperId(operId);
    }

    /**
     * 查询操作日志记录列表
     * 
     * @param sysOperLog 操作日志记录
     * @return 操作日志记录
     */
    @Override
    public List<WenDaoSysOperLog> selectSysOperLogList(WenDaoSysOperLog sysOperLog)
    {
        return sysOperLogMapper.selectSysOperLogList(sysOperLog);
    }

    /**
     * 新增操作日志记录
     * 
     * @param sysOperLog 操作日志记录
     * @return 结果
     */
    @Override
    public int insertSysOperLog(WenDaoSysOperLog sysOperLog)
    {
        return sysOperLogMapper.insertSysOperLog(sysOperLog);
    }

    /**
     * 修改操作日志记录
     * 
     * @param sysOperLog 操作日志记录
     * @return 结果
     */
    @Override
    public int updateSysOperLog(WenDaoSysOperLog sysOperLog)
    {
        return sysOperLogMapper.updateSysOperLog(sysOperLog);
    }

    /**
     * 批量删除操作日志记录
     * 
     * @param operIds 需要删除的操作日志记录主键
     * @return 结果
     */
    @Override
    public int deleteSysOperLogByOperIds(Long[] operIds)
    {
        return sysOperLogMapper.deleteSysOperLogByOperIds(operIds);
    }

    /**
     * 删除操作日志记录信息
     * 
     * @param operId 操作日志记录主键
     * @return 结果
     */
    @Override
    public int deleteSysOperLogByOperId(Long operId)
    {
        return sysOperLogMapper.deleteSysOperLogByOperId(operId);
    }
}
