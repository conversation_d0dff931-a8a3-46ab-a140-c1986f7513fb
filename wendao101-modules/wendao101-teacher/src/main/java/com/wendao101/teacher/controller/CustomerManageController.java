package com.wendao101.teacher.controller;

import com.github.pagehelper.PageHelper;
import com.wendao101.common.core.utils.PageUtils;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.dto.CustomerManageDTO;
import com.wendao101.teacher.dto.UserManagementDTO;
import com.wendao101.teacher.service.ICustomerManageService;
import com.wendao101.teacher.vo.CustomerManageAndBlacklistVO;
import com.wendao101.teacher.vo.CustomerManageVO;
import com.wendao101.teacher.vo.UserManagementVO;
import com.wendao101.wendao.log.annotation.WenDaoLog;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 用户管理Controller
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@RestController
@RequestMapping("/manage")
public class CustomerManageController extends BaseController {
    @Autowired
    private ICustomerManageService customerManageService;


    /**
     * 店铺层面查询店铺与用户之间的关系及信息
     */

    @PostMapping("/userControl")
    public TableDataInfo userControl(@RequestBody UserManagementVO userManagementVO){
        String isAsc = userManagementVO.getIsAsc();
        if (StringUtils.isNotEmpty(isAsc))
        {
            // 兼容前端排序类型
            if ("ascending".equals(isAsc))
            {
                isAsc = "asc";
            }
            else if ("descending".equals(isAsc))
            {
                isAsc = "desc";
            }else{
                isAsc = "";
            }
            //this.isAsc = isAsc;
        }else{
            isAsc = "asc";
        }
        String orderBy = "";
        if(StringUtils.isNotBlank(userManagementVO.getOrderByColumn())){
            orderBy = userManagementVO.getOrderByColumn()+" "+isAsc;
        }
        PageHelper.startPage(userManagementVO.getPageNum(), userManagementVO.getPageSize(), orderBy).setReasonable(true);
        List<UserManagementDTO> list = customerManageService.selectShopInfomation(userManagementVO);
        return getDataTable(list);
    }


    /**
     * 查询用户管理列表
     */
    @PostMapping("/list")
    public TableDataInfo list(@RequestBody CustomerManageVO customerManageVO, HttpServletRequest request) {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        customerManageVO.setTeacherId(teacherId);
        startPage();
        PageUtils.startPage(customerManageVO.getPageNum(), customerManageVO.getPageSize());
        List<CustomerManageDTO> list = customerManageService.selectCustomerManageList(customerManageVO);
        return getDataTable(list);
    }


    /**
     * 加入黑名单
     */
    @WenDaoLog(title = "用户管理", businessType = "加入黑名单")
    @PostMapping("/update")
    public AjaxResult edit(@RequestBody CustomerManageAndBlacklistVO customerManageAndBlacklistVO, HttpServletRequest request) {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        customerManageAndBlacklistVO.setTeacherId(teacherId);
        return toAjax(customerManageService.updateCustomerManage(customerManageAndBlacklistVO));
    }

    /**
     * 移出黑名单
     */
    @WenDaoLog(title = "用户管理", businessType = "移出黑名单")
    @PostMapping("/update/blacklist")
    public AjaxResult editBlacklist(@RequestBody CustomerManageAndBlacklistVO customerManageAndBlacklistVO, HttpServletRequest request) {
        //获取教师id
        Long teacherId = SecurityUtils.getUserId();
        customerManageAndBlacklistVO.setTeacherId(teacherId);
        return toAjax(customerManageService.updateBlacklist(customerManageAndBlacklistVO));
    }
    /**
     * 获取黑名单信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        return success(customerManageService.selectCustomerManageByCustomerId(id, teacherId));
    }
}
