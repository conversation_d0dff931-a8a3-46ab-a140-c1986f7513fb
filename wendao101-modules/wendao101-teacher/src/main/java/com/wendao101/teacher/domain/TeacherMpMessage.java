package com.wendao101.teacher.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 给老师发送公众号信息对象 teacher_mp_message
 * 
 * <AUTHOR>
 * @date 2024-11-08
 */
public class TeacherMpMessage extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 老师ID */
    @Excel(name = "老师ID")
    private Long teacherId;

    /** 公众号app_id */
    @Excel(name = "公众号app_id")
    private String mpAppId;

    /** 对应的公众号openId */
    @Excel(name = "对应的公众号openId")
    private String openId;

    /** 对应的公众号unionId */
    @Excel(name = "对应的公众号unionId")
    private String unionId;

    /** 订单号 */
    @Excel(name = "订单号")
    private String orderId;

    /** 1支付，2退款 */
    @Excel(name = "1支付，2退款")
    private Integer orderType;

    /** 消息json数据 */
    @Excel(name = "消息json数据")
    private String sendInfo;

    /** 1成功，2失败 */
    @Excel(name = "1成功，2失败")
    private Integer messageResult;

    /** 返回json数据 */
    @Excel(name = "返回json数据")
    private String reponseInfo;

    /** 消息ID */
    @Excel(name = "消息ID")
    private String messageId;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setMpAppId(String mpAppId) 
    {
        this.mpAppId = mpAppId;
    }

    public String getMpAppId() 
    {
        return mpAppId;
    }
    public void setOpenId(String openId) 
    {
        this.openId = openId;
    }

    public String getOpenId() 
    {
        return openId;
    }
    public void setUnionId(String unionId) 
    {
        this.unionId = unionId;
    }

    public String getUnionId() 
    {
        return unionId;
    }
    public void setOrderId(String orderId) 
    {
        this.orderId = orderId;
    }

    public String getOrderId() 
    {
        return orderId;
    }
    public void setOrderType(Integer orderType) 
    {
        this.orderType = orderType;
    }

    public Integer getOrderType() 
    {
        return orderType;
    }
    public void setSendInfo(String sendInfo) 
    {
        this.sendInfo = sendInfo;
    }

    public String getSendInfo() 
    {
        return sendInfo;
    }
    public void setMessageResult(Integer messageResult) 
    {
        this.messageResult = messageResult;
    }

    public Integer getMessageResult() 
    {
        return messageResult;
    }
    public void setReponseInfo(String reponseInfo) 
    {
        this.reponseInfo = reponseInfo;
    }

    public String getReponseInfo() 
    {
        return reponseInfo;
    }
    public void setMessageId(String messageId) 
    {
        this.messageId = messageId;
    }

    public String getMessageId() 
    {
        return messageId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("teacherId", getTeacherId())
            .append("mpAppId", getMpAppId())
            .append("openId", getOpenId())
            .append("unionId", getUnionId())
            .append("orderId", getOrderId())
            .append("orderType", getOrderType())
            .append("sendInfo", getSendInfo())
            .append("messageResult", getMessageResult())
            .append("reponseInfo", getReponseInfo())
            .append("messageId", getMessageId())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
