package com.wendao101.teacher.vo;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddCourseVO {

    /** 主键 */
    private Long id;

    /** 课程id，由系统随机生成 */
    @Excel(name = "课程id，由系统随机生成")
    private Long courseIdNumber;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 课程封面图片url */
    @Excel(name = "课程封面图片url")
    private String coverPicUrl;

    /** 课程上传分享封面图 */
    @Excel(name = "课程上传分享封面图")
    private String courseUploadUrl;

    /** 轮播图片url，多个，以逗号分隔 */
    @Excel(name = "轮播图片url，多个，以逗号分隔")
    private String carouselPicUrls;

    /*课程序号*/
    private Integer serialNumber;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 课程副标题 */
    @Excel(name = "课程副标题")
    private String subTitle;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 划线价 */
    @Excel(name = "划线价")
    private BigDecimal originalPrice;

    /** 课程类型,0单课，1专栏 枚举值 */
    @Excel(name = "课程类型,0单课，1专栏 枚举值")
    private Integer courseType;

    /** 详情上传类型，0图片上传1模板上传 */
    @Excel(name = "详情上传类型，0图片上传1模板上传")
    private Integer detailType;

    /** 详情图片，针对图片上传，多个图片逗号分隔 */
    @Excel(name = "详情图片，针对图片上传，多个图片逗号分隔")
    private String detailPicUrls;

    /** 详情，针对模板上传 */
    @Excel(name = "详情，针对模板上传")
    private String detail;

    /** 跑马灯是否开启，0关闭，1开启 */
    @Excel(name = "跑马灯是否开启，0关闭，1开启")
    private Integer marqueeIsOpen;

    /** 课程推广是否开启 */
    @Excel(name = "课程推广是否开启")
    private Integer spreadIsOpen;

    /** 推广比例 */
    @Excel(name = "推广比例")
    private Long spreadRate;

    /** 有效期，永久999999,30天，60天，90天，180天，360天 */
    @Excel(name = "有效期，永久999999,30天，60天，90天，180天，360天")
    private Long expirationDay;

    /** 发布平台，快手小程序，抖音小程序，微信小程序 */
    @Excel(name = "发布平台，快手小程序，抖音小程序，微信小程序")
    private String publishPlatform;

    /** 观看平台，快手小程序，抖音小程序，微信小程序，微信公众号，电脑端 */
    @Excel(name = "观看平台，快手小程序，抖音小程序，微信小程序，微信公众号，电脑端")
    private String watchPlatform;

    /** 虚拟购买轮播是否开启，0关闭，1开启 */
    @Excel(name = "虚拟购买轮播是否开启，0关闭，1开启")
    private Integer visualBuyCarousel;

    /** 虚拟已学习人数 */
    @Excel(name = "虚拟已学习人数")
    private Long visualLearnNum;

    /** 锚点信息 */
    @Excel(name = "锚点信息")
    private String anchorInfo;

    /** 选择的资质 */
    @Excel(name = "选择的资质")
    private String qualification;

    /** 0未提审 1审核中 2审核通过 3审核驳回 4图片上传完成 5视频音频上传完成 */
    @Excel(name = "0未提审 1审核中 2审核通过 3审核驳回 4图片上传完成 5视频音频上传完成")
    private Integer dyAuditStatus;

    /** 问到的审核，针对微信快手，0审核中，1审核通过,-1审核驳回 */
    @Excel(name = "问到的审核，针对微信快手，0审核中，1审核通过,2审核驳回")
    private Integer auditStatus;

    /** 快手的审核，快手 0预审完成,1审核中，2审核通过,3审核驳回 */
    @Excel(name = "问到的审核，针对微信快手，0审核中，1审核通过,2审核驳回")
    private Integer ksAuditStatus;

    /** 微信的审核，快手 0预审完成,1审核中，2审核通过,3审核驳回 */
    @Excel(name = "问到的审核，针对微信快手，0审核中，1审核通过,2审核驳回")
    private Integer wxAuditStatus;

    /** 课程浏览量 */
    @Excel(name = "课程浏览量")
    private Long viewsNum;

    /** 评论量 */
    @Excel(name = "评论量")
    private Long commentsNum;

    /** 参与活动，多个活动逗号分隔，备用，关联活动表 */
    @Excel(name = "参与活动，多个活动逗号分隔，备用，关联活动表")
    private String participateInActivities;

    /** 抖音二维码 */
    @Excel(name = "抖音二维码")
    private String dyQrCodeUrl;

    /** 快手二维码 */
    @Excel(name = "快手二维码")
    private String ksQrCodeUrl;

    /** 微信二维码 */
    @Excel(name = "微信二维码")
    private String wxQrCodeUrl;

    /** 学员发送邮箱 */
    @Excel(name = "学员发送邮箱")
    private Integer sendEmail;

    /** 学习后解锁 */
    @Excel(name = "学习后解锁")
    private Integer studyUnlock;

    /** 添加老师企业微信 */
    @Excel(name = "添加老师企业微信")
    private Integer increaseTeacherWecom;

    /** 添加老师微信或手机号 */
    @Excel(name = "添加老师微信或手机号")
    private Integer increaseTeacherWxphone;

    /** 分类id */
    @Excel(name = "分类id")
    private Long classId;

    /*课程分类名称*/
    private String courseClassName;

    /** 销量 */
    @Excel(name = "销量")
    private Long salesVolume;

    /** 课程删除0未删除，1已删除 */
    @Excel(name = "课程删除0未删除，1已删除")
    private Integer isDelete;

    /** 课程状态（0下架状态，1上架状态） */
    @Excel(name = "课程状态", readConverterExp = "0=下架状态，1上架状态")
    private Long courseOnShelfStatus;

    /** 审核驳回原因 */
    @Excel(name = "审核驳回原因")
    private String auditRejectReason;

    /** 总课时，是几节课 */
    @Excel(name = "总课时，是几节课")
    private Long totalCourseCount;

    /** 评论数 */
    @Excel(name = "评论数")
    private Long commentCount;

    /** 浏览数 */
    @Excel(name = "浏览数")
    private Long viewCount;

    /** 章节id */
    @Excel(name = "章节id")
    private Long chapterId;

    /** 章节名称 */
    @Excel(name = "章节名称")
    private String chapterName;

    /** 目录名称 */
    @Excel(name = "目录名称")
    private String directoryName;

    /** 时长 */
    @Excel(name = "时长")
    private BigDecimal duration;

    /** 是否试看(0不能试看1可以试看) */
    @Excel(name = "是否试看(0不能试看1可以试看)")
    private Integer isTrySee;

    /** 试看时长 */
    @Excel(name = "试看时长")
    private BigDecimal trySeeDuration;
}
