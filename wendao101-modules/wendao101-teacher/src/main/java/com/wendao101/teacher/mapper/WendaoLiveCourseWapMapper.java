package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.WendaoLiveCourseWap;

/**
 * h5直播关联售卖课程Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-31
 */
public interface WendaoLiveCourseWapMapper 
{
    /**
     * 查询h5直播关联售卖课程
     * 
     * @param id h5直播关联售卖课程主键
     * @return h5直播关联售卖课程
     */
    public WendaoLiveCourseWap selectWendaoLiveCourseWapById(Long id);

    /**
     * 查询h5直播关联售卖课程列表
     * 
     * @param wendaoLiveCourseWap h5直播关联售卖课程
     * @return h5直播关联售卖课程集合
     */
    public List<WendaoLiveCourseWap> selectWendaoLiveCourseWapList(WendaoLiveCourseWap wendaoLiveCourseWap);

    /**
     * 新增h5直播关联售卖课程
     * 
     * @param wendaoLiveCourseWap h5直播关联售卖课程
     * @return 结果
     */
    public int insertWendaoLiveCourseWap(WendaoLiveCourseWap wendaoLiveCourseWap);

    /**
     * 修改h5直播关联售卖课程
     * 
     * @param wendaoLiveCourseWap h5直播关联售卖课程
     * @return 结果
     */
    public int updateWendaoLiveCourseWap(WendaoLiveCourseWap wendaoLiveCourseWap);

    /**
     * 删除h5直播关联售卖课程
     * 
     * @param id h5直播关联售卖课程主键
     * @return 结果
     */
    public int deleteWendaoLiveCourseWapById(Long id);

    /**
     * 批量删除h5直播关联售卖课程
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWendaoLiveCourseWapByIds(Long[] ids);
}
