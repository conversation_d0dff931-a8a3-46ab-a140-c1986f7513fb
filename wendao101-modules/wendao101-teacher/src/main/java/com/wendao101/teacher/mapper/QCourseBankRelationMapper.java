package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.QCourseBankRelation;
import org.apache.ibatis.annotations.Param;

/**
 * 课程和题库关系Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-23
 */
public interface QCourseBankRelationMapper 
{
    /**
     * 查询课程和题库关系
     * 
     * @param id 课程和题库关系主键
     * @return 课程和题库关系
     */
    public QCourseBankRelation selectQCourseBankRelationById(Long id);

    /**
     * 查询课程和题库关系列表
     * 
     * @param qCourseBankRelation 课程和题库关系
     * @return 课程和题库关系集合
     */
    public List<QCourseBankRelation> selectQCourseBankRelationList(QCourseBankRelation qCourseBankRelation);

    /**
     * 新增课程和题库关系
     * 
     * @param qCourseBankRelation 课程和题库关系
     * @return 结果
     */
    public int insertQCourseBankRelation(QCourseBankRelation qCourseBankRelation);

    /**
     * 修改课程和题库关系
     * 
     * @param qCourseBankRelation 课程和题库关系
     * @return 结果
     */
    public int updateQCourseBankRelation(QCourseBankRelation qCourseBankRelation);

    /**
     * 删除课程和题库关系
     * 
     * @param id 课程和题库关系主键
     * @return 结果
     */
    public int deleteQCourseBankRelationById(Long id);

    /**
     * 批量删除课程和题库关系
     * 
     * @param list 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteQCourseBankRelationByIds(@Param("list")List<Long> list, @Param("teacherId")Long teacherId);

    void deleteQCourseBankRelationByQBIdsAndCourseId(@Param("list")List<Long> list, @Param("courseId")Long courseId, @Param("teacherId")Long teacherId);

    int countQuestionByBankId(@Param("questionBankId") Long questionBankId);

}
