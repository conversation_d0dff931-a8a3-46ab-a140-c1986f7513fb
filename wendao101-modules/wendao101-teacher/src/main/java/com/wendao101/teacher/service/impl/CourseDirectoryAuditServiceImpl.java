package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.CourseDirectoryAuditMapper;
import com.wendao101.teacher.domain.CourseDirectoryAudit;
import com.wendao101.teacher.service.ICourseDirectoryAuditService;

/**
 * 目录待审核Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-20
 */
@Service
public class CourseDirectoryAuditServiceImpl implements ICourseDirectoryAuditService 
{
    @Autowired
    private CourseDirectoryAuditMapper courseDirectoryAuditMapper;

    /**
     * 查询目录待审核
     * 
     * @param id 目录待审核主键
     * @return 目录待审核
     */
    @Override
    public CourseDirectoryAudit selectCourseDirectoryAuditById(Long id)
    {
        return courseDirectoryAuditMapper.selectCourseDirectoryAuditById(id);
    }

    /**
     * 查询目录待审核列表
     * 
     * @param courseDirectoryAudit 目录待审核
     * @return 目录待审核
     */
    @Override
    public List<CourseDirectoryAudit> selectCourseDirectoryAuditList(CourseDirectoryAudit courseDirectoryAudit)
    {
        return courseDirectoryAuditMapper.selectCourseDirectoryAuditList(courseDirectoryAudit);
    }

    /**
     * 新增目录待审核
     * 
     * @param courseDirectoryAudit 目录待审核
     * @return 结果
     */
    @Override
    public int insertCourseDirectoryAudit(CourseDirectoryAudit courseDirectoryAudit)
    {
        courseDirectoryAudit.setCreateTime(DateUtils.getNowDate());
        return courseDirectoryAuditMapper.insertCourseDirectoryAudit(courseDirectoryAudit);
    }

    /**
     * 修改目录待审核
     * 
     * @param courseDirectoryAudit 目录待审核
     * @return 结果
     */
    @Override
    public int updateCourseDirectoryAudit(CourseDirectoryAudit courseDirectoryAudit)
    {
        courseDirectoryAudit.setUpdateTime(DateUtils.getNowDate());
        return courseDirectoryAuditMapper.updateCourseDirectoryAudit(courseDirectoryAudit);
    }

    /**
     * 批量删除目录待审核
     * 
     * @param ids 需要删除的目录待审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryAuditByIds(Long[] ids)
    {
        return courseDirectoryAuditMapper.deleteCourseDirectoryAuditByIds(ids);
    }

    /**
     * 删除目录待审核信息
     * 
     * @param id 目录待审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseDirectoryAuditById(Long id)
    {
        return courseDirectoryAuditMapper.deleteCourseDirectoryAuditById(id);
    }

    @Override
    public void batchInsert(List<CourseDirectoryAudit> cdList) {
        courseDirectoryAuditMapper.batchInsert(cdList);
    }

    @Override
    public void deleteCourseDirectoryDyByCourseId(Long courseId) {
        courseDirectoryAuditMapper.deleteCourseDirectoryDyByCourseId(courseId);
    }
}
