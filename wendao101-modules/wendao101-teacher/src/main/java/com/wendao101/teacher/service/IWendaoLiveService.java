package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.WendaoLive;

/**
 * 直播Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-28
 */
public interface IWendaoLiveService 
{
    /**
     * 查询直播
     * 
     * @param id 直播主键
     * @return 直播
     */
    public WendaoLive selectWendaoLiveById(Long id,Long teacherId);

    /**
     * 查询直播列表
     * 
     * @param wendaoLive 直播
     * @return 直播集合
     */
    public List<WendaoLive> selectWendaoLiveList(WendaoLive wendaoLive);

    /**
     * 新增直播
     * 
     * @param wendaoLive 直播
     * @return 结果
     */
    public int insertWendaoLive(WendaoLive wendaoLive);

    /**
     * 修改直播
     * 
     * @param wendaoLive 直播
     * @return 结果
     */
    public int updateWendaoLive(WendaoLive wendaoLive);

    /**
     * 批量删除直播
     * 
     * @param ids 需要删除的直播主键集合
     * @return 结果
     */
    public int deleteWendaoLiveByIds(Long[] ids,Long teacherId);

    /**
     * 删除直播信息
     * 
     * @param id 直播主键
     * @return 结果
     */
    public int deleteWendaoLiveById(Long id);

    WendaoLive selectWendaoLiveByIdDelete(Long id, Long teacherId);

    int changeLiveStatus(Long id, Integer liveStatus, Long teacherId);

    void changeAliyunLiveStatusStop(String id, Integer liveStatus);

    void changeAliyunLiveStatusStart(String id, Integer liveStatus);

    void changeAliyunLiveStatusPrepare(String id, Integer liveStatus);
}
