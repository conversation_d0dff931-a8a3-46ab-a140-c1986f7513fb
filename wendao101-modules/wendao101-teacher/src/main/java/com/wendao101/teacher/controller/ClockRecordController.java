package com.wendao101.teacher.controller;

import com.github.pagehelper.PageInfo;
import com.wendao101.common.core.douyin.clock.*;
import com.wendao101.common.core.douyin.clock.request.AddClockRecordRequest;
import com.wendao101.common.core.douyin.clock.request.AddClockReplyRequest;
import com.wendao101.common.core.douyin.clock.request.QueryClockRecordRequest;
import com.wendao101.common.core.douyin.clock.response.ClockInQuestDTO;
import com.wendao101.common.core.douyin.clock.response.ClockReplyResponse;
import com.wendao101.common.core.douyin.clock.response.QueryClockRecordResponse;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.ClockRecord;
import com.wendao101.teacher.domain.ClockWork;
import com.wendao101.teacher.service.IClockInQuestService;
import com.wendao101.teacher.service.IClockRecordService;
import com.wendao101.teacher.service.IClockWorkService;
import com.wendao101.teacher.vo.ClockRecordAndUserVO;
import com.wendao101.teacher.vo.ClockRecordUserVO;
import com.wendao101.teacher.vo.ClockRecordVO;
import com.wendao101.teacher.vo.ClockRecordWorkVO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Objects;

/**
 * 打卡记录Controller
 *
 * <AUTHOR>
 * @date 2023-08-12
 */
@RestController
@RequestMapping("/clock_record")
public class ClockRecordController extends BaseController {
    @Autowired
    private IClockRecordService clockRecordService;

    @Autowired
    private IClockInQuestService clockInQuestService;

    @Autowired
    private IClockWorkService clockWorkService;

    /**
     * 打卡记录表
     *
     * @param clockRecordVO
     * @param request
     * @return
     */
    @PostMapping("/selectByCourseId")
    public AjaxResult selectByCourseId(@RequestBody ClockRecordVO clockRecordVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockRecordVO.setTeacherId(teacherId);
        return success(clockRecordService.selectByCourseId(clockRecordVO));
    }

    /**
     * 隐藏作业内容
     *
     * @param id
     * @return
     */
    @GetMapping(value = "/removeById/{id}")
    public AjaxResult removeById(@PathVariable("id") Long id) {

        return success(clockRecordService.removeById(id));
    }

    /**
     * 打卡记录表用户
     *
     * @param clockRecordUserVO
     * @param request
     * @return
     */
    @PostMapping("/selectByUserId")
    public AjaxResult selectByUserId(@RequestBody ClockRecordUserVO clockRecordUserVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockRecordUserVO.setTeacherId(teacherId);
        return success(clockRecordService.selectByUserId(clockRecordUserVO));
    }

    /**
     * 查询小程序 当前用户是否有课程记录
     *
     * @param clockRecordDTO
     * @return
     */
    @PostMapping("/getClockRecord")
    public ClockRecordResult getClockRecord(@RequestBody ClockRecordDTO clockRecordDTO) {

        ClockRecordResult result = new ClockRecordResult();
        //打卡记录
        List<ClockRecordResultData> clockRecord = clockRecordService.getClockRecord(clockRecordDTO);
        //打卡任务
        ClockInQuestDTO clock = clockInQuestService.getClockInQuestByCourseId(clockRecordDTO.getCourseId());
        result.setClockRecordResultList(clockRecord);
        result.setClock(clock);
        if (clock.getQuestType() == 1) {
            //作业内容
            List<ClockWorkResponse> clockWorkList = clockWorkService.getClockWorkByClockInQuestId(clock.getId());
            if (CollectionUtils.isNotEmpty(clockWorkList)) {
                clockWorkList.stream().forEach(item -> {
                    ClockRecordWorkVO clockRecordWorkVO = new ClockRecordWorkVO();
                    clockRecordWorkVO.setClockWorkId(item.getId());
                    clockRecordWorkVO.setCourseId(clockRecordDTO.getCourseId());
                    clockRecordWorkVO.setUserId(clockRecordDTO.getUserId());
                    int status = clockRecordService.getClockRecordByClockWorkId(clockRecordWorkVO);
                    item.setStatus(status);
                });
            }
            result.setClockWorkList(clockWorkList);
        }

        return result;
    }

    /**
     * 小程序 新增打卡记录
     *
     * @param request
     * @return
     */
    @PostMapping("/addClockRecord")
    public int addClockRecord(@RequestBody AddClockRecordRequest request) {

        return clockRecordService.addClockRecord(request);
    }

    /**
     * 小程序 查看所有人打卡
     *
     * @param request
     * @return
     */
    @PostMapping("/queryClockRecord")
    public PageInfo<QueryClockRecordResponse> queryClockRecord(@RequestBody QueryClockRecordRequest request) {

        return clockRecordService.queryClockRecord(request);
    }

    /**
     * 小程序 回复评论
     *
     * @param request
     * @return
     */
    @PostMapping("/addClockReply")
    public int AddClockReply(@RequestBody AddClockReplyRequest request) {
        return clockRecordService.AddClockReply(request);
    }

    /**
     * 后台 查询这个作业是否已有学院学习
     *
     * @param clockRecordWorkVO
     * @return
     */
    @PostMapping("/getClockRecordByClockWorkId")
    public int getClockRecordByClockWorkId(@RequestBody ClockRecordWorkVO clockRecordWorkVO) {

        return clockRecordService.getClockRecordByClockWorkId(clockRecordWorkVO);
    }

    /**
     * 查询该课程有多少用户打卡
     *
     * @param clockRecordAndUserVO
     * @param request
     * @return
     */
    @PostMapping("/getByCourseId")
    public AjaxResult getByCourseId(@RequestBody ClockRecordAndUserVO clockRecordAndUserVO, HttpServletRequest request) {
        Long teacherId = SecurityUtils.getUserId();
        clockRecordAndUserVO.setTeacherId(teacherId);
        return success(clockRecordService.getByCourseId(clockRecordAndUserVO));
    }


    /**
     * 获取作业详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getClockWork")
    ClockWorkResult getClockWork(@RequestBody ClockWorkRequest request) {
        ClockWorkResult result = new ClockWorkResult();
        ClockWorkResponse clockWorkResponse = new ClockWorkResponse();
        //作业详情
        ClockWork clockWork = clockWorkService.selectClockWorkById(request.getId());
        BeanUtils.copyProperties(clockWork, clockWorkResponse);
        result.setClockWork(clockWorkResponse);
        //作业记录
        ClockRecord clockRecord = clockRecordService.selectClockRecord(request);
        if (Objects.nonNull(clockRecord)){
            QueryClockRecordResponse response = new QueryClockRecordResponse();
            BeanUtils.copyProperties(clockRecord, response);
            //回复记录
            List<ClockReplyResponse> clockReplies = clockRecordService.selectClockReply(response.getId());
            response.setReplyResponseList(clockReplies);
            result.setClockRecord(response);
        }
        return result;
    }
}
