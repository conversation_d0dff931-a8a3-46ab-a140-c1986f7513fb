package com.wendao101.teacher.mapper;


import com.wendao101.teacher.domain.ContcatForm;

import java.util.List;

/**
 * 招商信息Mapper接口
 * 
 * <AUTHOR>
 */
public interface ContcatFormMapper {
    /**
     * 查询招商信息
     * 
     * @param id 招商信息主键
     * @return 招商信息
     */
    public ContcatForm selectContcatFormById(Long id);

    /**
     * 查询招商信息列表
     * 
     * @param contcatForm 招商信息
     * @return 招商信息集合
     */
    public List<ContcatForm> selectContcatFormList(ContcatForm contcatForm);

    /**
     * 新增招商信息
     * 
     * @param contcatForm 招商信息
     * @return 结果
     */
    public int insertContcatForm(ContcatForm contcatForm);

    /**
     * 修改招商信息
     * 
     * @param contcatForm 招商信息
     * @return 结果
     */
    public int updateContcatForm(ContcatForm contcatForm);

    /**
     * 删除招商信息
     * 
     * @param id 招商信息主键
     * @return 结果
     */
    public int deleteContcatFormById(Long id);

    /**
     * 批量删除招商信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteContcatFormByIds(Long[] ids);
} 