package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.SphxdCourseMapper;
import com.wendao101.teacher.domain.SphxdCourse;
import com.wendao101.teacher.service.ISphxdCourseService;

/**
 * 视频号小店课程Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-11-13
 */
@Service
public class SphxdCourseServiceImpl implements ISphxdCourseService 
{
    @Autowired
    private SphxdCourseMapper sphxdCourseMapper;

    /**
     * 查询视频号小店课程
     * 
     * @param id 视频号小店课程主键
     * @return 视频号小店课程
     */
    @Override
    public SphxdCourse selectSphxdCourseById(Long id)
    {
        return sphxdCourseMapper.selectSphxdCourseById(id);
    }

    /**
     * 查询视频号小店课程列表
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 视频号小店课程
     */
    @Override
    public List<SphxdCourse> selectSphxdCourseList(SphxdCourse sphxdCourse)
    {
        return sphxdCourseMapper.selectSphxdCourseList(sphxdCourse);
    }

    /**
     * 新增视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    @Override
    public int insertSphxdCourse(SphxdCourse sphxdCourse)
    {
        sphxdCourse.setCreateTime(DateUtils.getNowDate());
        return sphxdCourseMapper.insertSphxdCourse(sphxdCourse);
    }

    /**
     * 修改视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    @Override
    public int updateSphxdCourse(SphxdCourse sphxdCourse)
    {
        sphxdCourse.setUpdateTime(DateUtils.getNowDate());
        return sphxdCourseMapper.updateSphxdCourse(sphxdCourse);
    }

    /**
     * 批量删除视频号小店课程
     * 
     * @param ids 需要删除的视频号小店课程主键
     * @return 结果
     */
    @Override
    public int deleteSphxdCourseByIds(Long[] ids)
    {
        return sphxdCourseMapper.deleteSphxdCourseByIds(ids);
    }

    /**
     * 删除视频号小店课程信息
     * 
     * @param id 视频号小店课程主键
     * @return 结果
     */
    @Override
    public int deleteSphxdCourseById(Long id)
    {
        return sphxdCourseMapper.deleteSphxdCourseById(id);
    }

    @Override
    public SphxdCourse selectSphxdCourseByProductId(String productId) {
        return sphxdCourseMapper.selectSphxdCourseByProductId(productId);
    }

    @Override
    public int deleteSphxdCourseByProductId(String productId) {
        return sphxdCourseMapper.deleteSphxdCourseByProductId(productId);
    }

    @Override
    public List<SphxdCourse> selectByWendaoCourseId(Long wendaoCourseId) {
        return sphxdCourseMapper.selectByWendaoCourseId(wendaoCourseId);
    }

    @Override
    public List<SphxdCourse> selectByWendaoCourseIdAndWxxdAppId(Long wendaoCourseId, String wxxdAppId) {
        return sphxdCourseMapper.selectByWendaoCourseIdAndWxxdAppId(wendaoCourseId,wxxdAppId);
    }
}
