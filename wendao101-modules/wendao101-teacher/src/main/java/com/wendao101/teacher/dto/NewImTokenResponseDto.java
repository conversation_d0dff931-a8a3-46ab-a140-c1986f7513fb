package com.wendao101.teacher.dto;

import lombok.Builder;
import lombok.Data;

/**
 * 新IM Token响应DTO
 */
@Data
@Builder
public class NewImTokenResponseDto {
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 应用签名
     */
    private String appSign;
    
    /**
     * 应用Token
     */
    private String appToken;
    
    /**
     * 认证信息
     */
    private Auth auth;
    
    @Data
    @Builder
    public static class Auth {
        /**
         * 用户ID
         */
        private String userId;
        
        /**
         * 随机数
         */
        private String nonce;
        
        /**
         * 时间戳
         */
        private Long timestamp;
        
        /**
         * 角色
         */
        private String role;
    }
} 