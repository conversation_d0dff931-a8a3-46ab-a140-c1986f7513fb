package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.WendaoLive;
import org.apache.ibatis.annotations.Param;

/**
 * 直播Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-28
 */
public interface WendaoLiveMapper 
{
    /**
     * 查询直播
     * 
     * @param id 直播主键
     * @return 直播
     */
    public WendaoLive selectWendaoLiveById(@Param("id") Long id, @Param("teacherId")Long teacherId);

    /**
     * 查询直播列表
     * 
     * @param wendaoLive 直播
     * @return 直播集合
     */
    public List<WendaoLive> selectWendaoLiveList(WendaoLive wendaoLive);

    /**
     * 新增直播
     * 
     * @param wendaoLive 直播
     * @return 结果
     */
    public int insertWendaoLive(WendaoLive wendaoLive);

    /**
     * 修改直播
     * 
     * @param wendaoLive 直播
     * @return 结果
     */
    public int updateWendaoLive(WendaoLive wendaoLive);

    /**
     * 删除直播
     * 
     * @param id 直播主键
     * @return 结果
     */
    public int deleteWendaoLiveById(Long id);

    /**
     * 批量删除直播
     * 
     * @return 结果
     */
    public int deleteWendaoLiveByIds(@Param("idList") List<Long> idList,@Param("teacherId")Long teacherId);

    WendaoLive selectWendaoLiveByIdDelete(@Param("id")Long id, @Param("teacherId")Long teacherId);

    int changeLiveStatus(@Param("id")Long id, @Param("liveStatus")Integer liveStatus, @Param("teacherId")Long teacherId);

    void changeAliyunLiveStatusStop(@Param("id")String id, @Param("liveStatus")Integer liveStatus);

    void changeAliyunLiveStatusStart(@Param("id")String id, @Param("liveStatus")Integer liveStatus);

    void changeAliyunLiveStatusPrepare(@Param("id")String id, @Param("liveStatus")Integer liveStatus);
}
