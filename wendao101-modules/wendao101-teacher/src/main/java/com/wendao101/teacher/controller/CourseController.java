package com.wendao101.teacher.controller;

import com.github.pagehelper.PageHelper;
import com.wendao101.common.core.text.Convert;
import com.wendao101.common.core.utils.CourseUtils;
import com.wendao101.common.core.utils.ServletUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.web.page.TableDataInfo;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.*;
import com.wendao101.teacher.dto.*;
import com.wendao101.teacher.mapper.CourseMapper;
import com.wendao101.teacher.service.*;
import com.wendao101.teacher.vo.StoreLevelCourseVO;
import com.wendao101.wendao.log.annotation.WenDaoLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 课程Controller
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@RestController
@RequestMapping("/course_controller")
public class CourseController extends BaseController {

    @Autowired
    private IDoudianCourseService doudianCourseService;

    @Autowired
    private CourseMapper courseMapper;

    @Autowired
    private ICourseService courseService;

    @Autowired
    private ICourseDyService courseDyService;

    @Autowired
    private ICourseAuditService courseAuditService;

    @Autowired
    private ICourseDirectoryService courseDirectoryService;

    @Autowired
    private ICourseDirectoryDyService courseDirectoryDyService;

    @Autowired
    private IChapterService chapterService;

    @Autowired
    private IChapterDyService chapterDyService;

    @Autowired
    private ICourseCategoryService courseCategoryService;

    @Autowired
    private IStudyDataService studyDataService;

    @Autowired
    private ICourseDyAuditService courseDyAuditService;


    @Autowired
    private IWendaoCertInfoService wendaoCertInfoService;

    @Autowired
    private ICourseTwContentService courseTwContentService;

    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "导入课程")
    @PostMapping("/import_course")
    public AjaxResult importCourse(@RequestBody ImportCourseDTO importCourseDTO) {
        Long[] ids = importCourseDTO.getIds();
        Long courseId = importCourseDTO.getId();
        Long teacherId = SecurityUtils.getUserId();
        Course course = courseService.selectCourseByTeacherIdAndId(courseId, teacherId);
        if (course == null) {
            return error("参数错误，未找到课程");
        }
        List<Course> courseList = new ArrayList<>();
        if (ids != null) {
            for (Long id : ids) {
                Course course1 = fetchChapterAndDirectory(id);
                courseList.add(course1);
            }
        }
        List<Chapter> allChapter = new ArrayList<>();
        List<CourseDirectory> allCourseDirectory = new ArrayList<>();

        for (Course courseLoop : courseList) {
            List<Chapter> chapterList = courseLoop.getChapterList();
            if (CollectionUtils.isNotEmpty(chapterList)) {
                for (Chapter chapter : chapterList) {
                    chapter.setId(null);
                    if (CollectionUtils.isNotEmpty(chapter.getCourseDirectoryList())) {
                        List<CourseDirectory> collect = chapter.getCourseDirectoryList().stream().map(item -> {
                            item.setId(null);
                            item.setFrontUUID(UUID.randomUUID().toString());
                            return item;
                        }).collect(Collectors.toList());
                        allCourseDirectory.addAll(collect);
                        chapter.setCourseDirectoryList(collect);
                    }
                }
            }
            allChapter.addAll(chapterList);

            //所有目录
            List<CourseDirectory> allCourseDirectoryList = courseLoop.getAllCourseDirectoryList();
            if (CollectionUtils.isNotEmpty(allCourseDirectoryList)) {
                List<CourseDirectory> collect = allCourseDirectoryList.stream().map(item -> {
                    item.setCourseId(null);
                    item.setChapterId(null);
                    item.setFrontUUID(UUID.randomUUID().toString());
                    return item;
                }).collect(Collectors.toList());
                allCourseDirectory.addAll(collect);
            }

        }
        course.setChapterList(allChapter);
        course.setAllCourseDirectoryList(allCourseDirectory);
        return AjaxResult.success(course);
    }

    private Course fetchChapterAndDirectory(Long id) {
        //不返回过多信息
        Course courseResult = new Course();
        courseResult.setId(id);
        //设置章节
        //查询章节
        Chapter chapterQuery = new Chapter();
        chapterQuery.setIsDelete(0);
        chapterQuery.setCourseId(courseResult.getId());
        List<Chapter> chapters = chapterService.selectChapterList(chapterQuery);
        if (CollectionUtils.isNotEmpty(chapters)) {
            for (Chapter chapter : chapters) {
                CourseDirectory courseDirectoryQuery = new CourseDirectory();
                courseDirectoryQuery.setIsDelete(0);
                courseDirectoryQuery.setChapterId(chapter.getId());
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryList(courseDirectoryQuery);
                chapter.setCourseDirectoryList(courseDirectories);
            }
        }
        courseResult.setChapterList(chapters);
        //设置所有目录
        CourseDirectory courseDirectoryQuery = new CourseDirectory();
        courseDirectoryQuery.setIsDelete(0);
        courseDirectoryQuery.setCourseId(courseResult.getId());
        List<CourseDirectory> list = courseDirectoryService.selectCourseDirectoryWhereChapterIdIsNull(courseDirectoryQuery);
        courseResult.setAllCourseDirectoryList(list);
        return courseResult;

    }

    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "复制课程")
    @PostMapping("/copy_course")
    public AjaxResult copyCourse(@RequestBody CopyCourseDTO copyCourseDTO) {
        Long teacherId = SecurityUtils.getUserId();

        Course course = courseService.selectCourseByTeacherIdAndId(copyCourseDTO.getId(), teacherId);
        long oldId = course.getId();
        int draftType =course.getCourseDraftType();
        Long oldCourseId = course.getId();
        course.setTeacherId(teacherId);
        course.setId(null);
        course.setKsCourseId(null);
        course.setKsCourseVersion(0);

        course.setCourseDraftType(1);
        String idNumber = null;
        Course result = null;
        do {
            idNumber = CourseUtils.generateIdNumber();
            result = courseService.selectCourseByCourseIdNumber(Long.valueOf(idNumber));

        } while (result != null);
        course.setCourseIdNumber(Long.valueOf(idNumber));
        //查询最大serial_number
        Long maxSerialNumber = courseService.selectMaxSerialNumberByTeacherId(teacherId);
        if (maxSerialNumber == null) {
            maxSerialNumber = 1L;
        } else {
            maxSerialNumber = maxSerialNumber + 1;
        }
        course.setSerialNumber(maxSerialNumber.intValue());
        course.setTeacherId(teacherId);

        //状态不应该复制
        course.setDyAuditStatus(0);
        course.setAuditStatus(1);
        course.setViewCount(0L);
        course.setCommentsNum(0L);
        course.setSalesVolume(0L);
        course.setCourseOnShelfStatus(0);
        course.setKsAuditStatus(2);
        course.setWxAuditStatus(2);
        course.setProductId(null);
        course.setAuditRejectReason(null);

        courseService.insertCourse(course);

        //2图文课
        if(course.getCourseType()==2){
            CourseTwContent courseTwContent = courseTwContentService.selectCourseTwContentByCourseId(oldId);
            //保存图文
            if(courseTwContent!=null){
                courseTwContent.setId(null);
                courseTwContent.setCourseId(course.getId());
                //保存草稿
                if (draftType == 1) {
                    String textAndImageDraft = courseTwContent.getTextAndImageDraft();
                    courseTwContent.setTextAndImageDraft(textAndImageDraft);
                }else{
                    String textAndImageInAudit = courseTwContent.getTextAndImageInAudit();
                    courseTwContent.setTextAndImageDraft(textAndImageInAudit);
                }
                courseTwContentService.insertCourseTwContent(courseTwContent);
            }
        }

        Long newCourseId = course.getId();
        copyCourseDTO.setNewId(newCourseId);
        copyCourseDTO.setId(null);
        copyCourseDTO.setCourseType(course.getCourseType());
        //学习资料
        StudyData studyData = new StudyData();
        studyData.setCourseId(oldCourseId);
        List<StudyData> studyDataList = studyDataService.selectStudyDataList(studyData);
        if (CollectionUtils.isNotEmpty(studyDataList)) {
            for (StudyData studyData1 : studyDataList) {
                studyData1.setId(null);
                studyData1.setTeacherId(teacherId);
                studyData1.setCourseId(newCourseId);
                studyDataService.insertStudyData(studyData1);
            }
        }
        //查询章节
        Chapter chapterQuery = new Chapter();
        chapterQuery.setIsDelete(0);
        chapterQuery.setCourseId(oldCourseId);
        List<Chapter> chapters = chapterService.selectChapterList(chapterQuery);
        if (CollectionUtils.isNotEmpty(chapters)) {
            for (Chapter chapter : chapters) {
                Long oldChapterId = chapter.getId();
                chapter.setTeacherId(teacherId);
                chapter.setCourseId(newCourseId);
                chapter.setId(null);
                chapterService.insertChapter(chapter);
                Long newChapterId = chapter.getId();
                CourseDirectory courseDirectoryQuery = new CourseDirectory();
                courseDirectoryQuery.setIsDelete(0);
                courseDirectoryQuery.setChapterId(oldChapterId);
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryList(courseDirectoryQuery);
                if (CollectionUtils.isNotEmpty(courseDirectories)) {
                    for (CourseDirectory courseDirectoryCopy : courseDirectories) {
                        courseDirectoryCopy.setCourseId(newCourseId);
                        courseDirectoryCopy.setTeacherId(teacherId);
                        courseDirectoryCopy.setChapterId(newChapterId);
                        courseDirectoryCopy.setId(null);
                        courseDirectoryService.insertCourseDirectory(courseDirectoryCopy);
                    }
                }
            }
        }
        //设置所有目录
        CourseDirectory courseDirectoryQuery = new CourseDirectory();
        courseDirectoryQuery.setIsDelete(0);
        courseDirectoryQuery.setCourseId(oldCourseId);
        List<CourseDirectory> list = courseDirectoryService.selectCourseDirectoryWhereChapterIdIsNull(courseDirectoryQuery);
        if (CollectionUtils.isNotEmpty(list)) {
            for (CourseDirectory cd : list) {
                cd.setTeacherId(teacherId);
                cd.setCourseId(newCourseId);
                cd.setChapterId(null);
                cd.setId(null);
                courseDirectoryService.insertCourseDirectory(cd);
            }
        }
        return success(copyCourseDTO);
    }

    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "修改课程第三步")
    @PostMapping("/add_or_modify_third")
    public AjaxResult addOrModifyThird(@RequestBody AddAnchorInfoDTO addAnchorInfoDTO) {
        Long teacherId = SecurityUtils.getUserId();
        //查出原始记录
        Course course = courseService.selectCourseById(addAnchorInfoDTO.getId());

        course.setCourseUploadTitle(addAnchorInfoDTO.getCourseUploadTitle());
        course.setCourseUploadUrl(addAnchorInfoDTO.getCourseUploadUrl());
        course.setId(addAnchorInfoDTO.getId());
        course.setCourseDraftType(addAnchorInfoDTO.getActionType());
        //
        course.setCertificationId(addAnchorInfoDTO.getCertificationId());
        course.setFirstClassId(addAnchorInfoDTO.getFirstClassId());
        course.setFirstClassPid(addAnchorInfoDTO.getFirstClassPid());
        course.setFirstClassTitle(addAnchorInfoDTO.getFirstClassTitle());
        course.setFirstClassDouyinClassId(addAnchorInfoDTO.getFirstClassDouyinClassId());

        course.setSecondClassId(addAnchorInfoDTO.getSecondClassId());
        course.setSecondClassPid(addAnchorInfoDTO.getSecondClassPid());
        course.setSecondClassTitle(addAnchorInfoDTO.getSecondClassTitle());
        course.setSecondClassDouyinClassId(addAnchorInfoDTO.getSecondClassDouyinClassId());
        //如果没有提交抖音类目就从资质里查询
        if (course.getFirstClassDouyinClassId() == null && course.getSecondClassDouyinClassId() == null) {
            //认证通过并授权的
            List<WendaoCertInfo> certs = wendaoCertInfoService.getWendaoCertAnfoByTeacherId(teacherId);
            if (CollectionUtils.isNotEmpty(certs)) {
                WendaoCertInfo c = certs.get(0);//只取一个
                course.setCertificationId(c.getId());
                course.setFirstClassId(c.getFirstClassId());
                course.setFirstClassPid(c.getFirstClassPid());
                course.setFirstClassTitle(c.getFirstClassTitle());
                course.setFirstClassDouyinClassId(c.getFirstClassDouyinClassId());

                course.setSecondClassId(c.getSecondClassId());
                course.setSecondClassPid(c.getSecondClassPid());
                course.setSecondClassTitle(c.getSecondClassTitle());
                course.setSecondClassDouyinClassId(c.getSecondClassDouyinClassId());
            }
        }

        //计算课程中视频和音频的总时长
        CourseDirectory cdquery = new CourseDirectory();
        cdquery.setCourseId(course.getId());
        cdquery.setIsDelete(0);
        int totalTime = 0;
        List<CourseDirectory> allDirectory = courseDirectoryService.selectCourseDirectoryList(cdquery);
        for (CourseDirectory item : allDirectory) {
            totalTime = totalTime + (item.getDuration() == null ? 0 : item.getDuration().intValue());
        }
        course.setCourseDuration(totalTime);
        //结束
        if (addAnchorInfoDTO.getActionType() == 0) {
            course.setDyAuditStatus(0);
            course.setKsAuditStatus(2);
            course.setWxAuditStatus(2);
            //提交审核
            //同步保存到course_dy表
            //暂时这么实现,其实还要比较老数据看更改了什么东西
            //修改course_dy表,不能是主键自增,以保持多个表id一致
            CourseDy courseDy = courseDyService.selectCourseDyById(course.getId());
            if (courseDy == null) {
                courseDy = new CourseDy();
                BeanUtils.copyProperties(course, courseDy);
                courseDyService.insertCourseDy(courseDy);
                //章节
                Chapter chapter = new Chapter();
                chapter.setCourseId(course.getId());
                List<Chapter> chapters = chapterService.selectChapterList(chapter);
                for (Chapter chapterItem : chapters) {
                    ChapterDy chapterDy = new ChapterDy();
                    BeanUtils.copyProperties(chapterItem, chapterDy);
                    chapterDyService.insertChapterDy(chapterDy);
                }
                //目录
                CourseDirectory cd = new CourseDirectory();
                cd.setCourseId(course.getId());
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryList(cd);
                for (CourseDirectory cdItem : courseDirectories) {
                    CourseDirectoryDy courseDirectoryDy = new CourseDirectoryDy();
                    BeanUtils.copyProperties(cdItem, courseDirectoryDy);
                    courseDirectoryDyService.insertCourseDirectoryDy(courseDirectoryDy);
                }
            } else {
                BeanUtils.copyProperties(course, courseDy);
                courseDyService.updateCourseDy(courseDy);
                //章节
                Chapter chapter = new Chapter();
                chapter.setCourseId(course.getId());
                List<Chapter> chapters = chapterService.selectChapterList(chapter);
                List<Long> collect = chapters.stream().map(Chapter::getId).collect(Collectors.toList());
                ChapterDy chapterDy = new ChapterDy();
                chapterDy.setCourseId(course.getId());
                List<ChapterDy> chapterDyList = chapterDyService.selectChapterDyList(chapterDy);
                List<Long> collect1 = chapterDyList.stream().map(ChapterDy::getId).collect(Collectors.toList());

                List<Long> subtract = ListUtils.subtract(collect1, collect);
                if (CollectionUtils.isNotEmpty(subtract)) {
                    Long[] array2 = subtract.toArray(new Long[0]);
                    chapterDyService.deleteChapterDyByIds(array2);
                }
                //有则更新,没有插入
                for (Chapter c : chapters) {
                    ChapterDy chapterDy1 = chapterDyService.selectChapterDyById(c.getId());
                    ChapterDy chapterDy2 = new ChapterDy();
                    BeanUtils.copyProperties(c, chapterDy2);
                    if (chapterDy1 == null) {
                        chapterDyService.insertChapterDy(chapterDy2);
                    } else {
                        chapterDyService.updateChapterDy(chapterDy2);
                    }
                }
                //目录
                CourseDirectory cd = new CourseDirectory();
                cd.setCourseId(course.getId());
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryList(cd);
                List<Long> collect2 = courseDirectories.stream().map(CourseDirectory::getId).collect(Collectors.toList());
                CourseDirectoryDy cddy = new CourseDirectoryDy();
                cddy.setCourseId(course.getId());
                List<CourseDirectoryDy> courseDirectoryDyList = courseDirectoryDyService.selectCourseDirectoryDyList(cddy);
                List<Long> collect3 = courseDirectoryDyList.stream().map(CourseDirectoryDy::getId).collect(Collectors.toList());

                List<Long> subtract1 = ListUtils.subtract(collect3, collect2);
                if (CollectionUtils.isNotEmpty(subtract1)) {
                    Long[] array2 = subtract1.toArray(new Long[0]);
                    courseDirectoryDyService.deleteCourseDirectoryDyByIds(array2);
                }
                //有则更新,没有插入
                for (CourseDirectory cdItem : courseDirectories) {
                    CourseDirectoryDy courseDirectoryDy = courseDirectoryDyService.selectCourseDirectoryDyById(cdItem.getId());
                    CourseDirectoryDy courseDirectoryDy2 = new CourseDirectoryDy();
                    BeanUtils.copyProperties(cdItem, courseDirectoryDy2);
                    if (courseDirectoryDy == null) {
                        courseDirectoryDyService.insertCourseDirectoryDy(courseDirectoryDy2);
                    } else {
                        courseDirectoryDyService.updateCourseDirectoryDy(courseDirectoryDy2);
                        if (courseDirectoryDy2.getChapterId() == null) {
                            courseDirectoryDyService.updateCourseDirectoryChapterIdNull(courseDirectoryDy2.getId());
                        }
                    }
                }
                //更新
            }
        }
        int i = courseService.updateCourse(course);
        return toAjax(i);
    }

    @GetMapping(value = "/get_third_info/{id}")
    public AjaxResult getThirdInfo(@PathVariable("id") Long id) {
        Long teacherId = SecurityUtils.getUserId();
        Course course = courseService.selectCourseByTeacherIdAndId(id, teacherId);
        AddAnchorInfoDTO addAnchorInfoDTO = new AddAnchorInfoDTO();
        if (course != null) {
            BeanUtils.copyProperties(course, addAnchorInfoDTO);
            addAnchorInfoDTO.setActionType(null);
        }
        return success(addAnchorInfoDTO);
    }

    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "修改课程第二步")
    @PostMapping("/add_or_modify_second")
    public AjaxResult addOrModifySecond(@RequestBody Course course) {
        Long teacherId = SecurityUtils.getUserId();
        //前端组装好参数,新的
        List<CourseDirectory> allDirListInCourse = course.getAllCourseDirectoryList();
        //新的
        List<Chapter> chapterList = course.getChapterList();
        //获取删除的Chapter Id
        List<Long> deleteChapterIdList = course.getDeleteChapterIdList();
        if (CollectionUtils.isNotEmpty(deleteChapterIdList)) {
            Long[] dcArr = new Long[deleteChapterIdList.size()];
            deleteChapterIdList.toArray(dcArr);
            chapterService.deleteChapterByIdsAndTeacherId(dcArr, teacherId);
        }
        //获取删除的CourseDirectory id
        List<Long> deleteCourseDirectoryIdList = course.getDeleteCourseDirectoryIdList();
        if (CollectionUtils.isNotEmpty(deleteCourseDirectoryIdList)) {
            Long[] dcdArr = new Long[deleteCourseDirectoryIdList.size()];
            deleteCourseDirectoryIdList.toArray(dcdArr);
            courseDirectoryService.deleteCourseDirectoryByIdsAndTeacherId(dcdArr, teacherId);
        }
        Map<String, CourseDirectory> uuidMap = new HashMap<>();
        Map<Long, CourseDirectory> idMap = new HashMap<>();

        List<CourseDirectory> allDirectoriesInCourse = ListUtils.emptyIfNull(allDirListInCourse);
        //更新数据
        for (int i = 0; i < allDirectoriesInCourse.size(); i++) {
            int seq = i + 1;
            CourseDirectory courseDirectory = allDirectoriesInCourse.get(i);
            courseDirectory.setSerialAllNumber(seq);
            if (StringUtils.isNotBlank(courseDirectory.getFrontUUID())) {
                uuidMap.put(courseDirectory.getFrontUUID(), courseDirectory);
            }
            if (courseDirectory.getId() != null) {
                idMap.put(courseDirectory.getId(), courseDirectory);
            }
        }
        List<Chapter> chapters = ListUtils.emptyIfNull(chapterList);
        //更新章节
        for (int i = 0; i < chapters.size(); i++) {
            int seq = i + 1;
            Chapter chapter = chapters.get(i);
            chapter.setSerialNumber(seq);
            //新增或更新章节
            chapter.setCourseId(course.getId());
            chapter.setTeacherId(teacherId);
            if (chapter.getId() != null) {
                chapterService.updateChapter(chapter);
            } else {
                chapterService.insertChapter(chapter);
            }
            Long chapterPK = chapter.getId();
            //获取章节里面的目录
            List<CourseDirectory> dirListInChapter = chapter.getCourseDirectoryList();
            List<CourseDirectory> directoriesInChapter = ListUtils.emptyIfNull(dirListInChapter);
            courseDirectoryService.updateChapterIdNull(chapter.getId());
            for (int j = 0; j < directoriesInChapter.size(); j++) {
                int innerSeq = j + 1;
                CourseDirectory courseDirectory = directoriesInChapter.get(j);
                courseDirectory.setSerialNumber(innerSeq);
                courseDirectory.setChapterId(chapterPK);
                courseDirectory.setCourseId(course.getId());
                courseDirectory.setTeacherId(teacherId);
                if (courseDirectory.getId() != null) {
                    if (idMap.get(courseDirectory.getId()) != null) {
                        courseDirectory.setSerialAllNumber(idMap.get(courseDirectory.getId()).getSerialAllNumber());
                        idMap.remove(courseDirectory.getId());
                    }
                    courseDirectoryService.updateCourseDirectory(courseDirectory);
                } else {
                    if (uuidMap.get(courseDirectory.getFrontUUID()) != null) {
                        courseDirectory.setSerialAllNumber(uuidMap.get(courseDirectory.getFrontUUID()).getSerialAllNumber());
                        uuidMap.remove(courseDirectory.getFrontUUID());
                    }
                    courseDirectoryService.insertCourseDirectory(courseDirectory);
                }

            }
        }
        //不在目录中的
        for (Map.Entry<String, CourseDirectory> entry : uuidMap.entrySet()) {
            updateOrInsertDirectory(course.getId(), teacherId, entry.getValue());
        }
        for (Map.Entry<Long, CourseDirectory> entry : idMap.entrySet()) {
            updateOrInsertDirectory(course.getId(), teacherId, entry.getValue());
        }
        return AjaxResult.success();
    }

    private void updateOrInsertDirectory(Long courseId, Long teacherId, CourseDirectory courseDirectory) {
        courseDirectory.setChapterId(null);
        courseDirectory.setCourseId(courseId);
        courseDirectory.setTeacherId(teacherId);
        if (courseDirectory.getId() != null) {
            courseDirectoryService.updateCourseDirectorySetChapterIdNull(courseDirectory);
        } else {
            courseDirectoryService.insertCourseDirectory(courseDirectory);
        }
    }

    @GetMapping("/change_course_seq")
    public AjaxResult changeCourseSeq(ChangeCourseSeqDTO changeCourseSeqDTO) {
        Long teacherId = SecurityUtils.getUserId();
        if (changeCourseSeqDTO.getSerialNumber() == null) {
            return AjaxResult.error("未填写序号");
        }
        if (changeCourseSeqDTO.getId() == null) {
            return AjaxResult.error("未填写id");
        }
        int newSeq = changeCourseSeqDTO.getSerialNumber().intValue();
        if (newSeq <= 0) {
            newSeq = 1;
        }
        //按排序取出课程,不包含当前课程
        List<Course> courseList = courseService.selectCourseSeqByTeacherIdButNotCurrent(teacherId, changeCourseSeqDTO.getId());
        //序号不能大于总课程数量
        int courseListLength = courseList.size() + 1;
        if (newSeq > courseListLength) {
            newSeq = courseListLength;
        }
        //重新排序
        //取出当前课程
        Course course = courseService.selectCourseById(changeCourseSeqDTO.getId());
        if (course != null && !course.getTeacherId().equals(teacherId)) {
            return AjaxResult.error("当前课程不是你的课程");
        }
        //插入到List指定位置
        courseList.add(newSeq - 1, course);

        int courseSeqNumber = 1;
        for (Course c : courseList) {
            courseService.updateCourseSeq(c.getId(), courseSeqNumber);
            courseSeqNumber++;
        }
        return AjaxResult.success();
    }

    public void changeCourseSeqAudit(ChangeCourseSeqDTO changeCourseSeqDTO,Long teacherId,Long newSeq) {
        //查出所有课程
        CourseAudit course = new CourseAudit();
        course.setIsDelete(0);
        course.setTeacherId(teacherId);
        List<CourseAudit> list = courseAuditService.selectCourseAuditList(course);
        CourseAudit courseAudit = courseAuditService.selectCourseAuditById(changeCourseSeqDTO.getId());
        if(courseAudit==null){
            return;
        }
        Map<Long, Integer> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            collect = list.stream().collect(Collectors.toMap(CourseAudit::getId, CourseAudit::getSerialNumber));
        }
        Integer oldSeq = collect.get(changeCourseSeqDTO.getId());
        if (oldSeq != null) {
            if (newSeq.longValue() == oldSeq.longValue()) {
                return;
            }
            //先比较,新值比老的值大
            if (newSeq.longValue() > oldSeq.longValue()) {
                //修改范围
                int row = courseAuditService.updateMinusOne(oldSeq.longValue(), newSeq, teacherId);
                //String sql1 = "update Course set serial_number=serial_number-1 where teacher_id="+teacherId+" and serial_number>"+oldSeq+" and serial_number<="+newSeq;
            } else {
                //新值比老值小
                int row = courseAuditService.updatePlusOne(oldSeq.longValue(), newSeq, teacherId);
                //String sql = "update Course set serial_number=serial_number+1 where teacher_id="+teacherId+" and serial_number>="+newSeq+" and serial_number<"+oldSeq;
            }
            int row = courseAuditService.updateOldSeq2NewSeq(oldSeq.longValue(), newSeq, teacherId, changeCourseSeqDTO.getId());
            //String sql2 = "update Course set serial_number="+newSeq+" where serial_number="+oldSeq;
            //执行sql语句
        }
    }



    public void changeCourseSeqDy(ChangeCourseSeqDTO changeCourseSeqDTO,Long teacherId,Long newSeq) {
        //查出所有课程
        CourseDy course = new CourseDy();
        course.setIsDelete(0);
        course.setTeacherId(teacherId);
        List<CourseDy> list = courseDyService.selectCourseDyList(course);
        CourseDy courseDy = courseDyService.selectCourseDyById(changeCourseSeqDTO.getId());
        if(courseDy==null){
            return;
        }
        Map<Long, Integer> collect = new HashMap<>();
        if (CollectionUtils.isNotEmpty(list)) {
            collect = list.stream().collect(Collectors.toMap(CourseDy::getId, CourseDy::getSerialNumber));
        }
        Integer oldSeq = collect.get(changeCourseSeqDTO.getId());
        if (oldSeq != null) {
            if (newSeq.longValue() == oldSeq.longValue()) {
                return;
            }
            //先比较,新值比老的值大
            if (newSeq.longValue() > oldSeq.longValue()) {
                //修改范围
                int row = courseDyService.updateMinusOne(oldSeq.longValue(), newSeq, teacherId);
                //String sql1 = "update Course set serial_number=serial_number-1 where teacher_id="+teacherId+" and serial_number>"+oldSeq+" and serial_number<="+newSeq;
            } else {
                //新值比老值小
                int row = courseDyService.updatePlusOne(oldSeq.longValue(), newSeq, teacherId);
                //String sql = "update Course set serial_number=serial_number+1 where teacher_id="+teacherId+" and serial_number>="+newSeq+" and serial_number<"+oldSeq;
            }
            int row = courseDyService.updateOldSeq2NewSeq(oldSeq.longValue(), newSeq, teacherId, changeCourseSeqDTO.getId());
            //String sql2 = "update Course set serial_number="+newSeq+" where serial_number="+oldSeq;
            //执行sql语句
        }
    }

    @GetMapping("/query_category_list")
    public AjaxResult queryCategoryList() {
        List<CourseCategory> list = courseCategoryService.selectCourseCategoryList(null);
        return AjaxResult.success(list);
    }
    @GetMapping("/query_course_paged")
    public TableDataInfo queryCoursePaged(CourseAndWendaoDTO courseDTO) {
        Long teacherId = SecurityUtils.getUserId();

        if (courseDTO.getType() != null && courseDTO.getType() == 1) {
            if (courseDTO.getTeacherId() == null) {
                teacherId = null;
            } else {
                teacherId = courseDTO.getTeacherId();
            }
        } else {
            if (teacherId == null) {
                return TableDataInfo.error("没有权限");
            }
            if (teacherId == 0) {
                return TableDataInfo.error("系统错误");
            }
        }

        List<CourseCategory> categoryList = courseCategoryService.selectCourseCategoryList(null);
        Map<Long, String> categoryMap = categoryList.stream().collect(Collectors.toMap(CourseCategory::getId, CourseCategory::getCategoryName));
        courseDTO.setIsDelete(0);
        courseDTO.setTeacherId(teacherId);
        startPage();
        boolean isOther = false;
        int pageSize = Convert.toInt(ServletUtils.getParameter("pageSize"));
        if(pageSize==10000){
            isOther = true;
        }
        CourseAndWendaoDTO courseAndWendaoDTO = new CourseAndWendaoDTO();
        BeanUtils.copyProperties(courseDTO, courseAndWendaoDTO);
        List<CourseAndWendaoDTO> list = courseService.getCourseList(courseAndWendaoDTO);
        if(!isOther){
            for (int i = 0; i < list.size(); i++) {
                CourseAndWendaoDTO c = list.get(i);
                c.setClassName(categoryMap.get(c.getClassId()));
                //审核数据
                CourseDyAudit courseDyAudit = courseDyAuditService.selectCourseDyAuditByCourseId(c.getId());
                //评论数
                c.setCommentsNum(courseService.getReviewCourseNum(c.getId()));
                //销量
                c.setSalesVolume(courseService.getSalesCourseNum(c.getId(), c.getTeacherId()));
                //课程浏览数
                Long mamPictureView = courseService.getMamPictureView(c.getId(), c.getTeacherId());
                //防止定时任务跑完 还有课程审核通过 没有浏览数 只有评论数跟购买数  所以相加
                c.setViewsNum((mamPictureView == null ? 0 : mamPictureView) + (c.getCommentsNum() == null ? 0 : c.getCommentsNum()) + (c.getSalesVolume() == null ? 0 : c.getSalesVolume()));

                c.setCourseDyAudit(courseDyAudit);
            }
        }
        for (CourseAndWendaoDTO item : list) {
            Integer dyAuditStatus = item.getDyAuditStatus();
            Long id = item.getId();
            String joinActivity = courseService.getJoinActivity(id, dyAuditStatus);
            item.setParticipateInActivities(joinActivity);
        }
        return getDataTable(list);
    }

    @GetMapping("/query_course_audit_paged")
    public TableDataInfo queryCourseAuditPaged(CourseAndWendaoDTO courseDTO) {
        Long teacherId = SecurityUtils.getUserId();

        if (courseDTO.getType() != null && courseDTO.getType() == 1) {
            if (courseDTO.getTeacherId() == null) {
                teacherId = null;
            } else {
                teacherId = courseDTO.getTeacherId();
            }
        } else {
            if (teacherId == null) {
                return TableDataInfo.error("没有权限");
            }
            if (teacherId == 0) {
                return TableDataInfo.error("系统错误");
            }
        }

        List<CourseCategory> categoryList = courseCategoryService.selectCourseCategoryList(null);
        Map<Long, String> categoryMap = categoryList.stream().collect(Collectors.toMap(CourseCategory::getId, CourseCategory::getCategoryName));
        courseDTO.setIsDelete(0);
        courseDTO.setTeacherId(teacherId);
        startPage();
        CourseAndWendaoDTO courseAndWendaoDTO = new CourseAndWendaoDTO();

        BeanUtils.copyProperties(courseDTO, courseAndWendaoDTO);
        List<CourseAndWendaoDTO> list = courseService.getCourseList(courseAndWendaoDTO);
        List<Long> courseIdList = list.stream().map(CourseAndWendaoDTO::getId).collect(Collectors.toList());
        /**
         * 获取抖店列表
         */
        Map<Long, DouDianExistDTO> map = new HashMap<>();
        Map<Long, List<DoudianProductPublishStatus>> statusMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(courseIdList) && teacherId != null) {
            List<Long> courseIdListD = new ArrayList<>();
            for (Long courseIdD : courseIdList) {
                courseIdListD.add(courseIdD);
                courseIdListD.add(Long.parseLong("90000" + courseIdD));
                courseIdListD.add(Long.parseLong("80000" + courseIdD));
            }
            List<DoudianCourse> doudianCourses = doudianCourseService.selectDoudianCourseListExist(teacherId, courseIdListD);
            for (DoudianCourse doudianCourse : doudianCourses) {
                String outProductId = String.valueOf(doudianCourse.getOutProductId());
                if (outProductId.startsWith("90000")) {
                    outProductId = outProductId.replace("90000", "");
                }
                if (outProductId.startsWith("80000")) {
                    outProductId = outProductId.replace("80000", "");
                }
                DouDianExistDTO douDianExistDTO = map.get(Long.valueOf(outProductId));
                if (douDianExistDTO == null) {
                    douDianExistDTO = new DouDianExistDTO();
                    map.put(Long.valueOf(outProductId), douDianExistDTO);
                }
                douDianExistDTO.setRealGoods(douDianExistDTO.isRealGoods() || doudianCourse.getProductType() == 0);
                douDianExistDTO.setVirtualGoods(douDianExistDTO.isVirtualGoods() || doudianCourse.getProductType() == 3);
                douDianExistDTO.setCourseId(Long.valueOf(outProductId));

                List<DoudianProductPublishStatus> doudianProductPublishStatusList = statusMap.get(Long.valueOf(outProductId));
                if (CollectionUtils.isEmpty(doudianProductPublishStatusList)) {
                    doudianProductPublishStatusList = new ArrayList<>();
                    statusMap.put(Long.valueOf(outProductId), doudianProductPublishStatusList);
                }
                DoudianProductPublishStatus doudianProductPublishStatus = doudianProductPublishStatusList.stream().filter(d -> d.getShopId().equals(doudianCourse.getShopId())).findFirst().orElse(null);
                if (doudianProductPublishStatus == null) {
                    doudianProductPublishStatus = new DoudianProductPublishStatus();
                    doudianProductPublishStatusList.add(doudianProductPublishStatus);
                }
                doudianProductPublishStatus.setShopId(doudianCourse.getShopId());
                doudianProductPublishStatus.setDoudianRealGoods(doudianProductPublishStatus.isDoudianRealGoods() || doudianCourse.getProductType() == 0);
                doudianProductPublishStatus.setDoudianVirtualGoods(doudianProductPublishStatus.isDoudianVirtualGoods() || doudianCourse.getProductType() == 3);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(item -> {
                item.setClassName(categoryMap.get(item.getClassId()));
                DouDianExistDTO douDianExistDTO = map.get(item.getId());
                if (douDianExistDTO != null) {
                    item.setDoudianVirtualGoods(douDianExistDTO.isVirtualGoods());
                    item.setDoudianRealGoods(douDianExistDTO.isRealGoods());
                }
                item.setDoudianProductPublishStatusList(statusMap.get(item.getId()) == null ? new ArrayList<>() : statusMap.get(item.getId()));
            });
        }
        return getDataTable(list);
    }


    /**
     * 获取课程详细信息
     * 第一步信息
     */
    @GetMapping(value = "/get_first_info/{id}")
    public AjaxResult getFirstInfo(@PathVariable("id") Long id) {
        Long teacherId = SecurityUtils.getUserId();
        Course course = courseService.selectCourseByTeacherIdAndId(id, teacherId);
        List<CourseCategory> categoryList = courseCategoryService.selectCourseCategoryList(null);
        Map<Long, String> categoryMap = categoryList.stream().collect(Collectors.toMap(CourseCategory::getId, CourseCategory::getCategoryName));
        course.setClassName(categoryMap.get(course.getClassId()));
        //学习资料
        StudyData studyData = new StudyData();
        studyData.setCourseId(course.getId());
        List<StudyData> studyDataList = studyDataService.selectStudyDataList(studyData);
        course.setStudyDataList(studyDataList);
        return success(course);
    }

    /**
     * 获取课程详细信息
     * 第二步信息
     */
    @GetMapping(value = "/get_second_info/{id}")
    public AjaxResult getSecondInfo(@PathVariable("id") Long id) {
        Long teacherId = SecurityUtils.getUserId();
        Course course = courseService.selectCourseByTeacherIdAndId(id, teacherId);
        if (course == null) {
            return error("参数错误，未找到课程");
        }
        //不返回过多信息
        Course courseResult = new Course();
        courseResult.setId(course.getId());
        courseResult.setCourseIdNumber(course.getCourseIdNumber());
        //设置章节
        //查询章节
        Chapter chapterQuery = new Chapter();
        chapterQuery.setIsDelete(0);
        chapterQuery.setCourseId(courseResult.getId());
        List<Chapter> chapters = chapterService.selectChapterList(chapterQuery);
        if (CollectionUtils.isNotEmpty(chapters)) {
            for (Chapter chapter : chapters) {
                CourseDirectory courseDirectoryQuery = new CourseDirectory();
                courseDirectoryQuery.setIsDelete(0);
                courseDirectoryQuery.setChapterId(chapter.getId());
                List<CourseDirectory> courseDirectories = courseDirectoryService.selectCourseDirectoryList(courseDirectoryQuery);
                chapter.setCourseDirectoryList(courseDirectories);
            }
        }
        courseResult.setChapterList(chapters);
        //设置所有目录
        CourseDirectory courseDirectoryQuery = new CourseDirectory();
        courseDirectoryQuery.setIsDelete(0);
        courseDirectoryQuery.setCourseId(courseResult.getId());
        List<CourseDirectory> list = courseDirectoryService.selectCourseDirectoryListOrderByAllSeq(courseDirectoryQuery);
        courseResult.setAllCourseDirectoryList(list);
        return success(courseResult);
    }

    /**
     * 新增课程第一步
     */
    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "新增课程第一步")
    @PostMapping
    public AjaxResult add(@RequestBody Course course) {
        Long teacherId = SecurityUtils.getUserId();
        course.setCourseDraftType(1);
        String idNumber = null;
        Course result = null;
        do {
            idNumber = CourseUtils.generateIdNumber();
            result = courseService.selectCourseByCourseIdNumber(Long.valueOf(idNumber));

        } while (result != null);
        course.setCourseIdNumber(Long.valueOf(idNumber));
        //查询最大serial_number
        Long maxSerialNumber = courseService.selectMaxSerialNumberByTeacherId(teacherId);
        if (maxSerialNumber == null) {
            maxSerialNumber = 1L;
        } else {
            maxSerialNumber = maxSerialNumber + 1;
        }
        course.setSerialNumber(maxSerialNumber.intValue());
        course.setTeacherId(teacherId);
        //获取课程详情,如果是模板则解码base64
        if (course.getDetailType() == 1 && StringUtils.isNotBlank(course.getDetail())) {
            byte[] decoded = Base64.getDecoder().decode(course.getDetail());
            String decodedStr = new String(decoded, StandardCharsets.UTF_8);
            course.setDetail(decodedStr);
        }

        //返回课程主键id
        courseService.insertCourse(course);
        Long courseId = course.getId();
        //TODO:需要事物
        //学习资料
        List<StudyData> studyDataList1 = course.getStudyDataList();
        if (CollectionUtils.isNotEmpty(studyDataList1)) {
            for (StudyData studyData1 : studyDataList1) {
                studyData1.setCourseId(courseId);
                studyData1.setTeacherId(teacherId);
                studyDataService.insertStudyData(studyData1);
            }
        }
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("id", courseId);
        return AjaxResult.success(resultMap);
    }

    /**
     * 修改课程第一步
     */
    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "修改课程第一步")
    @PutMapping
    public AjaxResult edit(@RequestBody Course course) {
        Long teacherId = SecurityUtils.getUserId();
        //学习资料
        StudyData studyData = new StudyData();
        studyData.setCourseId(course.getId());
        List<StudyData> studyDataList = studyDataService.selectStudyDataList(studyData);
        if (CollectionUtils.isNotEmpty(studyDataList)) {
            List<Long> idList = studyDataList.stream().map(StudyData::getId).collect(Collectors.toList());
            Long[] longArr = new Long[idList.size()];
            studyDataService.deleteStudyDataByIds(idList.toArray(longArr));
        }
        List<StudyData> studyDataList1 = course.getStudyDataList();
        if (CollectionUtils.isNotEmpty(studyDataList1)) {
            for (StudyData studyData1 : studyDataList1) {
                studyData1.setCourseId(course.getId());
                studyData1.setTeacherId(teacherId);
                studyDataService.insertStudyData(studyData1);
            }
        }
        Map<String, Long> resultMap = new HashMap<>();
        resultMap.put("id", course.getId());
        //获取课程详情,如果是模板则解码base64
        if (course.getDetailType() == 1 && StringUtils.isNotBlank(course.getDetail())) {
            byte[] decoded = Base64.getDecoder().decode(course.getDetail());
            String decodedStr = new String(decoded, StandardCharsets.UTF_8);
            course.setDetail(decodedStr);
        }
        courseService.updateCourse(course);
        return AjaxResult.success(resultMap);
    }

    /**
     * 删除课程
     */
    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "删除课程")
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        Long teacherId = SecurityUtils.getUserId();
        if (ids == null || ids.length == 0) {
            return error("参数错误");
        }
        courseService.deleteCourseByIds(ids, teacherId);
        return toAjax(1);
    }

    /**
     * 课程上下架
     */
    @WenDaoLog(title = "内容管理", subTitle = "课程管理", businessType = "课程上下架")
    @PostMapping("/updateCourse")
    public AjaxResult updateCourse(@RequestBody Course course) {
        Long id = course.getId();
        CourseAudit courseAudit = courseAuditService.selectCourseAuditById(id);
        if (courseAudit == null) {
            return AjaxResult.error("没有上架的课程");
        }
        /**
         * 课程上架
         */
        Integer courseOnShelfStatus = course.getCourseOnShelfStatus();
        if (courseOnShelfStatus != null && courseOnShelfStatus == 1) {
            if (courseAudit != null) {
                CourseAudit courseAuditNew = new CourseAudit();
                courseAuditNew.setId(id);
                courseAuditNew.setCourseOnShelfStatus(courseOnShelfStatus);
                CourseDy courseDyNew = new CourseDy();
                courseDyNew.setId(id);
                courseDyNew.setCourseOnShelfStatus(courseOnShelfStatus);
                courseAuditService.updateCourseAudit(courseAuditNew);
                courseDyService.updateCourseDy(courseDyNew);
                courseService.updateCourse(course);
                return AjaxResult.success("上架成功");
            }
        }
        if (courseOnShelfStatus != null && courseOnShelfStatus == 0) {
            //下架
            if (courseAudit != null) {
                CourseAudit courseAuditNew = new CourseAudit();
                courseAuditNew.setId(id);
                courseAuditNew.setCourseOnShelfStatus(courseOnShelfStatus);
                CourseDy courseDyNew = new CourseDy();
                courseDyNew.setId(id);
                courseDyNew.setCourseOnShelfStatus(courseOnShelfStatus);
                courseAuditService.updateCourseAudit(courseAuditNew);
                courseDyService.updateCourseDy(courseDyNew);
                courseService.updateCourse(course);
                return AjaxResult.success("下架成功");
            }
        }
        return AjaxResult.error("下架状态不正确");
    }


    /**
     * 短信营销 h5 页面返回参数
     */
    @GetMapping("/getByCidAndTid")
    public AjaxResult getByCidAndTid(@RequestParam("cid") Long cid) {
        //tid无用
        SendSmsTemplateContentDTO result = courseService.getByCidAndTid(cid);
        return success(result);
    }

    @GetMapping("/selectCourseById")
    public Course selectCourseById(@RequestParam("id") Long id) {
        return courseService.selectCourseById(id);
    }

    /**
     * 课程管理中最外层的页面
     *
     * @param storeLevelCourseVO
     * @return
     */
    @PostMapping("/storeLevelCourse")
    public TableDataInfoExtend storeLevelCourse(@RequestBody StoreLevelCourseVO storeLevelCourseVO) {
        String isAsc = storeLevelCourseVO.getIsAsc();
        if (com.wendao101.common.core.utils.StringUtils.isNotEmpty(isAsc)) {
            if ("ascending".equals(isAsc)) {
                isAsc = "asc";
            } else if ("descending".equals(isAsc)) {
                isAsc = "desc";
            } else {
                isAsc = "";
            }
        } else {
            isAsc = "asc";
        }
        String orderBy = "";
        if (com.wendao101.common.core.utils.StringUtils.isNotBlank(storeLevelCourseVO.getOrderByColumn())) {
            orderBy = storeLevelCourseVO.getOrderByColumn() + " " + isAsc;
        }
        if(StringUtils.isBlank(orderBy)){
            orderBy = "courseCount desc";
        }

        boolean selectTeacher = storeLevelCourseVO.getBeginTime() != null || storeLevelCourseVO.getEndTime() != null;
        if (!com.aliyun.core.utils.StringUtils.isBlank(storeLevelCourseVO.getPlatform())) {
            selectTeacher = true;
        }
        if (!com.aliyun.core.utils.StringUtils.isBlank(storeLevelCourseVO.getShopInfomation())) {
            selectTeacher = true;
        }
        if(selectTeacher){
            orderBy = "";
        }
        PageHelper.startPage(storeLevelCourseVO.getPageNum(), storeLevelCourseVO.getPageSize(), orderBy).setReasonable(true);
        List<StoreLevelCourseDTO> list = courseService.storeLevelCourse(storeLevelCourseVO);
        int totalAuditRejectCount = courseMapper.countTotalReject();
        TableDataInfo dataTable = getDataTable(list);

        TableDataInfoExtend extend = new TableDataInfoExtend();
        BeanUtils.copyProperties(dataTable,extend);
        extend.setCountTotal(totalAuditRejectCount);
        return extend;
    }
}
