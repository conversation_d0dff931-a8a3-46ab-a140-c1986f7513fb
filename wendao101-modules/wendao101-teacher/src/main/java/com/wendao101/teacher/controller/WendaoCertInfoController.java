package com.wendao101.teacher.controller;

import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.douyin.api.feign.DouyinQrcodeService;
import com.wendao101.teacher.domain.PublicWendaoCertInfo;
import com.wendao101.teacher.domain.WendaoCertInfo;
import com.wendao101.teacher.service.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 资质中心信息Controller
 * 
 * <AUTHOR>
 * @date 2023-08-02
 */
@RestController
@RequestMapping("/wendao_cert_info")
public class WendaoCertInfoController extends BaseController
{
    @Autowired
    private IWendaoCertInfoService wendaoCertInfoService;
//    @Autowired
//    private DouyinCertAuditService douyinCertAuditService;
    @Autowired
    AccessTokenService accessTokenService;

    @Autowired
    DouyinQrcodeService douyinQrcodeService;
    //解绑
//    @Autowired
//    private BindMountService bindMountService;

//    @Autowired
//    private DouyinConfig douyinConfig;
//
//    @Autowired
//    private IWendaoCertInfoBindService wendaoCertInfoBindService;

//    @Autowired
//    private IUserService userService;

    @Autowired
    private IPublicWendaoCertInfoService publicWendaoCertInfoService;


    /**
     * 解绑抖音号绑定
     * @return
     */
//    @GetMapping("/unbindCert")
//    public AjaxResult unbindCert(@RequestParam("bindId") Long bindId,@RequestParam("appNameType") Integer appNameType) {
//        WendaoCertInfoBind wendaoCertInfoBind = wendaoCertInfoBindService.selectWendaoCertInfoBindById(bindId);
//        if(wendaoCertInfoBind==null){
//            return AjaxResult.error();
//        }
//        EnableMountscopeDTO enableMountscopeDTO = new EnableMountscopeDTO();
//        enableMountscopeDTO.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//        enableMountscopeDTO.setIndustry_role(wendaoCertInfoBind.getEntityType().intValue());
//        enableMountscopeDTO.setMerchant_entity_id(wendaoCertInfoBind.getMerchantEntityId());
//        enableMountscopeDTO.setPartner_entity_id(douyinConfig.getEntityId());
//        enableMountscopeDTO.setAccess_token(accessTokenService.getDyAccessTokenByAppNameType(appNameType));
//        enableMountscopeDTO.setIndustry_code(10000);
//        if(StringUtils.isNotBlank(wendaoCertInfoBind.getOpenid())){
//            CUser c_user = new CUser();
//            c_user.setOpenid(wendaoCertInfoBind.getOpenid());
//            c_user.setClient_key(accessTokenService.getAppIdByAppNameType(appNameType));
//            enableMountscopeDTO.setC_user(c_user);
//        }else{
//            enableMountscopeDTO.setAweme_id(wendaoCertInfoBind.getDouyinhao());
//        }
//        EnableMountscopeResult enableMountscopeResult = bindMountService.unbindAccount(enableMountscopeDTO);
//        if(enableMountscopeResult.getErr().getErrCode()==0){
//            wendaoCertInfoBind.setLiveStreamStatus(2);
//            wendaoCertInfoBind.setShortVideoStatus(2);
//            wendaoCertInfoBind.setImBandStatus(2);
//            wendaoCertInfoBindService.updateWendaoCertInfoBind(wendaoCertInfoBind);
//            return AjaxResult.success("解绑成功");
//        }else{
//            if("C端账户和实体不存在绑定关系".equals(enableMountscopeResult.getErr().getErrMsg())){
//                //也算成功
//                wendaoCertInfoBind.setLiveStreamStatus(2);
//                wendaoCertInfoBind.setShortVideoStatus(2);
//                wendaoCertInfoBind.setImBandStatus(2);
//                wendaoCertInfoBindService.updateWendaoCertInfoBind(wendaoCertInfoBind);
//                return AjaxResult.success("解绑成功");
//            }
//            return AjaxResult.error(enableMountscopeResult.getErr().getErrMsg());
//        }
//    }

//    /**
//     * 获取挂载能力二维码并授权
//     * @param certId
//     * @return
//     */
//    @GetMapping("/createQualificationQrCode")
//    public AjaxResult createQualificationQrCode(@RequestParam("certId") Long certId) {
//        QrCodeDTO qrCodeDTO = new QrCodeDTO();
//        qrCodeDTO.setAppname("all");
//        qrCodeDTO.set_circle_code(false);
//        qrCodeDTO.setSet_icon(true);
//        qrCodeDTO.setPath("pages_mine/mount/mount%3fcertId%3d"+certId);
//        qrCodeDTO.setWidth(430);
//        qrCodeDTO.setAccess_token(accessTokenService.getAccessToken());
//        byte[] qrcode = douyinQrcodeService.qrcode(qrCodeDTO);
//        // 使用 Base64 编码将 byte[] 数组转换为字符串
//        String base64String = Base64.getEncoder().encodeToString(qrcode);
//        return AjaxResult.success("操作成功", base64String);
//    }

    /**
     * 查询审核通过的并且授权角色了的
     * @return
     */
    @PostMapping("/passedQualificationList")
    public AjaxResult passedQualificationList() {
        Long teacherId = SecurityUtils.getUserId();
        WendaoCertInfo wendaoCertInfo = new WendaoCertInfo();
        wendaoCertInfo.setTeacherId(teacherId);
        wendaoCertInfo.setAuditStatus(1);
        wendaoCertInfo.setAuthRoleStatus(1);
        List<WendaoCertInfo> resultList = wendaoCertInfoService.selectWendaoCertInfoList(wendaoCertInfo);
        if(CollectionUtils.isNotEmpty(resultList)){
            for (WendaoCertInfo certInfo:resultList){
                String businessLicenseCompanyName = certInfo.getBusinessLicenseCompanyName();
                PublicWendaoCertInfo publicWendaoCertInfo = publicWendaoCertInfoService.selectDistinctNameNickName(businessLicenseCompanyName);
                if (publicWendaoCertInfo!=null) {
                    certInfo.setBusinessLicenseCompanyName(publicWendaoCertInfo.getNickName());
                }
                if(certInfo.getEntityType()!=null&&certInfo.getEntityType().intValue()==3){
                    certInfo.setEntityType(2L);
                }
            }
        }
        return AjaxResult.success(resultList);
    }

    /**
     * 新增资质中心信息
     */
//    @PostMapping
//    public AjaxResult add(@RequestBody WendaoCertInfo wendaoCertInfo)
//    {
//        Long teacherId = SecurityUtils.getUserId();
//        wendaoCertInfo.setTeacherId(teacherId);
//        int row = wendaoCertInfoService.insertWendaoCertInfo(wendaoCertInfo);
//        return toAjax(row);
//    }

    /**
     * 获取资质中心信息详细信息
     */
//    @GetMapping(value = "/info")
//    public AjaxResult getInfo(Long id )
//    {
//        Long teacherId = SecurityUtils.getUserId();
//        WendaoCertInfo result = wendaoCertInfoService.selectWendaoCertInfoById(id);
//        if(result==null||!result.getTeacherId().equals(teacherId)){
//            return error("没有记录");
//        }
//        return success(result);
//    }

    /**
     * 修改资质中心信息
     */
//    @PutMapping
//    public AjaxResult edit(@RequestBody WendaoCertInfo wendaoCertInfo)
//    {
//        Long teacherId = SecurityUtils.getUserId();
//        if(wendaoCertInfo==null||wendaoCertInfo.getId()==null){
//            return error("请求数据错误,数据为空或无id");
//        }
//        WendaoCertInfo wendaoCertInfo1 = wendaoCertInfoService.selectWendaoCertInfoById(wendaoCertInfo.getId());
//        if(wendaoCertInfo1==null){
//            return error("没有找到记录");
//        }
//        if(wendaoCertInfo1.getTeacherId()==null){
//            return error("该记录老师id错误");
//        }
//        if(!wendaoCertInfo1.getTeacherId().equals(teacherId)){
//            return error("该记录不归属于当前用户");
//        }
//        wendaoCertInfo.setTeacherId(teacherId);
//        wendaoCertInfo.setEntityId(wendaoCertInfo1.getEntityId());
//        wendaoCertInfo.setAuditStatus(0);
//
//
//        wendaoCertInfo.setDouyinAuditMessage(null);
//        wendaoCertInfo.setErrCode(null);
//        wendaoCertInfo.setErrMsg(null);
//        wendaoCertInfo.setBasicAuthTaskid(null);
//        wendaoCertInfo.setClassAuthTaskid(null);
//
//        wendaoCertInfoService.updateWendaoKtSonmeInfoNull(wendaoCertInfo.getId(),teacherId);
//        return toAjax(wendaoCertInfoService.updateWendaoCertInfo(wendaoCertInfo));
//    }


//    /**
//     * 查询资质中心信息列表
//     */
//    @GetMapping("/list")
//    public TableDataInfo list()
//    {
//        WendaoCertInfo certInfo = new WendaoCertInfo();
//        List<TVideo> list = new ArrayList<>();
//        Long teacherId = SecurityUtils.getUserId();
//        certInfo.setTeacherId(teacherId);
//        startPage();
//        List<WendaoCertInfo> resultList = wendaoCertInfoService.selectWendaoCertInfoList(certInfo);
//        if(CollectionUtils.isEmpty(resultList)){
//            return getDataTable(list);
//        }
//        List<WendaoCertInfoResultDTO> result = new ArrayList<>();
//        int i =0;
//        for(WendaoCertInfo item :resultList){
//            i=i+1;
//            WendaoCertInfoResultDTO dto = new WendaoCertInfoResultDTO();
//            dto.setId(item.getId());
//            dto.setFirstClassName(item.getFirstClassTitle());
//            dto.setSecondClassName(item.getSecondClassTitle());
//            EntityType entityType = EntityType.parse(item.getEntityType());
//            dto.setEntityType(entityType.getEntityName());
//            CertAuditStatus auditStatus = CertAuditStatus.parse(item.getAuditStatus());
//            if(auditStatus==CertAuditStatus.auditSuccess){
//                if(item.getAuthRoleStatus()==2||item.getAuthRoleStatus()==0){
//                    //授权小程序审核中
//                    auditStatus = CertAuditStatus.auditing;
//                }
//            }
//            dto.setAuditStatus(auditStatus.getStatusName());
//            dto.setSeq(i);
//            if(entityType == EntityType.person){
//                dto.setEntityName(item.getTeacherRealName());
//            }else if(entityType==EntityType.service){
//                PublicWendaoCertInfo publicWendaoCertInfo = publicWendaoCertInfoService.selectDistinctNameNickName(item.getBusinessLicenseCompanyName());
//                if (publicWendaoCertInfo!=null) {
//                    dto.setEntityName(publicWendaoCertInfo.getNickName());
//                } else {
//                    dto.setEntityName(item.getBusinessLicenseCompanyName());
//                }
//            }else {
//                dto.setEntityName(item.getBusinessLicenseCompanyName());
//            }
//            result.add(dto);
//        }
//        return getDataTable(result);
//    }


    /**
     * 查询资质中心信息列表
     */
//    @GetMapping("/queryUserList")
//    public AjaxResult queryUserList(@RequestParam("phoneNumber") String phoneNumber,@RequestParam("appNameType") Integer appNameType)
//    {
//        User user = new User();
//        user.setPlatform(1);
//        user.setAppNameType(appNameType);
//        if(phoneNumber.length()!=11){
//            user.setId(Long.parseLong(phoneNumber));
//            List<User> users = userService.selectUserList(user);
//            if(CollectionUtils.isEmpty(users)){
//                return AjaxResult.error("用户ID不存在请联系对方前往小程序查看WID");
//            }
//            return AjaxResult.success(users);
//        }else {
//            user.setTelNumber(phoneNumber);
//            List<User> users = userService.selectUserList(user);
//            if(CollectionUtils.isEmpty(users)){
//                return AjaxResult.error("手机号不存在请联系对方前往小程序进行授权");
//            }
//            return AjaxResult.success(users);
//        }
//
//    }

    /**
     * 发起抖音绑定,包含短视频和直播一起绑定
     * @param certId
     * @param openid
     * @return
     */
//    @GetMapping("/apply/short")
//    public AjaxResult applyShort(@RequestParam("certId")Long certId,@RequestParam("openid")String openid,@RequestParam("appNameType") Integer appNameType) {
//        if(com.wendao101.common.core.utils.StringUtils.isBlank(openid)){
//            return AjaxResult.error();
//        }
//        WendaoCertInfoBind wendaoCertInfoBind = wendaoCertInfoBindService.selectWendaoCertInfoByCertIdAndOpenId(certId,openid);
//        if(wendaoCertInfoBind!=null&&(wendaoCertInfoBind.getShortVideoStatus()==1||wendaoCertInfoBind.getShortVideoStatus()==0)){
//            return AjaxResult.error("该手机号对应的用户已绑定或正在绑定当前资质,请确认后再试!");
//        }
//        //全局范围查找
//        int row = wendaoCertInfoBindService.countBindingByOpenId(openid);
//        if(row>0){
//            return AjaxResult.error("该手机号对应的用户已绑定或正在绑定另外的资质,请解绑或拒绝绑定后再试!");
//        }
//        WendaoCertInfo wendaoCertInfo = wendaoCertInfoService.selectWendaoCertInfoById(certId);
//        if(wendaoCertInfo==null||wendaoCertInfo.getAuditStatus()!=1){
//            return AjaxResult.error("没有审核通过的资质");
//        }
//        EnableMountscopeDTO enableMountscopeDTO = new EnableMountscopeDTO();
//        enableMountscopeDTO.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//        if(wendaoCertInfo.getEntityType().intValue()==3){
//            enableMountscopeDTO.setIndustry_role(2);
//        }else{
//            enableMountscopeDTO.setIndustry_role(wendaoCertInfo.getEntityType().intValue());
//        }
//        enableMountscopeDTO.setMerchant_entity_id(wendaoCertInfo.getEntityId());
//        enableMountscopeDTO.setPartner_entity_id(douyinConfig.getEntityId());
//        enableMountscopeDTO.setAccess_token(accessTokenService.getDyAccessTokenByAppNameType(appNameType));
//        ArrayList<Integer> integers = new ArrayList<>();
//        integers.add(1);
//        integers.add(2);
//        enableMountscopeDTO.setMount_scope_list(integers);
//        enableMountscopeDTO.setIndustry_code(10000);
//        CUser c_user = new CUser();
//        c_user.setOpenid(openid);
//        c_user.setClient_key(accessTokenService.getAppIdByAppNameType(appNameType));
//        enableMountscopeDTO.setC_user(c_user);
//        EnableMountscopeResult enableMountscopeResult = bindMountService.enableMountscope(enableMountscopeDTO);
//        if(enableMountscopeResult.getErr().getErrCode()==0){
//            if(wendaoCertInfoBind==null){
//                wendaoCertInfoBind = new WendaoCertInfoBind();
//                wendaoCertInfoBind.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//                wendaoCertInfoBind.setEntityType((long) enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBind.setPartnerEntityId(douyinConfig.getEntityId());
//                wendaoCertInfoBind.setMerchantEntityId(wendaoCertInfo.getEntityId());
//                wendaoCertInfoBind.setTeacherId(wendaoCertInfo.getTeacherId());
//                wendaoCertInfoBind.setShortVideoStatus(0);
//                wendaoCertInfoBind.setLiveStreamStatus(0);
//                wendaoCertInfoBind.setCertInfoId(certId);
//                wendaoCertInfoBind.setOpenid(openid);
//                wendaoCertInfoBind.setAppNameType(appNameType);
//                User wendaoUser = userService.selectUserByOpenId(openid);
//                if(wendaoUser!=null){
//                    wendaoCertInfoBind.setWendaoUserId(wendaoUser.getId());
//                    wendaoCertInfoBind.setNickName(wendaoUser.getNickName());
//                }
//                wendaoCertInfoBindService.insertWendaoCertInfoBind(wendaoCertInfoBind);
//            }else{
//                wendaoCertInfoBind.setShortVideoStatus(0);
//                wendaoCertInfoBind.setLiveStreamStatus(0);
//                wendaoCertInfoBind.setEntityType((long)enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBindService.updateWendaoCertInfoBind(wendaoCertInfoBind);
//            }
//            return AjaxResult.success();
//        }else{
//            return AjaxResult.error("发起绑定失败,请稍后再试,原因:"+enableMountscopeResult.getErr().getErrMsg());
//        }
//    }

//    @GetMapping("/apply/shortWithDouyinhao")
//    public AjaxResult shortWithDouyinhao(@RequestParam("certId")Long certId,@RequestParam("douyinhao")String douyinhao,@RequestParam("appNameType") Integer appNameType) {
//        if(com.wendao101.common.core.utils.StringUtils.isBlank(douyinhao)){
//            return AjaxResult.error("抖音号必传!");
//        }
//        WendaoCertInfoBind wendaoCertInfoBind = wendaoCertInfoBindService.selectWendaoCertInfoByCertIdAndDouyinhao(certId,douyinhao);
//        if(wendaoCertInfoBind!=null&&(wendaoCertInfoBind.getShortVideoStatus()==1||wendaoCertInfoBind.getShortVideoStatus()==0)){
//            return AjaxResult.error("此抖音号已绑定或正在绑定当前资质!请检查您的绑定列表.如确实未绑定,请更正已绑定列表中的相同抖音号为正确的抖音号再发起!");
//        }
//        //全局范围查找
//        int row = wendaoCertInfoBindService.countBindingByDouyinhao(douyinhao);
//        if(row>0){
//            return AjaxResult.error("该抖音号对应的用户已绑定或正在绑定另外的资质,请解绑或拒绝绑定后再试!");
//        }
//        WendaoCertInfo wendaoCertInfo = wendaoCertInfoService.selectWendaoCertInfoById(certId);
//        if(wendaoCertInfo==null||wendaoCertInfo.getAuditStatus()!=1){
//            return AjaxResult.error("没有审核通过的资质");
//        }
//        EnableMountscopeDTO enableMountscopeDTO = new EnableMountscopeDTO();
//        enableMountscopeDTO.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//        if(wendaoCertInfo.getEntityType().intValue()==3){
//            enableMountscopeDTO.setIndustry_role(2);
//        }else{
//            enableMountscopeDTO.setIndustry_role(wendaoCertInfo.getEntityType().intValue());
//        }
//        enableMountscopeDTO.setMerchant_entity_id(wendaoCertInfo.getEntityId());
//        enableMountscopeDTO.setPartner_entity_id(douyinConfig.getEntityId());
//        enableMountscopeDTO.setAccess_token(accessTokenService.getDyAccessTokenByAppNameType(appNameType));
//        ArrayList<Integer> integers = new ArrayList<>();
//        integers.add(1);
//        integers.add(2);
//        enableMountscopeDTO.setMount_scope_list(integers);
//        enableMountscopeDTO.setIndustry_code(10000);
//        enableMountscopeDTO.setAweme_id(douyinhao);
//        EnableMountscopeResult enableMountscopeResult = bindMountService.enableMountscope(enableMountscopeDTO);
//        if(enableMountscopeResult.getErr().getErrCode()==0){
//            if(wendaoCertInfoBind==null){
//                wendaoCertInfoBind = new WendaoCertInfoBind();
//                wendaoCertInfoBind.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//                wendaoCertInfoBind.setEntityType((long) enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBind.setPartnerEntityId(douyinConfig.getEntityId());
//                wendaoCertInfoBind.setMerchantEntityId(wendaoCertInfo.getEntityId());
//                wendaoCertInfoBind.setTeacherId(wendaoCertInfo.getTeacherId());
//                wendaoCertInfoBind.setShortVideoStatus(0);
//                wendaoCertInfoBind.setLiveStreamStatus(0);
//                wendaoCertInfoBind.setCertInfoId(certId);
//                wendaoCertInfoBind.setDouyinhao(douyinhao);
//                wendaoCertInfoBind.setAppNameType(appNameType);
//                wendaoCertInfoBindService.insertWendaoCertInfoBind(wendaoCertInfoBind);
//            }else{
//                wendaoCertInfoBind.setShortVideoStatus(0);
//                wendaoCertInfoBind.setLiveStreamStatus(0);
//                wendaoCertInfoBind.setEntityType((long)enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBindService.updateWendaoCertInfoBind(wendaoCertInfoBind);
//            }
//            return AjaxResult.success();
//        }else{
//            return AjaxResult.error("发起绑定失败,请稍后再试,原因:"+enableMountscopeResult.getErr().getErrMsg());
//        }
//    }

    /**
     * Im绑定
     * @param certId 认证记录
     * @param douyinhao 抖音号
     * @param appNameType app类型,1问到好课,2问到课堂
     * @return 成功失败消息
     */
//    @GetMapping("/apply/imBinding")
//    public AjaxResult applyImBinding(@RequestParam("certId")Long certId,@RequestParam("douyinhao")String douyinhao,@RequestParam("appNameType") Integer appNameType) {
//        if(com.wendao101.common.core.utils.StringUtils.isBlank(douyinhao)){
//            return AjaxResult.error("抖音号不能为空!!");
//        }
//        WendaoCertInfo wendaoCertInfo = wendaoCertInfoService.selectWendaoCertInfoById(certId);
//        if(wendaoCertInfo==null||wendaoCertInfo.getAuditStatus()!=1){
//            return AjaxResult.error("没有审核通过的资质");
//        }
//        WendaoCertInfoBind wendaoCertInfoBind = wendaoCertInfoBindService.selectWendaoCertInfoByCertIdAndDouyinhao(certId,douyinhao);
//        if(wendaoCertInfoBind!=null&&(wendaoCertInfoBind.getImBandStatus()==1||wendaoCertInfoBind.getImBandStatus()==0)){
//            return AjaxResult.error("此抖音号已绑定或正在绑定当前资质对应IM!请检查您的IM绑定列表!");
//        }
//        //全局范围查找
//        int row = wendaoCertInfoBindService.countBindingImByDouyinhao(douyinhao);
//        if(row>0){
//            return AjaxResult.error("该抖音号对应的用户已绑定或正在绑定另外的资质对应的IM,请解绑或拒绝绑定后再试!");
//        }
//                //判断是否能发起IM绑定,只有在所有的已绑定或绑定中的记录都完善了抖音号才能发起
//        int row1 = wendaoCertInfoBindService.countNoDouyinhao(wendaoCertInfo.getTeacherId());
//        if(row1>0){
//            return AjaxResult.error("请先完善已绑定或绑定中的短视频和直播绑定记录中的抖音号,才能发起绑定!");
//        }
//        EnableMountscopeDTO enableMountscopeDTO = new EnableMountscopeDTO();
//        enableMountscopeDTO.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//        if(wendaoCertInfo.getEntityType().intValue()==3){
//            enableMountscopeDTO.setIndustry_role(2);
//        }else{
//            enableMountscopeDTO.setIndustry_role(wendaoCertInfo.getEntityType().intValue());
//        }
//        enableMountscopeDTO.setMerchant_entity_id(wendaoCertInfo.getEntityId());
//        enableMountscopeDTO.setPartner_entity_id(douyinConfig.getEntityId());
//        enableMountscopeDTO.setAccess_token(accessTokenService.getDyAccessTokenByAppNameType(appNameType));
//        //发起im绑定
//        //enableMountscopeDTO.setMount_scope(3);
//        Set<Integer> scopeList = new HashSet<>();
//        scopeList.add(3);
//        enableMountscopeDTO.setMount_scope_list(new ArrayList<>(scopeList));
//        enableMountscopeDTO.setIndustry_code(10000);
//        enableMountscopeDTO.setAweme_id(douyinhao);
//        //先查询
//        QueryMountscopeDTO queryMountscopeDTO = new QueryMountscopeDTO();
//        QueryMountscopeResult queryMountscopeResult = bindMountService.queryMountscope(queryMountscopeDTO);
//        if(queryMountscopeResult.getErr().getErrCode()==0){
//            List<Integer> scopeList1 = queryMountscopeResult.getScope_list();
//            if(CollectionUtils.isNotEmpty(scopeList1)){
//                scopeList.addAll(scopeList1);
//                enableMountscopeDTO.setMount_scope_list(new ArrayList<>(scopeList));
//            }
//            if(queryMountscopeResult.getStatus()==1||queryMountscopeResult.getStatus()==0){
//                if(scopeList1.contains(3)){
//                    return AjaxResult.error("此抖音号已绑定或正在绑定当前资质对应的IM");
//                }
//            }
//        }
//        EnableMountscopeResult enableMountscopeResult = bindMountService.enableMountscope(enableMountscopeDTO);
//        if(enableMountscopeResult.getErr().getErrCode()==0){
//            if(wendaoCertInfoBind==null){
//                //新的绑定
//                wendaoCertInfoBind = new WendaoCertInfoBind();
//                wendaoCertInfoBind.setAppid(accessTokenService.getAppIdByAppNameType(appNameType));
//                wendaoCertInfoBind.setEntityType((long) enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBind.setPartnerEntityId(douyinConfig.getEntityId());
//                wendaoCertInfoBind.setMerchantEntityId(wendaoCertInfo.getEntityId());
//                wendaoCertInfoBind.setTeacherId(wendaoCertInfo.getTeacherId());
//                wendaoCertInfoBind.setShortVideoStatus(2);
//                wendaoCertInfoBind.setLiveStreamStatus(2);
//                wendaoCertInfoBind.setImBandStatus(0);
//                wendaoCertInfoBind.setCertInfoId(certId);
//                wendaoCertInfoBind.setDouyinhao(douyinhao);
//                wendaoCertInfoBind.setAppNameType(appNameType);
//                wendaoCertInfoBindService.insertWendaoCertInfoBind(wendaoCertInfoBind);
//            }else{
//                //原来的基础上绑定
//                wendaoCertInfoBind.setImBandStatus(0);
//                wendaoCertInfoBind.setEntityType((long)enableMountscopeDTO.getIndustry_role());
//                wendaoCertInfoBindService.updateWendaoCertInfoBind(wendaoCertInfoBind);
//            }
//            return AjaxResult.success("发起IM绑定成功!");
//        }else{
//            return AjaxResult.error("发起绑定失败,请稍后再试,原因:"+enableMountscopeResult.getErr().getErrMsg());
//        }
//    }

}
