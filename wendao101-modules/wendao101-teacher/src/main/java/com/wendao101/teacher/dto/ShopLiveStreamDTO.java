package com.wendao101.teacher.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShopLiveStreamDTO {

    private String shopName;

    private String mobile;

    private Long teacherId;

    //所属平台，入驻平台，0抖音 1微信 2快手 3视频号, 多个用逗号隔开
    private String platform;
//直播集合页总数
    private Long liveStreamCount;

}
