package com.wendao101.teacher.domain;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 素材视频对象 t_pic
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
public class TDoc extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 正式使用的路径
     */
    @Excel(name = "正式使用的路径")
    private String pathUrl;

    /**
     * 视频文件名称
     */
    @Excel(name = "视频文件名称")
    private String fileName;

    /**
     * 视频文件原始大小单位是Bytes
     */
    @Excel(name = "视频文件原始大小单位是Bytes")
    private Long fileOriginalSize;

    /**
     * 视频是否删除0未删除，1已删除
     */
    @Excel(name = "视频是否删除0未删除，1已删除")
    private Integer isDelete;
    private Integer appNameType;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setPathUrl(String pathUrl) {
        this.pathUrl = pathUrl;
    }

    public String getPathUrl() {
        return pathUrl;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileOriginalSize(Long fileOriginalSize) {
        this.fileOriginalSize = fileOriginalSize;
    }

    public Long getFileOriginalSize() {
        return fileOriginalSize;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public Integer getAppNameType() {
        return appNameType;
    }

    public void setAppNameType(Integer appNameType) {
        this.appNameType = appNameType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("teacherId", getTeacherId())
                .append("pathUrl", getPathUrl())
                .append("fileName", getFileName())
                .append("fileOriginalSize", getFileOriginalSize())
                .append("isDelete", getIsDelete())
                .append("createTime", getCreateTime())
                .append("updateTime", getUpdateTime())
                .append("appNameType", getAppNameType())
                .toString();
    }
}
