package com.wendao101.teacher.controller;

import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.wendao101.common.log.annotation.Log;
import com.wendao101.common.log.enums.BusinessType;
import com.wendao101.common.security.annotation.RequiresPermissions;
import com.wendao101.teacher.domain.SmsSendRecord;
import com.wendao101.teacher.service.ISmsSendRecordService;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.core.utils.poi.ExcelUtil;
import com.wendao101.common.core.web.page.TableDataInfo;

/**
 * 云片短信发送记录Controller
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/sms_send_record")
public class SmsSendRecordController extends BaseController
{
    @Autowired
    private ISmsSendRecordService smsSendRecordService;

    /**
     * 查询云片短信发送记录列表
     */
    @RequiresPermissions("teacher:record:list")
    @GetMapping("/list")
    public TableDataInfo list(SmsSendRecord smsSendRecord)
    {
        startPage();
        List<SmsSendRecord> list = smsSendRecordService.selectSmsSendRecordList(smsSendRecord);
        return getDataTable(list);
    }

    /**
     * 导出云片短信发送记录列表
     */
    @RequiresPermissions("teacher:record:export")
    @Log(title = "云片短信发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SmsSendRecord smsSendRecord)
    {
        List<SmsSendRecord> list = smsSendRecordService.selectSmsSendRecordList(smsSendRecord);
        ExcelUtil<SmsSendRecord> util = new ExcelUtil<SmsSendRecord>(SmsSendRecord.class);
        util.exportExcel(response, list, "云片短信发送记录数据");
    }

    /**
     * 获取云片短信发送记录详细信息
     */
    @RequiresPermissions("teacher:record:query")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(smsSendRecordService.selectSmsSendRecordById(id));
    }

    /**
     * 新增云片短信发送记录
     */
    @RequiresPermissions("teacher:record:add")
    @Log(title = "云片短信发送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SmsSendRecord smsSendRecord)
    {
        return toAjax(smsSendRecordService.insertSmsSendRecord(smsSendRecord));
    }

    /**
     * 修改云片短信发送记录
     */
    @RequiresPermissions("teacher:record:edit")
    @Log(title = "云片短信发送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SmsSendRecord smsSendRecord)
    {
        return toAjax(smsSendRecordService.updateSmsSendRecord(smsSendRecord));
    }

    /**
     * 删除云片短信发送记录
     */
    @RequiresPermissions("teacher:record:remove")
    @Log(title = "云片短信发送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(smsSendRecordService.deleteSmsSendRecordByIds(ids));
    }
}
