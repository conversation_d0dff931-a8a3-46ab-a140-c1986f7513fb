package com.wendao101.teacher.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.teacher.vo.MGiveEntityAndTextbookVO;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class MGiveEntityDTO {

    private Long id;

    /** 课程id */
    @Excel(name = "课程主键id")
    private Long courseId;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseIdNumber;

    /** 课程标题 */
    @Excel(name = "课程标题")
    private String title;

    /** 课程图片 */
    @Excel(name = "课程图片")
    private String coverPicUrl;

    /** 是否授权手机号，0否，1是 */
    @Excel(name = "是否授权手机号，0否，1是")
    private Integer phoneType;

    /** 是否授权地址，0否，1是 */
    @Excel(name = "是否授权地址，0否，1是")
    private Integer isGetAdress;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 教材列表*/
    @Excel(name = "教材列表")
    private String materials;
}
