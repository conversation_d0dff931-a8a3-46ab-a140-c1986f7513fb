package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.MReceiveRecordMapper;
import com.wendao101.teacher.domain.MReceiveRecord;
import com.wendao101.teacher.service.IMReceiveRecordService;

/**
 * 优惠码领取记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-09
 */
@Service
public class MReceiveRecordServiceImpl implements IMReceiveRecordService 
{
    @Autowired
    private MReceiveRecordMapper mReceiveRecordMapper;

    /**
     * 查询优惠码领取记录
     * 
     * @param id 优惠码领取记录主键
     * @return 优惠码领取记录
     */
    @Override
    public MReceiveRecord selectMReceiveRecordById(Long id)
    {
        return mReceiveRecordMapper.selectMReceiveRecordById(id);
    }

    /**
     * 查询优惠码领取记录列表
     * 
     * @param mReceiveRecord 优惠码领取记录
     * @return 优惠码领取记录
     */
    @Override
    public List<MReceiveRecord> selectMReceiveRecordList(MReceiveRecord mReceiveRecord)
    {
        return mReceiveRecordMapper.selectMReceiveRecordList(mReceiveRecord);
    }

    /**
     * 新增优惠码领取记录
     * 
     * @param mReceiveRecord 优惠码领取记录
     * @return 结果
     */
    @Override
    public int insertMReceiveRecord(MReceiveRecord mReceiveRecord)
    {
        mReceiveRecord.setCreateTime(DateUtils.getNowDate());
        return mReceiveRecordMapper.insertMReceiveRecord(mReceiveRecord);
    }

    /**
     * 修改优惠码领取记录
     * 
     * @param mReceiveRecord 优惠码领取记录
     * @return 结果
     */
    @Override
    public int updateMReceiveRecord(MReceiveRecord mReceiveRecord)
    {
        mReceiveRecord.setUpdateTime(DateUtils.getNowDate());
        return mReceiveRecordMapper.updateMReceiveRecord(mReceiveRecord);
    }

    /**
     * 批量删除优惠码领取记录
     * 
     * @param ids 需要删除的优惠码领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMReceiveRecordByIds(Long[] ids)
    {
        return mReceiveRecordMapper.deleteMReceiveRecordByIds(ids);
    }

    /**
     * 删除优惠码领取记录信息
     * 
     * @param id 优惠码领取记录主键
     * @return 结果
     */
    @Override
    public int deleteMReceiveRecordById(Long id)
    {
        return mReceiveRecordMapper.deleteMReceiveRecordById(id);
    }
}
