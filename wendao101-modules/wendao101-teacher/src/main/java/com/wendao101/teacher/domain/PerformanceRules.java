package com.wendao101.teacher.domain;

import java.math.BigDecimal;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 奖金池规则对象 performance_rules
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
public class PerformanceRules extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 规则月份 */
    @Excel(name = "规则月份")
    private String ruleMonth;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 类型,1员工,2部门 */
    @Excel(name = "类型,1员工,2部门")
    private Integer itemType;

    /** 员工列表,多个逗号分隔,如果是部门,所有员工加入 */
    @Excel(name = "员工列表,多个逗号分隔,如果是部门,所有员工加入")
    private String employees;

    /** 起提公司/部门销售额 */
    @Excel(name = "起提公司/部门销售额")
    private BigDecimal companyTotalGoal;

    /** 提成 */
    @Excel(name = "提成")
    private BigDecimal commissions;

    /** 达成条件,大于起提额,个人业绩 */
    @Excel(name = "达成条件,大于起提额,个人业绩")
    private String conditions;

    /** 起提个人销售额 */
    @Excel(name = "起提个人销售额")
    private BigDecimal personTotalGoal;

    /** 计算方法 */
    @Excel(name = "计算方法")
    private String calculationMethod;

    /** 公司整体销售额是否达成,0未达成,1已经达成 */
    @Excel(name = "公司整体销售额是否达成,0未达成,1已经达成")
    private Integer companyTotalGoalReached;

    /** 个人整体销售额是否达成,0未达成,1已经达成 */
    @Excel(name = "个人整体销售额是否达成,0未达成,1已经达成")
    private Integer personTotalGoalReached;

    /** 设置的月份总销售额(真实数据,当月的动态计算) */
    @Excel(name = "设置的月份总销售额(真实数据,当月的动态计算)")
    private BigDecimal totalSaleMoney;

    /**  付出的总的技术服务费(真实数据,当月的动态计算) */
    @Excel(name = " 付出的总的技术服务费(真实数据,当月的动态计算)")
    private BigDecimal totalServiceMoney;

    /** 毛利润,收到老师扣点的钱(真实数据,当月的动态计算) */
    @Excel(name = "毛利润,收到老师扣点的钱(真实数据,当月的动态计算)")
    private BigDecimal grossProfit;

    /** 净利润,毛利润-技术服务费(抖音,快手,微信和视频号收取的服务费)(真实数据,当月的动态计算) */
    @Excel(name = "净利润,毛利润-技术服务费(抖音,快手,微信和视频号收取的服务费)(真实数据,当月的动态计算)")
    private BigDecimal netProfit;

    /** 绩效金额(total_sale_money-company_total_goal),总销售额-起提销售额 */
    @Excel(name = "绩效金额(total_sale_money-company_total_goal),总销售额-起提销售额")
    private BigDecimal performanceAmount;

    /** 当月综合抽佣比,默认为10,可设置调整 */
    @Excel(name = "当月综合抽佣比,默认为10,可设置调整")
    private BigDecimal rakesRate;


    private BigDecimal personTotalSaleMoney;

    public BigDecimal getPersonTotalSaleMoney() {
        return personTotalSaleMoney;
    }

    public void setPersonTotalSaleMoney(BigDecimal personTotalSaleMoney) {
        this.personTotalSaleMoney = personTotalSaleMoney;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setRuleMonth(String ruleMonth) 
    {
        this.ruleMonth = ruleMonth;
    }

    public String getRuleMonth() 
    {
        return ruleMonth;
    }
    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }
    public void setItemType(Integer itemType) 
    {
        this.itemType = itemType;
    }

    public Integer getItemType() 
    {
        return itemType;
    }
    public void setEmployees(String employees) 
    {
        this.employees = employees;
    }

    public String getEmployees() 
    {
        return employees;
    }
    public void setCompanyTotalGoal(BigDecimal companyTotalGoal) 
    {
        this.companyTotalGoal = companyTotalGoal;
    }

    public BigDecimal getCompanyTotalGoal() 
    {
        return companyTotalGoal;
    }
    public void setCommissions(BigDecimal commissions) 
    {
        this.commissions = commissions;
    }

    public BigDecimal getCommissions() 
    {
        return commissions;
    }
    public void setConditions(String conditions) 
    {
        this.conditions = conditions;
    }

    public String getConditions() 
    {
        return conditions;
    }
    public void setPersonTotalGoal(BigDecimal personTotalGoal) 
    {
        this.personTotalGoal = personTotalGoal;
    }

    public BigDecimal getPersonTotalGoal() 
    {
        return personTotalGoal;
    }
    public void setCalculationMethod(String calculationMethod) 
    {
        this.calculationMethod = calculationMethod;
    }

    public String getCalculationMethod() 
    {
        return calculationMethod;
    }
    public void setCompanyTotalGoalReached(Integer companyTotalGoalReached) 
    {
        this.companyTotalGoalReached = companyTotalGoalReached;
    }

    public Integer getCompanyTotalGoalReached() 
    {
        return companyTotalGoalReached;
    }
    public void setPersonTotalGoalReached(Integer personTotalGoalReached) 
    {
        this.personTotalGoalReached = personTotalGoalReached;
    }

    public Integer getPersonTotalGoalReached() 
    {
        return personTotalGoalReached;
    }
    public void setTotalSaleMoney(BigDecimal totalSaleMoney) 
    {
        this.totalSaleMoney = totalSaleMoney;
    }

    public BigDecimal getTotalSaleMoney() 
    {
        return totalSaleMoney;
    }
    public void setTotalServiceMoney(BigDecimal totalServiceMoney) 
    {
        this.totalServiceMoney = totalServiceMoney;
    }

    public BigDecimal getTotalServiceMoney() 
    {
        return totalServiceMoney;
    }
    public void setGrossProfit(BigDecimal grossProfit) 
    {
        this.grossProfit = grossProfit;
    }

    public BigDecimal getGrossProfit() 
    {
        return grossProfit;
    }
    public void setNetProfit(BigDecimal netProfit) 
    {
        this.netProfit = netProfit;
    }

    public BigDecimal getNetProfit() 
    {
        return netProfit;
    }
    public void setPerformanceAmount(BigDecimal performanceAmount) 
    {
        this.performanceAmount = performanceAmount;
    }

    public BigDecimal getPerformanceAmount() 
    {
        return performanceAmount;
    }
    public void setRakesRate(BigDecimal rakesRate) 
    {
        this.rakesRate = rakesRate;
    }

    public BigDecimal getRakesRate() 
    {
        return rakesRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("ruleMonth", getRuleMonth())
            .append("deptName", getDeptName())
            .append("itemType", getItemType())
            .append("employees", getEmployees())
            .append("companyTotalGoal", getCompanyTotalGoal())
            .append("commissions", getCommissions())
            .append("conditions", getConditions())
            .append("personTotalGoal", getPersonTotalGoal())
            .append("calculationMethod", getCalculationMethod())
            .append("companyTotalGoalReached", getCompanyTotalGoalReached())
            .append("personTotalGoalReached", getPersonTotalGoalReached())
            .append("totalSaleMoney", getTotalSaleMoney())
            .append("totalServiceMoney", getTotalServiceMoney())
            .append("grossProfit", getGrossProfit())
            .append("netProfit", getNetProfit())
            .append("performanceAmount", getPerformanceAmount())
            .append("rakesRate", getRakesRate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
