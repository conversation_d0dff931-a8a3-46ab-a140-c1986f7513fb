package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.SysUser0301;

/**
 * 用户信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-18
 */
public interface SysUser0301Mapper 
{
    /**
     * 查询用户信息
     * 
     * @param userId 用户信息主键
     * @return 用户信息
     */
    public SysUser0301 selectSysUser0301ByUserId(Long userId);

    /**
     * 查询用户信息列表
     * 
     * @param sysUser0301 用户信息
     * @return 用户信息集合
     */
    public List<SysUser0301> selectSysUser0301List(SysUser0301 sysUser0301);

    /**
     * 新增用户信息
     * 
     * @param sysUser0301 用户信息
     * @return 结果
     */
    public int insertSysUser0301(SysUser0301 sysUser0301);

    /**
     * 修改用户信息
     * 
     * @param sysUser0301 用户信息
     * @return 结果
     */
    public int updateSysUser0301(SysUser0301 sysUser0301);

    /**
     * 删除用户信息
     * 
     * @param userId 用户信息主键
     * @return 结果
     */
    public int deleteSysUser0301ByUserId(Long userId);

    /**
     * 批量删除用户信息
     * 
     * @param userIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysUser0301ByUserIds(Long[] userIds);
}
