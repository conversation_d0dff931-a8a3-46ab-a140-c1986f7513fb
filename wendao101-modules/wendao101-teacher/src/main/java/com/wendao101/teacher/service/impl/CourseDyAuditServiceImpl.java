package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.CourseDyAuditMapper;
import com.wendao101.teacher.domain.CourseDyAudit;
import com.wendao101.teacher.service.ICourseDyAuditService;

/**
 * 课程抖音审核Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-08-10
 */
@Service
public class CourseDyAuditServiceImpl implements ICourseDyAuditService 
{
    @Autowired
    private CourseDyAuditMapper courseDyAuditMapper;

    /**
     * 查询课程抖音审核
     * 
     * @param id 课程抖音审核主键
     * @return 课程抖音审核
     */
    @Override
    public CourseDyAudit selectCourseDyAuditById(Long id)
    {
        return courseDyAuditMapper.selectCourseDyAuditById(id);
    }

    /**
     * 查询课程抖音审核列表
     * 
     * @param courseDyAudit 课程抖音审核
     * @return 课程抖音审核
     */
    @Override
    public List<CourseDyAudit> selectCourseDyAuditList(CourseDyAudit courseDyAudit)
    {
        return courseDyAuditMapper.selectCourseDyAuditList(courseDyAudit);
    }

    /**
     * 新增课程抖音审核
     * 
     * @param courseDyAudit 课程抖音审核
     * @return 结果
     */
    @Override
    public int insertCourseDyAudit(CourseDyAudit courseDyAudit)
    {
        courseDyAudit.setCreateTime(DateUtils.getNowDate());
        return courseDyAuditMapper.insertCourseDyAudit(courseDyAudit);
    }

    /**
     * 修改课程抖音审核
     * 
     * @param courseDyAudit 课程抖音审核
     * @return 结果
     */
    @Override
    public int updateCourseDyAudit(CourseDyAudit courseDyAudit)
    {
        courseDyAudit.setUpdateTime(DateUtils.getNowDate());
        return courseDyAuditMapper.updateCourseDyAudit(courseDyAudit);
    }

    /**
     * 批量删除课程抖音审核
     * 
     * @param ids 需要删除的课程抖音审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseDyAuditByIds(Long[] ids)
    {
        return courseDyAuditMapper.deleteCourseDyAuditByIds(ids);
    }

    /**
     * 删除课程抖音审核信息
     * 
     * @param id 课程抖音审核主键
     * @return 结果
     */
    @Override
    public int deleteCourseDyAuditById(Long id)
    {
        return courseDyAuditMapper.deleteCourseDyAuditById(id);
    }

    @Override
    public CourseDyAudit selectCourseDyAuditByCourseId(Long courseId) {
        return courseDyAuditMapper.selectCourseDyAuditByCourseId(courseId);
    }
}
