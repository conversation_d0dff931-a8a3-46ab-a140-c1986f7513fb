package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.MSpreadRatio;

/**
 * 推广人和购买信息关联Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-05
 */
public interface MSpreadRatioMapper 
{
    /**
     * 查询推广人和购买信息关联
     * 
     * @param id 推广人和购买信息关联主键
     * @return 推广人和购买信息关联
     */
    public MSpreadRatio selectMSpreadRatioById(Long id);

    /**
     * 查询推广人和购买信息关联列表
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 推广人和购买信息关联集合
     */
    public List<MSpreadRatio> selectMSpreadRatioList(MSpreadRatio mSpreadRatio);

    /**
     * 新增推广人和购买信息关联
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 结果
     */
    public int insertMSpreadRatio(MSpreadRatio mSpreadRatio);

    /**
     * 修改推广人和购买信息关联
     * 
     * @param mSpreadRatio 推广人和购买信息关联
     * @return 结果
     */
    public int updateMSpreadRatio(MSpreadRatio mSpreadRatio);

    /**
     * 删除推广人和购买信息关联
     * 
     * @param id 推广人和购买信息关联主键
     * @return 结果
     */
    public int deleteMSpreadRatioById(Long id);

    /**
     * 批量删除推广人和购买信息关联
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMSpreadRatioByIds(Long[] ids);
}
