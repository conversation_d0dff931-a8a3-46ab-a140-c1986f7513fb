package com.wendao101.teacher.dto;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import com.wendao101.teacher.domain.CourseAudit;
import com.xiaohongshu.fls.opensdk.entity.product.request.v3.CreateItemV3Request;
import com.xiaohongshu.fls.opensdk.entity.product.response.v3.SkuDetail;
import lombok.Data;

import java.util.List;

@Data
public class XhsCourseDTO extends BaseEntity {
    private static final long serialVersionUID = 1L;
    private List<SkuDetail> skuInfos;
    /** 主键自增id */
    private Long id;

    /** 课程id */
    @Excel(name = "课程id")
    private Long courseId;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 小红书的课程id */
    @Excel(name = "小红书的课程id")
    private String xhsId;

    /** item标题 */
    @Excel(name = "item标题")
    private String name;

    /** 品牌ID,目前查询品牌返回均为String但是是数字可以强转成Long使用 */
    @Excel(name = "品牌ID,目前查询品牌返回均为String但是是数字可以强转成Long使用")
    private Long brandId;

    /** 末级商品类目ID,根据common.getCategories获取 */
    @Excel(name = "末级商品类目ID,根据common.getCategories获取")
    private String categoryId;

    private List<CreateItemV3Request.ItemAttribute> attributes;

    /** 运费模板ID，根据common.getCarriageTemplateList获取 */
    @Excel(name = "运费模板ID，根据common.getCarriageTemplateList获取")
    private String shippingTemplateId;

    /** 商品物流重量（克），当运费模版选择按重量计费时，该值必须大于0 */
    @Excel(name = "商品物流重量", readConverterExp = "克=")
    private Integer shippingGrossWeight;

    /** 定义item可以有的规格类型，例如颜色，尺码，sku依赖了此处定义的规格类型 */
    @Excel(name = "定义item可以有的规格类型，例如颜色，尺码，sku依赖了此处定义的规格类型")
    private List<String> variantIds;

    /** 商品主图(必传) */
    @Excel(name = "商品主图(必传)")
    private List<String> images;

    /** 主图视频 */
    @Excel(name = "主图视频")
    private String videoUrl;

    /** 商品货号 */
    @Excel(name = "商品货号")
    private String articleNo;

    /** 商品详情描述图片 */
    @Excel(name = "商品详情描述图片")
    private List<String> imageDescriptions;

    /** 商品描述文字，上限500字 */
    @Excel(name = "商品描述文字，上限500字")
    private String description;

    /** 透明图 */
    @Excel(name = "透明图")
    private String transparentImage;

    private List<CreateItemV3Request.Faq> faq;

    /** 物流模式,0：普通，1：支持无物流发货（限定类目支持，不支持的类目创建会报错） */
    @Excel(name = "物流模式,0：普通，1：支持无物流发货", readConverterExp = "限=定类目支持，不支持的类目创建会报错")
    private Integer deliveryMode;

    /** 是否支持7天无理由,1：支持，2：不支持，不传会按照规则给默认值，必须支持则支持，不必须则不支持 */
    @Excel(name = "是否支持7天无理由,1：支持，2：不支持，不传会按照规则给默认值，必须支持则支持，不必须则不支持")
    private Integer freeReturn;

    /** 是否渠道0否1是 */
    @Excel(name = "是否渠道0否1是")
    private Integer isChannel;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Integer unionType;

    private CourseAudit courseInfo;

    private Integer originalPrice;
    private Integer price;
    private Integer stock;

    private Integer available;
    private Integer auditStatus;
    private String auditFailReason;

    private String productImages;
    private List<String> productImageList;

    private String skuId;

    private String firstCatName  ;
    private String firstCatId    ;
    private String secondCatName ;
    private String secondCatId   ;
    private String thirdCatName  ;
    private String thirdCatId    ;
    private String fourthCatName ;
    private String fourthCatId   ;
}
