package com.wendao101.teacher.service;

import com.wendao101.teacher.domain.TDocGroup;

import java.util.List;

/**
 * 图片分组Service接口
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface ITDocGroupService 
{
    /**
     * 查询图片分组
     * 
     * @param id 图片分组主键
     * @return 图片分组
     */
    public TDocGroup selectTDocGroupById(Long id);

    /**
     * 查询图片分组列表
     * 
     * @param tDocGroup 图片分组
     * @return 图片分组集合
     */
    public List<TDocGroup> selectTDocGroupList(TDocGroup tDocGroup);

    /**
     * 新增图片分组
     * 
     * @param tDocGroup 图片分组
     * @return 结果
     */
    public int insertTDocGroup(TDocGroup tDocGroup);

    /**
     * 修改图片分组
     * 
     * @param tDocGroup 图片分组
     * @return 结果
     */
    public int updateTDocGroup(TDocGroup tDocGroup);

    /**
     * 批量删除图片分组
     * 
     * @param ids 需要删除的图片分组主键集合
     * @return 结果
     */
    public int deleteTDocGroupByIds(Long[] ids);

    /**
     * 删除图片分组信息
     * 
     * @param id 图片分组主键
     * @return 结果
     */
    public int deleteTDocGroupById(Long id);

    /**
     * 根据分组名称查询
     *
     * @param groupName
     * @return
     */
    TDocGroup selectTDocGroupByGroupName(String groupName,Long teacherId);
}
