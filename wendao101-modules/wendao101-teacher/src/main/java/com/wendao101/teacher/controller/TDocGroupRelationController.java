package com.wendao101.teacher.controller;

import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.web.controller.BaseController;
import com.wendao101.common.core.web.domain.AjaxResult;
import com.wendao101.common.security.utils.SecurityUtils;
import com.wendao101.teacher.domain.TDocGroup;
import com.wendao101.teacher.dto.AddDocGroupRelationDTO;
import com.wendao101.teacher.dto.AddDocGroupRelationMapDTO;
import com.wendao101.teacher.service.ITDocGroupRelationService;
import com.wendao101.teacher.service.ITDocGroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 文档和分组关系Controller
 *
 * <AUTHOR>
 * @date 2023-07-27
 */
@RestController
@RequestMapping("/doc_group_relation")
public class TDocGroupRelationController extends BaseController {
    @Autowired
    private ITDocGroupRelationService tDocGroupRelationService;

    @Autowired
    private ITDocGroupService tDocGroupService;

    /**
     * 新增图片和分组关系
     */
    //@WenDaoLog(title = "素材库",subTitle = "图片",businessType = "图片添加到分组")
    @PostMapping
    public AjaxResult add(@RequestBody AddDocGroupRelationDTO addDocGroupRelationDTO) {
        Long teacherId = SecurityUtils.getUserId();
        if (addDocGroupRelationDTO == null || StringUtils.isEmpty(addDocGroupRelationDTO.getDocIds())) {
            return error("数据不正确");
        }
        //先查询数据库，看是否为当前老师的
        TDocGroup tDocGroup1 = tDocGroupService.selectTDocGroupById(addDocGroupRelationDTO.getGroupId());
        if (tDocGroup1 == null) {
            return error("分组不存在");
        }
        if (tDocGroup1.getTeacherId() == null) {
            return error("分组不正确");
        }
        if (tDocGroup1.getTeacherId().longValue() != teacherId.longValue()) {
            return error("分组不属于当前用户");
        }
        if (tDocGroup1.getIsDelete() != 0) {
            return error("分组已经删除,不能操作");
        }
        String docIds = addDocGroupRelationDTO.getDocIds();
        String[] ids = StringUtils.split(docIds.trim(), ",");
        List<AddDocGroupRelationMapDTO> list = new ArrayList<>();
        for (String id : ids) {
            AddDocGroupRelationMapDTO addDocGroupRelationMapDTO = new AddDocGroupRelationMapDTO();
            addDocGroupRelationMapDTO.setGroupId(addDocGroupRelationDTO.getGroupId());
            addDocGroupRelationMapDTO.setTeacherId(teacherId);
            addDocGroupRelationMapDTO.setDocId(new Long(id.trim()));
            list.add(addDocGroupRelationMapDTO);
        }
        tDocGroupRelationService.insertTDocGroupRelation(list);
        return AjaxResult.success();
    }

    /**
     * 将图片从分组中移除
     *
     * @param addDocGroupRelationDTO
     * @return
     */
    //@WenDaoLog(title = "素材库",subTitle = "图片",businessType = "图片移出分组")
    @PostMapping("remove_from_group")
    public AjaxResult removeFromGroup(@RequestBody AddDocGroupRelationDTO addDocGroupRelationDTO) {
        Long teacherId = SecurityUtils.getUserId();
        if (addDocGroupRelationDTO == null || StringUtils.isEmpty(addDocGroupRelationDTO.getDocIds())) {
            return error("数据不正确");
        }
        //先查询数据库，看是否为当前老师的
        TDocGroup tDocGroup1 = tDocGroupService.selectTDocGroupById(addDocGroupRelationDTO.getGroupId());
        if (tDocGroup1 == null) {
            return error("分组不存在");
        }
        if (tDocGroup1.getTeacherId() == null) {
            return error("分组不正确");
        }
        if (tDocGroup1.getTeacherId().longValue() != teacherId.longValue()) {
            return error("分组不属于当前用户");
        }
        if (tDocGroup1.getIsDelete() != 0) {
            return error("分组已经删除,不能操作");
        }
        String docIds = addDocGroupRelationDTO.getDocIds();
        String[] docIdArr = StringUtils.split(docIds.trim(), ",");
        int rows = tDocGroupRelationService.removeFromGroup(teacherId, addDocGroupRelationDTO.getGroupId(), docIdArr);
        return toAjax(rows);
    }
}
