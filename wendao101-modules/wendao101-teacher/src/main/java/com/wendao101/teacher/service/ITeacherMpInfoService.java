package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.TeacherMpInfo;

/**
 * 老师公众号绑定信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-11-08
 */
public interface ITeacherMpInfoService 
{
    /**
     * 查询老师公众号绑定信息
     * 
     * @param id 老师公众号绑定信息主键
     * @return 老师公众号绑定信息
     */
    public TeacherMpInfo selectTeacherMpInfoById(Long id);

    /**
     * 查询老师公众号绑定信息列表
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 老师公众号绑定信息集合
     */
    public List<TeacherMpInfo> selectTeacherMpInfoList(TeacherMpInfo teacherMpInfo);

    /**
     * 新增老师公众号绑定信息
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 结果
     */
    public int insertTeacherMpInfo(TeacherMpInfo teacherMpInfo);

    /**
     * 修改老师公众号绑定信息
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 结果
     */
    public int updateTeacherMpInfo(TeacherMpInfo teacherMpInfo);

    /**
     * 批量删除老师公众号绑定信息
     * 
     * @param ids 需要删除的老师公众号绑定信息主键集合
     * @return 结果
     */
    public int deleteTeacherMpInfoByIds(Long[] ids);

    /**
     * 删除老师公众号绑定信息信息
     * 
     * @param id 老师公众号绑定信息主键
     * @return 结果
     */
    public int deleteTeacherMpInfoById(Long id);
}
