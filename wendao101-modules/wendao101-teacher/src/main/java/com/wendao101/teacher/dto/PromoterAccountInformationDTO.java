package com.wendao101.teacher.dto;

import com.wendao101.common.core.annotation.Excel;
import com.wendao101.teacher.domain.PromoterCourse;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-08-05
 */
@Data
public class PromoterAccountInformationDTO {


    /**
     * 账号
     */
    @Excel(name = "账号")
    private Long account;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatarUrl;

    /**
     * 来源平台1：抖音，2：快手，3：微信 4: 视频号
     */
    @Excel(name = "来源平台1：抖音，2：快手，3：微信 4: 视频号")
    private Integer platform;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickName;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;


}
