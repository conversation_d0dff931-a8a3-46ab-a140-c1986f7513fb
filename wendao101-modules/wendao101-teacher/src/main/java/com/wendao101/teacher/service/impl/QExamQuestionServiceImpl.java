package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.QExamQuestionMapper;
import com.wendao101.teacher.domain.QExamQuestion;
import com.wendao101.teacher.service.IQExamQuestionService;

/**
 * 题库测试题Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
@Service
public class QExamQuestionServiceImpl implements IQExamQuestionService 
{
    @Autowired
    private QExamQuestionMapper qExamQuestionMapper;

    /**
     * 查询题库测试题
     * 
     * @param id 题库测试题主键
     * @return 题库测试题
     */
    @Override
    public QExamQuestion selectQExamQuestionById(Long id)
    {
        return qExamQuestionMapper.selectQExamQuestionById(id);
    }

    /**
     * 查询题库测试题列表
     * 
     * @param qExamQuestion 题库测试题
     * @return 题库测试题
     */
    @Override
    public List<QExamQuestion> selectQExamQuestionList(QExamQuestion qExamQuestion)
    {
        return qExamQuestionMapper.selectQExamQuestionList(qExamQuestion);
    }

    /**
     * 新增题库测试题
     * 
     * @param qExamQuestion 题库测试题
     * @return 结果
     */
    @Override
    public int insertQExamQuestion(QExamQuestion qExamQuestion)
    {
        qExamQuestion.setCreateTime(DateUtils.getNowDate());
        return qExamQuestionMapper.insertQExamQuestion(qExamQuestion);
    }

    /**
     * 修改题库测试题
     * 
     * @param qExamQuestion 题库测试题
     * @return 结果
     */
    @Override
    public int updateQExamQuestion(QExamQuestion qExamQuestion)
    {
        qExamQuestion.setUpdateTime(DateUtils.getNowDate());
        return qExamQuestionMapper.updateQExamQuestion(qExamQuestion);
    }

    /**
     * 批量删除题库测试题
     * 
     * @param ids 需要删除的题库测试题主键
     * @return 结果
     */
    @Override
    public int deleteQExamQuestionByIds(Long[] ids)
    {
        return qExamQuestionMapper.deleteQExamQuestionByIds(ids);
    }

    /**
     * 删除题库测试题信息
     * 
     * @param id 题库测试题主键
     * @return 结果
     */
    @Override
    public int deleteQExamQuestionById(Long id)
    {
        return qExamQuestionMapper.deleteQExamQuestionById(id);
    }

    @Override
    public int deleteQExamQuestionByIdsAndTeacherId(List<Long> idList, Long teacherId) {
        return qExamQuestionMapper.deleteQExamQuestionByIdsAndTeacherId(idList,teacherId);
    }

    @Override
    public int deleteQuestionByQuestionBankIdAndIdsAndTeacherId(Long questionBankId, List<Long> idList, Long teacherId) {
        return qExamQuestionMapper.deleteQuestionByQuestionBankIdAndIdsAndTeacherId(questionBankId,idList,teacherId);
    }
}
