package com.wendao101.teacher.vo;

import com.wendao101.common.core.annotation.Excel;
import lombok.Data;

import java.util.List;

@Data
public class ChapterVO {

    private Integer pageNum;

    private Integer pageSize;

    /**
     * 老师id
     */
    @Excel(name = "老师id")
    private Long teacherId;

    /**
     * 课程id
     */
    @Excel(name = "课程id")
    private List<Long> courseIds;

    /**
     * 章节名称检索
     */
    @Excel(name = "章节名称检索")
    private String chapterName;
}
