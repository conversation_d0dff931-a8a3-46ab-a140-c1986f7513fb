package com.wendao101.teacher.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;

/**
 * 题库对象 q_question_bank
 * 
 * <AUTHOR>
 * @date 2024-07-18
 */
public class QQuestionBank extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 题库名称 */
    @Excel(name = "题库名称")
    private String title;

    /** 建议答题时间分钟,建议值不做强制约束 */
    @Excel(name = "建议答题时间分钟,建议值不做强制约束")
    private Integer recommendAnsweringTime;

    /** 题库简介 */
    @Excel(name = "题库简介")
    private String desc;

    /** 答题前说明,富文本 */
    @Excel(name = "答题前说明,富文本")
    private String instructionsBeforeAnswer;

    /** 老师id */
    @Excel(name = "老师id")
    private Long teacherId;

    /** 是否删除，0未删除，1已删除 */
    @Excel(name = "是否删除，0未删除，1已删除")
    private Integer isDelete;

    /** 店铺所属APP，1问到好课，2问到课堂 */
    @Excel(name = "店铺所属APP，1问到好课，2问到课堂")
    private Integer appNameType;

    /** 限制答题时间设置,0不限时,1限时间,到时间自动提交答案,默认0 */
    @Excel(name = "限制答题时间设置,0不限时,1限时间,到时间自动提交答案,默认0")
    private Integer isLimitTime;

    /** 如果限制时间则有值,强制约束,单位为分钟,到时间提示时间到自动提交答案 */
    @Excel(name = "如果限制时间则有值,强制约束,单位为分钟,到时间提示时间到自动提交答案")
    private Integer limitTime;

    /** 题目计分规则,0为按正确题目和总题目比例计分,1为每道题可设置分数,总分100分,默认0 */
    @Excel(name = "题目计分规则,0为按正确题目和总题目比例计分,1为每道题可设置分数,总分100分,默认0")
    private Integer scoringRule;

    /** 提交试题限制,0不限制,1限制,默认限制 */
    @Excel(name = "提交试题限制,0不限制,1限制,默认限制")
    private Integer isLimitSubmit;

    /** 提交限制率,表示必须做到百分之多少才能提交,比如60%就填60,默认60.按做题数量比例 */
    @Excel(name = "提交限制率,表示必须做到百分之多少才能提交,比如60%就填60,默认60.按做题数量比例")
    private Integer limitSubmitRate;


    private Integer questionNum;

    public Integer getQuestionNum() {
        return questionNum;
    }

    public void setQuestionNum(Integer questionNum) {
        this.questionNum = questionNum;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setRecommendAnsweringTime(Integer recommendAnsweringTime) 
    {
        this.recommendAnsweringTime = recommendAnsweringTime;
    }

    public Integer getRecommendAnsweringTime() 
    {
        return recommendAnsweringTime;
    }
    public void setDesc(String desc) 
    {
        this.desc = desc;
    }

    public String getDesc() 
    {
        return desc;
    }
    public void setInstructionsBeforeAnswer(String instructionsBeforeAnswer) 
    {
        this.instructionsBeforeAnswer = instructionsBeforeAnswer;
    }

    public String getInstructionsBeforeAnswer() 
    {
        return instructionsBeforeAnswer;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setIsDelete(Integer isDelete) 
    {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() 
    {
        return isDelete;
    }
    public void setAppNameType(Integer appNameType) 
    {
        this.appNameType = appNameType;
    }

    public Integer getAppNameType() 
    {
        return appNameType;
    }
    public void setIsLimitTime(Integer isLimitTime) 
    {
        this.isLimitTime = isLimitTime;
    }

    public Integer getIsLimitTime() 
    {
        return isLimitTime;
    }
    public void setLimitTime(Integer limitTime) 
    {
        this.limitTime = limitTime;
    }

    public Integer getLimitTime() 
    {
        return limitTime;
    }
    public void setScoringRule(Integer scoringRule) 
    {
        this.scoringRule = scoringRule;
    }

    public Integer getScoringRule() 
    {
        return scoringRule;
    }
    public void setIsLimitSubmit(Integer isLimitSubmit) 
    {
        this.isLimitSubmit = isLimitSubmit;
    }

    public Integer getIsLimitSubmit() 
    {
        return isLimitSubmit;
    }
    public void setLimitSubmitRate(Integer limitSubmitRate) 
    {
        this.limitSubmitRate = limitSubmitRate;
    }

    public Integer getLimitSubmitRate() 
    {
        return limitSubmitRate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("title", getTitle())
            .append("recommendAnsweringTime", getRecommendAnsweringTime())
            .append("desc", getDesc())
            .append("instructionsBeforeAnswer", getInstructionsBeforeAnswer())
            .append("teacherId", getTeacherId())
            .append("isDelete", getIsDelete())
            .append("appNameType", getAppNameType())
            .append("isLimitTime", getIsLimitTime())
            .append("limitTime", getLimitTime())
            .append("scoringRule", getScoringRule())
            .append("isLimitSubmit", getIsLimitSubmit())
            .append("limitSubmitRate", getLimitSubmitRate())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
