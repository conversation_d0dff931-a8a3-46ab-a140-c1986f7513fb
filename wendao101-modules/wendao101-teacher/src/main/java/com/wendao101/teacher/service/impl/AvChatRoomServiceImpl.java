package com.wendao101.teacher.service.impl;

import com.alibaba.fastjson.JSON;
import com.wendao101.common.core.avchatroomdto.*;
import com.wendao101.common.core.utils.StringUtils;
import com.wendao101.common.core.utils.livechat.ImPostUtil;
import com.wendao101.common.core.utils.livechat.TLSSigAPIv2;
import com.wendao101.common.redis.service.RedisService;
import com.wendao101.teacher.domain.WendaoLive;
import com.wendao101.teacher.domain.WendaoLiveWap;
import com.wendao101.teacher.dto.ForbidUserMsgDTO;
import com.wendao101.teacher.dto.GroupMutedAccountResultDTO;
import com.wendao101.teacher.service.AvChatRoomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
public class AvChatRoomServiceImpl implements AvChatRoomService {
    @Autowired
    private RedisService redisService;

    private static final String keyPrefix = "wendao_live_room_admin_sign:";

    private static final String userSignKeyPrefix = "wendao_live_room_user_sign:";

    private final TLSSigAPIv2 tlsSigAPIv2 = new TLSSigAPIv2(ImPostUtil.sdkAppId, ImPostUtil.secret);
    @Override
    public boolean createGroup(WendaoLive wendaoLive) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.createGroupUrl+realQuery;
        CreateAvChatRoomDTO createAvChatRoomDTO = new CreateAvChatRoomDTO();
        createAvChatRoomDTO.setGroupId(String.valueOf(wendaoLive.getId()));
        //群名称，最长100字节，使用 UTF-8 编码，1个汉字占3个字节 wendaoLive.getLiveName()
        createAvChatRoomDTO.setName(wendaoLive.getLiveName().substring(0,Math.min(wendaoLive.getLiveName().length(),33)));
        //createAvChatRoomDTO.setName(wendaoLive.getLiveName());
        createAvChatRoomDTO.setType("AVChatRoom");
        CreateAvChatRoomResultDTO createAvChatRoomResultDTO = ImPostUtil.sendPostData(createAvChatRoomDTO, uri, CreateAvChatRoomResultDTO.class);
        System.out.println("建立群结果:"+JSON.toJSONString(createAvChatRoomResultDTO));

        if(createAvChatRoomResultDTO!=null&&createAvChatRoomResultDTO.getErrorCode()==0){
            return true;
        }
        System.out.println("建群失败:"+JSON.toJSONString(createAvChatRoomResultDTO));
        return false;

    }

    @Override
    public boolean createGroupWap(WendaoLiveWap wendaoLiveWap) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.createGroupUrl+realQuery;
        CreateAvChatRoomDTO createAvChatRoomDTO = new CreateAvChatRoomDTO();
        createAvChatRoomDTO.setGroupId(String.valueOf(wendaoLiveWap.getId()));
        //群名称，最长100字节，使用 UTF-8 编码，1个汉字占3个字节 wendaoLive.getLiveName()
        createAvChatRoomDTO.setName(wendaoLiveWap.getLiveName().substring(0,Math.min(wendaoLiveWap.getLiveName().length(),33)));
        //createAvChatRoomDTO.setName(wendaoLive.getLiveName());
        createAvChatRoomDTO.setType("AVChatRoom");
        CreateAvChatRoomResultDTO createAvChatRoomResultDTO = ImPostUtil.sendPostData(createAvChatRoomDTO, uri, CreateAvChatRoomResultDTO.class);
        System.out.println("建立群结果:"+JSON.toJSONString(createAvChatRoomResultDTO));

        if(createAvChatRoomResultDTO!=null&&createAvChatRoomResultDTO.getErrorCode()==0){
            return true;
        }
        System.out.println("建群失败:"+JSON.toJSONString(createAvChatRoomResultDTO));
        return false;

    }

    private String getQueryString() {
        String sign = redisService.getCacheObject(keyPrefix + "wendao");
        if(StringUtils.isBlank(sign)){
            sign = tlsSigAPIv2.genUserSig("wendao", ImPostUtil.EXPIRETIME);
            redisService.setCacheObject(keyPrefix+"wendao", sign,ImPostUtil.EXPIRETIME-300L, TimeUnit.SECONDS);
        }
        String queryInfo = ImPostUtil.queryInfo;
        return String.format(queryInfo, sign, (long) (System.currentTimeMillis() / 1000L));
    }

    @Override
    public boolean modifyAdmin(String[] userIds, String groupId, Integer commandType) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.modifyAdminUrl + realQuery;
        ModifyAdminDTO modifyAdminDTO = new ModifyAdminDTO();
        modifyAdminDTO.setAdminAccount(userIds);
        modifyAdminDTO.setGroupId(groupId);
        modifyAdminDTO.setCommandType(commandType);
        ResultDTO resultDTO = ImPostUtil.sendPostData(modifyAdminDTO, uri, ResultDTO.class);
        if (resultDTO != null && resultDTO.isSuccess()) {
            return true;
        } else {
            System.out.println("修改群主失败:" + JSON.toJSONString(resultDTO));
            return false;
        }
    }

    /**
     * @param groupId
     * @param key
     * @param value
     * @return
     */
    @Override
    public boolean setGroupAttr(String groupId, String key, String value) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.set_group_attr + realQuery;
        SetGroupAttrDTO setGroupAttrDTO = new SetGroupAttrDTO();
        setGroupAttrDTO.setGroupId(groupId);
        List<GroupAttrDTO> groupAttr = new ArrayList<>();
        GroupAttrDTO groupAttrDTO = new GroupAttrDTO();
        groupAttrDTO.setKey(key);
        groupAttrDTO.setValue(value);
        groupAttr.add(groupAttrDTO);
        setGroupAttrDTO.setGroupAttr(groupAttr);
        ResultDTO resultDTO = ImPostUtil.sendPostData(setGroupAttrDTO, uri, ResultDTO.class);
        return resultDTO != null && resultDTO.getErrorCode() == 0;
    }

    @Override
    public List<GroupAttrDTO> getGroupAttrs(String groupId) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.get_group_attrs + realQuery;
        GetRoomGroupAttrsDTO param = new GetRoomGroupAttrsDTO();
        param.setGroupId(groupId);
        RoomAttrResultDTO resultDTO = ImPostUtil.sendPostData(param, uri, RoomAttrResultDTO.class);
        if(resultDTO != null && resultDTO.getErrorCode() == 0) {
            return resultDTO.getGroupAttrAry();
        }
        return Collections.emptyList();
    }

    @Override
    public boolean modifyGroupAttr(String groupId, String key, String value) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.modify_group_attr + realQuery;
        SetGroupAttrDTO setGroupAttrDTO = new SetGroupAttrDTO();
        setGroupAttrDTO.setGroupId(groupId);
        List<GroupAttrDTO> groupAttr = new ArrayList<>();
        GroupAttrDTO groupAttrDTO = new GroupAttrDTO();
        groupAttrDTO.setKey(key);
        groupAttrDTO.setValue(value);
        groupAttr.add(groupAttrDTO);
        setGroupAttrDTO.setGroupAttr(groupAttr);
        ResultDTO resultDTO = ImPostUtil.sendPostData(setGroupAttrDTO, uri, ResultDTO.class);
        return resultDTO != null && resultDTO.getErrorCode() == 0;
    }

    @Override
    public String queryUserEnterLiveRoomSign(String userId) {
        String redisKey = userSignKeyPrefix + userId;
        String sign = redisService.getCacheObject(redisKey);
        if (StringUtils.isBlank(sign)) {
            sign = tlsSigAPIv2.genUserSig(userId, ImPostUtil.EXPIRETIME);
            redisService.setCacheObject(redisKey, sign, ImPostUtil.EXPIRETIME - 300L, TimeUnit.SECONDS);
        }
        return sign;
    }

    @Override
    public ResultDTO forbidSendMsg(String groupId, String forbidUserId, Integer muteTime) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.forbidSendMsg + realQuery;
        ForbidUserMsgDTO portraitSetDTO = new ForbidUserMsgDTO();
        portraitSetDTO.setGroupId(groupId);
        portraitSetDTO.setMembersAccount(new String[]{forbidUserId});
        portraitSetDTO.setMuteTime(muteTime);
        return ImPostUtil.sendPostData(portraitSetDTO, uri, ResultDTO.class);
    }

    @Override
    public GroupMutedAccountResultDTO getGroupMutedAccount(String groupId) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.get_group_muted_account + realQuery;
        ForbidUserMsgDTO portraitSetDTO = new ForbidUserMsgDTO();
        portraitSetDTO.setGroupId(groupId);
        return ImPostUtil.sendPostData(portraitSetDTO, uri, GroupMutedAccountResultDTO.class);
    }

    @Override
    public boolean setGroupAttrs(String groupId, List<GroupAttrDTO> groupAttrs) {
        String realQuery = getQueryString();
        String uri = ImPostUtil.set_group_attr + realQuery;
        SetGroupAttrDTO setGroupAttrDTO = new SetGroupAttrDTO();
        setGroupAttrDTO.setGroupId(groupId);
        setGroupAttrDTO.setGroupAttr(groupAttrs);
        ResultDTO resultDTO = ImPostUtil.sendPostData(setGroupAttrDTO, uri, ResultDTO.class);
        return resultDTO != null && resultDTO.getErrorCode() == 0;
    }
}
