package com.wendao101.teacher.mapper;

import java.util.List;
import com.wendao101.teacher.domain.CourseDirectoryDy;
import org.apache.ibatis.annotations.Param;

/**
 * 目录Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface CourseDirectoryDyMapper 
{
    /**
     * 查询目录
     * 
     * @param id 目录主键
     * @return 目录
     */
    public CourseDirectoryDy selectCourseDirectoryDyById(Long id);

    /**
     * 查询目录列表
     * 
     * @param courseDirectoryDy 目录
     * @return 目录集合
     */
    public List<CourseDirectoryDy> selectCourseDirectoryDyList(CourseDirectoryDy courseDirectoryDy);

    /**
     * 新增目录
     * 
     * @param courseDirectoryDy 目录
     * @return 结果
     */
    public int insertCourseDirectoryDy(CourseDirectoryDy courseDirectoryDy);

    /**
     * 修改目录
     * 
     * @param courseDirectoryDy 目录
     * @return 结果
     */
    public int updateCourseDirectoryDy(CourseDirectoryDy courseDirectoryDy);

    /**
     * 删除目录
     * 
     * @param id 目录主键
     * @return 结果
     */
    public int deleteCourseDirectoryDyById(Long id);

    /**
     * 批量删除目录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCourseDirectoryDyByIds(Long[] ids);

    void updateCourseDirectoryChapterIdNull(@Param("id") Long id);

    void batchInsert(@Param("list") List<CourseDirectoryDy> insertCdList);

    void batchUpdate(@Param("list")List<CourseDirectoryDy> updateList);

    void deleteCourseDirectoryDyByCourseId(@Param("courseId")Long courseId);
}
