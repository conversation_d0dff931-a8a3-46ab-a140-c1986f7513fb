package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.MReceiveCoupon;
import com.wendao101.teacher.dto.MreceiveCouponDTO;
import com.wendao101.teacher.vo.MreceiveCouponVO;

/**
 * 领取优惠券Service接口
 * 
 * <AUTHOR>
 * @date 2023-08-07
 */
public interface IMReceiveCouponService 
{
    /**
     * 查询领取优惠券列表
     *
     */
    public List<MreceiveCouponDTO> selectReceiveCoupon(MreceiveCouponVO mreceiveCouponVO);



    /**
     * 查询领取优惠券
     * 
     * @param id 领取优惠券主键
     * @return 领取优惠券
     */
    public MReceiveCoupon selectMReceiveCouponById(Long id);

    /**
     * 查询领取优惠券列表
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 领取优惠券集合
     */
    public List<MReceiveCoupon> selectMReceiveCouponList(MReceiveCoupon mReceiveCoupon);

    /**
     * 新增领取优惠券
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 结果
     */
    public int insertMReceiveCoupon(MReceiveCoupon mReceiveCoupon);

    /**
     * 修改领取优惠券
     * 
     * @param mReceiveCoupon 领取优惠券
     * @return 结果
     */
    public int updateMReceiveCoupon(MReceiveCoupon mReceiveCoupon);

    /**
     * 批量删除领取优惠券
     * 
     * @param ids 需要删除的领取优惠券主键集合
     * @return 结果
     */
    public int deleteMReceiveCouponByIds(Long[] ids);

    /**
     * 删除领取优惠券信息
     * 
     * @param id 领取优惠券主键
     * @return 结果
     */
    public int deleteMReceiveCouponById(Long id);
}
