package com.wendao101.teacher.service;

import java.util.List;
import com.wendao101.teacher.domain.SphxdCourse;

/**
 * 视频号小店课程Service接口
 * 
 * <AUTHOR>
 * @date 2023-11-13
 */
public interface ISphxdCourseService 
{
    /**
     * 查询视频号小店课程
     * 
     * @param id 视频号小店课程主键
     * @return 视频号小店课程
     */
    public SphxdCourse selectSphxdCourseById(Long id);

    /**
     * 查询视频号小店课程列表
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 视频号小店课程集合
     */
    public List<SphxdCourse> selectSphxdCourseList(SphxdCourse sphxdCourse);

    /**
     * 新增视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    public int insertSphxdCourse(SphxdCourse sphxdCourse);

    /**
     * 修改视频号小店课程
     * 
     * @param sphxdCourse 视频号小店课程
     * @return 结果
     */
    public int updateSphxdCourse(SphxdCourse sphxdCourse);

    /**
     * 批量删除视频号小店课程
     * 
     * @param ids 需要删除的视频号小店课程主键集合
     * @return 结果
     */
    public int deleteSphxdCourseByIds(Long[] ids);

    /**
     * 删除视频号小店课程信息
     * 
     * @param id 视频号小店课程主键
     * @return 结果
     */
    public int deleteSphxdCourseById(Long id);

    SphxdCourse selectSphxdCourseByProductId(String productId);

    int deleteSphxdCourseByProductId(String productId);

    List<SphxdCourse> selectByWendaoCourseId(Long wendaoCourseId);

    List<SphxdCourse> selectByWendaoCourseIdAndWxxdAppId(Long wendaoCourseId, String wxxdAppId);
}
