package com.wendao101.teacher.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

import com.alibaba.fastjson.annotation.JSONField;

@Data
public class StatusReportDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    
    @JSONField(name = "mobile")
    private String mobile;
    
    @JSONField(name = "report_status")
    private String reportStatus;
    
    @JSONField(name = "error_detail")
    private String errorDetail;
    
    @JSONField(name = "sid")
    private Long sid;
    
    @JSONField(name = "uid")
    private String uid;
    
    @JSONField(name = "user_receive_time", format = "yyyy-MM-dd HH:mm:ss")
    private Date userReceiveTime;
    
    @JSONField(name = "error_msg")
    private String errorMsg;
}
