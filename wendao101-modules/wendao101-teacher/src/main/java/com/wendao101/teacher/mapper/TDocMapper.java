package com.wendao101.teacher.mapper;

import com.wendao101.teacher.domain.TDoc;
import com.wendao101.teacher.dto.QueryDocDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 素材视频Mapper接口
 * 
 * <AUTHOR>
 * @date 2023-07-27
 */
public interface TDocMapper 
{
    /**
     * 查询素材视频
     * 
     * @param id 素材视频主键
     * @return 素材视频
     */
    public TDoc selectTDocById(Long id);

    /**
     * 查询素材视频列表
     * 
     * @param tDoc 素材视频
     * @return 素材视频集合
     */
    public List<TDoc> selectTDocList(TDoc tDoc);

    /**
     * 新增素材视频
     * 
     * @param tDoc 素材视频
     * @return 结果
     */
    public int insertTDoc(TDoc tDoc);

    /**
     * 修改素材视频
     * 
     * @param tDoc 素材视频
     * @return 结果
     */
    public int updateTDoc(TDoc tDoc);

    /**
     * 删除素材视频
     * 
     * @param id 素材视频主键
     * @return 结果
     */
    public int deleteTDocById(Long id);

    /**
     * 批量删除素材视频
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTDocByIds(@Param("ids")Long[] ids,@Param("teacherId")Long teacherId);

    List<TDoc> selectTDocListByGroup(QueryDocDTO queryDocDTO);

    int updateTDocNameByIdAndTeacherId(@Param("teacherId")Long teacherId, @Param("id")Long id, @Param("fileName")String fileName);
}
