package com.wendao101.teacher.service;

import com.wendao101.common.core.aliyunlive.standard.PullStreamUrls;
import com.wendao101.common.core.aliyunlive.standard.PushStreamUrls;

/**
 * 非连麦直播地址生成服务
 */
public interface AliyunStandardLiveService {
    /**
     * 生成主播推流地址
     * @param streamName 流名称
     * @param exp 过期时间,秒
     * @return 推流地址集合
     */
    PushStreamUrls generatePushStreamUrls(String streamName, long exp);

    /**
     * 生成播放地址
     * @param streamName 流名称
     * @param exp 过期时间,秒
     * @return 播放地址集合
     */
    PullStreamUrls generatePullStreamUrls(String streamName, long exp);

    PullStreamUrls generateOriaacPullStreamUrls(String streamName, long exp);
}
