package com.wendao101.teacher.service.impl;

import java.util.List;
import com.wendao101.common.core.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.wendao101.teacher.mapper.TeacherMpInfoMapper;
import com.wendao101.teacher.domain.TeacherMpInfo;
import com.wendao101.teacher.service.ITeacherMpInfoService;

/**
 * 老师公众号绑定信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-11-08
 */
@Service
public class TeacherMpInfoServiceImpl implements ITeacherMpInfoService 
{
    @Autowired
    private TeacherMpInfoMapper teacherMpInfoMapper;

    /**
     * 查询老师公众号绑定信息
     * 
     * @param id 老师公众号绑定信息主键
     * @return 老师公众号绑定信息
     */
    @Override
    public TeacherMpInfo selectTeacherMpInfoById(Long id)
    {
        return teacherMpInfoMapper.selectTeacherMpInfoById(id);
    }

    /**
     * 查询老师公众号绑定信息列表
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 老师公众号绑定信息
     */
    @Override
    public List<TeacherMpInfo> selectTeacherMpInfoList(TeacherMpInfo teacherMpInfo)
    {
        return teacherMpInfoMapper.selectTeacherMpInfoList(teacherMpInfo);
    }

    /**
     * 新增老师公众号绑定信息
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 结果
     */
    @Override
    public int insertTeacherMpInfo(TeacherMpInfo teacherMpInfo)
    {
        teacherMpInfo.setCreateTime(DateUtils.getNowDate());
        return teacherMpInfoMapper.insertTeacherMpInfo(teacherMpInfo);
    }

    /**
     * 修改老师公众号绑定信息
     * 
     * @param teacherMpInfo 老师公众号绑定信息
     * @return 结果
     */
    @Override
    public int updateTeacherMpInfo(TeacherMpInfo teacherMpInfo)
    {
        teacherMpInfo.setUpdateTime(DateUtils.getNowDate());
        return teacherMpInfoMapper.updateTeacherMpInfo(teacherMpInfo);
    }

    /**
     * 批量删除老师公众号绑定信息
     * 
     * @param ids 需要删除的老师公众号绑定信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherMpInfoByIds(Long[] ids)
    {
        return teacherMpInfoMapper.deleteTeacherMpInfoByIds(ids);
    }

    /**
     * 删除老师公众号绑定信息信息
     * 
     * @param id 老师公众号绑定信息主键
     * @return 结果
     */
    @Override
    public int deleteTeacherMpInfoById(Long id)
    {
        return teacherMpInfoMapper.deleteTeacherMpInfoById(id);
    }
}
