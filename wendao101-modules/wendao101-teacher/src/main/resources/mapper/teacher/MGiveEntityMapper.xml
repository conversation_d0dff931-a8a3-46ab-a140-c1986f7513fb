<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.MGiveEntityMapper">
    
    <resultMap type="MGiveEntity" id="MGiveEntityResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="phoneType"    column="phone_type"    />
        <result property="isGetAdress"    column="is_get_adress"    />
        <result property="materials"    column="materials"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectMGiveEntityVo">
        select id, teacher_id, course_id, phone_type, is_get_adress, materials, create_time, update_time from m_give_entity
    </sql>

    <select id="selectMGiveEntityList" parameterType="com.wendao101.teacher.vo.MGiveEntityVO" resultType="com.wendao101.teacher.dto.MGiveEntityDTO">
        SELECT
        a.id id,
        b.id courseId,
        b.course_id_number courseIdNumber,
        b.title title,
        b.cover_pic_url coverPicUrl,
        a.phone_type phoneType,
        a.is_get_adress isGetAdress,
        a.create_time createTime,
        a.update_time updateTime,
        a.materials
        FROM
        m_give_entity a
        LEFT JOIN course b ON a.course_id = b.id
        <where>
            <if test="teacherId != null "> and a.teacher_id = #{teacherId}</if>
            <if test="courseId != null "> and  a.course_id = #{courseId}</if>
            and b.is_delete = 0
        </where>
        ORDER BY a.create_time desc
    </select>
    
    <select id="selectMGiveEntityById" parameterType="Long" resultMap="MGiveEntityResult">
        <include refid="selectMGiveEntityVo"/>
        where id = #{id} and teacher_id = #{teacherId}
    </select>

    <select id="selectMGiveEntity" parameterType="Long" resultMap="MGiveEntityResult">
        <include refid="selectMGiveEntityVo"/>
        where course_id = #{courseId} and teacher_id = #{teacherId}
    </select>
        
    <insert id="insertMGiveEntity" parameterType="MGiveEntity" useGeneratedKeys="true" keyProperty="id">
        insert into m_give_entity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="phoneType != null">phone_type,</if>
            <if test="isGetAdress != null">is_get_adress,</if>
            <if test="materials != null">materials,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="phoneType != null">#{phoneType},</if>
            <if test="isGetAdress != null">#{isGetAdress},</if>
            <if test="materials != null">#{materials},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <select id="selectMGiveEntityByCourseId" parameterType="Long" resultType="java.lang.Integer">
        select count(1) from m_give_entity where course_id = #{courseId} and teacher_id = #{teacherId}
    </select>
    <select id="selectShopInfomation" resultType="com.wendao101.teacher.dto.ShopGiveEntityDTO">
        select
        shop_name shopName,
        mobile,
        teacher_id teacherId,
        platform,
        app_name_type appNameType
        from t_teacher
        <where>
            <if test="shopInfomation != null and shopInfomation != ''">AND (
                shop_name LIKE CONCAT('%', #{shopInfomation}, '%')
                OR teacher_id LIKE CONCAT('%', #{shopInfomation}, '%')
                OR mobile LIKE CONCAT('%', #{shopInfomation}, '%')
                )</if>
            <if test="platform != null">and platform like concat('%', #{platform}, '%')</if>
        and `status` = 0
        </where>
    </select>

    <select id="selectCourseCount" resultType="java.lang.Integer">
        select count(1)
        from  course
        where teacher_id = #{teacherId} and is_delete = 0
    </select>

    <select id="selectEntityCount" resultType="java.lang.Integer">
        select count(1)
        FROM
        m_give_entity a
        LEFT JOIN course b ON a.course_id = b.id
        <where>
            <if test="teacherId != null "> and a.teacher_id = #{teacherId}</if>
            and b.is_delete = 0
        </where>
    </select>
    <select id="selectAuditCount" resultType="java.lang.Integer">
        select count(1)
        from m_give_entity m
        LEFT JOIN course b ON m.course_id = b.id
        where
        m.teacher_id = #{teacherId}
        and b.is_delete = 0
        <if test="phoneType != null">and m.phone_type = #{phoneType}</if>
        <if test="isGetAdress != null">and m.is_get_adress = #{isGetAdress}</if>
    </select>


    <update id="updateMGiveEntity" parameterType="MGiveEntity">
        update m_give_entity
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="phoneType != null">phone_type = #{phoneType},</if>
            <if test="isGetAdress != null">is_get_adress = #{isGetAdress},</if>
            <if test="materials != null">materials = #{materials},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id} and teacher_id = #{teacherId}
    </update>




    <delete id="deleteMGiveEntityById" parameterType="java.lang.Long">
        delete from m_give_entity where id = #{id}
    </delete>
</mapper>