<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TTeacher0301Mapper">
    
    <resultMap type="TTeacher0301" id="TTeacher0301Result">
        <result property="teacherId"    column="teacher_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="openId"    column="open_id"    />
        <result property="shopName"    column="shop_name"    />
        <result property="mobile"    column="mobile"    />
        <result property="loginNum"    column="login_num"    />
        <result property="password"    column="password"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="teacherDesc"    column="teacher_desc"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="platform"    column="platform"    />
        <result property="appType"    column="app_type"    />
        <result property="hot"    column="hot"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="dyUid"    column="dy_uid"    />
        <result property="gender"    column="gender"    />
        <result property="version"    column="version"    />
        <result property="endDate"    column="end_date"    />
        <result property="appNameType"    column="app_name_type"    />
    </resultMap>

    <sql id="selectTTeacher0301Vo">
        select teacher_id, union_id, open_id, shop_name, mobile, login_num, password, teacher_name, teacher_desc, avatar_url, platform, app_type, hot, login_ip, login_date, status, create_time, update_time, dy_uid, gender, version, end_date, app_name_type from `wendao-delete-0301`.t_teacher_0301
    </sql>

    <select id="selectTTeacher0301List" parameterType="TTeacher0301" resultMap="TTeacher0301Result">
        <include refid="selectTTeacher0301Vo"/>
        <where>  
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="shopName != null  and shopName != ''"> and shop_name like concat('%', #{shopName}, '%')</if>
            <if test="mobile != null  and mobile != ''"> and mobile = #{mobile}</if>
            <if test="loginNum != null "> and login_num = #{loginNum}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="teacherDesc != null  and teacherDesc != ''"> and teacher_desc = #{teacherDesc}</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="platform != null  and platform != ''"> and platform = #{platform}</if>
            <if test="appType != null "> and app_type = #{appType}</if>
            <if test="hot != null "> and hot = #{hot}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="dyUid != null  and dyUid != ''"> and dy_uid = #{dyUid}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectTTeacher0301ByTeacherId" parameterType="Long" resultMap="TTeacher0301Result">
        <include refid="selectTTeacher0301Vo"/>
        where teacher_id = #{teacherId}
    </select>
        
    <insert id="insertTTeacher0301" parameterType="TTeacher0301">
        insert into `wendao-delete-0301`.t_teacher_0301
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="shopName != null">shop_name,</if>
            <if test="mobile != null">mobile,</if>
            <if test="loginNum != null">login_num,</if>
            <if test="password != null">password,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="teacherDesc != null">teacher_desc,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="platform != null">platform,</if>
            <if test="appType != null">app_type,</if>
            <if test="hot != null">hot,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="dyUid != null">dy_uid,</if>
            <if test="gender != null">gender,</if>
            <if test="version != null">version,</if>
            <if test="endDate != null">end_date,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="shopName != null">#{shopName},</if>
            <if test="mobile != null">#{mobile},</if>
            <if test="loginNum != null">#{loginNum},</if>
            <if test="password != null">#{password},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="teacherDesc != null">#{teacherDesc},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="platform != null">#{platform},</if>
            <if test="appType != null">#{appType},</if>
            <if test="hot != null">#{hot},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="dyUid != null">#{dyUid},</if>
            <if test="gender != null">#{gender},</if>
            <if test="version != null">#{version},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updateTTeacher0301" parameterType="TTeacher0301">
        update `wendao-delete-0301`.t_teacher_0301
        <trim prefix="SET" suffixOverrides=",">
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="shopName != null">shop_name = #{shopName},</if>
            <if test="mobile != null">mobile = #{mobile},</if>
            <if test="loginNum != null">login_num = #{loginNum},</if>
            <if test="password != null">password = #{password},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="teacherDesc != null">teacher_desc = #{teacherDesc},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="appType != null">app_type = #{appType},</if>
            <if test="hot != null">hot = #{hot},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="dyUid != null">dy_uid = #{dyUid},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="version != null">version = #{version},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteTTeacher0301ByTeacherId" parameterType="Long">
        delete from `wendao-delete-0301`.t_teacher_0301 where teacher_id = #{teacherId}
    </delete>

    <delete id="deleteTTeacher0301ByTeacherIds" parameterType="String">
        delete from `wendao-delete-0301`.t_teacher_0301 where teacher_id in
        <foreach item="teacherId" collection="array" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </delete>
</mapper>