<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.StudyDataMapper">
    
    <resultMap type="StudyData" id="StudyDataResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="studyFileName"    column="study_file_name"    />
        <result property="studyFileType"    column="study_file_type"    />
        <result property="studyFileUrl"    column="study_file_url"    />
        <result property="studyFileSize"    column="study_file_size"    />
        <result property="status"    column="status"    />
        <result property="delType"    column="del_type"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectStudyDataVo">
        select id, teacher_id, course_id, study_file_name, study_file_type, study_file_url, study_file_size, status, del_type, create_time, update_time from study_data
    </sql>

    <select id="selectStudyDataList" parameterType="StudyData" resultMap="StudyDataResult">
        <include refid="selectStudyDataVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="studyFileName != null  and studyFileName != ''"> and study_file_name like concat('%', #{studyFileName}, '%')</if>
            <if test="studyFileType != null  and studyFileType != ''"> and study_file_type = #{studyFileType}</if>
            <if test="studyFileUrl != null  and studyFileUrl != ''"> and study_file_url = #{studyFileUrl}</if>
            <if test="studyFileSize != null "> and study_file_size = #{studyFileSize}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="delType != null "> and del_type = #{delType}</if>
        </where>
    </select>
    
    <select id="selectStudyDataById" parameterType="Long" resultMap="StudyDataResult">
        <include refid="selectStudyDataVo"/>
        where id = #{id}
    </select>


    <insert id="insertStudyData" parameterType="StudyData" useGeneratedKeys="true" keyProperty="id">
        insert into study_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="studyFileName != null">study_file_name,</if>
            <if test="studyFileType != null">study_file_type,</if>
            <if test="studyFileUrl != null">study_file_url,</if>
            <if test="studyFileSize != null">study_file_size,</if>
            <if test="status != null">status,</if>
            <if test="delType != null">del_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="studyFileName != null">#{studyFileName},</if>
            <if test="studyFileType != null">#{studyFileType},</if>
            <if test="studyFileUrl != null">#{studyFileUrl},</if>
            <if test="studyFileSize != null">#{studyFileSize},</if>
            <if test="status != null">#{status},</if>
            <if test="delType != null">#{delType},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>


    <update id="updateStudyData" parameterType="StudyData">
        update study_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="studyFileName != null">study_file_name = #{studyFileName},</if>
            <if test="studyFileType != null">study_file_type = #{studyFileType},</if>
            <if test="studyFileUrl != null">study_file_url = #{studyFileUrl},</if>
            <if test="studyFileSize != null">study_file_size = #{studyFileSize},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delType != null">del_type = #{delType},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteStudyDataById" parameterType="Long">
        delete from study_data where id = #{id}
    </delete>

    <delete id="deleteStudyDataByIds" parameterType="String">
        delete from study_data where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>