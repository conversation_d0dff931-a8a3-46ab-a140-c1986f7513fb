<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.TDocGroupMapper">
    
    <resultMap type="TDocGroup" id="TDocGroupResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="groupName"    column="group_name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectTDocGroupVo">
        select id, teacher_id, group_name, is_delete, create_time, update_time from t_doc_group
    </sql>

    <select id="selectTDocGroupList" parameterType="TDocGroup" resultMap="TDocGroupResult">
        <include refid="selectTDocGroupVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="groupName != null  and groupName != ''"> and group_name like concat('%', #{groupName}, '%')</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
        </where>
    </select>
    
    <select id="selectTDocGroupById" parameterType="Long" resultMap="TDocGroupResult">
        <include refid="selectTDocGroupVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertTDocGroup" parameterType="TDocGroup" useGeneratedKeys="true" keyProperty="id">
        insert into t_doc_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="groupName != null">group_name,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="groupName != null">#{groupName},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateTDocGroup" parameterType="TDocGroup">
        update t_doc_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="groupName != null">group_name = #{groupName},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTDocGroupById" parameterType="Long">
        delete from t_doc_group where id = #{id}
    </delete>

    <delete id="deleteTDocGroupByIds" parameterType="String">
        delete from t_doc_group where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectTDocGroupByGroupName"  resultMap="TDocGroupResult">
        <include refid="selectTDocGroupVo"/>
        where group_name = #{groupName}
        and teacher_id = #{teacherId}
        and is_delete = 0
    </select>
</mapper>