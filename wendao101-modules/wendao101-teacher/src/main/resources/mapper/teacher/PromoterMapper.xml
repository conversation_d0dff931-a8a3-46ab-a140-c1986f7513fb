<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wendao101.teacher.mapper.PromoterMapper">
    
    <resultMap type="Promoter" id="PromoterResult">
        <result property="id"    column="id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="unionId"    column="union_id"    />
        <result property="openId"    column="open_id"    />
        <result property="promoterName"    column="promoter_name"    />
        <result property="promoterPhone"    column="promoter_phone"    />
        <result property="promoterImg"    column="promoter_img"    />
        <result property="platform"    column="platform"    />
        <result property="accountState"    column="account_state"    />
        <result property="authorizedAccountList"    column="authorized_account_list"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="appNameType"    column="app_name_type"    />

    </resultMap>

    <sql id="selectPromoterVo">
        select id, teacher_id, union_id, open_id, promoter_name, promoter_phone, promoter_img, is_delete, platform, account_state, authorized_account_list, create_time, update_time,app_name_type from promoter
    </sql>

    <select id="selectPromoterList" parameterType="Promoter" resultMap="PromoterResult">
        <include refid="selectPromoterVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="unionId != null  and unionId != ''"> and union_id = #{unionId}</if>
            <if test="openId != null  and openId != ''"> and open_id = #{openId}</if>
            <if test="promoterName != null  and promoterName != ''"> and promoter_name like concat('%', #{promoterName}, '%')</if>
            <if test="promoterPhone != null  and promoterPhone != ''"> and promoter_phone = #{promoterPhone}</if>
            <if test="promoterImg != null  and promoterImg != ''"> and promoter_img = #{promoterImg}</if>
            <if test="isDelete != null "> and is_delete = #{isDelete}</if>
            <if test="appNameType != null "> and app_name_type = #{appNameType}</if>
        </where>
    </select>
    
    <select id="selectPromoterById" resultMap="PromoterResult">
        <include refid="selectPromoterVo"/>
        where id = #{id} and teacher_id = #{teacherId}
    </select>

    <select id="selectPromoterByPhone" resultMap="PromoterResult">
        <include refid="selectPromoterVo"/>
        where  teacher_id = #{teacherId} and promoter_phone = #{phone} and app_name_type = #{appNameType}
    </select>
        
    <insert id="insertPromoter" parameterType="Promoter" useGeneratedKeys="true" keyProperty="id">
        insert into promoter
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="unionId != null">union_id,</if>
            <if test="openId != null">open_id,</if>
            <if test="promoterName != null">promoter_name,</if>
            <if test="promoterPhone != null">promoter_phone,</if>
            <if test="promoterImg != null">promoter_img,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="platform != null">platform,</if>
            <if test="accountState != null">account_state,</if>
            <if test="authorizedAccountList != null">authorized_account_list,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="appNameType != null">app_name_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="unionId != null">#{unionId},</if>
            <if test="openId != null">#{openId},</if>
            <if test="promoterName != null">#{promoterName},</if>
            <if test="promoterPhone != null">#{promoterPhone},</if>
            <if test="promoterImg != null">#{promoterImg},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="platform != null">#{platform},</if>
            <if test="accountState != null">#{accountState},</if>
            <if test="authorizedAccountList != null">#{authorizedAccountList},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="appNameType != null">#{appNameType},</if>
         </trim>
    </insert>

    <update id="updatePromoter" parameterType="Promoter">
        update promoter
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="unionId != null">union_id = #{unionId},</if>
            <if test="openId != null">open_id = #{openId},</if>
            <if test="promoterName != null">promoter_name = #{promoterName},</if>
            <if test="promoterPhone != null">promoter_phone = #{promoterPhone},</if>
            <if test="promoterImg != null">promoter_img = #{promoterImg},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="platform != null">platform = #{platform},</if>
            <if test="accountState != null">account_state = #{accountState},</if>
            <if test="authorizedAccountList != null">authorized_account_list = #{authorizedAccountList},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="appNameType != null">app_name_type = #{appNameType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePromoterById" parameterType="Long">
        delete from promoter where id = #{id}
    </delete>

    <delete id="deletePromoterByIds" parameterType="String">
        delete from promoter where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectPromoterAll" parameterType="com.wendao101.teacher.vo.PromoterVO" resultType="com.wendao101.teacher.dto.PromoterDTO">
        SELECT
        id,
        promoter_name promoterName,
        promoter_phone promoterPhone,
        platform,
        account_state accountState,
        create_time createTime,
        update_time updateTime,
        authorized_account_list authorizedAccountList
        FROM
        promoter
        <where>
            <if test="teacherId != null ">and teacher_id = #{teacherId}</if>
            <if test="promoterName != null  and promoterName != ''">and promoter_name like concat('%', #{promoterName}, '%')</if>
            <if test="platform != null  and platform != ''">
                <if test="platform == '0'.toString()">
                    <![CDATA[ and platform->'$.dy' = 1 ]]>
                </if>
                <if test="platform == '1'.toString()">
                    <![CDATA[ and platform->'$.wx' = 1 ]]>
                </if>
                <if test="platform == '2'.toString()">
                    <![CDATA[ and platform->'$.ks' = 1 ]]>
                </if>
                <if test="platform == '3'.toString()">
                    <![CDATA[ and platform->'$.sp' = 1 ]]>
                </if>
                <if test="platform == '11'.toString()">
                    <![CDATA[ and platform->'$.dd' = 1 ]]>
                </if>
            </if>

            <if test="accountState != null ">and account_state = #{accountState}</if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                AND create_time &lt;= #{endTime}
            </if>
            <if test="promoterPhone != null  and promoterPhone != ''"> and promoter_phone = #{promoterPhone}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectCourseOrder" parameterType="Long" resultType="java.math.BigDecimal">
        select promoter_earnings_price from `wendao101-order`.course_order where promoter_id = #{id} and  order_status in (1,6) and  is_delete = 0
    </select>

    <update id="updateStatus" parameterType="com.wendao101.teacher.vo.PromoterStateVO">
        update  promoter
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountState != null">account_state = #{accountState},</if>
        </trim>
        where id = #{id} and teacher_id = #{teacherId}
    </update>

    <select id="selectReceiveList" parameterType="com.wendao101.teacher.vo.PromoterAndReceiveVO" resultType="com.wendao101.teacher.dto.PromoterAndReceiveDTO" >
        SELECT
        buyer_user_id buyerUserId,
        buyer_user_img buyerUserImg,
        buyer_user_name buyerUserName,
        course_title courseTitle,
        order_id orderId,
        pay_price payPrice,
        promotion_ratio promotionRatio,
        promoter_earnings_price promoterEarningsPrice
        FROM `wendao101-order`.course_order
        <where>
            <if test="teacherId != null ">and teacher_id = #{teacherId}</if>
            <if test="promoterId != null ">and promoter_id = #{promoterId}</if>
            <if test="orderId != null and orderId != ''">and order_id = #{orderId}</if>
            <if test="courseTitle != null and courseTitle != ''">and course_title like concat('%', #{courseTitle}, '%')</if>
            <if test="orderPlatform != null  and orderPlatform != ''">and order_platform like concat('%', #{orderPlatform}, '%')</if>
            <!-- 查询手机号和名称 -->
            <if test="nameOrPhone != null  and nameOrPhone != ''">and (buyer_user_name like concat('%', #{nameOrPhone}, '%') or buyer_user_mobile = #{nameOrPhone})</if>
            <if test="beginTime != null"><!-- 开始时间检索 -->
                AND create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null"><!-- 结束时间检索 -->
                AND create_time &lt;= #{endTime}
            </if>
              AND is_delete = 0
            order by create_time desc
        </where>
    </select>

    <update id="updatePromotionStatus" parameterType="Long">
        update promoter
        set promotion_status = 1
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <update id="updatePromoterOpenId">
        update  promoter
        set open_id = #{openId}
        where id = #{id}
    </update>

    <!--    <select id="getPromoterAll" parameterType="com.wendao101.teacher.vo.CoursePromoterVO" resultMap="PromoterResult">-->
<!--        <include refid="selectPromoterVo"/>-->
<!--        where-->
<!--        teacher_id = #{teacherId}-->
<!--        and promotion_status = #{promotionStatus}-->
<!--        and course_id = #{courseId}-->
<!--        <if test="promoterKeywords != null and promoterKeywords != ''">&lt;!&ndash; 推广人员姓名检索 &ndash;&gt;-->
<!--            and (promoter_name like concat('%', #{promoterKeywords}, '%') or promoter_phone like concat('%', #{promoterKeywords}, '%'))-->
<!--        </if>-->
<!--        order by create_time desc-->
<!--    </select>-->

    <select id="getPromoterAll" parameterType="com.wendao101.teacher.vo.CoursePromoterVO" resultType="com.wendao101.teacher.dto.AppointPromoterDTO">
        SELECT a.promoter_id promoterId,
               b.promoter_name promoterName,
               b.promoter_phone promoterPhone,
               b.platform platform,
               a.spread_rate spreadRate
        FROM promoter_course a JOIN promoter b ON a.promoter_id = b.id
        where
        a.teacher_id = #{teacherId}
        and a.course_id = #{courseId}
        and b.account_state = 1
        <if test="promoterKeywords != null and promoterKeywords != ''"><!-- 推广人员姓名检索 -->
            and (b.promoter_name like concat('%', #{promoterKeywords}, '%') or b.promoter_phone = #{promoterKeywords})
        </if>
        order by b.create_time desc
    </select>

    <select id="getPromoterAddAll" parameterType="com.wendao101.teacher.vo.CoursePromoterVO" resultType="com.wendao101.teacher.dto.AppointPromoterDTO">
        SELECT id promoterId,
               promoter_name promoterName,
               promoter_phone promoterPhone,
               platform  platform
        FROM promoter
        where id not in (SELECT promoter_id FROM promoter_course WHERE teacher_id = #{teacherId} and course_id = #{courseId})
        and account_state = 1
        and teacher_id = #{teacherId}
        <if test="promoterKeywords != null and promoterKeywords != ''"><!-- 推广人员姓名检索 -->
            and (promoter_name like concat('%', #{promoterKeywords}, '%') or promoter_phone = #{promoterKeywords})
        </if>
        order by create_time desc
    </select>
    <select id="selectTeacherIdList" resultType="com.wendao101.teacher.vo.PromoterTeacherVO">
        SELECT
        a.teacher_id AS teacherId,
        count(*) AS promoterNum
        FROM
        promoter a
        LEFT JOIN t_teacher b ON a.teacher_id = b.teacher_id
        <where>
            and a.is_delete=0
            <if test="appNameType != null ">and a.app_name_type = #{appNameType}</if>
            <if test="searchValue != null and searchValue != ''">and (
                b.shop_name like concat('%', #{searchValue}, '%')
                or b.teacher_id like concat('%', #{searchValue}, '%')
                or b.mobile like concat('%', #{searchValue}, '%')
                )
            </if>
            <if test="teacherIdList != null and teacherIdList.size > 0">
                and a.teacher_id in
                <foreach collection="teacherIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.teacher_id
        <if test="isPromoterNum != null and isPromoterNum==0"> order by promoterNum desc</if>
        <if test="isPromoterNum != null and isPromoterNum==1"> order by promoterNum asc</if>
    </select>
    <select id="selectTeacherIdList1" resultType="com.wendao101.teacher.vo.PromoterTeacherVO">
        SELECT
        a.teacher_id as teacherId,
        sum( b.may_withdraw_price + b.funds_transit_price + b.total_withdraw_price ) AS promoterincome
        FROM
        promoter a
        LEFT JOIN `wendao101-order`.withdraw_price b ON a.id = b.promoter_id
        LEFT JOIN t_teacher c ON a.teacher_id = c.teacher_id
        <where>
            a.is_delete = 0
            <if test="appNameType != null ">and a.app_name_type = #{appNameType}</if>
            <if test="searchValue != null and searchValue != ''">and (
                c.shop_name like concat('%', #{searchValue}, '%')
                or c.teacher_id like concat('%', #{searchValue}, '%')
                or c.mobile like concat('%', #{searchValue}, '%')
                )
            </if>
            <if test="teacherIdList != null and teacherIdList.size > 0">
                and a.teacher_id in
                <foreach collection="teacherIdList" index="index" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by a.teacher_id
        <if test="isPromoternIcome != null and isPromoternIcome==0"> order by promoterincome desc</if>
        <if test="isPromoternIcome != null and isPromoternIcome==1"> order by promoterincome asc</if>
    </select>
</mapper>