package com.wendao101.system.api.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wendao101.common.core.annotation.Excel;
import com.wendao101.common.core.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 短信验证码对象 t_sms
 * 
 * <AUTHOR>
 * @date 2023-08-12
 */
public class TSms extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long smsId;

    /** 验证码 */
    @Excel(name = "验证码")
    private String code;

    /** 发送顺序唯一序列（需前端回传） */
    @Excel(name = "发送顺序唯一序列", readConverterExp = "需=前端回传")
    private String codeIndexKey;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 手机号码 */
    @Excel(name = "手机号码")
    private String phoneNumber;

    public void setSmsId(Long smsId) 
    {
        this.smsId = smsId;
    }

    public Long getSmsId() 
    {
        return smsId;
    }
    public void setCode(String code) 
    {
        this.code = code;
    }

    public String getCode() 
    {
        return code;
    }
    public void setCodeIndexKey(String codeIndexKey) 
    {
        this.codeIndexKey = codeIndexKey;
    }

    public String getCodeIndexKey() 
    {
        return codeIndexKey;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setPhoneNumber(String phoneNumber) 
    {
        this.phoneNumber = phoneNumber;
    }

    public String getPhoneNumber() 
    {
        return phoneNumber;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("smsId", getSmsId())
            .append("code", getCode())
            .append("codeIndexKey", getCodeIndexKey())
            .append("expireTime", getExpireTime())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .append("phoneNumber", getPhoneNumber())
            .toString();
    }
}
