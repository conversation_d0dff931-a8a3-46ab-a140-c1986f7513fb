package com.wendao101.system.api;

import com.wendao101.common.core.web.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;
import com.wendao101.common.core.constant.SecurityConstants;
import com.wendao101.common.core.constant.ServiceNameConstants;
import com.wendao101.common.core.domain.R;
import com.wendao101.system.api.domain.SysUser;
import com.wendao101.system.api.factory.RemoteUserFallbackFactory;
import com.wendao101.system.api.model.LoginUser;

/**
 * 用户服务
 * 
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteUserService", value = ServiceNameConstants.SYSTEM_SERVICE, fallbackFactory = RemoteUserFallbackFactory.class)
public interface RemoteUserService
{
    /**
     * 通过用户名查询用户信息
     *
     * @param username 用户名
     * @param source 请求来源
     * @return 结果
     */
    @GetMapping("/user/info/{username}")
    public R<LoginUser> getUserInfo(@PathVariable("username") String username, @RequestParam("appNameType") Integer appNameType, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 注册用户信息
     *
     * @param sysUser 用户信息
     * @param source 请求来源
     * @return 结果
     */
    @PostMapping("/user/register")
    public R<Boolean> registerUserInfo(@RequestBody SysUser sysUser, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

    /**
     * 修改用户密码
     * @param user
     * @return
     */
    @PostMapping("/restTeacherPassword")
    public R<Boolean> restTeacherPassword(@RequestBody SysUser user, @RequestHeader(SecurityConstants.FROM_SOURCE) String source);

}
