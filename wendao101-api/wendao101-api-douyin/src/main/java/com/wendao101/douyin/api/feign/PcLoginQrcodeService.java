package com.wendao101.douyin.api.feign;

import com.wendao101.common.core.constant.ServiceNameConstants;
import com.wendao101.common.core.kspaydto.CreateKuaishouPreOrderDTO;
import com.wendao101.common.core.kspaydto.CreateWendaoOrderDTO;
import com.wendao101.common.core.kspaydto.CreateWendaoRefundDTO;
import com.wendao101.common.core.web.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@Component
@FeignClient(contextId = "pcLoginQrcodeService", value = ServiceNameConstants.TEACHER_SERVICE)
public interface PcLoginQrcodeService {
    @GetMapping("/qrcode/pcLogin")
    public AjaxResult pcLogin(@RequestParam("platform") Integer platform, @RequestParam("appNameType") Integer appNameType);
}
