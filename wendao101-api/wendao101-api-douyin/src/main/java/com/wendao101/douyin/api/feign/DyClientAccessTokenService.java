package com.wendao101.douyin.api.feign;

//import com.wendao101.system.api.domain.SysFile;

import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenDTO;
import com.wendao101.common.core.douyin.accesstoken.GetAccessTokenResult;
import com.wendao101.common.core.douyin.accesstoken.GetClientTokenDTO;
import com.wendao101.common.core.douyin.accesstoken.GetClientTokenResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 抖音token服务
 * 
 * <AUTHOR>
 */
@Component
@FeignClient(url = "${wendao.douyin.openDouyinDomain}", name = "dyClientAccessTokenService")
public interface DyClientAccessTokenService
{
    /**
     * 获取应用授权调用凭证
     * @param getClientTokenDTO
     * @return
     */
    @PostMapping("/oauth/client_token/")
    GetClientTokenResult getClientToken(GetClientTokenDTO getClientTokenDTO);

}
