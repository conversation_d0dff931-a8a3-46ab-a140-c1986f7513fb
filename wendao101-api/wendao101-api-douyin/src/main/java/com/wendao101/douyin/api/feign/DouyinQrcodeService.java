package com.wendao101.douyin.api.feign;

import com.wendao101.common.core.douyin.login.Code2SessionResult;
import com.wendao101.common.core.douyin.login.GetCode2SessionDTO;
import com.wendao101.common.core.douyin.qrcode.QrCodeDTO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 抖音code2Session服务
 *
 * <AUTHOR>
 */
@Component
@FeignClient(url = "${wendao.douyin.domain}", name = "douyinQrcodeService")
public interface DouyinQrcodeService {

    /**
     * qrcode
     * @param qrCodeDTO
     * @return
     */
    @PostMapping("/api/apps/qrcode")
    byte[] qrcode(QrCodeDTO qrCodeDTO);

}
