# WendaoRedirectAppServer API Documentation

## 概述

本文档描述了直播转发到AppServer的REST API接口。所有接口都基于阿里云直播AppServer文档，提供直播间管理、连麦管理、Token验证等功能。

**Base URL**: `https://${KNOWLEDGE_SHOP_DOMAIN}/prod-api/knowledge_store/live_app_server`

## 认证

所有API接口都需要在请求头中包含token进行身份验证，支持以下三种方式：

**方式一：使用token头**
```
Headers:
  token: <用户登录token>
```

**方式二：使用Authorization头（标准方式）**
```
Headers:
  Authorization: Bearer <用户登录token>
```

**方式三：使用Authorization头（直接值）**
```
Headers:
  Authorization: <用户登录token>
```

**说明**：
- 系统会依次尝试从`token`、`Authorization`、`authorization`头获取token
- 如果值以`Bearer `开头，会自动去掉前缀提取真实token
- 如果token无效或缺失，将返回401未登录错误

## API接口

### V1版本接口

#### 1. 获取直播间列表

**接口路径**: `POST /api/v1/live/list`

**描述**: 获取用户的直播间列表

**请求参数**:
```json
{
  "user_id": "5868695",
  "page_num": 1,
  "page_size": 20,
  "im_server": ["aliyun_new"]
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/list" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "page_num": 1,
    "page_size": 20,
    "im_server": ["aliyun_new"]
  }'
```

**响应**: 返回直播间列表数据

---

#### 2. 获取直播间信息

**接口路径**: `POST /api/v1/live/get`

**描述**: 获取指定直播间的详细信息

**请求参数**:
```json
{
  "user_id": "5868695",
  "id": "f9af7a0e83114c3596ed122a95340455",
  "im_server": ["aliyun_new"]
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/get" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "id": "f9af7a0e83114c3596ed122a95340455",
    "im_server": ["aliyun_new"]
  }'
```

**响应**: 返回直播间详细信息

---

#### 3. 开始直播

**接口路径**: `POST /api/v1/live/start`

**描述**: 启动指定直播间的直播

**请求参数**:
```json
{
  "user_id": "5868695",
  "id": "f9af7a0e83114c3596ed122a95340455"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/start" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "id": "f9af7a0e83114c3596ed122a95340455"
  }'
```

**响应**: 返回开始直播结果

---

#### 4. 停止直播

**接口路径**: `POST /api/v1/live/stop`

**描述**: 停止指定直播间的直播

**请求参数**:
```json
{
  "user_id": "5868695",
  "id": "f9af7a0e83114c3596ed122a95340455"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/stop" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "id": "f9af7a0e83114c3596ed122a95340455"
  }'
```

**响应**: 返回停止直播结果

---

#### 5. 更新直播间信息

**接口路径**: `POST /api/v1/live/update`

**描述**: 更新直播间的基本信息（需要群主或管理员权限）

**请求参数**:
```json
{
  "id": "f9af7a0e83114c3596ed122a95340455",
  "title": "我的直播间",
  "notice": "公告内容",
  "extends": "直播间扩展信息,可以是json字符串"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/update" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "id": "f9af7a0e83114c3596ed122a95340455",
    "title": "我的直播间",
    "notice": "公告内容",
    "extends": "直播间扩展信息"
  }'
```

**响应**: 返回更新结果

---

#### 6. 获取连麦信息

**接口路径**: `POST /api/v1/live/getMeetingInfo`

**描述**: 获取直播间的连麦信息

**请求参数**:
```json
{
  "id": "f9af7a0e83114c3596ed122a95340455"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/getMeetingInfo" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "id": "f9af7a0e83114c3596ed122a95340455"
  }'
```

**响应**: 返回连麦信息

---

#### 7. 更新连麦信息

**接口路径**: `POST /api/v1/live/updateMeetingInfo`

**描述**: 更新直播间的连麦成员信息

**请求参数**:
```json
{
  "id": "f9af7a0e83114c3596ed122a95340455",
  "members": "这里是连麦成员的json信息"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/updateMeetingInfo" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "id": "f9af7a0e83114c3596ed122a95340455",
    "members": "连麦成员json信息"
  }'
```

**响应**: 返回更新结果

---

#### 8. 获取直播跳转链接

**接口路径**: `POST /api/v1/live/getLiveJumpUrl`

**描述**: 获取直播间的跳转链接

**请求参数**:
```json
{
  "user_id": "5868695",
  "live_id": "f9af7a0e83114c3596ed122a95340455",
  "user_name": "mick",
  "version": "v2"
}
```

**参数说明**:
- `version`: 固定值为 "v2"

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/getLiveJumpUrl" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "live_id": "f9af7a0e83114c3596ed122a95340455",
    "user_name": "mick",
    "version": "v2"
  }'
```

**响应**: 返回跳转链接信息

---

#### 9. 验证授权Token

**接口路径**: `POST /api/v1/live/verifyAuthToken`

**描述**: 验证用户的授权Token

**请求参数**:
```json
{
  "user_id": "5868695",
  "live_id": "f9af7a0e83114c3596ed122a95340455",
  "user_name": "mick",
  "app_server": "https://alive-test.wendao101.com:443",
  "token": "abcd78954477575758ppo"
}
```

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v1/live/verifyAuthToken" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "live_id": "f9af7a0e83114c3596ed122a95340455",
    "user_name": "mick",
    "app_server": "https://alive-test.wendao101.com:443",
    "token": "abcd78954477575758ppo"
  }'
```

**响应**: 返回Token验证结果

---

### V2版本接口

#### 10. 获取IM Token

**接口路径**: `POST /api/v2/live/token`

**描述**: 获取即时通讯Token

**请求参数**:
```json
{
  "live_id": "123456",
  "user_id": "5868695",
  "device_id": "uuid-1232555-uuid-2255",
  "device_type": "web",
  "im_server": ["aliyun_new"]
}
```

**返回值中的role说明**:
- `role`: 普通用户为空字符串 ""，管理员为 "admin"(有管理群的能力,包含禁言)

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v2/live/token" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "user_id": "5868695",
    "device_id": "uuid-1232555-uuid-2255",
    "device_type": "web",
    "im_server": ["aliyun_new"],
    "role": ""
  }'
```

**响应**: 返回IM Token信息

---

#### 11. 创建直播间

**接口路径**: `POST /api/v2/live/create`

**描述**: 创建新的直播间

**请求参数**:
```json
{
  "title": "我的直播",
  "notice": "公告内容",
  "anchor": "7788877",
  "anchor_nick": "花儿主播",
  "mode": 1,
  "extends": "扩展字段, json格式",
  "im_server": ["aliyun_new"]
}
```

**参数说明**:
- `mode`: 直播模式，0为普通直播，1为连麦直播（默认0）
- `anchor`: 主播ID，由系统自动设置为当前用户ID

**请求示例**:
```bash
curl -X POST "http://localhost:9550/prod-api/knowledge_store/live_app_server/api/v2/live/create" \
  -H "Content-Type: application/json" \
  -H "token: your-auth-token" \
  -d '{
    "title": "我的直播",
    "notice": "公告内容",
    "anchor": "7788877",
    "anchor_nick": "花儿主播",
    "mode": 1,
    "extends": "扩展字段, json格式",
    "im_server": ["aliyun_new"]
  }'
```

**响应**: 返回创建的直播间信息

---

## 错误响应

### 401 未登录
```json
{
  "code": 401,
  "msg": "未登录"
}
```

### 转发失败
```json
{
  "code": 500,
  "msg": "转发请求失败: [具体错误信息]"
}
```

## 注意事项

1. 所有接口都需要有效的token进行身份验证
2. 接口会自动将当前用户ID设置到相应的参数中
3. 创建直播间接口需要验证用户是否具有主播权限
4. 更新直播间信息需要用户是群主或管理员
5. 所有请求都会转发到目标服务器 `https://alive-test.wendao101.com`
6. 请求超时时间为30秒，连接超时时间为15秒

## 数据模型

### RoomListRequestDto
```json
{
  "user_id": "string",
  "page_num": "integer",
  "page_size": "integer", 
  "im_server": ["string"]
}
```

### RoomGetRequestDto
```json
{
  "user_id": "string",
  "id": "string",
  "im_server": ["string"]
}
```

### RoomUpdateStatusRequestDto
```json
{
  "user_id": "string",
  "id": "string"
}
```

### RoomUpdateRequestDto
```json
{
  "id": "string",
  "title": "string",
  "notice": "string",
  "extends": "string"
}
```

### JumpUrlRequestDto
```json
{
  "user_id": "string",
  "live_id": "string",
  "user_name": "string",
  "version": "string"
}
```

### AuthTokenRequestDto
```json
{
  "user_id": "string",
  "live_id": "string", 
  "user_name": "string",
  "app_server": "string",
  "token": "string"
}
```

### ImTokenRequestDto
```json
{
  "user_id": "string",
  "device_id": "string",
  "device_type": "string",
  "im_server": ["string"],
  "role": "string"
}
```

### RoomCreateRequestDto
```json
{
  "title": "string",
  "notice": "string",
  "anchor": "string",
  "anchor_nick": "string",
  "mode": "integer",
  "extends": "string",
  "im_server": ["string"]
}
``` 